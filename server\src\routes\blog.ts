import express from "express";
import {
  getBlogPosts,
  getBlogPost,
  createBlogPost,
  updateBlogPost,
  deleteBlogPost,
  toggleBlogPostVisibility,
} from "../controllers/blogController";
import { authenticate, authorize } from "../middleware/auth";
import { uploadBlogImage } from "../middleware/upload";

const router = express.Router();

// Public routes
router.get("/", getBlogPosts);
router.get("/:slug", getBlogPost);

// Protected routes (Admin only)
router.post(
  "/",
  authenticate,
  authorize("ADMIN"),
  uploadBlogImage.single("featuredImage"),
  createBlogPost
);
router.put(
  "/:id",
  authenticate,
  authorize("ADMIN"),
  uploadBlogImage.single("featuredImage"),
  updateBlogPost
);
router.delete("/:id", authenticate, authorize("ADMIN"), deleteBlogPost);
router.patch(
  "/:id/toggle-visibility",
  authenticate,
  authorize("ADMIN"),
  toggleBlogPostVisibility
);

export default router;
