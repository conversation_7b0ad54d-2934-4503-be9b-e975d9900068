{"version": 3, "sources": ["../../.pnpm/@tiptap+extension-table-cell@2.22.3_@tiptap+core@2.22.3_@tiptap+pm@2.22.3_/node_modules/@tiptap/extension-table-cell/src/table-cell.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TableCellOptions {\n  /**\n   * The HTML attributes for a table cell node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\n/**\n * This extension allows you to create table cells.\n * @see https://www.tiptap.dev/api/nodes/table-cell\n */\nexport const TableCell = Node.create<TableCellOptions>({\n  name: 'tableCell',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  addAttributes() {\n    return {\n      colspan: {\n        default: 1,\n      },\n      rowspan: {\n        default: 1,\n      },\n      colwidth: {\n        default: null,\n        parseHTML: element => {\n          const colwidth = element.getAttribute('colwidth')\n          const value = colwidth\n            ? colwidth.split(',').map(width => parseInt(width, 10))\n            : null\n\n          return value\n        },\n      },\n    }\n  },\n\n  tableRole: 'cell',\n\n  isolating: true,\n\n  parseHTML() {\n    return [\n      { tag: 'td' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['td', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n})\n"], "mappings": ";;;;;;;AAea,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,SAAS;EAET,gBAAa;AACX,WAAO;MACL,SAAS;QACP,SAAS;MACV;MACD,SAAS;QACP,SAAS;MACV;MACD,UAAU;QACR,SAAS;QACT,WAAW,aAAU;AACnB,gBAAM,WAAW,QAAQ,aAAa,UAAU;AAChD,gBAAM,QAAQ,WACV,SAAS,MAAM,GAAG,EAAE,IAAI,WAAS,SAAS,OAAO,EAAE,CAAC,IACpD;AAEJ,iBAAO;;MAEV;;;EAIL,WAAW;EAEX,WAAW;EAEX,YAAS;AACP,WAAO;MACL,EAAE,KAAK,KAAI;;;EAIf,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;AAGhF,CAAA;", "names": []}