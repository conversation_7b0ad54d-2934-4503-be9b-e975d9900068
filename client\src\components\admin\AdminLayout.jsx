import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

const AdminLayout = ({ children, title }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("adminToken");
    localStorage.removeItem("adminUser");
    navigate("/admin");
    setUserDropdownOpen(false);
  };

  const toggleUserDropdown = () => {
    setUserDropdownOpen(!userDropdownOpen);
  };

  const isActive = (path) => {
    return (
      location.pathname === path || location.pathname.startsWith(path + "/")
    );
  };

  const user = JSON.parse(localStorage.getItem("adminUser") || "{}");

  const menuItems = [
    {
      path: "/admin/dashboard",
      icon: "solar:widget-2-bold",
      label: "Dashboard",
      exact: true,
    },
    {
      path: "/admin/posts",
      icon: "solar:document-text-bold",
      label: "Blog Posts",
      exact: false,
    },
    {
      path: "/admin/blog/new",
      icon: "solar:add-circle-bold",
      label: "New Post",
      exact: true,
    },
    {
      path: "/admin/categories",
      icon: "solar:folder-bold",
      label: "Categories",
      exact: true,
    },
    {
      path: "/admin/tags",
      icon: "solar:tag-bold",
      label: "Tags",
      exact: true,
    },
  ];

  return (
    <div className="admin-layout-wrapper">
      {/* Mobile Overlay */}
      {mobileSidebarOpen && (
        <div
          className="admin-mobile-overlay"
          onClick={() => setMobileSidebarOpen(false)}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={`admin-sidebar ${sidebarCollapsed ? "collapsed" : ""} ${
          mobileSidebarOpen ? "mobile-open" : ""
        }`}
      >
        {/* Logo */}
        <div className="admin-sidebar-header">
          <div className="admin-logo">
            {!sidebarCollapsed ? (
              <span>
                <span className="color-primary-1">DevSkills</span> Admin
              </span>
            ) : (
              <span className="color-primary-1">DS</span>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="admin-sidebar-nav">
          <ul className="admin-nav-list">
            {menuItems.map((item) => (
              <li
                key={item.path}
                className={`admin-nav-item ${
                  isActive(item.path) ? "active" : ""
                }`}
              >
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(item.path);
                    setMobileSidebarOpen(false);
                  }}
                  className="admin-nav-link"
                  title={item.label}
                >
                  <iconify-icon
                    icon={item.icon}
                    className="admin-nav-icon"
                  ></iconify-icon>
                  <span className="admin-nav-text">{item.label}</span>
                </a>
              </li>
            ))}

            {/* Divider */}
            <li className="admin-nav-divider"></li>

            {/* View Site */}
            <li className="admin-nav-item">
              <a
                href="/"
                target="_blank"
                className="admin-nav-link"
                title="View Site"
              >
                <iconify-icon
                  icon="solar:link-bold"
                  className="admin-nav-icon"
                ></iconify-icon>
                <span className="admin-nav-text">View Site</span>
              </a>
            </li>
          </ul>
        </nav>

        {/* User Menu */}
        <div className="admin-sidebar-footer">
          <div className="admin-user-menu" ref={dropdownRef}>
            <div
              className="admin-user-info clickable"
              onClick={toggleUserDropdown}
              title="User menu"
            >
              <div className="admin-user-avatar">
                <iconify-icon icon="solar:user-bold"></iconify-icon>
              </div>
              {!sidebarCollapsed && (
                <div className="admin-user-details">
                  <div className="admin-user-name">{user.name || "Admin"}</div>
                  <div className="admin-user-email">{user.email}</div>
                </div>
              )}
              <div className="admin-user-dropdown-arrow">
                <iconify-icon
                  icon={`solar:alt-arrow-${
                    userDropdownOpen ? "up" : "down"
                  }-bold`}
                ></iconify-icon>
              </div>
            </div>

            {/* Dropdown Menu */}
            {userDropdownOpen && (
              <div className="admin-user-dropdown">
                <div
                  className="admin-dropdown-item"
                  onClick={() => {
                    navigate("/admin/dashboard");
                    setUserDropdownOpen(false);
                  }}
                >
                  <iconify-icon
                    icon="solar:widget-2-bold"
                    className="me-2"
                  ></iconify-icon>
                  Dashboard
                </div>
                <div
                  className="admin-dropdown-item"
                  onClick={() => {
                    window.open("/", "_blank");
                    setUserDropdownOpen(false);
                  }}
                >
                  <iconify-icon
                    icon="solar:link-bold"
                    className="me-2"
                  ></iconify-icon>
                  View Site
                </div>
                <div className="admin-dropdown-divider"></div>
                <div
                  className="admin-dropdown-item logout"
                  onClick={handleLogout}
                >
                  <iconify-icon
                    icon="solar:power-bold"
                    className="me-2"
                  ></iconify-icon>
                  Logout
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main
        className={`admin-main-content ${
          sidebarCollapsed ? "sidebar-collapsed" : ""
        }`}
      >
        {/* Top Bar */}
        <header className="admin-topbar">
          <div className="admin-topbar-left">
            <button
              className="admin-sidebar-toggle"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              <iconify-icon icon="solar:hamburger-menu-bold"></iconify-icon>
            </button>
            <button
              className="admin-mobile-toggle"
              onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}
            >
              <iconify-icon icon="solar:hamburger-menu-bold"></iconify-icon>
            </button>
            {title && <h1 className="admin-page-title">{title}</h1>}
          </div>
          <div className="admin-topbar-right">
            <span className="admin-welcome">
              Welcome, {user.name || user.email}
            </span>
          </div>
        </header>

        {/* Page Content */}
        <div className="admin-content">{children}</div>
      </main>
    </div>
  );
};

export default AdminLayout;
