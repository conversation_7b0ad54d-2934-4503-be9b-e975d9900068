import multer from 'multer';
export declare const uploadBlogImage: multer.Multer;
export declare const uploadBlogImages: multer.Multer;
export declare const uploadTemp: multer.Multer;
export declare const moveFromTemp: (tempFilename: string, permanentPath: string) => Promise<string>;
export declare const deleteFile: (filePath: string) => Promise<void>;
export declare const getFileUrl: (filename: string, type?: "blog-images" | "temp") => string;
export declare const cleanupTempFiles: (maxAgeHours?: number) => void;
//# sourceMappingURL=upload.d.ts.map