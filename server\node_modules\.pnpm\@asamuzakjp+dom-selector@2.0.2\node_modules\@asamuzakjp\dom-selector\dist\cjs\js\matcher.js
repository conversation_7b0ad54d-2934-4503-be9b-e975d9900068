var W=Object.create;var R=Object.defineProperty;var z=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var j=Object.getPrototypeOf,V=Object.prototype.hasOwnProperty;var F=(T,s)=>{for(var e in s)R(T,e,{get:s[e],enumerable:!0})},I=(T,s,e,f)=>{if(s&&typeof s=="object"||typeof s=="function")for(let n of B(s))!V.call(T,n)&&n!==e&&R(T,n,{get:()=>s[n],enumerable:!(f=z(s,n))||f.enumerable});return T};var H=(T,s,e)=>(e=T!=null?W(j(T)):{},I(s||!T||!T.__esModule?R(e,"default",{value:T,enumerable:!0}):e,T)),G=T=>I(R({},"__esModule",{value:!0}),T);var Y={};F(Y,{Matcher:()=>q});module.exports=G(Y);var U=H(require("is-potential-custom-element-name"),1),S=require("./dom-util.js"),v=require("./parser.js"),i=require("./constant.js");const C="next",O="prev",A="all",L="first",$="lineal",M="self";class q{#i;#h;#r;#s;#a;#e;#o;#t;#c;#f;#l;#n;constructor(s,e,f={}){const{warn:n}=f;this.#h=new Map([[i.SELECTOR_PSEUDO_ELEMENT,i.BIT_01],[i.SELECTOR_ID,i.BIT_02],[i.SELECTOR_CLASS,i.BIT_04],[i.SELECTOR_TYPE,i.BIT_08],[i.SELECTOR_ATTR,i.BIT_16],[i.SELECTOR_PSEUDO_CLASS,i.BIT_32]]),this.#r=new WeakMap,this.#e=e,[this.#n,this.#s,this.#t,this.#f]=this._setup(e),this.#c=(0,S.isInShadowTree)(e),[this.#i,this.#o]=this._correspond(s),this.#l=!!n}_onError(s){if((s instanceof DOMException||s instanceof this.#n.DOMException)&&s.name===i.NOT_SUPPORTED_ERR)this.#l&&console.warn(s.message);else throw s instanceof DOMException?new this.#n.DOMException(s.message,s.name):s instanceof TypeError?new this.#n.TypeError(s.message):s}_setup(s){let e,f;switch(s?.nodeType){case i.DOCUMENT_NODE:{e=s,f=s;break}case i.DOCUMENT_FRAGMENT_NODE:{e=s.ownerDocument,f=s;break}case i.ELEMENT_NODE:{if(s.ownerDocument.contains(s))e=s.ownerDocument,f=s.ownerDocument;else{let c=s;for(;c&&c.parentNode;)c=c.parentNode;e=c.ownerDocument,f=c}break}default:{let c;throw s?.nodeName?c=`Unexpected node ${s.nodeName}`:c=`Unexpected node ${Object.prototype.toString.call(s).slice(i.TYPE_FROM,i.TYPE_TO)}`,new TypeError(c)}}const n=i.SHOW_DOCUMENT|i.SHOW_DOCUMENT_FRAGMENT|i.SHOW_ELEMENT,l=e.createTreeWalker(f,n);return[e.defaultView,e,f,l]}_sortLeaves(s){const e=[...s];return e.length>1&&e.sort((f,n)=>{const{type:l}=f,{type:b}=n,c=this.#h.get(l),d=this.#h.get(b);let t;return c===d?t=0:c>d?t=1:t=-1,t}),e}_correspond(s){let e;try{e=(0,v.parseSelector)(s)}catch(c){this._onError(c)}const f=(0,v.walkAST)(e),n=[],l=[];let b=0;for(const[...c]of f){const d=[];let t=c.shift();if(t&&t.type!==i.COMBINATOR){const r=new Set;for(;t;){if(t.type===i.COMBINATOR){const[a]=c;if(a.type===i.COMBINATOR){const o=`Invalid combinator ${t.name}${a.name}`;throw new this.#n.DOMException(o,i.SYNTAX_ERR)}d.push({combo:t,leaves:this._sortLeaves(r)}),r.clear()}else t&&r.add(t);if(c.length)t=c.shift();else{d.push({combo:null,leaves:this._sortLeaves(r)}),r.clear();break}}}n.push({branch:d,dir:null,filtered:!1,find:!1}),l[b]=new Set,b++}return[n,l]}_traverse(s={},e=this.#f){let f,n=e.currentNode;if(s.nodeType===i.ELEMENT_NODE&&n===s)f=n;else{if(n!==e.root)for(;n&&!(n===e.root||s.nodeType===i.ELEMENT_NODE&&n===s);)n=e.parentNode();if(s.nodeType===i.ELEMENT_NODE)for(;n;){if(n===s){f=n;break}n=e.nextNode()}else f=n}return f??null}_collectNthChild(s,e){const{a:f,b:n,reverse:l,selector:b}=s,{parentNode:c}=e;let d=new Set,t;if(b&&(this.#r.has(b)?t=this.#r.get(b):(t=(0,v.walkAST)(b),this.#r.set(b,t))),c){const r=i.SHOW_DOCUMENT|i.SHOW_DOCUMENT_FRAGMENT|i.SHOW_ELEMENT,a=this.#s.createTreeWalker(c,r);let o=0,h=a.firstChild();for(;h;)o++,h=a.nextSibling();h=this._traverse(c,a);const m=new Set;if(t)for(h=this._traverse(c,a),h=a.firstChild();h;){let p;for(const g of t)if(p=this._matchLeaves(g,h),!p)break;p&&m.add(h),h=a.nextSibling()}if(f===0){if(n>0&&n<=o){if(m.size){let p=0;for(h=this._traverse(c,a),l?h=a.lastChild():h=a.firstChild();h;){if(m.has(h)){if(p===n-1){d.add(h);break}p++}l?h=a.previousSibling():h=a.nextSibling()}}else if(!b){let p=0;for(h=this._traverse(c,a),l?h=a.lastChild():h=a.firstChild();h;){if(p===n-1){d.add(h);break}l?h=a.previousSibling():h=a.nextSibling(),p++}}}}else{let p=n-1;if(f>0)for(;p<0;)p+=f;if(p>=0&&p<o){let g=0,N=f>0?0:n-1;for(h=this._traverse(c,a),l?h=a.lastChild():h=a.firstChild();h&&(h&&p>=0&&p<o);)m.size?m.has(h)&&(N===p&&(d.add(h),p+=f),f>0?N++:N--):g===p&&(b||d.add(h),p+=f),l?h=a.previousSibling():h=a.nextSibling(),g++}}if(l&&d.size>1){const p=[...d];d=new Set(p.reverse())}}else if(e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE&&f+n===1)if(t){let r;for(const a of t)if(r=this._matchLeaves(a,e),r)break;r&&d.add(e)}else d.add(e);return d}_collectNthOfType(s,e){const{a:f,b:n,reverse:l}=s,{localName:b,parentNode:c,prefix:d}=e;let t=new Set;if(c){const r=i.SHOW_DOCUMENT|i.SHOW_DOCUMENT_FRAGMENT|i.SHOW_ELEMENT,a=this.#s.createTreeWalker(c,r);let o=0,h=a.firstChild();for(;h;)o++,h=a.nextSibling();if(f===0){if(n>0&&n<=o){let m=0;for(h=this._traverse(c,a),l?h=a.lastChild():h=a.firstChild();h;){const{localName:p,prefix:g}=h;if(p===b&&g===d){if(m===n-1){t.add(h);break}m++}l?h=a.previousSibling():h=a.nextSibling()}}}else{let m=n-1;if(f>0)for(;m<0;)m+=f;if(m>=0&&m<o){let p=f>0?0:n-1;for(h=this._traverse(c,a),l?h=a.lastChild():h=a.firstChild();h;){const{localName:g,prefix:N}=h;if(g===b&&N===d){if(p===m&&(t.add(h),m+=f),m<0||m>=o)break;f>0?p++:p--}l?h=a.previousSibling():h=a.nextSibling()}}}if(l&&t.size>1){const m=[...t];t=new Set(m.reverse())}}else e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE&&f+n===1&&t.add(e);return t}_matchAnPlusB(s,e,f){const{nth:{a:n,b:l,name:b},selector:c}=s,d=(0,v.unescapeSelector)(b),t=new Map;d?(d==="even"?(t.set("a",2),t.set("b",0)):d==="odd"&&(t.set("a",2),t.set("b",1)),f.indexOf("last")>-1&&t.set("reverse",!0)):(typeof n=="string"&&/-?\d+/.test(n)?t.set("a",n*1):t.set("a",0),typeof l=="string"&&/-?\d+/.test(l)?t.set("b",l*1):t.set("b",0),f.indexOf("last")>-1&&t.set("reverse",!0));let r=new Set;if(t.has("a")&&t.has("b")){if(/^nth-(?:last-)?child$/.test(f)){c&&t.set("selector",c);const a=Object.fromEntries(t),o=this._collectNthChild(a,e);o.size&&(r=o)}else if(/^nth-(?:last-)?of-type$/.test(f)){const a=Object.fromEntries(t),o=this._collectNthOfType(a,e);o.size&&(r=o)}}return r}_matchPseudoElementSelector(s,e={}){const{forgive:f}=e;switch(s){case"after":case"backdrop":case"before":case"cue":case"cue-region":case"first-letter":case"first-line":case"file-selector-button":case"marker":case"placeholder":case"selection":case"target-text":{if(this.#l){const n=`Unsupported pseudo-element ::${s}`;throw new DOMException(n,i.NOT_SUPPORTED_ERR)}break}case"part":case"slotted":{if(this.#l){const n=`Unsupported pseudo-element ::${s}()`;throw new DOMException(n,i.NOT_SUPPORTED_ERR)}break}default:if(s.startsWith("-webkit-")){if(this.#l){const n=`Unsupported pseudo-element ::${s}`;throw new DOMException(n,i.NOT_SUPPORTED_ERR)}}else if(!f){const n=`Unknown pseudo-element ::${s}`;throw new DOMException(n,i.SYNTAX_ERR)}}}_matchDirectionPseudoClass(s,e){const f=(0,v.unescapeSelector)(s.name),n=(0,S.getDirectionality)(e);let l;return f===n&&(l=e),l??null}_matchLanguagePseudoClass(s,e){const f=(0,v.unescapeSelector)(s.name);let n;if(f==="*")if(e.hasAttribute("lang"))e.getAttribute("lang")&&(n=e);else{let l=e.parentNode;for(;l&&l.nodeType===i.ELEMENT_NODE;){if(l.hasAttribute("lang")){l.getAttribute("lang")&&(n=e);break}l=l.parentNode}}else if(f){const l=`(?:-${i.ALPHA_NUM})*`;if(new RegExp(`^(?:\\*-)?${i.ALPHA_NUM}${l}$`,"i").test(f)){let c;if(f.indexOf("-")>-1){const[d,t,...r]=f.split("-");let a;d==="*"?a=`${i.ALPHA_NUM}${l}`:a=`${d}${l}`;const o=`-${t}${l}`,h=r.length;let m="";if(h)for(let p=0;p<h;p++)m+=`-${r[p]}${l}`;c=new RegExp(`^${a}${o}${m}$`,"i")}else c=new RegExp(`^${f}${l}$`,"i");if(e.hasAttribute("lang"))c.test(e.getAttribute("lang"))&&(n=e);else{let d=e.parentNode;for(;d&&d.nodeType===i.ELEMENT_NODE;){if(d.hasAttribute("lang")){const t=d.getAttribute("lang");c.test(t)&&(n=e);break}d=d.parentNode}}}}return n??null}_matchHasPseudoFunc(s,e){let f;if(Array.isArray(s)&&s.length){const[n]=s,{type:l}=n;let b;l===i.COMBINATOR?b=s.shift():b={name:" ",type:i.COMBINATOR};const c=[];for(;s.length;){const[r]=s,{type:a}=r;if(a===i.COMBINATOR)break;c.push(s.shift())}const d={combo:b,leaves:c},t=this._matchCombinator(d,e,{dir:C});if(t.size)if(s.length){for(const r of t)if(f=this._matchHasPseudoFunc(Object.assign([],s),r),f)break}else f=!0}return!!f}_matchLogicalPseudoFunc(s,e){const{astName:f="",branches:n=[],selector:l="",twigBranches:b=[]}=s;let c;if(f==="has")if(l.includes(":has("))c=null;else{let d;for(const t of n)if(d=this._matchHasPseudoFunc(Object.assign([],t),e),d)break;d&&(c=e)}else{const d=/^(?:is|where)$/.test(f),t=b.length;let r;for(let a=0;a<t;a++){const o=b[a],h=o.length-1,{leaves:m}=o[h];if(r=this._matchLeaves(m,e,{forgive:d}),r&&h>0){let p=new Set([e]);for(let g=h-1;g>=0;g--){const N=o[g],u=[];for(const w of p){const k=this._matchCombinator(N,w,{forgive:d,dir:O});k.size&&u.push(...k)}if(u.length)g===0?r=!0:p=new Set(u);else{r=!1;break}}}if(r)break}f==="not"?r||(c=e):r&&(c=e)}return c??null}_matchPseudoClassSelector(s,e,f={}){const{children:n}=s,{localName:l,parentNode:b}=e,{forgive:c}=f,d=(0,v.unescapeSelector)(s.name);let t=new Set;if(i.REG_LOGICAL_PSEUDO.test(d)){let r;if(this.#r.has(s))r=this.#r.get(s);else{const o=(0,v.walkAST)(s),h=[],m=[];for(const[...p]of o){for(const w of p){const k=(0,v.generateCSS)(w);h.push(k)}const g=[],N=new Set;let u=p.shift();for(;u;)if(u.type===i.COMBINATOR?(g.push({combo:u,leaves:[...N]}),N.clear()):u&&N.add(u),p.length)u=p.shift();else{g.push({combo:null,leaves:[...N]}),N.clear();break}m.push(g)}r={astName:d,branches:o,twigBranches:m,selector:h.join(",")},this.#r.set(s,r)}const a=this._matchLogicalPseudoFunc(r,e);a&&t.add(a)}else if(Array.isArray(n)){const[r]=n;if(/^nth-(?:last-)?(?:child|of-type)$/.test(d)){const a=this._matchAnPlusB(r,e,d);a.size&&(t=a)}else if(d==="dir"){const a=this._matchDirectionPseudoClass(r,e);a&&t.add(a)}else if(d==="lang"){const a=this._matchLanguagePseudoClass(r,e);a&&t.add(a)}else switch(d){case"current":case"nth-col":case"nth-last-col":{if(this.#l){const a=`Unsupported pseudo-class :${d}()`;throw new DOMException(a,i.NOT_SUPPORTED_ERR)}break}case"host":case"host-context":break;default:if(!c){const a=`Unknown pseudo-class :${d}()`;throw new DOMException(a,i.SYNTAX_ERR)}}}else{const r=/^a(?:rea)?$/,a=/^(?:(?:fieldse|inpu|selec)t|button|opt(?:group|ion)|textarea)$/,o=/^(?:(?:inpu|selec)t|button|form|textarea)$/,h=/^d(?:etails|ialog)$/,m=/^(?:checkbox|radio)$/,p=/^(?:date(?:time-local)?|month|time|week)$/,g=/(?:(?:rang|tim)e|date(?:time-local)?|month|number|week)$/,N=/^(?:(?:emai|te|ur)l|number|password|search|text)$/;switch(d){case"any-link":case"link":{r.test(l)&&e.hasAttribute("href")&&t.add(e);break}case"local-link":{if(r.test(l)&&e.hasAttribute("href")){const{href:u,origin:w,pathname:k}=new URL(this.#s.URL),y=new URL(e.getAttribute("href"),u);y.origin===w&&y.pathname===k&&t.add(e)}break}case"visited":break;case"target":{const{hash:u}=new URL(this.#s.URL);e.id&&u===`#${e.id}`&&this.#s.contains(e)&&t.add(e);break}case"target-within":{const{hash:u}=new URL(this.#s.URL);if(u){const w=u.replace(/^#/,"");let k=this.#s.getElementById(w);for(;k;){if(k===e){t.add(e);break}k=k.parentNode}}break}case"scope":{this.#e.nodeType===i.ELEMENT_NODE?e===this.#e&&t.add(e):e===this.#s.documentElement&&t.add(e);break}case"focus":{e===this.#s.activeElement&&t.add(e);break}case"focus-within":{let u=this.#s.activeElement;for(;u;){if(u===e){t.add(e);break}u=u.parentNode}break}case"open":{h.test(l)&&e.hasAttribute("open")&&t.add(e);break}case"closed":{h.test(l)&&!e.hasAttribute("open")&&t.add(e);break}case"disabled":{if(a.test(l)||(0,U.default)(l))if(e.disabled||e.hasAttribute("disabled"))t.add(e);else{let u=b;for(;u&&u.localName!=="fieldset";)u=u.parentNode;u&&b.localName!=="legend"&&u.hasAttribute("disabled")&&t.add(e)}break}case"enabled":{(a.test(l)||(0,U.default)(l))&&!(e.disabled&&e.hasAttribute("disabled"))&&t.add(e);break}case"read-only":{switch(l){case"textarea":{(e.readonly||e.hasAttribute("readonly")||e.disabled||e.hasAttribute("disabled"))&&t.add(e);break}case"input":{(!e.type||p.test(e.type)||N.test(e.type))&&(e.readonly||e.hasAttribute("readonly")||e.disabled||e.hasAttribute("disabled"))&&t.add(e);break}default:(0,S.isContentEditable)(e)||t.add(e)}break}case"read-write":{switch(l){case"textarea":{e.readonly||e.hasAttribute("readonly")||e.disabled||e.hasAttribute("disabled")||t.add(e);break}case"input":{(!e.type||p.test(e.type)||N.test(e.type))&&!(e.readonly||e.hasAttribute("readonly")||e.disabled||e.hasAttribute("disabled"))&&t.add(e);break}default:(0,S.isContentEditable)(e)&&t.add(e)}break}case"placeholder-shown":{let u;l==="textarea"?u=e:l==="input"&&(e.hasAttribute("type")?N.test(e.getAttribute("type"))&&(u=e):u=e),u&&e.value===""&&e.hasAttribute("placeholder")&&e.getAttribute("placeholder").trim().length&&t.add(e);break}case"checked":{(e.checked&&l==="input"&&e.hasAttribute("type")&&m.test(e.getAttribute("type"))||e.selected&&l==="option")&&t.add(e);break}case"indeterminate":{if(e.indeterminate&&l==="input"&&e.type==="checkbox"||l==="progress"&&!e.hasAttribute("value"))t.add(e);else if(l==="input"&&e.type==="radio"&&!e.hasAttribute("checked")){const u=e.name;let w=e.parentNode;for(;w&&w.localName!=="form";)w=w.parentNode;w||(w=this.#s.documentElement);let k;const y=[].slice.call(w.getElementsByTagName("input"));for(const _ of y)if(_.getAttribute("type")==="radio"&&(u?_.getAttribute("name")===u&&(k=!!_.checked):_.hasAttribute("name")||(k=!!_.checked),k))break;k||t.add(e)}break}case"default":{const u=/^(?:button|reset)$/,w=/^(?:image|submit)$/;if(l==="button"&&!(e.hasAttribute("type")&&u.test(e.getAttribute("type")))||l==="input"&&e.hasAttribute("type")&&w.test(e.getAttribute("type"))){let k=e.parentNode;for(;k&&k.localName!=="form";)k=k.parentNode;if(k){const y=this.#s.createTreeWalker(k,i.SHOW_ELEMENT);let _=y.firstChild();for(;_;){const x=_.localName;let E;if(x==="button"?E=!(_.hasAttribute("type")&&u.test(_.getAttribute("type"))):x==="input"&&(E=_.hasAttribute("type")&&w.test(_.getAttribute("type"))),E){_===e&&t.add(e);break}_=y.nextNode()}}}else if(l==="input"&&e.hasAttribute("type")&&m.test(e.getAttribute("type"))&&(e.checked||e.hasAttribute("checked")))t.add(e);else if(l==="option"){let k=!1,y=b;for(;y&&y.localName!=="datalist";){if(y.localName==="select"){(y.multiple||y.hasAttribute("multiple"))&&(k=!0);break}y=y.parentNode}if(k)(e.selected||e.hasAttribute("selected"))&&t.add(e);else{const _=new Set,x=this.#s.createTreeWalker(b,i.SHOW_ELEMENT);let E=x.firstChild();for(;E;){if(E.selected||E.hasAttribute("selected")){_.add(E);break}E=x.nextSibling()}_.size&&_.has(e)&&t.add(e)}}break}case"valid":{if(o.test(l))e.checkValidity()&&t.add(e);else if(l==="fieldset"){let u;const w=this.#s.createTreeWalker(e,i.SHOW_ELEMENT);let k=w.firstChild();for(;k&&!(o.test(k.localName)&&(u=k.checkValidity(),!u));)k=w.nextNode();u&&t.add(e)}break}case"invalid":{if(o.test(l))e.checkValidity()||t.add(e);else if(l==="fieldset"){let u;const w=this.#s.createTreeWalker(e,i.SHOW_ELEMENT);let k=w.firstChild();for(;k&&!(o.test(k.localName)&&(u=k.checkValidity(),!u));)k=w.nextNode();u||t.add(e)}break}case"in-range":{l==="input"&&!(e.readonly||e.hasAttribute("readonly"))&&!(e.disabled||e.hasAttribute("disabled"))&&e.hasAttribute("type")&&g.test(e.getAttribute("type"))&&!(e.validity.rangeUnderflow||e.validity.rangeOverflow)&&(e.hasAttribute("min")||e.hasAttribute("max")||e.getAttribute("type")==="range")&&t.add(e);break}case"out-of-range":{l==="input"&&!(e.readonly||e.hasAttribute("readonly"))&&!(e.disabled||e.hasAttribute("disabled"))&&e.hasAttribute("type")&&g.test(e.getAttribute("type"))&&(e.validity.rangeUnderflow||e.validity.rangeOverflow)&&t.add(e);break}case"required":{let u;if(/^(?:select|textarea)$/.test(l))u=e;else if(l==="input")if(e.hasAttribute("type")){const w=e.getAttribute("type");(w==="file"||m.test(w)||p.test(w)||N.test(w))&&(u=e)}else u=e;u&&(e.required||e.hasAttribute("required"))&&t.add(e);break}case"optional":{let u;if(/^(?:select|textarea)$/.test(l))u=e;else if(l==="input")if(e.hasAttribute("type")){const w=e.getAttribute("type");(w==="file"||m.test(w)||p.test(w)||N.test(w))&&(u=e)}else u=e;u&&!(e.required||e.hasAttribute("required"))&&t.add(e);break}case"root":{e===this.#s.documentElement&&t.add(e);break}case"empty":{if(e.hasChildNodes()){let u;const w=this.#s.createTreeWalker(e,i.SHOW_ALL);let k=w.firstChild();for(;k&&(u=k.nodeType!==i.ELEMENT_NODE&&k.nodeType!==i.TEXT_NODE,!!u);)k=w.nextSibling();u&&t.add(e)}else t.add(e);break}case"first-child":{(b&&e===b.firstElementChild||e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE)&&t.add(e);break}case"last-child":{(b&&e===b.lastElementChild||e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE)&&t.add(e);break}case"only-child":{(b&&e===b.firstElementChild&&e===b.lastElementChild||e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE)&&t.add(e);break}case"first-of-type":{if(b){const[u]=this._collectNthOfType({a:0,b:1},e);u&&t.add(u)}else e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE&&t.add(e);break}case"last-of-type":{if(b){const[u]=this._collectNthOfType({a:0,b:1,reverse:!0},e);u&&t.add(u)}else e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE&&t.add(e);break}case"only-of-type":{if(b){const[u]=this._collectNthOfType({a:0,b:1},e);if(u===e){const[w]=this._collectNthOfType({a:0,b:1,reverse:!0},e);w===e&&t.add(e)}}else e===this.#t&&this.#t.nodeType===i.ELEMENT_NODE&&t.add(e);break}case"host":case"host-context":break;case"after":case"before":case"first-letter":case"first-line":{if(this.#l){const u=`Unsupported pseudo-element ::${d}`;throw new DOMException(u,i.NOT_SUPPORTED_ERR)}break}case"active":case"autofill":case"blank":case"buffering":case"current":case"defined":case"focus-visible":case"fullscreen":case"future":case"hover":case"modal":case"muted":case"past":case"paused":case"picture-in-picture":case"playing":case"seeking":case"stalled":case"user-invalid":case"user-valid":case"volume-locked":case"-webkit-autofill":{if(this.#l){const u=`Unsupported pseudo-class :${d}`;throw new DOMException(u,i.NOT_SUPPORTED_ERR)}break}default:if(d.startsWith("-webkit-")){if(this.#l){const u=`Unsupported pseudo-class :${d}`;throw new DOMException(u,i.NOT_SUPPORTED_ERR)}}else if(!c){const u=`Unknown pseudo-class :${d}`;throw new DOMException(u,i.SYNTAX_ERR)}}}return t}_matchAttributeSelector(s,e){const{flags:f,matcher:n,name:l,value:b}=s;if(typeof f=="string"&&!/^[is]$/i.test(f)){const r=`Invalid selector ${(0,v.generateCSS)(s)}`;throw new DOMException(r,i.SYNTAX_ERR)}const{attributes:c}=e;let d;if(c&&c.length){let t;this.#s.contentType==="text/html"?typeof f=="string"&&/^s$/i.test(f)?t=!1:t=!0:typeof f=="string"&&/^i$/i.test(f)?t=!0:t=!1;let r=(0,v.unescapeSelector)(l.name);t&&(r=r.toLowerCase());const a=new Set;if(r.indexOf("|")>-1){const{prefix:o,tagName:h}=(0,S.selectorToNodeProps)(r);for(let{name:m,value:p}of c)switch(t&&(m=m.toLowerCase(),p=p.toLowerCase()),o){case"":{h===m&&a.add(p);break}case"*":{m.indexOf(":")>-1?m.endsWith(`:${h}`)&&a.add(p):h===m&&a.add(p);break}default:if(m.indexOf(":")>-1){const[g,N]=m.split(":");o===g&&h===N&&(0,S.isNamespaceDeclared)(o,e)&&a.add(p)}}}else for(let{name:o,value:h}of c)if(t&&(o=o.toLowerCase(),h=h.toLowerCase()),o.indexOf(":")>-1){const[m,p]=o.split(":");if(m==="xml"&&p==="lang")continue;r===p&&a.add(h)}else r===o&&a.add(h);if(a.size){const{name:o,value:h}=b||{};let m;switch(o?t?m=o.toLowerCase():m=o:h?t?m=h.toLowerCase():m=h:h===""&&(m=h),n){case"=":{typeof m=="string"&&a.has(m)&&(d=e);break}case"~=":{if(m&&typeof m=="string"){for(const p of a)if(new Set(p.split(/\s+/)).has(m)){d=e;break}}break}case"|=":{if(m&&typeof m=="string"){let p;for(const g of a)if(g===m||g.startsWith(`${m}-`)){p=g;break}p&&(d=e)}break}case"^=":{if(m&&typeof m=="string"){let p;for(const g of a)if(g.startsWith(`${m}`)){p=g;break}p&&(d=e)}break}case"$=":{if(m&&typeof m=="string"){let p;for(const g of a)if(g.endsWith(`${m}`)){p=g;break}p&&(d=e)}break}case"*=":{if(m&&typeof m=="string"){let p;for(const g of a)if(g.includes(`${m}`)){p=g;break}p&&(d=e)}break}case null:default:d=e}}}return d??null}_matchClassSelector(s,e){const f=(0,v.unescapeSelector)(s.name);let n;return e.classList.contains(f)&&(n=e),n??null}_matchIDSelector(s,e){const f=(0,v.unescapeSelector)(s.name),{id:n}=e;let l;return f===n&&(l=e),l??null}_matchTypeSelector(s,e,f={}){const n=(0,v.unescapeSelector)(s.name),{localName:l,prefix:b}=e,{forgive:c}=f;let{prefix:d,tagName:t}=(0,S.selectorToNodeProps)(n,e);this.#s.contentType==="text/html"&&(d=d.toLowerCase(),t=t.toLowerCase());let r,a;l.indexOf(":")>-1?[r,a]=l.split(":"):(r=b||"",a=l);let o;if(d===""&&r==="")e.namespaceURI===null&&(t==="*"||t===a)&&(o=e);else if(d==="*")(t==="*"||t===a)&&(o=e);else if(d===r){if((0,S.isNamespaceDeclared)(d,e))(t==="*"||t===a)&&(o=e);else if(!c){const h=`Undeclared namespace ${d}`;throw new DOMException(h,i.SYNTAX_ERR)}}else if(d&&!c&&!(0,S.isNamespaceDeclared)(d,e)){const h=`Undeclared namespace ${d}`;throw new DOMException(h,i.SYNTAX_ERR)}return o??null}_matchShadowHostPseudoClass(s,e){const{children:f}=s,n=(0,v.unescapeSelector)(s.name);let l;if(Array.isArray(f)){const[b]=(0,v.walkAST)(f[0]),[...c]=b,{host:d}=e;if(n==="host"){let t;for(const r of c){const{type:a}=r;if(a===i.COMBINATOR){const h=`Invalid selector ${(0,v.generateCSS)(s)}`;throw new DOMException(h,i.SYNTAX_ERR)}if(t=this._matchSelector(r,d).has(d),!t)break}t&&(l=e)}else if(n==="host-context"){let t,r=d;for(;r;){for(const a of c){const{type:o}=a;if(o===i.COMBINATOR){const m=`Invalid selector ${(0,v.generateCSS)(s)}`;throw new DOMException(m,i.SYNTAX_ERR)}if(t=this._matchSelector(a,r).has(r),!t)break}if(t)break;r=r.parentNode}t&&(l=e)}}else if(n==="host")l=e;else{const b=`Invalid selector :${n}`;throw new DOMException(b,i.SYNTAX_ERR)}return l??null}_matchSelector(s,e,f){const{type:n}=s,l=(0,v.unescapeSelector)(s.name);let b=new Set;if(e.nodeType===i.ELEMENT_NODE)switch(n){case i.SELECTOR_ATTR:{const c=this._matchAttributeSelector(s,e);c&&b.add(c);break}case i.SELECTOR_CLASS:{const c=this._matchClassSelector(s,e);c&&b.add(c);break}case i.SELECTOR_ID:{const c=this._matchIDSelector(s,e);c&&b.add(c);break}case i.SELECTOR_PSEUDO_CLASS:{const c=this._matchPseudoClassSelector(s,e,f);c.size&&(b=c);break}case i.SELECTOR_PSEUDO_ELEMENT:{this._matchPseudoElementSelector(l,f);break}case i.SELECTOR_TYPE:default:{const c=this._matchTypeSelector(s,e,f);c&&b.add(c)}}else if(this.#c&&n===i.SELECTOR_PSEUDO_CLASS&&e.nodeType===i.DOCUMENT_FRAGMENT_NODE){if(l!=="has"&&i.REG_LOGICAL_PSEUDO.test(l)){const c=this._matchPseudoClassSelector(s,e,f);c.size&&(b=c)}else if(i.REG_SHADOW_HOST.test(l)){const c=this._matchShadowHostPseudoClass(s,e);c&&b.add(c)}}return b}_matchLeaves(s,e,f){let n;for(const l of s)if(n=this._matchSelector(l,e,f).has(e),!n)break;return!!n}_findDescendantNodes(s,e){const[f,...n]=s,{type:l}=f,b=(0,v.unescapeSelector)(f.name),c=n.length>0;let d=new Set,t=!1;if(this.#c)t=!0;else switch(l){case i.SELECTOR_ID:{if(this.#t.nodeType===i.ELEMENT_NODE)t=!0;else{const r=this.#t.getElementById(b);r&&r!==e&&e.contains(r)&&(c?this._matchLeaves(n,r)&&d.add(r):d.add(r))}break}case i.SELECTOR_CLASS:{const r=[].slice.call(e.getElementsByClassName(b));if(r.length)if(c)for(const a of r)this._matchLeaves(n,a)&&d.add(a);else d=new Set(r);break}case i.SELECTOR_TYPE:{if(this.#s.contentType==="text/html"&&!/[*|]/.test(b)){const r=[].slice.call(e.getElementsByTagName(b));if(r.length)if(c)for(const a of r)this._matchLeaves(n,a)&&d.add(a);else d=new Set(r)}else t=!0;break}case i.SELECTOR_PSEUDO_ELEMENT:{this._matchPseudoElementSelector(b);break}default:t=!0}return{nodes:d,pending:t}}_matchCombinator(s,e,f={}){const{combo:n,leaves:l}=s,{name:b}=n,{dir:c,forgive:d}=f;let t=new Set;if(c===C)switch(b){case"+":{const r=e.nextElementSibling;r&&this._matchLeaves(l,r,{forgive:d})&&t.add(r);break}case"~":{const{parentNode:r}=e;if(r){const a=this.#s.createTreeWalker(r,i.SHOW_ELEMENT);let o=this._traverse(e,a);for(o===e&&(o=a.nextSibling());o;)this._matchLeaves(l,o,{forgive:d})&&t.add(o),o=a.nextSibling()}break}case">":{const r=this.#s.createTreeWalker(e,i.SHOW_ELEMENT);let a=r.firstChild();for(;a;)this._matchLeaves(l,a,{forgive:d})&&t.add(a),a=r.nextSibling();break}case" ":default:{const{nodes:r,pending:a}=this._findDescendantNodes(l,e);if(r.size)t=r;else if(a){const o=this.#s.createTreeWalker(e,i.SHOW_ELEMENT);let h=o.nextNode();for(;h;)this._matchLeaves(l,h,{forgive:d})&&t.add(h),h=o.nextNode()}}}else switch(b){case"+":{const r=e.previousElementSibling;r&&this._matchLeaves(l,r,{forgive:d})&&t.add(r);break}case"~":{const r=this.#s.createTreeWalker(e.parentNode,i.SHOW_ELEMENT);let a=r.firstChild();for(;a&&a!==e;)this._matchLeaves(l,a,{forgive:d})&&t.add(a),a=r.nextSibling();break}case">":{const r=e.parentNode;r&&this._matchLeaves(l,r,{forgive:d})&&t.add(r);break}case" ":default:{const r=[];let a=e.parentNode;for(;a;)this._matchLeaves(l,a,{forgive:d})&&r.push(a),a=a.parentNode;r.length&&(t=new Set(r.reverse()))}}return t}_findNode(s,e={}){let{node:f,walker:n}=e;n||(n=this.#f);let l,b=this._traverse(f,n);if(b)for((b.nodeType!==i.ELEMENT_NODE||b===f&&b!==this.#t)&&(b=n.nextNode());b;){let c;if(this.#e.nodeType===i.ELEMENT_NODE?b===this.#e?c=!0:c=this.#e.contains(b):c=!0,c&&this._matchLeaves(s,b)){l=b;break}b=n.nextNode()}return l??null}_findEntryNodes(s,e){const{leaves:f}=s,[n,...l]=f,{type:b}=n,c=(0,v.unescapeSelector)(n.name),d=l.length>0;let t=new Set,r=!1,a=!1;switch(b){case i.SELECTOR_ID:{if(e===M)this._matchLeaves(f,this.#e)&&(t.add(this.#e),r=!0);else if(e===$){let o=this.#e;for(;o;)this._matchLeaves(f,o)&&(t.add(o),r=!0),o=o.parentNode}else if(e===A||this.#t.nodeType===i.ELEMENT_NODE)a=!0;else{const o=this.#t.getElementById(c);o&&(t.add(o),r=!0)}break}case i.SELECTOR_CLASS:{if(e===M)this.#e.nodeType===i.ELEMENT_NODE&&this.#e.classList.contains(c)&&t.add(this.#e);else if(e===$){let o=this.#e;for(;o&&o.nodeType===i.ELEMENT_NODE;)o.classList.contains(c)&&t.add(o),o=o.parentNode}else if(e===L){const o=this._findNode(f,{node:this.#e,walker:this.#a});if(o){t.add(o),r=!0;break}}else if(this.#t.nodeType===i.DOCUMENT_FRAGMENT_NODE||this.#t.nodeType===i.ELEMENT_NODE)a=!0;else{const o=[].slice.call(this.#t.getElementsByClassName(c));if(this.#e.nodeType===i.ELEMENT_NODE)for(const h of o)(h===this.#e||(0,S.isInclusive)(h,this.#e))&&t.add(h);else o.length&&(t=new Set(o))}break}case i.SELECTOR_TYPE:{if(e===M)this.#e.nodeType===i.ELEMENT_NODE&&this._matchLeaves(f,this.#e)&&(t.add(this.#e),r=!0);else if(e===$){let o=this.#e;for(;o&&o.nodeType===i.ELEMENT_NODE;)this._matchLeaves(f,o)&&(t.add(o),r=!0),o=o.parentNode}else if(e===L){const o=this._findNode(f,{node:this.#e,walker:this.#a});if(o){t.add(o),r=!0;break}}else if(this.#s.contentType!=="text/html"||/[*|]/.test(c)||this.#t.nodeType===i.DOCUMENT_FRAGMENT_NODE||this.#t.nodeType===i.ELEMENT_NODE)a=!0;else{const o=[].slice.call(this.#t.getElementsByTagName(c));if(this.#e.nodeType===i.ELEMENT_NODE)for(const h of o)(h===this.#e||(0,S.isInclusive)(h,this.#e))&&t.add(h);else o.length&&(t=new Set(o))}break}case i.SELECTOR_PSEUDO_ELEMENT:{this._matchPseudoElementSelector(c);break}default:if(e!==$&&i.REG_SHADOW_HOST.test(c)){if(this.#c&&this.#e.nodeType===i.DOCUMENT_FRAGMENT_NODE){const o=this._matchShadowHostPseudoClass(n,this.#e);o&&t.add(o)}}else if(e===M)this._matchLeaves(f,this.#e)&&(t.add(this.#e),r=!0);else if(e===$){let o=this.#e;for(;o;)this._matchLeaves(f,o)&&(t.add(o),r=!0),o=o.parentNode}else if(e===L){const o=this._findNode(f,{node:this.#e,walker:this.#a});if(o){t.add(o),r=!0;break}}else a=!0}return{compound:d,filtered:r,nodes:t,pending:a}}_getEntryTwig(s,e){const f=s.length,n=f>1,l=s[0];let b,c;if(n){const{combo:d,leaves:[{type:t}]}=l,r=s[f-1],{leaves:[{type:a}]}=r;if(a===i.SELECTOR_PSEUDO_ELEMENT||a===i.SELECTOR_ID)b=O,c=r;else if(t===i.SELECTOR_PSEUDO_ELEMENT||t===i.SELECTOR_ID)b=C,c=l;else if(e===A)if(f===2){const{name:o}=d;/^[+~]$/.test(o)?(b=O,c=r):(b=C,c=l)}else b=C,c=l;else{let o,h;for(const{combo:m,leaves:[p]}of s){const{type:g}=p,N=(0,v.unescapeSelector)(p.name);if(g===i.SELECTOR_PSEUDO_CLASS&&N==="dir"){o=!1;break}if(m&&!h){const{name:u}=m;/^[+~]$/.test(u)&&(o=!0,h=!0)}}o?(b=C,c=l):(b=O,c=r)}}else b=O,c=l;return{complex:n,dir:b,twig:c}}_collectNodes(s){const e=this.#i.values();if(s===A||s===L){const f=new Set;let n=0;for(const{branch:l}of e){const{dir:b,twig:c}=this._getEntryTwig(l,s),{compound:d,filtered:t,nodes:r,pending:a}=this._findEntryNodes(c,s);r.size?(this.#i[n].find=!0,this.#o[n]=r):a&&f.add(new Map([["index",n],["twig",c]])),this.#i[n].dir=b,this.#i[n].filtered=t||!d,n++}if(f.size){let l,b;this.#e!==this.#t&&this.#e.nodeType===i.ELEMENT_NODE?(l=this.#e,b=this.#a):(l=this.#t,b=this.#f);let c=this._traverse(l,b);for(;c;){let d=!1;if(this.#e.nodeType===i.ELEMENT_NODE?c===this.#e?d=!0:d=this.#e.contains(c):d=!0,d)for(const t of f){const{leaves:r}=t.get("twig");if(this._matchLeaves(r,c)){const o=t.get("index");this.#i[o].filtered=!0,this.#i[o].find=!0,this.#o[o].add(c)}}c=b.nextNode()}}}else{let f=0;for(const{branch:n}of e){const l=n[n.length-1],{compound:b,filtered:c,nodes:d}=this._findEntryNodes(l,s);d.size&&(this.#i[f].find=!0,this.#o[f]=d),this.#i[f].dir=O,this.#i[f].filtered=c||!b,f++}}return[this.#i,this.#o]}_sortNodes(s){const e=[...s];return e.length>1&&e.sort((f,n)=>{let l;return(0,S.isPreceding)(n,f)?l=1:l=-1,l}),e}_matchNodes(s){const[...e]=this.#i,f=e.length;let n=new Set;for(let l=0;l<f;l++){const{branch:b,dir:c,filtered:d,find:t}=e[l],r=b.length;if(r&&t){const a=this.#o[l],o=r-1;if(o===0){const{leaves:[,...h]}=b[0];if((s===A||s===L)&&this.#e.nodeType===i.ELEMENT_NODE){for(const m of a)if((d||this._matchLeaves(h,m))&&m!==this.#e&&this.#e.contains(m)&&(n.add(m),s!==A))break}else if(h.length){for(const m of a)if((d||this._matchLeaves(h,m))&&(n.add(m),s!==A))break}else if(s===A){const m=[...n];n=new Set([...m,...a])}else{const[m]=[...a];n.add(m)}}else if(c===C){let{combo:h,leaves:m}=b[0];const[,...p]=m;let g;for(const N of a){if(d||this._matchLeaves(p,N)){let w=new Set([N]);for(let k=1;k<r;k++){const{combo:y,leaves:_}=b[k],x=[];for(const E of w){const P={combo:h,leaves:_},D=this._matchCombinator(P,E,{dir:c});D.size&&x.push(...D)}if(x.length)if(k===o){if(s===A){const E=[...n];n=new Set([...E,...x])}else{const[E]=this._sortNodes(x);n.add(E)}g=!0}else h=y,w=new Set(x),g=!1;else{g=!1;break}}}else g=!1;if(g&&s!==A)break}if(!g&&s===L){const[N]=[...a];let u=this._findNode(m,{node:N,walker:this.#a});for(;u;){let w=new Set([u]);for(let k=1;k<r;k++){const{combo:y,leaves:_}=b[k],x=[];for(const E of w){const P={combo:h,leaves:_},D=this._matchCombinator(P,E,{dir:c});D.size&&x.push(...D)}if(x.length)if(k===o){const[E]=this._sortNodes(x);n.add(E),g=!0}else h=y,w=new Set(x),g=!1;else{g=!1;break}}if(g)break;u=this._findNode(m,{node:u,walker:this.#a}),w=new Set([u])}}}else{const{leaves:h}=b[o],[,...m]=h;let p;for(const g of a){if(d||this._matchLeaves(m,g)){let u=new Set([g]);for(let w=o-1;w>=0;w--){const k=b[w],y=[];for(const _ of u){const x=this._matchCombinator(k,_,{dir:c});x.size&&y.push(...x)}if(y.length)w===0?(n.add(g),p=!0):(u=new Set(y),p=!1);else{p=!1;break}}}if(p&&s!==A)break}if(!p&&s===L){const[g]=[...a];let N=this._findNode(h,{node:g,walker:this.#a});for(;N;){let u=new Set([N]);for(let w=o-1;w>=0;w--){const k=b[w],y=[];for(const _ of u){const x=this._matchCombinator(k,_,{dir:c});x.size&&y.push(...x)}if(y.length)w===0?(n.add(N),p=!0):(u=new Set(y),p=!1);else{p=!1;break}}if(p)break;N=this._findNode(h,{node:N,walker:this.#a}),u=new Set([N])}}}}}return n}_find(s){return(s===A||s===L)&&(this.#a=this.#s.createTreeWalker(this.#e,i.SHOW_ELEMENT)),this._collectNodes(s),this._matchNodes(s)}matches(){if(this.#e.nodeType!==i.ELEMENT_NODE){const e=`Unexpected node ${this.#e.nodeName}`;this._onError(new TypeError(e))}let s;try{const e=this._find(M);e.size&&(s=e.has(this.#e))}catch(e){this._onError(e)}return!!s}closest(){if(this.#e.nodeType!==i.ELEMENT_NODE){const e=`Unexpected node ${this.#e.nodeName}`;this._onError(new TypeError(e))}let s;try{const e=this._find($);let f=this.#e;for(;f;){if(e.has(f)){s=f;break}f=f.parentNode}}catch(e){this._onError(e)}return s??null}querySelector(){let s;try{const e=this._find(L);e.delete(this.#e),e.size&&([s]=this._sortNodes(e))}catch(e){this._onError(e)}return s??null}querySelectorAll(){let s;try{const e=this._find(A);e.delete(this.#e),e.size&&(s=this._sortNodes(e))}catch(e){this._onError(e)}return s??[]}}0&&(module.exports={Matcher});
//# sourceMappingURL=matcher.js.map
