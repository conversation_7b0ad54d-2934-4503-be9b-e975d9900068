import multer from "multer";
import path from "path";
import fs from "fs";
import { Request } from "express";

// Ensure upload directories exist
const uploadDir = path.join(process.cwd(), "uploads");
const blogImagesDir = path.join(uploadDir, "blog-images");
const tempDir = path.join(uploadDir, "temp");

// Create directories if they don't exist (with error handling for Docker volumes)
const ensureDirectories = () => {
  [uploadDir, blogImagesDir, tempDir].forEach((dir) => {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
        console.log(`✅ Created directory: ${dir}`);
      } else {
        console.log(`📁 Directory already exists: ${dir}`);
      }
    } catch (error: any) {
      if (error.code === "EACCES") {
        console.warn(
          `⚠️ Permission denied creating ${dir} - assuming Docker volume mount`
        );
      } else {
        console.error(`❌ Failed to create directory ${dir}:`, error);
      }
      // Don't throw error, let the application continue
      // The directories might be mounted as Docker volumes
    }
  });
};

// Try to create directories, but don't fail if we can't (Docker volumes handle this)
ensureDirectories();

// File filter function
const fileFilter = (
  req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  // Check file type
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedTypes.test(
    path.extname(file.originalname).toLowerCase()
  );
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error("Only image files (JPEG, JPG, PNG, GIF, WebP) are allowed!"));
  }
};

// Storage configuration for blog images
const blogImageStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, blogImagesDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    const name = file.fieldname + "-" + uniqueSuffix + ext;
    cb(null, name);
  },
});

// Storage configuration for temporary uploads
const tempStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    const name = "temp-" + uniqueSuffix + ext;
    cb(null, name);
  },
});

// Multer configurations
export const uploadBlogImage = multer({
  storage: blogImageStorage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1, // Single file upload
  },
});

export const uploadBlogImages = multer({
  storage: blogImageStorage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB per file
    files: 10, // Maximum 10 files
  },
});

export const uploadTemp = multer({
  storage: tempStorage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
});

// Utility function to move file from temp to permanent location
export const moveFromTemp = (
  tempFilename: string,
  permanentPath: string
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const tempFilePath = path.join(tempDir, tempFilename);
    const permanentFilePath = path.join(blogImagesDir, permanentPath);

    fs.rename(tempFilePath, permanentFilePath, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve(permanentFilePath);
      }
    });
  });
};

// Utility function to delete file
export const deleteFile = (filePath: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err && err.code !== "ENOENT") {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

// Utility function to get file URL
export const getFileUrl = (
  filename: string,
  type: "blog-images" | "temp" = "blog-images"
): string => {
  const baseUrl = process.env.BASE_URL || "http://localhost:4004";
  return `${baseUrl}/uploads/${type}/${filename}`;
};

// Clean up old temporary files (run periodically)
export const cleanupTempFiles = (maxAgeHours: number = 24): void => {
  fs.readdir(tempDir, (err, files) => {
    if (err) return;

    const now = Date.now();
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert hours to milliseconds

    files.forEach((file) => {
      const filePath = path.join(tempDir, file);
      fs.stat(filePath, (err, stats) => {
        if (err) return;

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlink(filePath, (err) => {
            if (err) console.error("Error deleting temp file:", err);
          });
        }
      });
    });
  });
};
