module.exports = {
  // Output directory (same as in vite.config.js)
  destination: "build",
  // Include all important routes for SEO
  include: [
    "/",
    "/about",
    "/products",
    "/products/bms",
    "/products/bms/core",
    "/products/bms/accounting",
    "/products/bms/budget",
    "/products/bms/hr",
    "/products/bms/recruiting",
    "/products/bms/production",
    "/products/bms/sales",
    "/products/bms/quality",
    "/services",
    "/blog",
    "/blog-single/complete-guide-app-development-company-2024",
    "/blog-single/software-development-company-vs-agency-2024",
    "/blog-single/mobile-app-development-complete-business-guide-2024",
    "/contact",
    "/404",
  ],
  // Wait for network requests to complete
  puppeteerArgs: [
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--disable-web-security",
    "--disable-features=IsolateOrigins,site-per-process",
    "--js-flags=--harmony",
  ],
  // Reduce concurrency to avoid issues
  concurrency: 1,
  // Increase timeout for slow machines
  puppeteerTimeout: 60000,
  // Skip minification to avoid issues
  minifyHtml: false,
  // Ignore specific resources to speed up the process
  skipThirdPartyRequests: false,
  // Wait until page is fully loaded
  waitFor: 1000,
  // Handle JavaScript errors
  onError: (error) => {
    console.error("Error during prerendering:", error);
    // Don't fail the build on errors
    return true;
  },
  // Use a more modern browser
  userAgent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
};
