# Production Environment Configuration

# Database
DATABASE_URL="postgresql://root:Onamission%23007@************:5432/devskills"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production-devskills-2024"
JWT_EXPIRES_IN="7d"

# Server Configuration
PORT=4004
NODE_ENV=production

# CORS Configuration
CORS_ORIGIN=https://devskills.ee

# Nodemailer credentials - Gmail (free SMTP)a
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=dxgn rpai tbmg hkyz

# Email Settings
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Security
API_KEY=9afe34d2134b43e19163c50924df6714

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads

# Base URL for file serving
BASE_URL=https://devskills.ee
