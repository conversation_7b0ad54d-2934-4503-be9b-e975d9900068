{"version": 3, "sources": ["../../.pnpm/@tiptap+extension-subscript@2.22.3_@tiptap+core@2.22.3_@tiptap+pm@2.22.3_/node_modules/@tiptap/extension-subscript/src/subscript.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\nimport type { StyleParseRule } from '@tiptap/pm/model'\n\nexport interface SubscriptExtensionOptions {\n  /**\n   * HTML attributes to add to the subscript element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    subscript: {\n      /**\n       * Set a subscript mark\n       * @example editor.commands.setSubscript()\n       */\n      setSubscript: () => ReturnType,\n      /**\n       * Toggle a subscript mark\n       * @example editor.commands.toggleSubscript()\n       */\n      toggleSubscript: () => ReturnType,\n      /**\n       * Unset a subscript mark\n       * @example editor.commands.unsetSubscript()\n       */\n      unsetSubscript: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create subscript text.\n * @see https://www.tiptap.dev/api/marks/subscript\n */\nexport const Subscript = Mark.create<SubscriptExtensionOptions>({\n  name: 'subscript',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'sub',\n      },\n      {\n        style: 'vertical-align',\n        getAttrs(value) {\n          // Don’t match this rule if the vertical align isn’t sub.\n          if (value !== 'sub') {\n            return false\n          }\n\n          // If it falls through we’ll match, and this mark will be applied.\n          return null\n        },\n      } as StyleParseRule,\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['sub', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setSubscript: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleSubscript: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetSubscript: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-,': () => this.editor.commands.toggleSubscript(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;AAsCa,IAAA,YAAY,KAAK,OAAkC;EAC9D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,OAAO;QACP,SAAS,OAAK;AAEZ,cAAI,UAAU,OAAO;AACnB,mBAAO;;AAIT,iBAAO;;MAEQ;;;EAIvB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,OAAO,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGhF,cAAW;AACT,WAAO;MACL,cAAc,MAAM,CAAC,EAAE,SAAQ,MAAM;AACnC,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,iBAAiB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACtC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,gBAAgB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACrC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAe;;;AAGxD,CAAA;", "names": []}