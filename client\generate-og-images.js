/*
 * This script generates Open Graph images for social media sharing
 * To use this script, you'll need to install the following packages:
 * npm install puppeteer sharp
 *
 * Then run: node generate-og-images.js
 */

import fs from "fs";
import path from "path";
import puppeteer from "puppeteer";
import { fileURLToPath } from "url";

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  template: path.join(__dirname, "public", "og-image-template.html"),
  outputDir: path.join(__dirname, "public", "og-images"),
  width: 1200,
  height: 630,
  pages: [
    {
      title: "Business Management System",
      description:
        "DevSkills.ee offers a comprehensive BMS solution to streamline your operations and boost productivity.",
      filename: "home.png",
    },
    {
      title: "About DevSkills.ee",
      description:
        "Learn about our mission, values, and the team behind our innovative solutions.",
      filename: "about.png",
    },
    {
      title: "Our Services",
      description:
        "Discover how our services can transform your business operations and drive growth.",
      filename: "services.png",
    },
    {
      title: "Portfolio",
      description:
        "Explore our successful projects and see how we have helped businesses like yours.",
      filename: "portfolio.png",
    },
    {
      title: "Contact Us",
      description:
        "Get in touch with our team to discuss how we can help your business succeed.",
      filename: "contact.png",
    },
  ],
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Read the template HTML
const templateHtml = fs.readFileSync(config.template, "utf8");

// Generate images
async function generateOgImages() {
  const browser = await puppeteer.launch({
    headless: "new",
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
  });

  try {
    for (const page of config.pages) {
      // Replace placeholders in the template
      let html = templateHtml
        .replace(
          '<h1 class="title">Business Management System</h1>',
          `<h1 class="title">${page.title}</h1>`
        )
        .replace(
          '<p class="description">Streamline your operations and boost productivity with our comprehensive BMS solution.</p>',
          `<p class="description">${page.description}</p>`
        );

      // Create a new page
      const pup = await browser.newPage();
      await pup.setViewport({ width: config.width, height: config.height });

      // Set content and wait for rendering
      await pup.setContent(html, { waitUntil: "networkidle0" });

      // Take screenshot
      const outputPath = path.join(config.outputDir, page.filename);
      await pup.screenshot({ path: outputPath, type: "png" });

      console.log(`Generated: ${outputPath}`);
      await pup.close();
    }
  } catch (error) {
    console.error("Error generating OG images:", error);
  } finally {
    await browser.close();
  }
}

// Run the generator
generateOgImages()
  .then(() => {
    console.log("OG image generation complete!");
  })
  .catch((err) => {
    console.error("Failed to generate OG images:", err);
  });
