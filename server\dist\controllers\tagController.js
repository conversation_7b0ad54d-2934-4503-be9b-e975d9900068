"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTag = exports.updateTag = exports.createTag = exports.getTags = void 0;
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const slugify_1 = __importDefault(require("slugify"));
const prisma = new client_1.PrismaClient();
const createTagSchema = joi_1.default.object({
    name: joi_1.default.string().min(1).max(50).required(),
});
const updateTagSchema = createTagSchema.keys({
    id: joi_1.default.string().required(),
});
const getTags = async (req, res) => {
    try {
        const tags = await prisma.tag.findMany({
            include: {
                _count: {
                    select: { blogPosts: true },
                },
            },
            orderBy: { name: 'asc' },
        });
        res.json({
            success: true,
            data: tags,
        });
    }
    catch (error) {
        console.error('Get tags error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.getTags = getTags;
const createTag = async (req, res) => {
    try {
        const { error, value } = createTagSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message,
            });
        }
        const { name } = value;
        const slug = (0, slugify_1.default)(name, { lower: true, strict: true });
        const existingTag = await prisma.tag.findUnique({
            where: { slug },
        });
        if (existingTag) {
            return res.status(400).json({
                success: false,
                message: 'A tag with this name already exists',
            });
        }
        const tag = await prisma.tag.create({
            data: {
                name,
                slug,
            },
        });
        res.status(201).json({
            success: true,
            data: tag,
        });
    }
    catch (error) {
        console.error('Create tag error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.createTag = createTag;
const updateTag = async (req, res) => {
    try {
        const { id } = req.params;
        const { error, value } = updateTagSchema.validate({ ...req.body, id });
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message,
            });
        }
        const { name } = value;
        const existingTag = await prisma.tag.findUnique({
            where: { id },
        });
        if (!existingTag) {
            return res.status(404).json({
                success: false,
                message: 'Tag not found',
            });
        }
        let slug = existingTag.slug;
        if (name !== existingTag.name) {
            slug = (0, slugify_1.default)(name, { lower: true, strict: true });
            const slugExists = await prisma.tag.findUnique({
                where: { slug },
            });
            if (slugExists && slugExists.id !== id) {
                return res.status(400).json({
                    success: false,
                    message: 'A tag with this name already exists',
                });
            }
        }
        const updatedTag = await prisma.tag.update({
            where: { id },
            data: {
                name,
                slug,
            },
        });
        res.json({
            success: true,
            data: updatedTag,
        });
    }
    catch (error) {
        console.error('Update tag error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.updateTag = updateTag;
const deleteTag = async (req, res) => {
    try {
        const { id } = req.params;
        const tag = await prisma.tag.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { blogPosts: true },
                },
            },
        });
        if (!tag) {
            return res.status(404).json({
                success: false,
                message: 'Tag not found',
            });
        }
        if (tag._count.blogPosts > 0) {
            return res.status(400).json({
                success: false,
                message: `Cannot delete tag. It is being used by ${tag._count.blogPosts} blog post(s).`,
            });
        }
        await prisma.tag.delete({
            where: { id },
        });
        res.json({
            success: true,
            message: 'Tag deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete tag error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.deleteTag = deleteTag;
