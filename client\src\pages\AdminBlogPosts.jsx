import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import { adminAPI } from "../utils/api";

const AdminBlogPosts = () => {
  const navigate = useNavigate();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    status: "all",
    search: "",
  });
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    loadPosts();
  }, [filters]);

  const loadPosts = async () => {
    try {
      setLoading(true);

      const params = {};
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== "all") {
          params[key] = value;
        }
      });

      const { response, data } = await adminAPI.getPosts(params);

      if (data.success) {
        setPosts(data.data.posts);
        setPagination(data.data.pagination);
      } else {
        setError(data.message || "Failed to load posts");
      }
    } catch (error) {
      console.error("Load posts error:", error);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (postId) => {
    if (
      !confirm(
        "Are you sure you want to delete this blog post? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch(`/api/blog/${postId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        loadPosts(); // Reload the list
      } else {
        setError(data.message || "Failed to delete post");
      }
    } catch (error) {
      console.error("Delete error:", error);
      setError("Network error. Please try again.");
    }
  };

  const handleToggleVisibility = async (postId) => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch(`/api/blog/${postId}/toggle-visibility`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        loadPosts(); // Reload the list
      } else {
        setError(data.message || "Failed to toggle visibility");
      }
    } catch (error) {
      console.error("Toggle visibility error:", error);
      setError("Network error. Please try again.");
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadge = (post) => {
    if (!post.published) {
      return (
        <span className="badge bg-secondary">
          <i className="mi-edit me-1"></i>
          Draft
        </span>
      );
    }

    if (post.scheduledAt && new Date(post.scheduledAt) > new Date()) {
      return (
        <span className="badge bg-warning">
          <i className="mi-clock me-1"></i>
          Scheduled
        </span>
      );
    }

    return (
      <span className="badge bg-success">
        <i className="mi-check me-1"></i>
        Published
      </span>
    );
  };

  return (
    <>
      <SEO
        title="Manage Blog Posts - Admin"
        description="Manage blog posts in the admin panel"
        noIndex={true}
      />

      <AdminLayout title="Blog Posts">
        {/* Action Bar */}
        <div className="mb-30">
          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="section-descr mb-0">
                Manage your blog posts, create new content, and organize your
                articles.
              </p>
            </div>
            <div className="col-md-6 text-md-end">
              <button
                onClick={() => navigate("/admin/blog/new")}
                className="btn btn-mod btn-color btn-round"
              >
                <i className="mi-plus me-2"></i>
                New Post
              </button>
            </div>
          </div>
        </div>
        {/* Filters */}
        <div className="admin-table mb-30">
          <div className="row">
            <div className="col-md-4 mb-20">
              <label className="form-label">Search Posts</label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    search: e.target.value,
                    page: 1,
                  }))
                }
                className="form-control"
                placeholder="Search by title..."
              />
            </div>

            <div className="col-md-3 mb-20">
              <label className="form-label">Status</label>
              <select
                value={filters.status}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    status: e.target.value,
                    page: 1,
                  }))
                }
                className="form-control"
              >
                <option value="all">All Posts</option>
                <option value="published">Published</option>
                <option value="draft">Drafts</option>
              </select>
            </div>

            <div className="col-md-3 mb-20">
              <label className="form-label">Per Page</label>
              <select
                value={filters.limit}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    limit: parseInt(e.target.value),
                    page: 1,
                  }))
                }
                className="form-control"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-danger mb-30" role="alert">
            <i className="mi-warning me-2"></i>
            {error}
          </div>
        )}

        {/* Posts Table */}
        <div className="admin-table">
          {loading ? (
            <div className="text-center py-60">
              <i className="fa fa-spinner fa-spin fa-2x color-primary-1 mb-20"></i>
              <div className="hs-line-4 font-alt black">Loading posts...</div>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-60">
              <i className="mi-edit fa-3x color-gray-light-1 mb-20"></i>
              <div className="hs-line-4 font-alt black mb-10">
                No blog posts found
              </div>
              <p className="section-descr mb-30">
                {filters.search || filters.status !== "all"
                  ? "Try adjusting your search filters or create your first blog post."
                  : "Get started by creating your first blog post."}
              </p>
              <button
                onClick={() => navigate("/admin/blog/new")}
                className="btn btn-mod btn-color btn-round"
              >
                <i className="mi-plus me-2"></i>
                Create First Post
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Status</th>
                    <th>Author</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {posts.map((post) => {
                    const englishTranslation =
                      post.translations.find((t) => t.language === "en") ||
                      post.translations[0];

                    return (
                      <tr key={post.id}>
                        <td>
                          <div className="d-flex align-items-center">
                            {post.featuredImage && (
                              <img
                                className="rounded me-3"
                                src={post.featuredImage}
                                alt=""
                                style={{
                                  width: "50px",
                                  height: "50px",
                                  objectFit: "cover",
                                }}
                              />
                            )}
                            <div>
                              <div className="fw-bold">
                                {englishTranslation?.title || "Untitled"}
                              </div>
                              <small className="text-muted">/{post.slug}</small>
                            </div>
                          </div>
                        </td>
                        <td>
                          {getStatusBadge(post)}
                          {post.featured && (
                            <span className="badge bg-primary ms-2">
                              <i className="mi-star me-1"></i>
                              Featured
                            </span>
                          )}
                        </td>
                        <td>{post.author.name || post.author.email}</td>
                        <td>{formatDate(post.createdAt)}</td>
                        <td>
                          <div className="btn-group" role="group">
                            <button
                              onClick={() =>
                                navigate(`/admin/blog/edit/${post.id}`)
                              }
                              className="btn btn-sm btn-outline-primary"
                              title="Edit"
                            >
                              <i className="mi-edit"></i>
                            </button>

                            <button
                              onClick={() => handleToggleVisibility(post.id)}
                              className={`btn btn-sm ${
                                post.published
                                  ? "btn-outline-warning"
                                  : "btn-outline-success"
                              }`}
                              title={post.published ? "Unpublish" : "Publish"}
                            >
                              <i
                                className={
                                  post.published ? "mi-eye-off" : "mi-eye"
                                }
                              ></i>
                            </button>

                            <button
                              onClick={() => handleDelete(post.id)}
                              className="btn btn-sm btn-outline-danger"
                              title="Delete"
                            >
                              <i className="mi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="row mt-30">
            <div className="col-md-6">
              <p className="small text-muted mb-0">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                of {pagination.total} results
              </p>
            </div>
            <div className="col-md-6 text-md-end">
              <nav aria-label="Blog posts pagination">
                <ul className="pagination pagination-sm justify-content-md-end">
                  <li
                    className={`page-item ${
                      pagination.page <= 1 ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))
                      }
                      disabled={pagination.page <= 1}
                    >
                      Previous
                    </button>
                  </li>

                  <li className="page-item active">
                    <span className="page-link">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                  </li>

                  <li
                    className={`page-item ${
                      pagination.page >= pagination.pages ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))
                      }
                      disabled={pagination.page >= pagination.pages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default AdminBlogPosts;
