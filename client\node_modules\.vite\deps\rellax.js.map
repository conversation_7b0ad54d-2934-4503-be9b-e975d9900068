{"version": 3, "sources": ["../../.pnpm/rellax@1.12.1/node_modules/rellax/rellax.js"], "sourcesContent": ["\n// ------------------------------------------\n// Rellax.js\n// Buttery smooth parallax library\n// Copyright (c) 2016 <PERSON> (@moeamaya)\n// MIT license\n//\n// Thanks to Paraxify.js and <PERSON>\n// for parallax concepts\n// ------------------------------------------\n\n(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define([], factory);\n  } else if (typeof module === 'object' && module.exports) {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory();\n  } else {\n    // Browser globals (root is window)\n    root.Rellax = factory();\n  }\n}(typeof window !== \"undefined\" ? window : global, function () {\n  var Rellax = function(el, options){\n    \"use strict\";\n\n    var self = Object.create(Rellax.prototype);\n\n    var posY = 0;\n    var screenY = 0;\n    var posX = 0;\n    var screenX = 0;\n    var blocks = [];\n    var pause = true;\n\n    // check what requestAnimationFrame to use, and if\n    // it's not supported, use the onscroll event\n    var loop = window.requestAnimationFrame ||\n      window.webkitRequestAnimationFrame ||\n      window.mozRequestAnimationFrame ||\n      window.msRequestAnimationFrame ||\n      window.oRequestAnimationFrame ||\n      function(callback){ return setTimeout(callback, 1000 / 60); };\n\n    // store the id for later use\n    var loopId = null;\n\n    // Test via a getter in the options object to see if the passive property is accessed\n    var supportsPassive = false;\n    try {\n      var opts = Object.defineProperty({}, 'passive', {\n        get: function() {\n          supportsPassive = true;\n        }\n      });\n      window.addEventListener(\"testPassive\", null, opts);\n      window.removeEventListener(\"testPassive\", null, opts);\n    } catch (e) {}\n\n    // check what cancelAnimation method to use\n    var clearLoop = window.cancelAnimationFrame || window.mozCancelAnimationFrame || clearTimeout;\n\n    // check which transform property to use\n    var transformProp = window.transformProp || (function(){\n        var testEl = document.createElement('div');\n        if (testEl.style.transform === null) {\n          var vendors = ['Webkit', 'Moz', 'ms'];\n          for (var vendor in vendors) {\n            if (testEl.style[ vendors[vendor] + 'Transform' ] !== undefined) {\n              return vendors[vendor] + 'Transform';\n            }\n          }\n        }\n        return 'transform';\n      })();\n\n    // Default Settings\n    self.options = {\n      speed: -2,\n\t    verticalSpeed: null,\n\t    horizontalSpeed: null,\n      breakpoints: [576, 768, 1201],\n      center: false,\n      wrapper: null,\n      relativeToWrapper: false,\n      round: true,\n      vertical: true,\n      horizontal: false,\n      verticalScrollAxis: \"y\",\n      horizontalScrollAxis: \"x\",\n      callback: function() {},\n    };\n\n    // User defined options (might have more in the future)\n    if (options){\n      Object.keys(options).forEach(function(key){\n        self.options[key] = options[key];\n      });\n    }\n\n    function validateCustomBreakpoints () {\n      if (self.options.breakpoints.length === 3 && Array.isArray(self.options.breakpoints)) {\n        var isAscending = true;\n        var isNumerical = true;\n        var lastVal;\n        self.options.breakpoints.forEach(function (i) {\n          if (typeof i !== 'number') isNumerical = false;\n          if (lastVal !== null) {\n            if (i < lastVal) isAscending = false;\n          }\n          lastVal = i;\n        });\n        if (isAscending && isNumerical) return;\n      }\n      // revert defaults if set incorrectly\n      self.options.breakpoints = [576, 768, 1201];\n      console.warn(\"Rellax: You must pass an array of 3 numbers in ascending order to the breakpoints option. Defaults reverted\");\n    }\n\n    if (options && options.breakpoints) {\n      validateCustomBreakpoints();\n    }\n\n    // By default, rellax class\n    if (!el) {\n      el = '.rellax';\n    }\n\n    // check if el is a className or a node\n    var elements = typeof el === 'string' ? document.querySelectorAll(el) : [el];\n\n    // Now query selector\n    if (elements.length > 0) {\n      self.elems = elements;\n    }\n\n    // The elements don't exist\n    else {\n      console.warn(\"Rellax: The elements you're trying to select don't exist.\");\n      return;\n    }\n\n    // Has a wrapper and it exists\n    if (self.options.wrapper) {\n      if (!self.options.wrapper.nodeType) {\n        var wrapper = document.querySelector(self.options.wrapper);\n\n        if (wrapper) {\n          self.options.wrapper = wrapper;\n        } else {\n          console.warn(\"Rellax: The wrapper you're trying to use doesn't exist.\");\n          return;\n        }\n      }\n    }\n\n    // set a placeholder for the current breakpoint\n    var currentBreakpoint;\n\n    // helper to determine current breakpoint\n    var getCurrentBreakpoint = function (w) {\n      var bp = self.options.breakpoints;\n      if (w < bp[0]) return 'xs';\n      if (w >= bp[0] && w < bp[1]) return 'sm';\n      if (w >= bp[1] && w < bp[2]) return 'md';\n      return 'lg';\n    };\n\n    // Get and cache initial position of all elements\n    var cacheBlocks = function() {\n      for (var i = 0; i < self.elems.length; i++){\n        var block = createBlock(self.elems[i]);\n        blocks.push(block);\n      }\n    };\n\n\n    // Let's kick this script off\n    // Build array for cached element values\n    var init = function() {\n      for (var i = 0; i < blocks.length; i++){\n        self.elems[i].style.cssText = blocks[i].style;\n      }\n\n      blocks = [];\n\n      screenY = window.innerHeight;\n      screenX = window.innerWidth;\n      currentBreakpoint = getCurrentBreakpoint(screenX);\n\n      setPosition();\n\n      cacheBlocks();\n\n      animate();\n\n      // If paused, unpause and set listener for window resizing events\n      if (pause) {\n        window.addEventListener('resize', init);\n        pause = false;\n        // Start the loop\n        update();\n      }\n    };\n\n    // We want to cache the parallax blocks'\n    // values: base, top, height, speed\n    // el: is dom object, return: el cache values\n    var createBlock = function(el) {\n      var dataPercentage = el.getAttribute( 'data-rellax-percentage' );\n      var dataSpeed = el.getAttribute( 'data-rellax-speed' );\n      var dataXsSpeed = el.getAttribute( 'data-rellax-xs-speed' );\n      var dataMobileSpeed = el.getAttribute( 'data-rellax-mobile-speed' );\n      var dataTabletSpeed = el.getAttribute( 'data-rellax-tablet-speed' );\n      var dataDesktopSpeed = el.getAttribute( 'data-rellax-desktop-speed' );\n      var dataVerticalSpeed = el.getAttribute('data-rellax-vertical-speed');\n      var dataHorizontalSpeed = el.getAttribute('data-rellax-horizontal-speed');\n      var dataVericalScrollAxis = el.getAttribute('data-rellax-vertical-scroll-axis');\n      var dataHorizontalScrollAxis = el.getAttribute('data-rellax-horizontal-scroll-axis');\n      var dataZindex = el.getAttribute( 'data-rellax-zindex' ) || 0;\n      var dataMin = el.getAttribute( 'data-rellax-min' );\n      var dataMax = el.getAttribute( 'data-rellax-max' );\n      var dataMinX = el.getAttribute('data-rellax-min-x');\n      var dataMaxX = el.getAttribute('data-rellax-max-x');\n      var dataMinY = el.getAttribute('data-rellax-min-y');\n      var dataMaxY = el.getAttribute('data-rellax-max-y');\n      var mapBreakpoints;\n      var breakpoints = true;\n\n      if (!dataXsSpeed && !dataMobileSpeed && !dataTabletSpeed && !dataDesktopSpeed) {\n        breakpoints = false;\n      } else {\n        mapBreakpoints = {\n          'xs': dataXsSpeed,\n          'sm': dataMobileSpeed,\n          'md': dataTabletSpeed,\n          'lg': dataDesktopSpeed\n        };\n      }\n\n      // initializing at scrollY = 0 (top of browser), scrollX = 0 (left of browser)\n      // ensures elements are positioned based on HTML layout.\n      //\n      // If the element has the percentage attribute, the posY and posX needs to be\n      // the current scroll position's value, so that the elements are still positioned based on HTML layout\n      var wrapperPosY = self.options.wrapper ? self.options.wrapper.scrollTop : (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop);\n      // If the option relativeToWrapper is true, use the wrappers offset to top, subtracted from the current page scroll.\n      if (self.options.relativeToWrapper) {\n        var scrollPosY = (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop);\n        wrapperPosY = scrollPosY - self.options.wrapper.offsetTop;\n      }\n      var posY = self.options.vertical ? ( dataPercentage || self.options.center ? wrapperPosY : 0 ) : 0;\n      var posX = self.options.horizontal ? ( dataPercentage || self.options.center ? self.options.wrapper ? self.options.wrapper.scrollLeft : (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft) : 0 ) : 0;\n\n      var blockTop = posY + el.getBoundingClientRect().top;\n      var blockHeight = el.clientHeight || el.offsetHeight || el.scrollHeight;\n\n      var blockLeft = posX + el.getBoundingClientRect().left;\n      var blockWidth = el.clientWidth || el.offsetWidth || el.scrollWidth;\n\n      // apparently parallax equation everyone uses\n      var percentageY = dataPercentage ? dataPercentage : (posY - blockTop + screenY) / (blockHeight + screenY);\n      var percentageX = dataPercentage ? dataPercentage : (posX - blockLeft + screenX) / (blockWidth + screenX);\n      if(self.options.center){ percentageX = 0.5; percentageY = 0.5; }\n\n      // Optional individual block speed as data attr, otherwise global speed\n      var speed = (breakpoints && mapBreakpoints[currentBreakpoint] !== null) ? Number(mapBreakpoints[currentBreakpoint]) : (dataSpeed ? dataSpeed : self.options.speed);\n      var verticalSpeed = dataVerticalSpeed ? dataVerticalSpeed : self.options.verticalSpeed;\n      var horizontalSpeed = dataHorizontalSpeed ? dataHorizontalSpeed : self.options.horizontalSpeed;\n\n      // Optional individual block movement axis direction as data attr, otherwise gobal movement direction\n      var verticalScrollAxis = dataVericalScrollAxis ? dataVericalScrollAxis : self.options.verticalScrollAxis;\n      var horizontalScrollAxis = dataHorizontalScrollAxis ? dataHorizontalScrollAxis : self.options.horizontalScrollAxis;\n\n      var bases = updatePosition(percentageX, percentageY, speed, verticalSpeed, horizontalSpeed);\n\n      // ~~Store non-translate3d transforms~~\n      // Store inline styles and extract transforms\n      var style = el.style.cssText;\n      var transform = '';\n\n      // Check if there's an inline styled transform\n      var searchResult = /transform\\s*:/i.exec(style);\n      if (searchResult) {\n        // Get the index of the transform\n        var index = searchResult.index;\n\n        // Trim the style to the transform point and get the following semi-colon index\n        var trimmedStyle = style.slice(index);\n        var delimiter = trimmedStyle.indexOf(';');\n\n        // Remove \"transform\" string and save the attribute\n        if (delimiter) {\n          transform = \" \" + trimmedStyle.slice(11, delimiter).replace(/\\s/g,'');\n        } else {\n          transform = \" \" + trimmedStyle.slice(11).replace(/\\s/g,'');\n        }\n      }\n\n      return {\n        baseX: bases.x,\n        baseY: bases.y,\n        top: blockTop,\n        left: blockLeft,\n        height: blockHeight,\n        width: blockWidth,\n        speed: speed,\n        verticalSpeed: verticalSpeed,\n        horizontalSpeed: horizontalSpeed,\n        verticalScrollAxis: verticalScrollAxis,\n        horizontalScrollAxis: horizontalScrollAxis,\n        style: style,\n        transform: transform,\n        zindex: dataZindex,\n        min: dataMin,\n        max: dataMax,\n        minX: dataMinX,\n        maxX: dataMaxX,\n        minY: dataMinY,\n        maxY: dataMaxY\n      };\n    };\n\n    // set scroll position (posY, posX)\n    // side effect method is not ideal, but okay for now\n    // returns true if the scroll changed, false if nothing happened\n    var setPosition = function() {\n      var oldY = posY;\n      var oldX = posX;\n\n      posY = self.options.wrapper ? self.options.wrapper.scrollTop : (document.documentElement || document.body.parentNode || document.body).scrollTop || window.pageYOffset;\n      posX = self.options.wrapper ? self.options.wrapper.scrollLeft : (document.documentElement || document.body.parentNode || document.body).scrollLeft || window.pageXOffset;\n      // If option relativeToWrapper is true, use relative wrapper value instead.\n      if (self.options.relativeToWrapper) {\n        var scrollPosY = (document.documentElement || document.body.parentNode || document.body).scrollTop || window.pageYOffset;\n        posY = scrollPosY - self.options.wrapper.offsetTop;\n      }\n\n\n      if (oldY != posY && self.options.vertical) {\n        // scroll changed, return true\n        return true;\n      }\n\n      if (oldX != posX && self.options.horizontal) {\n        // scroll changed, return true\n        return true;\n      }\n\n      // scroll did not change\n      return false;\n    };\n\n    // Ahh a pure function, gets new transform value\n    // based on scrollPosition and speed\n    // Allow for decimal pixel values\n    var updatePosition = function(percentageX, percentageY, speed, verticalSpeed, horizontalSpeed) {\n      var result = {};\n      var valueX = ((horizontalSpeed ? horizontalSpeed : speed) * (100 * (1 - percentageX)));\n      var valueY = ((verticalSpeed ? verticalSpeed : speed) * (100 * (1 - percentageY)));\n\n      result.x = self.options.round ? Math.round(valueX) : Math.round(valueX * 100) / 100;\n      result.y = self.options.round ? Math.round(valueY) : Math.round(valueY * 100) / 100;\n\n      return result;\n    };\n\n    // Remove event listeners and loop again\n    var deferredUpdate = function() {\n      window.removeEventListener('resize', deferredUpdate);\n      window.removeEventListener('orientationchange', deferredUpdate);\n      (self.options.wrapper ? self.options.wrapper : window).removeEventListener('scroll', deferredUpdate);\n      (self.options.wrapper ? self.options.wrapper : document).removeEventListener('touchmove', deferredUpdate);\n\n      // loop again\n      loopId = loop(update);\n    };\n\n    // Loop\n    var update = function() {\n      if (setPosition() && pause === false) {\n        animate();\n\n        // loop again\n        loopId = loop(update);\n      } else {\n        loopId = null;\n\n        // Don't animate until we get a position updating event\n        window.addEventListener('resize', deferredUpdate);\n        window.addEventListener('orientationchange', deferredUpdate);\n        (self.options.wrapper ? self.options.wrapper : window).addEventListener('scroll', deferredUpdate, supportsPassive ? { passive: true } : false);\n        (self.options.wrapper ? self.options.wrapper : document).addEventListener('touchmove', deferredUpdate, supportsPassive ? { passive: true } : false);\n      }\n    };\n\n    // Transform3d on parallax element\n    var animate = function() {\n      var positions;\n      for (var i = 0; i < self.elems.length; i++){\n        // Determine relevant movement directions\n        var verticalScrollAxis = blocks[i].verticalScrollAxis.toLowerCase();\n        var horizontalScrollAxis = blocks[i].horizontalScrollAxis.toLowerCase();\n        var verticalScrollX = verticalScrollAxis.indexOf(\"x\") != -1 ? posY : 0;\n        var verticalScrollY = verticalScrollAxis.indexOf(\"y\") != -1 ? posY : 0;\n        var horizontalScrollX = horizontalScrollAxis.indexOf(\"x\") != -1 ? posX : 0;\n        var horizontalScrollY = horizontalScrollAxis.indexOf(\"y\") != -1 ? posX : 0;\n\n        var percentageY = ((verticalScrollY + horizontalScrollY - blocks[i].top + screenY) / (blocks[i].height + screenY));\n        var percentageX = ((verticalScrollX + horizontalScrollX - blocks[i].left + screenX) / (blocks[i].width + screenX));\n\n        // Subtracting initialize value, so element stays in same spot as HTML\n        positions = updatePosition(percentageX, percentageY, blocks[i].speed, blocks[i].verticalSpeed, blocks[i].horizontalSpeed);\n        var positionY = positions.y - blocks[i].baseY;\n        var positionX = positions.x - blocks[i].baseX;\n\n        // The next two \"if\" blocks go like this:\n        // Check if a limit is defined (first \"min\", then \"max\");\n        // Check if we need to change the Y or the X\n        // (Currently working only if just one of the axes is enabled)\n        // Then, check if the new position is inside the allowed limit\n        // If so, use new position. If not, set position to limit.\n\n        // Check if a min limit is defined\n        if (blocks[i].min !== null) {\n          if (self.options.vertical && !self.options.horizontal) {\n            positionY = positionY <= blocks[i].min ? blocks[i].min : positionY;\n          }\n          if (self.options.horizontal && !self.options.vertical) {\n            positionX = positionX <= blocks[i].min ? blocks[i].min : positionX;\n          }\n        }\n\n        // Check if directional min limits are defined\n        if (blocks[i].minY != null) {\n            positionY = positionY <= blocks[i].minY ? blocks[i].minY : positionY;\n        }\n        if (blocks[i].minX != null) {\n            positionX = positionX <= blocks[i].minX ? blocks[i].minX : positionX;\n        }\n\n        // Check if a max limit is defined\n        if (blocks[i].max !== null) {\n          if (self.options.vertical && !self.options.horizontal) {\n            positionY = positionY >= blocks[i].max ? blocks[i].max : positionY;\n          }\n          if (self.options.horizontal && !self.options.vertical) {\n            positionX = positionX >= blocks[i].max ? blocks[i].max : positionX;\n          }\n        }\n\n        // Check if directional max limits are defined\n        if (blocks[i].maxY != null) {\n            positionY = positionY >= blocks[i].maxY ? blocks[i].maxY : positionY;\n        }\n        if (blocks[i].maxX != null) {\n            positionX = positionX >= blocks[i].maxX ? blocks[i].maxX : positionX;\n        }\n\n        var zindex = blocks[i].zindex;\n\n        // Move that element\n        // (Set the new translation and append initial inline transforms.)\n        var translate = 'translate3d(' + (self.options.horizontal ? positionX : '0') + 'px,' + (self.options.vertical ? positionY : '0') + 'px,' + zindex + 'px) ' + blocks[i].transform;\n        self.elems[i].style[transformProp] = translate;\n      }\n      self.options.callback(positions);\n    };\n\n    self.destroy = function() {\n      for (var i = 0; i < self.elems.length; i++){\n        self.elems[i].style.cssText = blocks[i].style;\n      }\n\n      // Remove resize event listener if not pause, and pause\n      if (!pause) {\n        window.removeEventListener('resize', init);\n        pause = true;\n      }\n\n      // Clear the animation loop to prevent possible memory leak\n      clearLoop(loopId);\n      loopId = null;\n    };\n\n    // Init\n    init();\n\n    // Allow to recalculate the initial values whenever we want\n    self.refresh = init;\n\n    return self;\n  };\n  return Rellax;\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAWA,KAAC,SAAU,MAAM,SAAS;AACxB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE9C,eAAO,CAAC,GAAG,OAAO;AAAA,MACpB,WAAW,OAAO,WAAW,YAAY,OAAO,SAAS;AAIvD,eAAO,UAAU,QAAQ;AAAA,MAC3B,OAAO;AAEL,aAAK,SAAS,QAAQ;AAAA,MACxB;AAAA,IACF,GAAE,OAAO,WAAW,cAAc,SAAS,QAAQ,WAAY;AAC7D,UAAI,SAAS,SAAS,IAAI,SAAQ;AAChC;AAEA,YAAI,OAAO,OAAO,OAAO,OAAO,SAAS;AAEzC,YAAI,OAAO;AACX,YAAI,UAAU;AACd,YAAI,OAAO;AACX,YAAI,UAAU;AACd,YAAI,SAAS,CAAC;AACd,YAAI,QAAQ;AAIZ,YAAI,OAAO,OAAO,yBAChB,OAAO,+BACP,OAAO,4BACP,OAAO,2BACP,OAAO,0BACP,SAAS,UAAS;AAAE,iBAAO,WAAW,UAAU,MAAO,EAAE;AAAA,QAAG;AAG9D,YAAI,SAAS;AAGb,YAAI,kBAAkB;AACtB,YAAI;AACF,cAAI,OAAO,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,YAC9C,KAAK,WAAW;AACd,gCAAkB;AAAA,YACpB;AAAA,UACF,CAAC;AACD,iBAAO,iBAAiB,eAAe,MAAM,IAAI;AACjD,iBAAO,oBAAoB,eAAe,MAAM,IAAI;AAAA,QACtD,SAAS,GAAG;AAAA,QAAC;AAGb,YAAI,YAAY,OAAO,wBAAwB,OAAO,2BAA2B;AAGjF,YAAI,gBAAgB,OAAO,iBAAkB,WAAU;AACnD,cAAI,SAAS,SAAS,cAAc,KAAK;AACzC,cAAI,OAAO,MAAM,cAAc,MAAM;AACnC,gBAAI,UAAU,CAAC,UAAU,OAAO,IAAI;AACpC,qBAAS,UAAU,SAAS;AAC1B,kBAAI,OAAO,MAAO,QAAQ,MAAM,IAAI,WAAY,MAAM,QAAW;AAC/D,uBAAO,QAAQ,MAAM,IAAI;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT,EAAG;AAGL,aAAK,UAAU;AAAA,UACb,OAAO;AAAA,UACR,eAAe;AAAA,UACf,iBAAiB;AAAA,UAChB,aAAa,CAAC,KAAK,KAAK,IAAI;AAAA,UAC5B,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,mBAAmB;AAAA,UACnB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,oBAAoB;AAAA,UACpB,sBAAsB;AAAA,UACtB,UAAU,WAAW;AAAA,UAAC;AAAA,QACxB;AAGA,YAAI,SAAQ;AACV,iBAAO,KAAK,OAAO,EAAE,QAAQ,SAAS,KAAI;AACxC,iBAAK,QAAQ,GAAG,IAAI,QAAQ,GAAG;AAAA,UACjC,CAAC;AAAA,QACH;AAEA,iBAAS,4BAA6B;AACpC,cAAI,KAAK,QAAQ,YAAY,WAAW,KAAK,MAAM,QAAQ,KAAK,QAAQ,WAAW,GAAG;AACpF,gBAAI,cAAc;AAClB,gBAAI,cAAc;AAClB,gBAAI;AACJ,iBAAK,QAAQ,YAAY,QAAQ,SAAU,GAAG;AAC5C,kBAAI,OAAO,MAAM,SAAU,eAAc;AACzC,kBAAI,YAAY,MAAM;AACpB,oBAAI,IAAI,QAAS,eAAc;AAAA,cACjC;AACA,wBAAU;AAAA,YACZ,CAAC;AACD,gBAAI,eAAe,YAAa;AAAA,UAClC;AAEA,eAAK,QAAQ,cAAc,CAAC,KAAK,KAAK,IAAI;AAC1C,kBAAQ,KAAK,6GAA6G;AAAA,QAC5H;AAEA,YAAI,WAAW,QAAQ,aAAa;AAClC,oCAA0B;AAAA,QAC5B;AAGA,YAAI,CAAC,IAAI;AACP,eAAK;AAAA,QACP;AAGA,YAAI,WAAW,OAAO,OAAO,WAAW,SAAS,iBAAiB,EAAE,IAAI,CAAC,EAAE;AAG3E,YAAI,SAAS,SAAS,GAAG;AACvB,eAAK,QAAQ;AAAA,QACf,OAGK;AACH,kBAAQ,KAAK,2DAA2D;AACxE;AAAA,QACF;AAGA,YAAI,KAAK,QAAQ,SAAS;AACxB,cAAI,CAAC,KAAK,QAAQ,QAAQ,UAAU;AAClC,gBAAI,UAAU,SAAS,cAAc,KAAK,QAAQ,OAAO;AAEzD,gBAAI,SAAS;AACX,mBAAK,QAAQ,UAAU;AAAA,YACzB,OAAO;AACL,sBAAQ,KAAK,yDAAyD;AACtE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,YAAI;AAGJ,YAAI,uBAAuB,SAAU,GAAG;AACtC,cAAI,KAAK,KAAK,QAAQ;AACtB,cAAI,IAAI,GAAG,CAAC,EAAG,QAAO;AACtB,cAAI,KAAK,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,EAAG,QAAO;AACpC,cAAI,KAAK,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,EAAG,QAAO;AACpC,iBAAO;AAAA,QACT;AAGA,YAAI,cAAc,WAAW;AAC3B,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAI;AACzC,gBAAI,QAAQ,YAAY,KAAK,MAAM,CAAC,CAAC;AACrC,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAKA,YAAI,OAAO,WAAW;AACpB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAI;AACrC,iBAAK,MAAM,CAAC,EAAE,MAAM,UAAU,OAAO,CAAC,EAAE;AAAA,UAC1C;AAEA,mBAAS,CAAC;AAEV,oBAAU,OAAO;AACjB,oBAAU,OAAO;AACjB,8BAAoB,qBAAqB,OAAO;AAEhD,sBAAY;AAEZ,sBAAY;AAEZ,kBAAQ;AAGR,cAAI,OAAO;AACT,mBAAO,iBAAiB,UAAU,IAAI;AACtC,oBAAQ;AAER,mBAAO;AAAA,UACT;AAAA,QACF;AAKA,YAAI,cAAc,SAASA,KAAI;AAC7B,cAAI,iBAAiBA,IAAG,aAAc,wBAAyB;AAC/D,cAAI,YAAYA,IAAG,aAAc,mBAAoB;AACrD,cAAI,cAAcA,IAAG,aAAc,sBAAuB;AAC1D,cAAI,kBAAkBA,IAAG,aAAc,0BAA2B;AAClE,cAAI,kBAAkBA,IAAG,aAAc,0BAA2B;AAClE,cAAI,mBAAmBA,IAAG,aAAc,2BAA4B;AACpE,cAAI,oBAAoBA,IAAG,aAAa,4BAA4B;AACpE,cAAI,sBAAsBA,IAAG,aAAa,8BAA8B;AACxE,cAAI,wBAAwBA,IAAG,aAAa,kCAAkC;AAC9E,cAAI,2BAA2BA,IAAG,aAAa,oCAAoC;AACnF,cAAI,aAAaA,IAAG,aAAc,oBAAqB,KAAK;AAC5D,cAAI,UAAUA,IAAG,aAAc,iBAAkB;AACjD,cAAI,UAAUA,IAAG,aAAc,iBAAkB;AACjD,cAAI,WAAWA,IAAG,aAAa,mBAAmB;AAClD,cAAI,WAAWA,IAAG,aAAa,mBAAmB;AAClD,cAAI,WAAWA,IAAG,aAAa,mBAAmB;AAClD,cAAI,WAAWA,IAAG,aAAa,mBAAmB;AAClD,cAAI;AACJ,cAAI,cAAc;AAElB,cAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,kBAAkB;AAC7E,0BAAc;AAAA,UAChB,OAAO;AACL,6BAAiB;AAAA,cACf,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,UACF;AAOA,cAAI,cAAc,KAAK,QAAQ,UAAU,KAAK,QAAQ,QAAQ,YAAa,OAAO,eAAe,SAAS,gBAAgB,aAAa,SAAS,KAAK;AAErJ,cAAI,KAAK,QAAQ,mBAAmB;AAClC,gBAAI,aAAc,OAAO,eAAe,SAAS,gBAAgB,aAAa,SAAS,KAAK;AAC5F,0BAAc,aAAa,KAAK,QAAQ,QAAQ;AAAA,UAClD;AACA,cAAIC,QAAO,KAAK,QAAQ,WAAa,kBAAkB,KAAK,QAAQ,SAAS,cAAc,IAAM;AACjG,cAAIC,QAAO,KAAK,QAAQ,aAAe,kBAAkB,KAAK,QAAQ,SAAS,KAAK,QAAQ,UAAU,KAAK,QAAQ,QAAQ,aAAc,OAAO,eAAe,SAAS,gBAAgB,cAAc,SAAS,KAAK,aAAc,IAAM;AAExO,cAAI,WAAWD,QAAOD,IAAG,sBAAsB,EAAE;AACjD,cAAI,cAAcA,IAAG,gBAAgBA,IAAG,gBAAgBA,IAAG;AAE3D,cAAI,YAAYE,QAAOF,IAAG,sBAAsB,EAAE;AAClD,cAAI,aAAaA,IAAG,eAAeA,IAAG,eAAeA,IAAG;AAGxD,cAAI,cAAc,iBAAiB,kBAAkBC,QAAO,WAAW,YAAY,cAAc;AACjG,cAAI,cAAc,iBAAiB,kBAAkBC,QAAO,YAAY,YAAY,aAAa;AACjG,cAAG,KAAK,QAAQ,QAAO;AAAE,0BAAc;AAAK,0BAAc;AAAA,UAAK;AAG/D,cAAI,QAAS,eAAe,eAAe,iBAAiB,MAAM,OAAQ,OAAO,eAAe,iBAAiB,CAAC,IAAK,YAAY,YAAY,KAAK,QAAQ;AAC5J,cAAI,gBAAgB,oBAAoB,oBAAoB,KAAK,QAAQ;AACzE,cAAI,kBAAkB,sBAAsB,sBAAsB,KAAK,QAAQ;AAG/E,cAAI,qBAAqB,wBAAwB,wBAAwB,KAAK,QAAQ;AACtF,cAAI,uBAAuB,2BAA2B,2BAA2B,KAAK,QAAQ;AAE9F,cAAI,QAAQ,eAAe,aAAa,aAAa,OAAO,eAAe,eAAe;AAI1F,cAAI,QAAQF,IAAG,MAAM;AACrB,cAAI,YAAY;AAGhB,cAAI,eAAe,iBAAiB,KAAK,KAAK;AAC9C,cAAI,cAAc;AAEhB,gBAAI,QAAQ,aAAa;AAGzB,gBAAI,eAAe,MAAM,MAAM,KAAK;AACpC,gBAAI,YAAY,aAAa,QAAQ,GAAG;AAGxC,gBAAI,WAAW;AACb,0BAAY,MAAM,aAAa,MAAM,IAAI,SAAS,EAAE,QAAQ,OAAM,EAAE;AAAA,YACtE,OAAO;AACL,0BAAY,MAAM,aAAa,MAAM,EAAE,EAAE,QAAQ,OAAM,EAAE;AAAA,YAC3D;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,OAAO,MAAM;AAAA,YACb,OAAO,MAAM;AAAA,YACb,KAAK;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,OAAO;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,KAAK;AAAA,YACL,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACR;AAAA,QACF;AAKA,YAAI,cAAc,WAAW;AAC3B,cAAI,OAAO;AACX,cAAI,OAAO;AAEX,iBAAO,KAAK,QAAQ,UAAU,KAAK,QAAQ,QAAQ,aAAa,SAAS,mBAAmB,SAAS,KAAK,cAAc,SAAS,MAAM,aAAa,OAAO;AAC3J,iBAAO,KAAK,QAAQ,UAAU,KAAK,QAAQ,QAAQ,cAAc,SAAS,mBAAmB,SAAS,KAAK,cAAc,SAAS,MAAM,cAAc,OAAO;AAE7J,cAAI,KAAK,QAAQ,mBAAmB;AAClC,gBAAI,cAAc,SAAS,mBAAmB,SAAS,KAAK,cAAc,SAAS,MAAM,aAAa,OAAO;AAC7G,mBAAO,aAAa,KAAK,QAAQ,QAAQ;AAAA,UAC3C;AAGA,cAAI,QAAQ,QAAQ,KAAK,QAAQ,UAAU;AAEzC,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,QAAQ,KAAK,QAAQ,YAAY;AAE3C,mBAAO;AAAA,UACT;AAGA,iBAAO;AAAA,QACT;AAKA,YAAI,iBAAiB,SAAS,aAAa,aAAa,OAAO,eAAe,iBAAiB;AAC7F,cAAI,SAAS,CAAC;AACd,cAAI,UAAW,kBAAkB,kBAAkB,UAAU,OAAO,IAAI;AACxE,cAAI,UAAW,gBAAgB,gBAAgB,UAAU,OAAO,IAAI;AAEpE,iBAAO,IAAI,KAAK,QAAQ,QAAQ,KAAK,MAAM,MAAM,IAAI,KAAK,MAAM,SAAS,GAAG,IAAI;AAChF,iBAAO,IAAI,KAAK,QAAQ,QAAQ,KAAK,MAAM,MAAM,IAAI,KAAK,MAAM,SAAS,GAAG,IAAI;AAEhF,iBAAO;AAAA,QACT;AAGA,YAAI,iBAAiB,WAAW;AAC9B,iBAAO,oBAAoB,UAAU,cAAc;AACnD,iBAAO,oBAAoB,qBAAqB,cAAc;AAC9D,WAAC,KAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,QAAQ,oBAAoB,UAAU,cAAc;AACnG,WAAC,KAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,UAAU,oBAAoB,aAAa,cAAc;AAGxG,mBAAS,KAAK,MAAM;AAAA,QACtB;AAGA,YAAI,SAAS,WAAW;AACtB,cAAI,YAAY,KAAK,UAAU,OAAO;AACpC,oBAAQ;AAGR,qBAAS,KAAK,MAAM;AAAA,UACtB,OAAO;AACL,qBAAS;AAGT,mBAAO,iBAAiB,UAAU,cAAc;AAChD,mBAAO,iBAAiB,qBAAqB,cAAc;AAC3D,aAAC,KAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,QAAQ,iBAAiB,UAAU,gBAAgB,kBAAkB,EAAE,SAAS,KAAK,IAAI,KAAK;AAC7I,aAAC,KAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,UAAU,iBAAiB,aAAa,gBAAgB,kBAAkB,EAAE,SAAS,KAAK,IAAI,KAAK;AAAA,UACpJ;AAAA,QACF;AAGA,YAAI,UAAU,WAAW;AACvB,cAAI;AACJ,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAI;AAEzC,gBAAI,qBAAqB,OAAO,CAAC,EAAE,mBAAmB,YAAY;AAClE,gBAAI,uBAAuB,OAAO,CAAC,EAAE,qBAAqB,YAAY;AACtE,gBAAI,kBAAkB,mBAAmB,QAAQ,GAAG,KAAK,KAAK,OAAO;AACrE,gBAAI,kBAAkB,mBAAmB,QAAQ,GAAG,KAAK,KAAK,OAAO;AACrE,gBAAI,oBAAoB,qBAAqB,QAAQ,GAAG,KAAK,KAAK,OAAO;AACzE,gBAAI,oBAAoB,qBAAqB,QAAQ,GAAG,KAAK,KAAK,OAAO;AAEzE,gBAAI,eAAgB,kBAAkB,oBAAoB,OAAO,CAAC,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE,SAAS;AACzG,gBAAI,eAAgB,kBAAkB,oBAAoB,OAAO,CAAC,EAAE,OAAO,YAAY,OAAO,CAAC,EAAE,QAAQ;AAGzG,wBAAY,eAAe,aAAa,aAAa,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,eAAe,OAAO,CAAC,EAAE,eAAe;AACxH,gBAAI,YAAY,UAAU,IAAI,OAAO,CAAC,EAAE;AACxC,gBAAI,YAAY,UAAU,IAAI,OAAO,CAAC,EAAE;AAUxC,gBAAI,OAAO,CAAC,EAAE,QAAQ,MAAM;AAC1B,kBAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,YAAY;AACrD,4BAAY,aAAa,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,MAAM;AAAA,cAC3D;AACA,kBAAI,KAAK,QAAQ,cAAc,CAAC,KAAK,QAAQ,UAAU;AACrD,4BAAY,aAAa,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,MAAM;AAAA,cAC3D;AAAA,YACF;AAGA,gBAAI,OAAO,CAAC,EAAE,QAAQ,MAAM;AACxB,0BAAY,aAAa,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,OAAO;AAAA,YAC/D;AACA,gBAAI,OAAO,CAAC,EAAE,QAAQ,MAAM;AACxB,0BAAY,aAAa,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,OAAO;AAAA,YAC/D;AAGA,gBAAI,OAAO,CAAC,EAAE,QAAQ,MAAM;AAC1B,kBAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,YAAY;AACrD,4BAAY,aAAa,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,MAAM;AAAA,cAC3D;AACA,kBAAI,KAAK,QAAQ,cAAc,CAAC,KAAK,QAAQ,UAAU;AACrD,4BAAY,aAAa,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,MAAM;AAAA,cAC3D;AAAA,YACF;AAGA,gBAAI,OAAO,CAAC,EAAE,QAAQ,MAAM;AACxB,0BAAY,aAAa,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,OAAO;AAAA,YAC/D;AACA,gBAAI,OAAO,CAAC,EAAE,QAAQ,MAAM;AACxB,0BAAY,aAAa,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,OAAO;AAAA,YAC/D;AAEA,gBAAI,SAAS,OAAO,CAAC,EAAE;AAIvB,gBAAI,YAAY,kBAAkB,KAAK,QAAQ,aAAa,YAAY,OAAO,SAAS,KAAK,QAAQ,WAAW,YAAY,OAAO,QAAQ,SAAS,SAAS,OAAO,CAAC,EAAE;AACvK,iBAAK,MAAM,CAAC,EAAE,MAAM,aAAa,IAAI;AAAA,UACvC;AACA,eAAK,QAAQ,SAAS,SAAS;AAAA,QACjC;AAEA,aAAK,UAAU,WAAW;AACxB,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAI;AACzC,iBAAK,MAAM,CAAC,EAAE,MAAM,UAAU,OAAO,CAAC,EAAE;AAAA,UAC1C;AAGA,cAAI,CAAC,OAAO;AACV,mBAAO,oBAAoB,UAAU,IAAI;AACzC,oBAAQ;AAAA,UACV;AAGA,oBAAU,MAAM;AAChB,mBAAS;AAAA,QACX;AAGA,aAAK;AAGL,aAAK,UAAU;AAEf,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;", "names": ["el", "posY", "posX"]}