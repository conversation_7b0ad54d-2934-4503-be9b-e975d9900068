{"version": 3, "sources": ["../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/defaults.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/global.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/css.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/extend.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/getParents.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/ready.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/isMobile.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/getWindowSize.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/utils/observer.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/core.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/node_modules/video-worker/dist/video-worker.esm.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/ext-video.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/deprecated/ext-element.js", "../../.pnpm/jarallax@2.2.1/node_modules/jarallax/src/core.esm.js"], "sourcesContent": ["export default {\n  // Base parallax options.\n  type: 'scroll',\n  speed: 0.5,\n  containerClass: 'jarallax-container',\n  imgSrc: null,\n  imgElement: '.jarallax-img',\n  imgSize: 'cover',\n  imgPosition: '50% 50%',\n  imgRepeat: 'no-repeat',\n  keepImg: false,\n  elementInViewport: null,\n  zIndex: -100,\n  disableParallax: false,\n\n  // Callbacks.\n  onScroll: null,\n  onInit: null,\n  onDestroy: null,\n  onCoverImage: null,\n\n  // Video options.\n  videoClass: 'jarallax-video',\n  videoSrc: null,\n  videoStartTime: 0,\n  videoEndTime: 0,\n  videoVolume: 0,\n  videoLoop: true,\n  videoPlayOnlyVisible: true,\n  videoLazyLoading: true,\n  disableVideo: false,\n\n  // Video callbacks.\n  onVideoInsert: null,\n  onVideoWorkerInit: null,\n};\n", "/* eslint-disable import/no-mutable-exports */\n/* eslint-disable no-restricted-globals */\nlet win;\n\nif (typeof window !== 'undefined') {\n  win = window;\n} else if (typeof global !== 'undefined') {\n  win = global;\n} else if (typeof self !== 'undefined') {\n  win = self;\n} else {\n  win = {};\n}\n\nexport default win;\n", "import global from './global';\n\n/**\n * Add styles to element.\n *\n * @param {Element} el - element.\n * @param {String|Object} styles - styles list.\n *\n * @returns {Element}\n */\nexport default function css(el, styles) {\n  if (typeof styles === 'string') {\n    return global.getComputedStyle(el).getPropertyValue(styles);\n  }\n\n  Object.keys(styles).forEach((key) => {\n    el.style[key] = styles[key];\n  });\n  return el;\n}\n", "/**\n * Extend like jQuery.extend\n *\n * @param {Object} out - output object.\n * @param {...any} args - additional objects to extend.\n *\n * @returns {Object}\n */\nexport default function extend(out, ...args) {\n  out = out || {};\n\n  Object.keys(args).forEach((i) => {\n    if (!args[i]) {\n      return;\n    }\n    Object.keys(args[i]).forEach((key) => {\n      out[key] = args[i][key];\n    });\n  });\n\n  return out;\n}\n", "/**\n * Get all parents of the element.\n *\n * @param {Element} elem - DOM element.\n *\n * @returns {Array}\n */\nexport default function getParents(elem) {\n  const parents = [];\n\n  while (elem.parentElement !== null) {\n    elem = elem.parentElement;\n\n    if (elem.nodeType === 1) {\n      parents.push(elem);\n    }\n  }\n\n  return parents;\n}\n", "/**\n * Document ready callback.\n * @param {Function} callback - callback will be fired once Document ready.\n */\nfunction ready(callback) {\n  if (document.readyState === 'complete' || document.readyState === 'interactive') {\n    // Already ready or interactive, execute callback\n    callback();\n  } else {\n    document.addEventListener('DOMContentLoaded', callback, {\n      capture: true,\n      once: true,\n      passive: true,\n    });\n  }\n}\n\nexport default ready;\n", "import global from './global';\n\nconst { navigator } = global;\n\nconst mobileAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n  navigator.userAgent\n);\n\nexport default function isMobile() {\n  return mobileAgent;\n}\n", "import global from './global';\nimport domReady from './ready';\nimport isMobile from './isMobile';\n\nlet wndW;\nlet wndH;\nlet $deviceHelper;\n\n/**\n * The most popular mobile browsers changes height after page scroll and this generates image jumping.\n * We can fix it using this workaround with vh units.\n */\nfunction getDeviceHeight() {\n  if (!$deviceHelper && document.body) {\n    $deviceHelper = document.createElement('div');\n    $deviceHelper.style.cssText =\n      'position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;';\n    document.body.appendChild($deviceHelper);\n  }\n\n  return (\n    ($deviceHelper ? $deviceHelper.clientHeight : 0) ||\n    global.innerHeight ||\n    document.documentElement.clientHeight\n  );\n}\n\nfunction updateWindowHeight() {\n  wndW = global.innerWidth || document.documentElement.clientWidth;\n\n  if (isMobile()) {\n    wndH = getDeviceHeight();\n  } else {\n    wndH = global.innerHeight || document.documentElement.clientHeight;\n  }\n}\n\nupdateWindowHeight();\nglobal.addEventListener('resize', updateWindowHeight);\nglobal.addEventListener('orientationchange', updateWindowHeight);\nglobal.addEventListener('load', updateWindowHeight);\ndomReady(() => {\n  updateWindowHeight({\n    type: 'dom-loaded',\n  });\n});\n\nexport default function getWindowSize() {\n  return {\n    width: wndW,\n    height: wndH,\n  };\n}\n", "import global from './global';\nimport getWindowSize from './getWindowSize';\n\n// List with all jarallax instances\n// need to render all in one scroll/resize event.\nconst jarallaxList = [];\n\nfunction updateParallax() {\n  if (!jarallaxList.length) {\n    return;\n  }\n\n  const { width: wndW, height: wndH } = getWindowSize();\n\n  jarallaxList.forEach((data, k) => {\n    const { instance, oldData } = data;\n\n    if (!instance.isVisible()) {\n      return;\n    }\n\n    const clientRect = instance.$item.getBoundingClientRect();\n\n    const newData = {\n      width: clientRect.width,\n      height: clientRect.height,\n      top: clientRect.top,\n      bottom: clientRect.bottom,\n      wndW,\n      wndH,\n    };\n\n    const isResized =\n      !oldData ||\n      oldData.wndW !== newData.wndW ||\n      oldData.wndH !== newData.wndH ||\n      oldData.width !== newData.width ||\n      oldData.height !== newData.height;\n    const isScrolled =\n      isResized || !oldData || oldData.top !== newData.top || oldData.bottom !== newData.bottom;\n\n    jarallaxList[k].oldData = newData;\n\n    if (isResized) {\n      instance.onResize();\n    }\n    if (isScrolled) {\n      instance.onScroll();\n    }\n  });\n\n  global.requestAnimationFrame(updateParallax);\n}\n\nconst visibilityObserver = new global.IntersectionObserver(\n  (entries) => {\n    entries.forEach((entry) => {\n      entry.target.jarallax.isElementInViewport = entry.isIntersecting;\n    });\n  },\n  {\n    // We have to start parallax calculation before the block is in view\n    // to prevent possible parallax jumping.\n    rootMargin: '50px',\n  }\n);\n\nexport function addObserver(instance) {\n  jarallaxList.push({\n    instance,\n  });\n\n  if (jarallaxList.length === 1) {\n    global.requestAnimationFrame(updateParallax);\n  }\n\n  visibilityObserver.observe(instance.options.elementInViewport || instance.$item);\n}\n\nexport function removeObserver(instance) {\n  jarallaxList.forEach((data, key) => {\n    if (data.instance.instanceID === instance.instanceID) {\n      jarallaxList.splice(key, 1);\n    }\n  });\n\n  visibilityObserver.unobserve(instance.options.elementInViewport || instance.$item);\n}\n", "/* eslint-disable class-methods-use-this */\nimport defaults from './defaults';\nimport global from './utils/global';\nimport css from './utils/css';\nimport extend from './utils/extend';\nimport getParents from './utils/getParents';\nimport getWindowSize from './utils/getWindowSize';\nimport { addObserver, removeObserver } from './utils/observer';\n\nconst { navigator } = global;\n\nlet instanceID = 0;\n\n// Jarallax class\nclass Jarallax {\n  constructor(item, userOptions) {\n    const self = this;\n\n    self.instanceID = instanceID;\n    instanceID += 1;\n\n    self.$item = item;\n\n    self.defaults = { ...defaults };\n\n    // prepare data-options\n    const dataOptions = self.$item.dataset || {};\n    const pureDataOptions = {};\n    Object.keys(dataOptions).forEach((key) => {\n      const lowerCaseOption = key.substr(0, 1).toLowerCase() + key.substr(1);\n      if (lowerCaseOption && typeof self.defaults[lowerCaseOption] !== 'undefined') {\n        pureDataOptions[lowerCaseOption] = dataOptions[key];\n      }\n    });\n\n    self.options = self.extend({}, self.defaults, pureDataOptions, userOptions);\n    self.pureOptions = self.extend({}, self.options);\n\n    // prepare 'true' and 'false' strings to boolean\n    Object.keys(self.options).forEach((key) => {\n      if (self.options[key] === 'true') {\n        self.options[key] = true;\n      } else if (self.options[key] === 'false') {\n        self.options[key] = false;\n      }\n    });\n\n    // fix speed option [-1.0, 2.0]\n    self.options.speed = Math.min(2, Math.max(-1, parseFloat(self.options.speed)));\n\n    // prepare disableParallax callback\n    if (typeof self.options.disableParallax === 'string') {\n      self.options.disableParallax = new RegExp(self.options.disableParallax);\n    }\n    if (self.options.disableParallax instanceof RegExp) {\n      const disableParallaxRegexp = self.options.disableParallax;\n      self.options.disableParallax = () => disableParallaxRegexp.test(navigator.userAgent);\n    }\n    if (typeof self.options.disableParallax !== 'function') {\n      // Support for `true` option value.\n      const disableParallaxDefault = self.options.disableParallax;\n      self.options.disableParallax = () => disableParallaxDefault === true;\n    }\n\n    // prepare disableVideo callback\n    if (typeof self.options.disableVideo === 'string') {\n      self.options.disableVideo = new RegExp(self.options.disableVideo);\n    }\n    if (self.options.disableVideo instanceof RegExp) {\n      const disableVideoRegexp = self.options.disableVideo;\n      self.options.disableVideo = () => disableVideoRegexp.test(navigator.userAgent);\n    }\n    if (typeof self.options.disableVideo !== 'function') {\n      // Support for `true` option value.\n      const disableVideoDefault = self.options.disableVideo;\n      self.options.disableVideo = () => disableVideoDefault === true;\n    }\n\n    // custom element to check if parallax in viewport\n    let elementInVP = self.options.elementInViewport;\n    // get first item from array\n    if (\n      elementInVP &&\n      typeof elementInVP === 'object' &&\n      typeof elementInVP.length !== 'undefined'\n    ) {\n      [elementInVP] = elementInVP;\n    }\n    // check if dom element\n    if (!(elementInVP instanceof Element)) {\n      elementInVP = null;\n    }\n    self.options.elementInViewport = elementInVP;\n\n    self.image = {\n      src: self.options.imgSrc || null,\n      $container: null,\n      useImgTag: false,\n\n      // 1. Position fixed is needed for the most of browsers because absolute position have glitches\n      // 2. On MacOS with smooth scroll there is a huge lags with absolute position - https://github.com/nk-o/jarallax/issues/75\n      // 3. Previously used 'absolute' for mobile devices. But we re-tested on iPhone 12 and 'fixed' position is working better, then 'absolute', so for now position is always 'fixed'\n      position: 'fixed',\n    };\n\n    if (self.initImg() && self.canInitParallax()) {\n      self.init();\n    }\n  }\n\n  css(el, styles) {\n    return css(el, styles);\n  }\n\n  extend(out, ...args) {\n    return extend(out, ...args);\n  }\n\n  // get window size and scroll position. Useful for extensions\n  getWindowData() {\n    const { width, height } = getWindowSize();\n\n    return {\n      width,\n      height,\n      y: document.documentElement.scrollTop,\n    };\n  }\n\n  // Jarallax functions\n  initImg() {\n    const self = this;\n\n    // find image element\n    let $imgElement = self.options.imgElement;\n    if ($imgElement && typeof $imgElement === 'string') {\n      $imgElement = self.$item.querySelector($imgElement);\n    }\n\n    // check if dom element\n    if (!($imgElement instanceof Element)) {\n      if (self.options.imgSrc) {\n        $imgElement = new Image();\n        $imgElement.src = self.options.imgSrc;\n      } else {\n        $imgElement = null;\n      }\n    }\n\n    if ($imgElement) {\n      if (self.options.keepImg) {\n        self.image.$item = $imgElement.cloneNode(true);\n      } else {\n        self.image.$item = $imgElement;\n        self.image.$itemParent = $imgElement.parentNode;\n      }\n      self.image.useImgTag = true;\n    }\n\n    // true if there is img tag\n    if (self.image.$item) {\n      return true;\n    }\n\n    // get image src\n    if (self.image.src === null) {\n      self.image.src =\n        'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n      self.image.bgImage = self.css(self.$item, 'background-image');\n    }\n    return !(!self.image.bgImage || self.image.bgImage === 'none');\n  }\n\n  canInitParallax() {\n    return !this.options.disableParallax();\n  }\n\n  init() {\n    const self = this;\n    const containerStyles = {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n    };\n    let imageStyles = {\n      pointerEvents: 'none',\n      transformStyle: 'preserve-3d',\n      backfaceVisibility: 'hidden',\n    };\n\n    if (!self.options.keepImg) {\n      // save default user styles\n      const curStyle = self.$item.getAttribute('style');\n      if (curStyle) {\n        self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n      }\n      if (self.image.useImgTag) {\n        const curImgStyle = self.image.$item.getAttribute('style');\n        if (curImgStyle) {\n          self.image.$item.setAttribute('data-jarallax-original-styles', curImgStyle);\n        }\n      }\n    }\n\n    // set relative position and z-index to the parent\n    if (self.css(self.$item, 'position') === 'static') {\n      self.css(self.$item, {\n        position: 'relative',\n      });\n    }\n    if (self.css(self.$item, 'z-index') === 'auto') {\n      self.css(self.$item, {\n        zIndex: 0,\n      });\n    }\n\n    // container for parallax image\n    self.image.$container = document.createElement('div');\n    self.css(self.image.$container, containerStyles);\n    self.css(self.image.$container, {\n      'z-index': self.options.zIndex,\n    });\n\n    // it will remove some image overlapping\n    // overlapping occur due to an image position fixed inside absolute position element\n    // needed only when background in fixed position\n    if (this.image.position === 'fixed') {\n      self.css(self.image.$container, {\n        '-webkit-clip-path': 'polygon(0 0, 100% 0, 100% 100%, 0 100%)',\n        'clip-path': 'polygon(0 0, 100% 0, 100% 100%, 0 100%)',\n      });\n    }\n\n    // Add container unique ID.\n    self.image.$container.setAttribute('id', `jarallax-container-${self.instanceID}`);\n\n    // Add container class.\n    if (self.options.containerClass) {\n      self.image.$container.setAttribute('class', self.options.containerClass);\n    }\n\n    self.$item.appendChild(self.image.$container);\n\n    // use img tag\n    if (self.image.useImgTag) {\n      imageStyles = self.extend(\n        {\n          'object-fit': self.options.imgSize,\n          'object-position': self.options.imgPosition,\n          'max-width': 'none',\n        },\n        containerStyles,\n        imageStyles\n      );\n\n      // use div with background image\n    } else {\n      self.image.$item = document.createElement('div');\n      if (self.image.src) {\n        imageStyles = self.extend(\n          {\n            'background-position': self.options.imgPosition,\n            'background-size': self.options.imgSize,\n            'background-repeat': self.options.imgRepeat,\n            'background-image': self.image.bgImage || `url(\"${self.image.src}\")`,\n          },\n          containerStyles,\n          imageStyles\n        );\n      }\n    }\n\n    if (\n      self.options.type === 'opacity' ||\n      self.options.type === 'scale' ||\n      self.options.type === 'scale-opacity' ||\n      self.options.speed === 1\n    ) {\n      self.image.position = 'absolute';\n    }\n\n    // 1. Check if one of parents have transform style (without this check, scroll transform will be inverted if used parallax with position fixed)\n    //    discussion - https://github.com/nk-o/jarallax/issues/9\n    // 2. Check if parents have overflow scroll\n    if (self.image.position === 'fixed') {\n      const $parents = getParents(self.$item).filter((el) => {\n        const styles = global.getComputedStyle(el);\n        const parentTransform =\n          styles['-webkit-transform'] || styles['-moz-transform'] || styles.transform;\n        const overflowRegex = /(auto|scroll)/;\n\n        return (\n          (parentTransform && parentTransform !== 'none') ||\n          overflowRegex.test(styles.overflow + styles['overflow-y'] + styles['overflow-x'])\n        );\n      });\n\n      self.image.position = $parents.length ? 'absolute' : 'fixed';\n    }\n\n    // add position to parallax block\n    imageStyles.position = self.image.position;\n\n    // insert parallax image\n    self.css(self.image.$item, imageStyles);\n    self.image.$container.appendChild(self.image.$item);\n\n    // set initial position and size\n    self.onResize();\n    self.onScroll(true);\n\n    // call onInit event\n    if (self.options.onInit) {\n      self.options.onInit.call(self);\n    }\n\n    // remove default user background\n    if (self.css(self.$item, 'background-image') !== 'none') {\n      self.css(self.$item, {\n        'background-image': 'none',\n      });\n    }\n\n    addObserver(self);\n  }\n\n  destroy() {\n    const self = this;\n\n    removeObserver(self);\n\n    // return styles on container as before jarallax init\n    const originalStylesTag = self.$item.getAttribute('data-jarallax-original-styles');\n    self.$item.removeAttribute('data-jarallax-original-styles');\n    // null occurs if there is no style tag before jarallax init\n    if (!originalStylesTag) {\n      self.$item.removeAttribute('style');\n    } else {\n      self.$item.setAttribute('style', originalStylesTag);\n    }\n\n    if (self.image.useImgTag) {\n      // return styles on img tag as before jarallax init\n      const originalStylesImgTag = self.image.$item.getAttribute('data-jarallax-original-styles');\n      self.image.$item.removeAttribute('data-jarallax-original-styles');\n      // null occurs if there is no style tag before jarallax init\n      if (!originalStylesImgTag) {\n        self.image.$item.removeAttribute('style');\n      } else {\n        self.image.$item.setAttribute('style', originalStylesTag);\n      }\n\n      // move img tag to its default position\n      if (self.image.$itemParent) {\n        self.image.$itemParent.appendChild(self.image.$item);\n      }\n    }\n\n    // remove additional dom elements\n    if (self.image.$container) {\n      self.image.$container.parentNode.removeChild(self.image.$container);\n    }\n\n    // call onDestroy event\n    if (self.options.onDestroy) {\n      self.options.onDestroy.call(self);\n    }\n\n    // delete jarallax from item\n    delete self.$item.jarallax;\n  }\n\n  coverImage() {\n    const self = this;\n\n    const { height: wndH } = getWindowSize();\n    const rect = self.image.$container.getBoundingClientRect();\n    const contH = rect.height;\n    const { speed } = self.options;\n    const isScroll = self.options.type === 'scroll' || self.options.type === 'scroll-opacity';\n    let scrollDist = 0;\n    let resultH = contH;\n    let resultMT = 0;\n\n    // scroll parallax\n    if (isScroll) {\n      // scroll distance and height for image\n      if (speed < 0) {\n        scrollDist = speed * Math.max(contH, wndH);\n\n        if (wndH < contH) {\n          scrollDist -= speed * (contH - wndH);\n        }\n      } else {\n        scrollDist = speed * (contH + wndH);\n      }\n\n      // size for scroll parallax\n      if (speed > 1) {\n        resultH = Math.abs(scrollDist - wndH);\n      } else if (speed < 0) {\n        resultH = scrollDist / speed + Math.abs(scrollDist);\n      } else {\n        resultH += (wndH - contH) * (1 - speed);\n      }\n\n      scrollDist /= 2;\n    }\n\n    // store scroll distance\n    self.parallaxScrollDistance = scrollDist;\n\n    // vertical center\n    if (isScroll) {\n      resultMT = (wndH - resultH) / 2;\n    } else {\n      resultMT = (contH - resultH) / 2;\n    }\n\n    // apply result to item\n    self.css(self.image.$item, {\n      height: `${resultH}px`,\n      marginTop: `${resultMT}px`,\n      left: self.image.position === 'fixed' ? `${rect.left}px` : '0',\n      width: `${rect.width}px`,\n    });\n\n    // call onCoverImage event\n    if (self.options.onCoverImage) {\n      self.options.onCoverImage.call(self);\n    }\n\n    // return some useful data. Used in the video cover function\n    return {\n      image: {\n        height: resultH,\n        marginTop: resultMT,\n      },\n      container: rect,\n    };\n  }\n\n  isVisible() {\n    return this.isElementInViewport || false;\n  }\n\n  onScroll(force) {\n    const self = this;\n\n    // stop calculations if item is not in viewport\n    if (!force && !self.isVisible()) {\n      return;\n    }\n\n    const { height: wndH } = getWindowSize();\n    const rect = self.$item.getBoundingClientRect();\n    const contT = rect.top;\n    const contH = rect.height;\n    const styles = {};\n\n    // calculate parallax helping variables\n    const beforeTop = Math.max(0, contT);\n    const beforeTopEnd = Math.max(0, contH + contT);\n    const afterTop = Math.max(0, -contT);\n    const beforeBottom = Math.max(0, contT + contH - wndH);\n    const beforeBottomEnd = Math.max(0, contH - (contT + contH - wndH));\n    const afterBottom = Math.max(0, -contT + wndH - contH);\n    const fromViewportCenter = 1 - 2 * ((wndH - contT) / (wndH + contH));\n\n    // calculate on how percent of section is visible\n    let visiblePercent = 1;\n    if (contH < wndH) {\n      visiblePercent = 1 - (afterTop || beforeBottom) / contH;\n    } else if (beforeTopEnd <= wndH) {\n      visiblePercent = beforeTopEnd / wndH;\n    } else if (beforeBottomEnd <= wndH) {\n      visiblePercent = beforeBottomEnd / wndH;\n    }\n\n    // opacity\n    if (\n      self.options.type === 'opacity' ||\n      self.options.type === 'scale-opacity' ||\n      self.options.type === 'scroll-opacity'\n    ) {\n      styles.transform = 'translate3d(0,0,0)';\n      styles.opacity = visiblePercent;\n    }\n\n    // scale\n    if (self.options.type === 'scale' || self.options.type === 'scale-opacity') {\n      let scale = 1;\n      if (self.options.speed < 0) {\n        scale -= self.options.speed * visiblePercent;\n      } else {\n        scale += self.options.speed * (1 - visiblePercent);\n      }\n      styles.transform = `scale(${scale}) translate3d(0,0,0)`;\n    }\n\n    // scroll\n    if (self.options.type === 'scroll' || self.options.type === 'scroll-opacity') {\n      let positionY = self.parallaxScrollDistance * fromViewportCenter;\n\n      // fix if parallax block in absolute position\n      if (self.image.position === 'absolute') {\n        positionY -= contT;\n      }\n\n      styles.transform = `translate3d(0,${positionY}px,0)`;\n    }\n\n    self.css(self.image.$item, styles);\n\n    // call onScroll event\n    if (self.options.onScroll) {\n      self.options.onScroll.call(self, {\n        section: rect,\n\n        beforeTop,\n        beforeTopEnd,\n        afterTop,\n        beforeBottom,\n        beforeBottomEnd,\n        afterBottom,\n\n        visiblePercent,\n        fromViewportCenter,\n      });\n    }\n  }\n\n  onResize() {\n    this.coverImage();\n  }\n}\n\n// global definition\nconst jarallax = function (items, options, ...args) {\n  // check for dom element\n  // thanks: http://stackoverflow.com/questions/384286/javascript-isdom-how-do-you-check-if-a-javascript-object-is-a-dom-object\n  if (\n    typeof HTMLElement === 'object'\n      ? items instanceof HTMLElement\n      : items &&\n        typeof items === 'object' &&\n        items !== null &&\n        items.nodeType === 1 &&\n        typeof items.nodeName === 'string'\n  ) {\n    items = [items];\n  }\n\n  const len = items.length;\n  let k = 0;\n  let ret;\n\n  for (k; k < len; k += 1) {\n    if (typeof options === 'object' || typeof options === 'undefined') {\n      if (!items[k].jarallax) {\n        items[k].jarallax = new Jarallax(items[k], options);\n      }\n    } else if (items[k].jarallax) {\n      // eslint-disable-next-line prefer-spread\n      ret = items[k].jarallax[options].apply(items[k].jarallax, args);\n    }\n    if (typeof ret !== 'undefined') {\n      return ret;\n    }\n  }\n\n  return items;\n};\njarallax.constructor = Jarallax;\n\nexport default jarallax;\n", "/*!\n * Video Worker v2.2.0 (https://github.com/nk-o/video-worker)\n * Copyright 2024 nK <https://nkdev.info>\n * Licensed under MIT (https://github.com/nk-o/video-worker/blob/master/LICENSE)\n */\n\nvar defaults = {\n  autoplay: false,\n  loop: false,\n  mute: false,\n  volume: 100,\n  showControls: true,\n  accessibilityHidden: false,\n  // start / end video time in seconds\n  startTime: 0,\n  endTime: 0\n};\n\n/**\n * Extend like jQuery.extend\n *\n * @param {Object} out - output object.\n * @param {...any} args - additional objects to extend.\n *\n * @returns {Object}\n */\nfunction extend(out, ...args) {\n  out = out || {};\n  Object.keys(args).forEach(i => {\n    if (!args[i]) {\n      return;\n    }\n    Object.keys(args[i]).forEach(key => {\n      out[key] = args[i][key];\n    });\n  });\n  return out;\n}\n\nlet ID = 0;\nclass VideoWorkerBase {\n  type = 'none';\n  constructor(url, options) {\n    const self = this;\n    self.url = url;\n    self.options_default = {\n      ...defaults\n    };\n    self.options = extend({}, self.options_default, options);\n\n    // check URL\n    self.videoID = self.constructor.parseURL(url);\n\n    // init\n    if (self.videoID) {\n      self.init();\n    }\n  }\n  isValid() {\n    return !!this.videoID;\n  }\n  init() {\n    const self = this;\n    self.ID = ID;\n    ID += 1;\n    self.playerID = `VideoWorker-${self.ID}`;\n  }\n\n  // events\n  on(name, callback) {\n    this.userEventsList = this.userEventsList || [];\n\n    // add new callback in events list\n    (this.userEventsList[name] || (this.userEventsList[name] = [])).push(callback);\n  }\n  off(name, callback) {\n    if (!this.userEventsList || !this.userEventsList[name]) {\n      return;\n    }\n    if (!callback) {\n      delete this.userEventsList[name];\n    } else {\n      this.userEventsList[name].forEach((val, key) => {\n        if (val === callback) {\n          this.userEventsList[name][key] = false;\n        }\n      });\n    }\n  }\n  fire(name, ...args) {\n    if (this.userEventsList && typeof this.userEventsList[name] !== 'undefined') {\n      this.userEventsList[name].forEach(val => {\n        // call with all arguments\n        if (val) {\n          val.apply(this, args);\n        }\n      });\n    }\n  }\n\n  /**\n   * Methods used in providers.\n   */\n  /* eslint-disable */\n  static parseURL(url) {\n    return false;\n  }\n  play(start) {}\n  pause() {}\n  mute() {}\n  unmute() {}\n  setVolume(volume = false) {}\n  getVolume(callback) {}\n  getMuted(callback) {}\n  setCurrentTime(currentTime = false) {}\n  getCurrentTime(callback) {}\n  getImageURL(callback) {}\n  getVideo(callback) {}\n  /* eslint-enable */\n}\n\n/* eslint-disable import/no-mutable-exports */\n/* eslint-disable no-restricted-globals */\nlet win;\nif (typeof window !== 'undefined') {\n  win = window;\n} else if (typeof global !== 'undefined') {\n  win = global;\n} else if (typeof self !== 'undefined') {\n  win = self;\n} else {\n  win = {};\n}\nvar global$1 = win;\n\n// Deferred\n// thanks http://stackoverflow.com/questions/18096715/implement-deferred-object-without-using-jquery\nfunction Deferred() {\n  this.doneCallbacks = [];\n  this.failCallbacks = [];\n}\nDeferred.prototype = {\n  execute(list, args) {\n    let i = list.length;\n    // eslint-disable-next-line no-param-reassign\n    args = Array.prototype.slice.call(args);\n    while (i) {\n      i -= 1;\n      list[i].apply(null, args);\n    }\n  },\n  resolve(...args) {\n    this.execute(this.doneCallbacks, args);\n  },\n  reject(...args) {\n    this.execute(this.failCallbacks, args);\n  },\n  done(callback) {\n    this.doneCallbacks.push(callback);\n  },\n  fail(callback) {\n    this.failCallbacks.push(callback);\n  }\n};\n\nlet YoutubeAPIadded = 0;\nlet loadingYoutubePlayer = 0;\nconst loadingYoutubeDefer = /*#__PURE__*/new Deferred();\nfunction loadAPI$1() {\n  if (YoutubeAPIadded) {\n    return;\n  }\n  YoutubeAPIadded = true;\n  const src = 'https://www.youtube.com/iframe_api';\n\n  // add script in head section\n  let tag = document.createElement('script');\n  let head = document.getElementsByTagName('head')[0];\n  tag.src = src;\n  head.appendChild(tag);\n  head = null;\n  tag = null;\n}\nfunction onAPIready$1(callback) {\n  // Listen for global YT player callback\n  if ((typeof global$1.YT === 'undefined' || global$1.YT.loaded === 0) && !loadingYoutubePlayer) {\n    // Prevents Ready event from being called twice\n    loadingYoutubePlayer = 1;\n\n    // Creates deferred so, other players know when to wait.\n    global$1.onYouTubeIframeAPIReady = function () {\n      global$1.onYouTubeIframeAPIReady = null;\n      loadingYoutubeDefer.resolve('done');\n      callback();\n    };\n  } else if (typeof global$1.YT === 'object' && global$1.YT.loaded === 1) {\n    callback();\n  } else {\n    loadingYoutubeDefer.done(() => {\n      callback();\n    });\n  }\n}\nclass VideoWorkerYoutube extends VideoWorkerBase {\n  type = 'youtube';\n  static parseURL(url) {\n    // eslint-disable-next-line no-useless-escape\n    const regExp = /.*(?:youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|shorts\\/|watch\\?v=)([^#\\&\\?]*).*/;\n    const match = url.match(regExp);\n    return match && match[1].length === 11 ? match[1] : false;\n  }\n  init() {\n    super.init();\n    loadAPI$1();\n  }\n  play(start) {\n    const self = this;\n    if (!self.player || !self.player.playVideo) {\n      return;\n    }\n    if (typeof start !== 'undefined') {\n      self.player.seekTo(start || 0);\n    }\n    if (global$1.YT.PlayerState.PLAYING !== self.player.getPlayerState()) {\n      // Don't play if video is already ended and with no loop.\n      if (self.options.endTime && !self.options.loop) {\n        self.getCurrentTime(seconds => {\n          if (seconds < self.options.endTime) {\n            self.player.playVideo();\n          }\n        });\n      } else {\n        self.player.playVideo();\n      }\n    }\n  }\n  pause() {\n    const self = this;\n    if (!self.player || !self.player.pauseVideo) {\n      return;\n    }\n    if (global$1.YT.PlayerState.PLAYING === self.player.getPlayerState()) {\n      self.player.pauseVideo();\n    }\n  }\n  mute() {\n    const self = this;\n    if (!self.player || !self.player.mute) {\n      return;\n    }\n    self.player.mute();\n  }\n  unmute() {\n    const self = this;\n    if (!self.player || !self.player.unMute) {\n      return;\n    }\n    self.player.unMute();\n  }\n  setVolume(volume = false) {\n    const self = this;\n    if (!self.player || typeof volume !== 'number' || !self.player.setVolume) {\n      return;\n    }\n    self.player.setVolume(volume);\n  }\n  getVolume(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(false);\n      return;\n    }\n    if (self.player.getVolume) {\n      callback(self.player.getVolume());\n    }\n  }\n  getMuted(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(null);\n      return;\n    }\n    if (self.player.isMuted) {\n      callback(self.player.isMuted());\n    }\n  }\n  setCurrentTime(currentTime = false) {\n    const self = this;\n    if (!self.player || typeof currentTime !== 'number' || !self.player.seekTo) {\n      return;\n    }\n    self.player.seekTo(currentTime);\n  }\n  getCurrentTime(callback) {\n    const self = this;\n    if (!self.player || !self.player.getCurrentTime) {\n      return;\n    }\n    callback(self.player.getCurrentTime());\n  }\n  getImageURL(callback) {\n    const self = this;\n    if (self.videoImage) {\n      callback(self.videoImage);\n      return;\n    }\n    const availableSizes = ['maxresdefault', 'sddefault', 'hqdefault', '0'];\n    let step = 0;\n    const tempImg = new Image();\n    tempImg.onload = function () {\n      // if no thumbnail, youtube add their own image with width = 120px\n      if ((this.naturalWidth || this.width) !== 120 || step === availableSizes.length - 1) {\n        // ok\n        self.videoImage = `https://img.youtube.com/vi/${self.videoID}/${availableSizes[step]}.jpg`;\n        callback(self.videoImage);\n      } else {\n        // try another size\n        step += 1;\n        this.src = `https://img.youtube.com/vi/${self.videoID}/${availableSizes[step]}.jpg`;\n      }\n    };\n    tempImg.src = `https://img.youtube.com/vi/${self.videoID}/${availableSizes[step]}.jpg`;\n  }\n  getVideo(callback) {\n    const self = this;\n\n    // return generated video block\n    if (self.$video) {\n      callback(self.$video);\n      return;\n    }\n\n    // generate new video block\n    onAPIready$1(() => {\n      let hiddenDiv;\n      if (!self.$video) {\n        hiddenDiv = document.createElement('div');\n        hiddenDiv.style.display = 'none';\n      }\n      self.playerOptions = {\n        // GDPR Compliance.\n        host: 'https://www.youtube-nocookie.com',\n        videoId: self.videoID,\n        playerVars: {\n          autohide: 1,\n          rel: 0,\n          autoplay: 0,\n          // autoplay enable on mobile devices\n          playsinline: 1\n        }\n      };\n\n      // hide controls\n      if (!self.options.showControls) {\n        self.playerOptions.playerVars.iv_load_policy = 3;\n        self.playerOptions.playerVars.modestbranding = 1;\n        self.playerOptions.playerVars.controls = 0;\n        self.playerOptions.playerVars.showinfo = 0;\n        self.playerOptions.playerVars.disablekb = 1;\n      }\n\n      // events\n      let ytStarted;\n      let ytProgressInterval;\n      self.playerOptions.events = {\n        onReady(e) {\n          // mute\n          if (self.options.mute) {\n            e.target.mute();\n          } else if (typeof self.options.volume === 'number') {\n            e.target.setVolume(self.options.volume);\n          }\n\n          // autoplay\n          if (self.options.autoplay) {\n            self.play(self.options.startTime);\n          }\n          self.fire('ready', e);\n\n          // For seamless loops, set the endTime to 0.1 seconds less than the video's duration\n          // https://github.com/nk-o/video-worker/issues/2\n          if (self.options.loop && !self.options.endTime) {\n            const secondsOffset = 0.1;\n            self.options.endTime = self.player.getDuration() - secondsOffset;\n          }\n\n          // volumechange\n          setInterval(() => {\n            self.getVolume(volume => {\n              if (self.options.volume !== volume) {\n                self.options.volume = volume;\n                self.fire('volumechange', e);\n              }\n            });\n          }, 150);\n        },\n        onStateChange(e) {\n          // loop\n          if (self.options.loop && e.data === global$1.YT.PlayerState.ENDED) {\n            self.play(self.options.startTime);\n          }\n          if (!ytStarted && e.data === global$1.YT.PlayerState.PLAYING) {\n            ytStarted = 1;\n            self.fire('started', e);\n          }\n          if (e.data === global$1.YT.PlayerState.PLAYING) {\n            self.fire('play', e);\n          }\n          if (e.data === global$1.YT.PlayerState.PAUSED) {\n            self.fire('pause', e);\n          }\n          if (e.data === global$1.YT.PlayerState.ENDED) {\n            self.fire('ended', e);\n          }\n\n          // progress check\n          if (e.data === global$1.YT.PlayerState.PLAYING) {\n            ytProgressInterval = setInterval(() => {\n              self.fire('timeupdate', e);\n\n              // check for end of video and play again or stop\n              if (self.options.endTime && self.player.getCurrentTime() >= self.options.endTime) {\n                if (self.options.loop) {\n                  self.play(self.options.startTime);\n                } else {\n                  self.pause();\n                }\n              }\n            }, 150);\n          } else {\n            clearInterval(ytProgressInterval);\n          }\n        },\n        onError(e) {\n          self.fire('error', e);\n        }\n      };\n      const firstInit = !self.$video;\n      if (firstInit) {\n        const div = document.createElement('div');\n        div.setAttribute('id', self.playerID);\n        hiddenDiv.appendChild(div);\n        document.body.appendChild(hiddenDiv);\n      }\n      self.player = self.player || new global$1.YT.Player(self.playerID, self.playerOptions);\n      if (firstInit) {\n        self.$video = document.getElementById(self.playerID);\n\n        // add accessibility attributes\n        if (self.options.accessibilityHidden) {\n          self.$video.setAttribute('tabindex', '-1');\n          self.$video.setAttribute('aria-hidden', 'true');\n        }\n\n        // get video width and height\n        self.videoWidth = parseInt(self.$video.getAttribute('width'), 10) || 1280;\n        self.videoHeight = parseInt(self.$video.getAttribute('height'), 10) || 720;\n      }\n      callback(self.$video);\n    });\n  }\n}\n\nlet VimeoAPIadded = 0;\nlet loadingVimeoPlayer = 0;\nconst loadingVimeoDefer = /*#__PURE__*/new Deferred();\nfunction loadAPI() {\n  if (VimeoAPIadded) {\n    return;\n  }\n  VimeoAPIadded = true;\n\n  // Useful when Vimeo API added using RequireJS https://github.com/nk-o/video-worker/pull/7\n  if (typeof global$1.Vimeo !== 'undefined') {\n    return;\n  }\n  const src = 'https://player.vimeo.com/api/player.js';\n\n  // add script in head section\n  let tag = document.createElement('script');\n  let head = document.getElementsByTagName('head')[0];\n  tag.src = src;\n  head.appendChild(tag);\n  head = null;\n  tag = null;\n}\nfunction onAPIready(callback) {\n  if (typeof global$1.Vimeo === 'undefined' && !loadingVimeoPlayer) {\n    loadingVimeoPlayer = 1;\n    const vimeoInterval = setInterval(() => {\n      if (typeof global$1.Vimeo !== 'undefined') {\n        clearInterval(vimeoInterval);\n        loadingVimeoDefer.resolve('done');\n        callback();\n      }\n    }, 20);\n  } else if (typeof global$1.Vimeo !== 'undefined') {\n    callback();\n  } else {\n    loadingVimeoDefer.done(() => {\n      callback();\n    });\n  }\n}\nclass VideoWorkerVimeo extends VideoWorkerBase {\n  type = 'vimeo';\n  static parseURL(url) {\n    // eslint-disable-next-line no-useless-escape\n    const regExp = /https?:\\/\\/(?:www\\.|player\\.)?vimeo.com\\/(?:channels\\/(?:\\w+\\/)?|groups\\/([^/]*)\\/videos\\/|album\\/(\\d+)\\/video\\/|video\\/|)(\\d+)(?:$|\\/|\\?)/;\n    const match = url.match(regExp);\n    return match && match[3] ? match[3] : false;\n  }\n\n  // Try to extract a hash for private videos from the URL.\n  // Thanks to https://github.com/sampotts/plyr\n  static parseURLHash(url) {\n    /* This regex matches a hexadecimal hash if given in any of these forms:\n     *  - [https://player.]vimeo.com/video/{id}/{hash}[?params]\n     *  - [https://player.]vimeo.com/video/{id}?h={hash}[&params]\n     *  - [https://player.]vimeo.com/video/{id}?[params]&h={hash}\n     *  - video/{id}/{hash}\n     * If matched, the hash is available in capture group 4\n     */\n    const regex = /^.*(vimeo.com\\/|video\\/)(\\d+)(\\?.*&*h=|\\/)+([\\d,a-f]+)/;\n    const found = url.match(regex);\n    return found && found.length === 5 ? found[4] : null;\n  }\n  init() {\n    super.init();\n    loadAPI();\n  }\n  play(start) {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    if (typeof start !== 'undefined') {\n      self.player.setCurrentTime(start);\n    }\n    self.player.getPaused().then(paused => {\n      if (paused) {\n        // Don't play if video is already ended and with no loop.\n        if (self.options.endTime && !self.options.loop) {\n          self.getCurrentTime(seconds => {\n            if (seconds < self.options.endTime) {\n              self.player.play();\n            }\n          });\n        } else {\n          self.player.play();\n        }\n      }\n    });\n  }\n  pause() {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    self.player.getPaused().then(paused => {\n      if (!paused) {\n        self.player.pause();\n      }\n    });\n  }\n  mute() {\n    const self = this;\n    if (!self.player || !self.player.setVolume) {\n      return;\n    }\n    self.setVolume(0);\n  }\n  unmute() {\n    const self = this;\n    if (!self.player || !self.player.setVolume) {\n      return;\n    }\n\n    // In case the default volume is 0, we have to set 100 when unmute.\n    self.setVolume(self.options.volume || 100);\n  }\n  setVolume(volume = false) {\n    const self = this;\n    if (!self.player || typeof volume !== 'number' || !self.player.setVolume) {\n      return;\n    }\n    self.player.setVolume(volume / 100);\n  }\n  getVolume(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(false);\n      return;\n    }\n    if (self.player.getVolume) {\n      self.player.getVolume().then(volume => {\n        callback(volume * 100);\n      });\n    }\n  }\n  getMuted(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(null);\n      return;\n    }\n    if (self.player.getVolume) {\n      self.player.getVolume().then(volume => {\n        callback(!!volume);\n      });\n    }\n  }\n  setCurrentTime(currentTime = false) {\n    const self = this;\n    if (!self.player || typeof currentTime !== 'number' || !self.player.setCurrentTime) {\n      return;\n    }\n    self.player.setCurrentTime(currentTime);\n  }\n  getCurrentTime(callback) {\n    const self = this;\n    if (!self.player || !self.player.getCurrentTime) {\n      return;\n    }\n    self.player.getCurrentTime().then(currentTime => {\n      callback(currentTime);\n    });\n  }\n  getImageURL(callback) {\n    const self = this;\n    if (self.videoImage) {\n      callback(self.videoImage);\n      return;\n    }\n\n    // We should provide width to get HQ thumbnail URL.\n    let width = global$1.innerWidth || 1920;\n    if (global$1.devicePixelRatio) {\n      width *= global$1.devicePixelRatio;\n    }\n    width = Math.min(width, 1920);\n    let request = new XMLHttpRequest();\n    // https://vimeo.com/api/oembed.json?url=https://vimeo.com/235212527\n    request.open('GET', `https://vimeo.com/api/oembed.json?url=${self.url}&width=${width}`, true);\n    request.onreadystatechange = function () {\n      if (this.readyState === 4) {\n        if (this.status >= 200 && this.status < 400) {\n          // Success!\n          const response = JSON.parse(this.responseText);\n          if (response.thumbnail_url) {\n            self.videoImage = response.thumbnail_url;\n            callback(self.videoImage);\n          }\n        }\n      }\n    };\n    request.send();\n    request = null;\n  }\n  getVideo(callback) {\n    const self = this;\n\n    // return generated video block\n    if (self.$video) {\n      callback(self.$video);\n      return;\n    }\n\n    // generate new video block\n    onAPIready(() => {\n      let hiddenDiv;\n      if (!self.$video) {\n        hiddenDiv = document.createElement('div');\n        hiddenDiv.style.display = 'none';\n      }\n      self.playerOptions = {\n        // GDPR Compliance.\n        dnt: 1,\n        id: self.videoID,\n        autopause: 0,\n        transparent: 0,\n        autoplay: self.options.autoplay ? 1 : 0,\n        loop: self.options.loop ? 1 : 0,\n        muted: self.options.mute || self.options.volume === 0 ? 1 : 0\n      };\n\n      // private video hash\n      const urlHash = self.constructor.parseURLHash(self.url);\n      if (urlHash) {\n        self.playerOptions.h = urlHash;\n      }\n\n      // hide controls\n      if (!self.options.showControls) {\n        self.playerOptions.controls = 0;\n      }\n\n      // enable background option\n      if (!self.options.showControls && self.options.loop && self.options.autoplay) {\n        self.playerOptions.background = 1;\n      }\n      if (!self.$video) {\n        let playerOptionsString = '';\n        Object.keys(self.playerOptions).forEach(key => {\n          if (playerOptionsString !== '') {\n            playerOptionsString += '&';\n          }\n          playerOptionsString += `${key}=${encodeURIComponent(self.playerOptions[key])}`;\n        });\n\n        // we need to create iframe manually because when we create it using API\n        // js events won't triggers after iframe moved to another place\n        self.$video = document.createElement('iframe');\n        self.$video.setAttribute('id', self.playerID);\n        self.$video.setAttribute('src', `https://player.vimeo.com/video/${self.videoID}?${playerOptionsString}`);\n        self.$video.setAttribute('frameborder', '0');\n        self.$video.setAttribute('mozallowfullscreen', '');\n        self.$video.setAttribute('allowfullscreen', '');\n        self.$video.setAttribute('title', 'Vimeo video player');\n\n        // add accessibility attributes\n        if (self.options.accessibilityHidden) {\n          self.$video.setAttribute('tabindex', '-1');\n          self.$video.setAttribute('aria-hidden', 'true');\n        }\n        hiddenDiv.appendChild(self.$video);\n        document.body.appendChild(hiddenDiv);\n      }\n      self.player = self.player || new global$1.Vimeo.Player(self.$video, self.playerOptions);\n\n      // Since Vimeo removed the `volume` parameter, we have to set it manually.\n      if (!self.options.mute && typeof self.options.volume === 'number') {\n        self.setVolume(self.options.volume);\n      }\n\n      // set current time for autoplay\n      if (self.options.startTime && self.options.autoplay) {\n        self.player.setCurrentTime(self.options.startTime);\n      }\n\n      // get video width and height\n      self.player.getVideoWidth().then(width => {\n        self.videoWidth = width || 1280;\n      });\n      self.player.getVideoHeight().then(height => {\n        self.videoHeight = height || 720;\n      });\n\n      // events\n      let vmStarted;\n      self.player.on('timeupdate', e => {\n        if (!vmStarted) {\n          self.fire('started', e);\n          vmStarted = 1;\n        }\n        self.fire('timeupdate', e);\n\n        // check for end of video and play again or stop\n        if (self.options.endTime && e.seconds >= self.options.endTime) {\n          if (self.options.loop) {\n            self.play(self.options.startTime);\n          } else {\n            self.pause();\n          }\n        }\n      });\n      self.player.on('play', e => {\n        self.fire('play', e);\n\n        // check for the start time and start with it\n        if (self.options.startTime && e.seconds === 0) {\n          self.play(self.options.startTime);\n        }\n      });\n      self.player.on('pause', e => {\n        self.fire('pause', e);\n      });\n      self.player.on('ended', e => {\n        self.fire('ended', e);\n      });\n      self.player.on('loaded', e => {\n        self.fire('ready', e);\n      });\n      self.player.on('volumechange', e => {\n        self.getVolume(volume => {\n          self.options.volume = volume;\n        });\n        self.fire('volumechange', e);\n      });\n      self.player.on('error', e => {\n        self.fire('error', e);\n      });\n      callback(self.$video);\n    });\n  }\n}\n\nclass VideoWorkerLocal extends VideoWorkerBase {\n  type = 'local';\n  static parseURL(url) {\n    // eslint-disable-next-line no-useless-escape\n    const videoFormats = url.split(/,(?=mp4\\:|webm\\:|ogv\\:|ogg\\:)/);\n    const result = {};\n    let ready = 0;\n    videoFormats.forEach(val => {\n      // eslint-disable-next-line no-useless-escape\n      const match = val.match(/^(mp4|webm|ogv|ogg)\\:(.*)/);\n      if (match && match[1] && match[2]) {\n        // eslint-disable-next-line prefer-destructuring\n        result[match[1] === 'ogv' ? 'ogg' : match[1]] = match[2];\n        ready = 1;\n      }\n    });\n    return ready ? result : false;\n  }\n  play(start) {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    if (typeof start !== 'undefined') {\n      self.player.currentTime = start;\n    }\n    if (self.player.paused) {\n      // Don't play if video is already ended and with no loop.\n      if (self.options.endTime && !self.options.loop) {\n        self.getCurrentTime(seconds => {\n          if (seconds < self.options.endTime) {\n            self.player.play();\n          }\n        });\n      } else {\n        self.player.play();\n      }\n    }\n  }\n  pause() {\n    const self = this;\n    if (!self.player || self.player.paused) {\n      return;\n    }\n    self.player.pause();\n  }\n  mute() {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    self.$video.muted = true;\n  }\n  unmute() {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    self.$video.muted = false;\n  }\n  setVolume(volume = false) {\n    const self = this;\n    if (!self.player || typeof volume !== 'number') {\n      return;\n    }\n    self.$video.volume = volume / 100;\n  }\n  getVolume(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(false);\n      return;\n    }\n    callback(self.$video.volume * 100);\n  }\n  getMuted(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(null);\n      return;\n    }\n    callback(self.$video.muted);\n  }\n  setCurrentTime(currentTime = false) {\n    const self = this;\n    if (!self.player || typeof currentTime !== 'number') {\n      return;\n    }\n    self.$video.currentTime = currentTime;\n  }\n  getCurrentTime(callback) {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    callback(self.player.currentTime);\n  }\n  getImageURL(callback) {\n    const self = this;\n    if (self.videoImage) {\n      callback(self.videoImage);\n    }\n  }\n  getVideo(callback) {\n    const self = this;\n\n    // return generated video block\n    if (self.$video) {\n      callback(self.$video);\n      return;\n    }\n\n    // generate new video block\n    let hiddenDiv;\n    if (!self.$video) {\n      hiddenDiv = document.createElement('div');\n      hiddenDiv.style.display = 'none';\n    }\n    function addSourceElement(element, src, type) {\n      const source = document.createElement('source');\n      source.src = src;\n      source.type = type;\n      element.appendChild(source);\n    }\n    if (!self.$video) {\n      self.$video = document.createElement('video');\n      self.player = self.$video;\n\n      // show controls\n      if (self.options.showControls) {\n        self.$video.controls = true;\n      }\n\n      // set volume\n      if (typeof self.options.volume === 'number') {\n        self.setVolume(self.options.volume);\n      }\n\n      // mute (it is required to mute after the volume set)\n      if (self.options.mute) {\n        self.mute();\n      }\n\n      // loop\n      if (self.options.loop) {\n        self.$video.loop = true;\n      }\n\n      // autoplay enable on mobile devices\n      self.$video.setAttribute('playsinline', '');\n      self.$video.setAttribute('webkit-playsinline', '');\n\n      // add accessibility attributes\n      if (self.options.accessibilityHidden) {\n        self.$video.setAttribute('tabindex', '-1');\n        self.$video.setAttribute('aria-hidden', 'true');\n      }\n      self.$video.setAttribute('id', self.playerID);\n      hiddenDiv.appendChild(self.$video);\n      document.body.appendChild(hiddenDiv);\n      Object.keys(self.videoID).forEach(key => {\n        addSourceElement(self.$video, self.videoID[key], `video/${key}`);\n      });\n    }\n    let locStarted;\n    self.player.addEventListener('playing', e => {\n      if (!locStarted) {\n        self.fire('started', e);\n      }\n      locStarted = 1;\n    });\n    self.player.addEventListener('timeupdate', function (e) {\n      self.fire('timeupdate', e);\n\n      // check for end of video and play again or stop\n      if (self.options.endTime && this.currentTime >= self.options.endTime) {\n        if (self.options.loop) {\n          self.play(self.options.startTime);\n        } else {\n          self.pause();\n        }\n      }\n    });\n    self.player.addEventListener('play', e => {\n      self.fire('play', e);\n    });\n    self.player.addEventListener('pause', e => {\n      self.fire('pause', e);\n    });\n    self.player.addEventListener('ended', e => {\n      self.fire('ended', e);\n    });\n    self.player.addEventListener('loadedmetadata', function () {\n      // get video width and height\n      self.videoWidth = this.videoWidth || 1280;\n      self.videoHeight = this.videoHeight || 720;\n      self.fire('ready');\n\n      // autoplay\n      if (self.options.autoplay) {\n        self.play(self.options.startTime);\n      }\n    });\n    self.player.addEventListener('volumechange', e => {\n      self.getVolume(volume => {\n        self.options.volume = volume;\n      });\n      self.fire('volumechange', e);\n    });\n    self.player.addEventListener('error', e => {\n      self.fire('error', e);\n    });\n    callback(self.$video);\n  }\n}\n\nfunction VideoWorker(url, options) {\n  let result = false;\n  Object.keys(VideoWorker.providers).forEach(key => {\n    if (!result && VideoWorker.providers[key].parseURL(url)) {\n      result = new VideoWorker.providers[key](url, options);\n    }\n  });\n  return result || new VideoWorkerBase(url, options);\n}\nVideoWorker.BaseClass = VideoWorkerBase;\nVideoWorker.providers = {\n  Youtube: VideoWorkerYoutube,\n  Vimeo: VideoWorkerVimeo,\n  Local: VideoWorkerLocal\n};\n\nexport { VideoWorker as default };\n//# sourceMappingURL=video-worker.esm.js.map\n", "import VideoWorker from 'video-worker';\n\nimport global from './utils/global';\n\nfunction jarallaxVideo(jarallax = global.jarallax) {\n  if (typeof jarallax === 'undefined') {\n    return;\n  }\n\n  const Jarallax = jarallax.constructor;\n\n  // append video after when block will be visible.\n  const defOnScroll = Jarallax.prototype.onScroll;\n  Jarallax.prototype.onScroll = function () {\n    const self = this;\n\n    defOnScroll.apply(self);\n\n    const isReady =\n      !self.isVideoInserted &&\n      self.video &&\n      (!self.options.videoLazyLoading || self.isElementInViewport) &&\n      !self.options.disableVideo();\n\n    if (isReady) {\n      self.isVideoInserted = true;\n\n      self.video.getVideo((video) => {\n        const $parent = video.parentNode;\n        self.css(video, {\n          position: self.image.position,\n          top: '0px',\n          left: '0px',\n          right: '0px',\n          bottom: '0px',\n          width: '100%',\n          height: '100%',\n          maxWidth: 'none',\n          maxHeight: 'none',\n          pointerEvents: 'none',\n          transformStyle: 'preserve-3d',\n          backfaceVisibility: 'hidden',\n          margin: 0,\n          zIndex: -1,\n        });\n        self.$video = video;\n\n        // add Poster attribute to self-hosted video\n        if (self.video.type === 'local') {\n          if (self.image.src) {\n            self.$video.setAttribute('poster', self.image.src);\n          } else if (\n            self.image.$item &&\n            self.image.$item.tagName === 'IMG' &&\n            self.image.$item.src\n          ) {\n            self.$video.setAttribute('poster', self.image.$item.src);\n          }\n        }\n\n        // add classname to video element\n        if (self.options.videoClass) {\n          self.$video.setAttribute(\n            'class',\n            `${self.options.videoClass} ${self.options.videoClass}-${self.video.type}`\n          );\n        }\n\n        // insert video tag\n        self.image.$container.appendChild(video);\n\n        // remove parent video element (created by VideoWorker)\n        $parent.parentNode.removeChild($parent);\n\n        // call onVideoInsert event\n        if (self.options.onVideoInsert) {\n          self.options.onVideoInsert.call(self);\n        }\n      });\n    }\n  };\n\n  // cover video\n  const defCoverImage = Jarallax.prototype.coverImage;\n  Jarallax.prototype.coverImage = function () {\n    const self = this;\n    const imageData = defCoverImage.apply(self);\n    const node = self.image.$item ? self.image.$item.nodeName : false;\n\n    if (imageData && self.video && node && (node === 'IFRAME' || node === 'VIDEO')) {\n      let h = imageData.image.height;\n      let w = (h * self.image.width) / self.image.height;\n      let ml = (imageData.container.width - w) / 2;\n      let mt = imageData.image.marginTop;\n\n      if (imageData.container.width > w) {\n        w = imageData.container.width;\n        h = (w * self.image.height) / self.image.width;\n        ml = 0;\n        mt += (imageData.image.height - h) / 2;\n      }\n\n      // add video height over than need to hide controls\n      if (node === 'IFRAME') {\n        h += 400;\n        mt -= 200;\n      }\n\n      self.css(self.$video, {\n        width: `${w}px`,\n        marginLeft: `${ml}px`,\n        height: `${h}px`,\n        marginTop: `${mt}px`,\n      });\n    }\n\n    return imageData;\n  };\n\n  // init video\n  const defInitImg = Jarallax.prototype.initImg;\n  Jarallax.prototype.initImg = function () {\n    const self = this;\n    const defaultResult = defInitImg.apply(self);\n\n    if (!self.options.videoSrc) {\n      self.options.videoSrc = self.$item.getAttribute('data-jarallax-video') || null;\n    }\n\n    if (self.options.videoSrc) {\n      self.defaultInitImgResult = defaultResult;\n      return true;\n    }\n\n    return defaultResult;\n  };\n\n  const defCanInitParallax = Jarallax.prototype.canInitParallax;\n  Jarallax.prototype.canInitParallax = function () {\n    const self = this;\n    let defaultResult = defCanInitParallax.apply(self);\n\n    if (!self.options.videoSrc) {\n      return defaultResult;\n    }\n\n    // Init video api\n    const video = new VideoWorker(self.options.videoSrc, {\n      autoplay: true,\n      loop: self.options.videoLoop,\n      showControls: false,\n      accessibilityHidden: true,\n      startTime: self.options.videoStartTime || 0,\n      endTime: self.options.videoEndTime || 0,\n      mute: !self.options.videoVolume,\n      volume: self.options.videoVolume || 0,\n    });\n\n    // call onVideoWorkerInit event\n    if (self.options.onVideoWorkerInit) {\n      self.options.onVideoWorkerInit.call(self, video);\n    }\n\n    function resetDefaultImage() {\n      if (self.image.$default_item) {\n        self.image.$item = self.image.$default_item;\n        self.image.$item.style.display = 'block';\n\n        // set image width and height\n        self.coverImage();\n        self.onScroll();\n      }\n    }\n\n    if (video.isValid()) {\n      // Force enable parallax.\n      // When the parallax disabled on mobile devices, we still need to display videos.\n      // https://github.com/nk-o/jarallax/issues/159\n      if (this.options.disableParallax()) {\n        defaultResult = true;\n        self.image.position = 'absolute';\n        self.options.type = 'scroll';\n        self.options.speed = 1;\n      }\n\n      // if parallax will not be inited, we can add thumbnail on background.\n      if (!defaultResult) {\n        if (!self.defaultInitImgResult) {\n          video.getImageURL((url) => {\n            // save default user styles\n            const curStyle = self.$item.getAttribute('style');\n            if (curStyle) {\n              self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n            }\n\n            // set new background\n            self.css(self.$item, {\n              'background-image': `url(\"${url}\")`,\n              'background-position': 'center',\n              'background-size': 'cover',\n            });\n          });\n        }\n\n        // init video\n      } else {\n        video.on('ready', () => {\n          if (self.options.videoPlayOnlyVisible) {\n            const oldOnScroll = self.onScroll;\n            self.onScroll = function () {\n              oldOnScroll.apply(self);\n              if (\n                !self.videoError &&\n                (self.options.videoLoop || (!self.options.videoLoop && !self.videoEnded))\n              ) {\n                if (self.isVisible()) {\n                  video.play();\n                } else {\n                  video.pause();\n                }\n              }\n            };\n          } else {\n            video.play();\n          }\n        });\n        video.on('started', () => {\n          self.image.$default_item = self.image.$item;\n          self.image.$item = self.$video;\n\n          // set video width and height\n          self.image.width = self.video.videoWidth || 1280;\n          self.image.height = self.video.videoHeight || 720;\n          self.coverImage();\n          self.onScroll();\n\n          // hide image\n          if (self.image.$default_item) {\n            self.image.$default_item.style.display = 'none';\n          }\n        });\n\n        video.on('ended', () => {\n          self.videoEnded = true;\n\n          if (!self.options.videoLoop) {\n            // show default image if Loop disabled.\n            resetDefaultImage();\n          }\n        });\n        video.on('error', () => {\n          self.videoError = true;\n\n          // show default image if video loading error.\n          resetDefaultImage();\n        });\n\n        self.video = video;\n\n        // set image if not exists\n        if (!self.defaultInitImgResult) {\n          // set empty image on self-hosted video if not defined\n          self.image.src =\n            'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n\n          if (video.type !== 'local') {\n            video.getImageURL((url) => {\n              self.image.bgImage = `url(\"${url}\")`;\n              self.init();\n            });\n\n            return false;\n          }\n        }\n      }\n    }\n\n    return defaultResult;\n  };\n\n  // Destroy video parallax\n  const defDestroy = Jarallax.prototype.destroy;\n  Jarallax.prototype.destroy = function () {\n    const self = this;\n\n    if (self.image.$default_item) {\n      self.image.$item = self.image.$default_item;\n      delete self.image.$default_item;\n    }\n\n    defDestroy.apply(self);\n  };\n}\n\nexport default jarallaxVideo;\n", "import global from '../utils/global';\n\nfunction jarallaxElement(jarallax = global.jarallax) {\n  // eslint-disable-next-line no-console\n  console.warn(\n    \"Jarallax Element extension is DEPRECATED, please, avoid using it. We recommend you look at something like `lax.js` library <https://github.com/alexfoxy/lax.js>. It is much more powerful and has a less code (in cases when you don't want to add parallax backgrounds).\"\n  );\n\n  if (typeof jarallax === 'undefined') {\n    return;\n  }\n\n  const Jarallax = jarallax.constructor;\n\n  // redefine default methods\n  [\n    'initImg',\n    'canInitParallax',\n    'init',\n    'destroy',\n    'coverImage',\n    'isVisible',\n    'onScroll',\n    'onResize',\n  ].forEach((key) => {\n    const def = Jarallax.prototype[key];\n    Jarallax.prototype[key] = function (...args) {\n      const self = this;\n\n      if (key === 'initImg' && self.$item.getAttribute('data-jarallax-element') !== null) {\n        self.options.type = 'element';\n        self.pureOptions.speed = self.$item.getAttribute('data-jarallax-element') || '100';\n      }\n      if (self.options.type !== 'element') {\n        return def.apply(self, args);\n      }\n\n      self.pureOptions.threshold = self.$item.getAttribute('data-threshold') || '';\n\n      switch (key) {\n        case 'init': {\n          const speedArr = `${self.pureOptions.speed}`.split(' ');\n          self.options.speed = self.pureOptions.speed || 0;\n          self.options.speedY = speedArr[0] ? parseFloat(speedArr[0]) : 0;\n          self.options.speedX = speedArr[1] ? parseFloat(speedArr[1]) : 0;\n\n          const thresholdArr = self.pureOptions.threshold.split(' ');\n          self.options.thresholdY = thresholdArr[0] ? parseFloat(thresholdArr[0]) : null;\n          self.options.thresholdX = thresholdArr[1] ? parseFloat(thresholdArr[1]) : null;\n\n          def.apply(self, args);\n\n          // restore background image if available.\n          const originalStylesTag = self.$item.getAttribute('data-jarallax-original-styles');\n          if (originalStylesTag) {\n            self.$item.setAttribute('style', originalStylesTag);\n          }\n\n          return true;\n        }\n        case 'onResize': {\n          const defTransform = self.css(self.$item, 'transform');\n          self.css(self.$item, { transform: '' });\n          const rect = self.$item.getBoundingClientRect();\n          self.itemData = {\n            width: rect.width,\n            height: rect.height,\n            y: rect.top + self.getWindowData().y,\n            x: rect.left,\n          };\n          self.css(self.$item, { transform: defTransform });\n          break;\n        }\n        case 'onScroll': {\n          const wnd = self.getWindowData();\n          const centerPercent =\n            (wnd.y + wnd.height / 2 - self.itemData.y - self.itemData.height / 2) /\n            (wnd.height / 2);\n          const moveY = centerPercent * self.options.speedY;\n          const moveX = centerPercent * self.options.speedX;\n          let my = moveY;\n          let mx = moveX;\n          if (self.options.thresholdY !== null && moveY > self.options.thresholdY) my = 0;\n          if (self.options.thresholdX !== null && moveX > self.options.thresholdX) mx = 0;\n          self.css(self.$item, { transform: `translate3d(${mx}px,${my}px,0)` });\n          break;\n        }\n        case 'initImg':\n        case 'isVisible':\n        case 'coverImage':\n          return true;\n        // no default\n      }\n      return def.apply(self, args);\n    };\n  });\n}\n\nexport default jarallaxElement;\n", "import jarallaxLib from './core';\nimport jarallaxVideoExt from './ext-video';\nimport jarallaxElementExt from './deprecated/ext-element';\n\nexport const jarallax = jarallaxLib;\n\nexport const jarallaxVideo = function jarallaxVideo() {\n  return jarallaxVideoExt(jarallax);\n};\n\nexport const jarallaxElement = function jarallaxElement() {\n  return jarallaxElementExt(jarallax);\n};\n"], "mappings": ";;;;;AAAA,IAAA,aAAe;;EAEbA,MAAM;EACNC,OAAO;EACPC,gBAAgB;EAChBC,QAAQ;EACRC,YAAY;EACZC,SAAS;EACTC,aAAa;EACbC,WAAW;EACXC,SAAS;EACTC,mBAAmB;EACnBC,QAAQ;EACRC,iBAAiB;;EAGjBC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,cAAc;;EAGdC,YAAY;EACZC,UAAU;EACVC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC,WAAW;EACXC,sBAAsB;EACtBC,kBAAkB;EAClBC,cAAc;;EAGdC,eAAe;EACfC,mBAAmB;AACrB;ACjCA,IAAIC;AAEJ,IAAI,OAAOC,WAAW,aAAa;AACjCD,UAAMC;AACR,WAAW,OAAOC,WAAW,aAAa;AACxCF,UAAME;AACR,WAAW,OAAOC,SAAS,aAAa;AACtCH,UAAMG;AACR,OAAO;AACLH,UAAM,CAAA;AACR;AAEA,IAAA,WAAeA;ACJA,SAASI,IAAIC,IAAIC,QAAQ;AACtC,MAAI,OAAOA,WAAW,UAAU;AAC9B,WAAOJ,SAAOK,iBAAiBF,EAAE,EAAEG,iBAAiBF,MAAM;EAC5D;AAEAG,SAAOC,KAAKJ,MAAM,EAAEK,QAASC,SAAQ;AACnCP,OAAGQ,MAAMD,GAAG,IAAIN,OAAOM,GAAG;EAC5B,CAAC;AACD,SAAOP;AACT;ACXe,SAASS,SAAOC,QAAQC,MAAM;AAC3CD,QAAMA,OAAO,CAAA;AAEbN,SAAOC,KAAKM,IAAI,EAAEL,QAASM,OAAM;AAC/B,QAAI,CAACD,KAAKC,CAAC,GAAG;AACZ;IACF;AACAR,WAAOC,KAAKM,KAAKC,CAAC,CAAC,EAAEN,QAASC,SAAQ;AACpCG,UAAIH,GAAG,IAAII,KAAKC,CAAC,EAAEL,GAAG;IACxB,CAAC;EACH,CAAC;AAED,SAAOG;AACT;ACde,SAASG,WAAWC,MAAM;AACvC,QAAMC,UAAU,CAAA;AAEhB,SAAOD,KAAKE,kBAAkB,MAAM;AAClCF,WAAOA,KAAKE;AAEZ,QAAIF,KAAKG,aAAa,GAAG;AACvBF,cAAQG,KAAKJ,IAAI;IACnB;EACF;AAEA,SAAOC;AACT;ACfA,SAASI,MAAMC,UAAU;AACvB,MAAIC,SAASC,eAAe,cAAcD,SAASC,eAAe,eAAe;AAE/EF,aAAQ;EACV,OAAO;AACLC,aAASE,iBAAiB,oBAAoBH,UAAU;MACtDI,SAAS;MACTC,MAAM;MACNC,SAAS;IACX,CAAC;EACH;AACF;ACbA,IAAM;EAAEC,WAAAA;AAAU,IAAI9B;AAEtB,IAAM+B,cAA8E,iEAACC,KACnFF,YAAUG,SACZ;AAEe,SAASC,WAAW;AACjC,SAAOH;AACT;ACNA,IAAII;AACJ,IAAIC;AACJ,IAAIC;AAMJ,SAASC,kBAAkB;AACzB,MAAI,CAACD,iBAAiBb,SAASe,MAAM;AACnCF,oBAAgBb,SAASgB,cAAc,KAAK;AAC5CH,kBAAc1B,MAAM8B,UAClB;AACFjB,aAASe,KAAKG,YAAYL,aAAa;EACzC;AAEA,UACGA,gBAAgBA,cAAcM,eAAe,MAC9C3C,SAAO4C,eACPpB,SAASqB,gBAAgBF;AAE7B;AAEA,SAASG,qBAAqB;AAC5BX,SAAOnC,SAAO+C,cAAcvB,SAASqB,gBAAgBG;AAErD,MAAId,SAAQ,GAAI;AACdE,WAAOE,gBAAe;EACxB,OAAO;AACLF,WAAOpC,SAAO4C,eAAepB,SAASqB,gBAAgBF;EACxD;AACF;AAEAG,mBAAkB;AAClB9C,SAAO0B,iBAAiB,UAAUoB,kBAAkB;AACpD9C,SAAO0B,iBAAiB,qBAAqBoB,kBAAkB;AAC/D9C,SAAO0B,iBAAiB,QAAQoB,kBAAkB;AAClDG,MAAS,MAAM;AACbH,qBAEC;AACH,CAAC;AAEc,SAASI,gBAAgB;AACtC,SAAO;IACLC,OAAOhB;IACPiB,QAAQhB;;AAEZ;AC/CA,IAAMiB,eAAe,CAAA;AAErB,SAASC,iBAAiB;AACxB,MAAI,CAACD,aAAaE,QAAQ;AACxB;EACF;AAEA,QAAM;IAAEJ,OAAOhB;IAAMiB,QAAQhB;MAASc,cAAa;AAEnDG,eAAa5C,QAAQ,CAAC+C,MAAMC,MAAM;AAChC,UAAM;MAAEC;MAAUC;IAAQ,IAAIH;AAE9B,QAAI,CAACE,SAASE,UAAS,GAAI;AACzB;IACF;AAEA,UAAMC,aAAaH,SAASI,MAAMC,sBAAqB;AAEvD,UAAMC,UAAU;MACdb,OAAOU,WAAWV;MAClBC,QAAQS,WAAWT;MACnBa,KAAKJ,WAAWI;MAChBC,QAAQL,WAAWK;MACnB/B,MAAAA;MACAC,MAAAA;;AAGF,UAAM+B,YACJ,CAACR,WACDA,QAAQxB,SAAS6B,QAAQ7B,QACzBwB,QAAQvB,SAAS4B,QAAQ5B,QACzBuB,QAAQR,UAAUa,QAAQb,SAC1BQ,QAAQP,WAAWY,QAAQZ;AAC7B,UAAMgB,aACJD,aAAa,CAACR,WAAWA,QAAQM,QAAQD,QAAQC,OAAON,QAAQO,WAAWF,QAAQE;AAErFb,iBAAaI,CAAC,EAAEE,UAAUK;AAE1B,QAAIG,WAAW;AACbT,eAASW,SAAQ;IACnB;AACA,QAAID,YAAY;AACdV,eAAS3E,SAAQ;IACnB;EACF,CAAC;AAEDiB,WAAOsE,sBAAsBhB,cAAc;AAC7C;AAEA,IAAMiB,qBAAqB,IAAIvE,SAAOwE,qBACnCC,aAAY;AACXA,UAAQhE,QAASiE,WAAU;AACzBA,UAAMC,OAAOC,SAASC,sBAAsBH,MAAMI;EACpD,CAAC;AACH,GACA;;;EAGEC,YAAY;AACd,CACF;AAEO,SAASC,YAAYtB,UAAU;AACpCL,eAAahC,KAAK;IAChBqC;EACF,CAAC;AAED,MAAIL,aAAaE,WAAW,GAAG;AAC7BvD,aAAOsE,sBAAsBhB,cAAc;EAC7C;AAEAiB,qBAAmBU,QAAQvB,SAASwB,QAAQtG,qBAAqB8E,SAASI,KAAK;AACjF;AAEO,SAASqB,eAAezB,UAAU;AACvCL,eAAa5C,QAAQ,CAAC+C,MAAM9C,QAAQ;AAClC,QAAI8C,KAAKE,SAAS0B,eAAe1B,SAAS0B,YAAY;AACpD/B,mBAAagC,OAAO3E,KAAK,CAAC;IAC5B;EACF,CAAC;AAED6D,qBAAmBe,UAAU5B,SAASwB,QAAQtG,qBAAqB8E,SAASI,KAAK;AACnF;AC9EA,IAAM;EAAEhC;AAAU,IAAI9B;AAEtB,IAAIoF,aAAa;AAGjB,IAAMG,WAAN,MAAe;EACbC,YAAYC,MAAMC,aAAa;AAC7B,UAAMzF,QAAO;AAEbA,IAAAA,MAAKmF,aAAaA;AAClBA,kBAAc;AAEdnF,IAAAA,MAAK6D,QAAQ2B;AAEbxF,IAAAA,MAAK0F,WAAW;MAAE,GAAGA;;AAGrB,UAAMC,cAAc3F,MAAK6D,MAAM+B,WAAW,CAAA;AAC1C,UAAMC,kBAAkB,CAAA;AACxBvF,WAAOC,KAAKoF,WAAW,EAAEnF,QAASC,SAAQ;AACxC,YAAMqF,kBAAkBrF,IAAIsF,OAAO,GAAG,CAAC,EAAEC,YAAW,IAAKvF,IAAIsF,OAAO,CAAC;AACrE,UAAID,mBAAmB,OAAO9F,MAAK0F,SAASI,eAAe,MAAM,aAAa;AAC5ED,wBAAgBC,eAAe,IAAIH,YAAYlF,GAAG;MACpD;IACF,CAAC;AAEDT,IAAAA,MAAKiF,UAAUjF,MAAKW,OAAO,CAAA,GAAIX,MAAK0F,UAAUG,iBAAiBJ,WAAW;AAC1EzF,IAAAA,MAAKiG,cAAcjG,MAAKW,OAAO,CAAA,GAAIX,MAAKiF,OAAO;AAG/C3E,WAAOC,KAAKP,MAAKiF,OAAO,EAAEzE,QAASC,SAAQ;AACzC,UAAIT,MAAKiF,QAAQxE,GAAG,MAAM,QAAQ;AAChCT,QAAAA,MAAKiF,QAAQxE,GAAG,IAAI;iBACXT,MAAKiF,QAAQxE,GAAG,MAAM,SAAS;AACxCT,QAAAA,MAAKiF,QAAQxE,GAAG,IAAI;MACtB;IACF,CAAC;AAGDT,IAAAA,MAAKiF,QAAQ9G,QAAQ+H,KAAKC,IAAI,GAAGD,KAAKE,IAAI,IAAIC,WAAWrG,MAAKiF,QAAQ9G,KAAK,CAAC,CAAC;AAG7E,QAAI,OAAO6B,MAAKiF,QAAQpG,oBAAoB,UAAU;AACpDmB,MAAAA,MAAKiF,QAAQpG,kBAAkB,IAAIyH,OAAOtG,MAAKiF,QAAQpG,eAAe;IACxE;AACA,QAAImB,MAAKiF,QAAQpG,2BAA2ByH,QAAQ;AAClD,YAAMC,wBAAwBvG,MAAKiF,QAAQpG;AAC3CmB,MAAAA,MAAKiF,QAAQpG,kBAAkB,MAAM0H,sBAAsBxE,KAAKF,UAAUG,SAAS;IACrF;AACA,QAAI,OAAOhC,MAAKiF,QAAQpG,oBAAoB,YAAY;AAEtD,YAAM2H,yBAAyBxG,MAAKiF,QAAQpG;AAC5CmB,MAAAA,MAAKiF,QAAQpG,kBAAkB,MAAM2H,2BAA2B;IAClE;AAGA,QAAI,OAAOxG,MAAKiF,QAAQvF,iBAAiB,UAAU;AACjDM,MAAAA,MAAKiF,QAAQvF,eAAe,IAAI4G,OAAOtG,MAAKiF,QAAQvF,YAAY;IAClE;AACA,QAAIM,MAAKiF,QAAQvF,wBAAwB4G,QAAQ;AAC/C,YAAMG,qBAAqBzG,MAAKiF,QAAQvF;AACxCM,MAAAA,MAAKiF,QAAQvF,eAAe,MAAM+G,mBAAmB1E,KAAKF,UAAUG,SAAS;IAC/E;AACA,QAAI,OAAOhC,MAAKiF,QAAQvF,iBAAiB,YAAY;AAEnD,YAAMgH,sBAAsB1G,MAAKiF,QAAQvF;AACzCM,MAAAA,MAAKiF,QAAQvF,eAAe,MAAMgH,wBAAwB;IAC5D;AAGA,QAAIC,cAAc3G,MAAKiF,QAAQtG;AAE/B,QACEgI,eACA,OAAOA,gBAAgB,YACvB,OAAOA,YAAYrD,WAAW,aAC9B;AACA,OAACqD,WAAW,IAAIA;IAClB;AAEA,QAAI,EAAEA,uBAAuBC,UAAU;AACrCD,oBAAc;IAChB;AACA3G,IAAAA,MAAKiF,QAAQtG,oBAAoBgI;AAEjC3G,IAAAA,MAAK6G,QAAQ;MACXC,KAAK9G,MAAKiF,QAAQ5G,UAAU;MAC5B0I,YAAY;MACZC,WAAW;;;;MAKXC,UAAU;;AAGZ,QAAIjH,MAAKkH,QAAO,KAAMlH,MAAKmH,gBAAe,GAAI;AAC5CnH,MAAAA,MAAKoH,KAAI;IACX;EACF;EAEAnH,IAAIC,IAAIC,QAAQ;AACd,WAAOF,IAAIC,IAAIC,MAAM;EACvB;EAEAQ,OAAOC,QAAQC,MAAM;AACnB,WAAOF,SAAOC,KAAK,GAAGC,IAAI;EAC5B;;EAGAwG,gBAAgB;AACd,UAAM;MAAEnE;MAAOC;QAAWF,cAAa;AAEvC,WAAO;MACLC;MACAC;MACAmE,GAAG/F,SAASqB,gBAAgB2E;;EAEhC;;EAGAL,UAAU;AACR,UAAMlH,QAAO;AAGb,QAAIwH,cAAcxH,MAAKiF,QAAQ3G;AAC/B,QAAIkJ,eAAe,OAAOA,gBAAgB,UAAU;AAClDA,oBAAcxH,MAAK6D,MAAM4D,cAAcD,WAAW;IACpD;AAGA,QAAI,EAAEA,uBAAuBZ,UAAU;AACrC,UAAI5G,MAAKiF,QAAQ5G,QAAQ;AACvBmJ,sBAAc,IAAIE,MAAK;AACvBF,oBAAYV,MAAM9G,MAAKiF,QAAQ5G;MACjC,OAAO;AACLmJ,sBAAc;MAChB;IACF;AAEA,QAAIA,aAAa;AACf,UAAIxH,MAAKiF,QAAQvG,SAAS;AACxBsB,QAAAA,MAAK6G,MAAMhD,QAAQ2D,YAAYG,UAAU,IAAI;MAC/C,OAAO;AACL3H,QAAAA,MAAK6G,MAAMhD,QAAQ2D;AACnBxH,QAAAA,MAAK6G,MAAMe,cAAcJ,YAAYK;MACvC;AACA7H,MAAAA,MAAK6G,MAAMG,YAAY;IACzB;AAGA,QAAIhH,MAAK6G,MAAMhD,OAAO;AACpB,aAAO;IACT;AAGA,QAAI7D,MAAK6G,MAAMC,QAAQ,MAAM;AAC3B9G,MAAAA,MAAK6G,MAAMC,MACT;AACF9G,MAAAA,MAAK6G,MAAMiB,UAAU9H,MAAKC,IAAID,MAAK6D,OAAO,kBAAkB;IAC9D;AACA,WAAO,EAAE,CAAC7D,MAAK6G,MAAMiB,WAAW9H,MAAK6G,MAAMiB,YAAY;EACzD;EAEAX,kBAAkB;AAChB,WAAO,CAAC,KAAKlC,QAAQpG,gBAAe;EACtC;EAEAuI,OAAO;AACL,UAAMpH,QAAO;AACb,UAAM+H,kBAAkB;MACtBd,UAAU;MACVjD,KAAK;MACLgE,MAAM;MACN9E,OAAO;MACPC,QAAQ;MACR8E,UAAU;;AAEZ,QAAIC,cAAc;MAChBC,eAAe;MACfC,gBAAgB;MAChBC,oBAAoB;;AAGtB,QAAI,CAACrI,MAAKiF,QAAQvG,SAAS;AAEzB,YAAM4J,WAAWtI,MAAK6D,MAAM0E,aAAa,OAAO;AAChD,UAAID,UAAU;AACZtI,QAAAA,MAAK6D,MAAM2E,aAAa,iCAAiCF,QAAQ;MACnE;AACA,UAAItI,MAAK6G,MAAMG,WAAW;AACxB,cAAMyB,cAAczI,MAAK6G,MAAMhD,MAAM0E,aAAa,OAAO;AACzD,YAAIE,aAAa;AACfzI,UAAAA,MAAK6G,MAAMhD,MAAM2E,aAAa,iCAAiCC,WAAW;QAC5E;MACF;IACF;AAGA,QAAIzI,MAAKC,IAAID,MAAK6D,OAAO,UAAU,MAAM,UAAU;AACjD7D,MAAAA,MAAKC,IAAID,MAAK6D,OAAO;QACnBoD,UAAU;MACZ,CAAC;IACH;AACA,QAAIjH,MAAKC,IAAID,MAAK6D,OAAO,SAAS,MAAM,QAAQ;AAC9C7D,MAAAA,MAAKC,IAAID,MAAK6D,OAAO;QACnBjF,QAAQ;MACV,CAAC;IACH;AAGAoB,IAAAA,MAAK6G,MAAME,aAAaxF,SAASgB,cAAc,KAAK;AACpDvC,IAAAA,MAAKC,IAAID,MAAK6G,MAAME,YAAYgB,eAAe;AAC/C/H,IAAAA,MAAKC,IAAID,MAAK6G,MAAME,YAAY;MAC9B,WAAW/G,MAAKiF,QAAQrG;IAC1B,CAAC;AAKD,QAAI,KAAKiI,MAAMI,aAAa,SAAS;AACnCjH,MAAAA,MAAKC,IAAID,MAAK6G,MAAME,YAAY;QAC9B,qBAAqB;QACrB,aAAa;MACf,CAAC;IACH;AAGA/G,IAAAA,MAAK6G,MAAME,WAAWyB,aAAa,MAAO,sBAAqBxI,MAAKmF,UAAW,EAAC;AAGhF,QAAInF,MAAKiF,QAAQ7G,gBAAgB;AAC/B4B,MAAAA,MAAK6G,MAAME,WAAWyB,aAAa,SAASxI,MAAKiF,QAAQ7G,cAAc;IACzE;AAEA4B,IAAAA,MAAK6D,MAAMpB,YAAYzC,MAAK6G,MAAME,UAAU;AAG5C,QAAI/G,MAAK6G,MAAMG,WAAW;AACxBkB,oBAAclI,MAAKW,OACjB;QACE,cAAcX,MAAKiF,QAAQ1G;QAC3B,mBAAmByB,MAAKiF,QAAQzG;QAChC,aAAa;MACf,GACAuJ,iBACAG,WACF;IAGF,OAAO;AACLlI,MAAAA,MAAK6G,MAAMhD,QAAQtC,SAASgB,cAAc,KAAK;AAC/C,UAAIvC,MAAK6G,MAAMC,KAAK;AAClBoB,sBAAclI,MAAKW,OACjB;UACE,uBAAuBX,MAAKiF,QAAQzG;UACpC,mBAAmBwB,MAAKiF,QAAQ1G;UAChC,qBAAqByB,MAAKiF,QAAQxG;UAClC,oBAAoBuB,MAAK6G,MAAMiB,WAAY,QAAO9H,MAAK6G,MAAMC,GAAI;QACnE,GACAiB,iBACAG,WACF;MACF;IACF;AAEA,QACElI,MAAKiF,QAAQ/G,SAAS,aACtB8B,MAAKiF,QAAQ/G,SAAS,WACtB8B,MAAKiF,QAAQ/G,SAAS,mBACtB8B,MAAKiF,QAAQ9G,UAAU,GACvB;AACA6B,MAAAA,MAAK6G,MAAMI,WAAW;IACxB;AAKA,QAAIjH,MAAK6G,MAAMI,aAAa,SAAS;AACnC,YAAMyB,WAAW3H,WAAWf,MAAK6D,KAAK,EAAE8E,OAAQzI,QAAO;AACrD,cAAMC,SAASJ,SAAOK,iBAAiBF,EAAE;AACzC,cAAM0I,kBACJzI,OAAO,mBAAmB,KAAKA,OAAO,gBAAgB,KAAKA,OAAO0I;AACpE,cAAMC,gBAAgB;AAEtB,eACGF,mBAAmBA,oBAAoB,UACxCE,cAAc/G,KAAK5B,OAAO8H,WAAW9H,OAAO,YAAY,IAAIA,OAAO,YAAY,CAAC;MAEpF,CAAC;AAEDH,MAAAA,MAAK6G,MAAMI,WAAWyB,SAASpF,SAAS,aAAa;IACvD;AAGA4E,gBAAYjB,WAAWjH,MAAK6G,MAAMI;AAGlCjH,IAAAA,MAAKC,IAAID,MAAK6G,MAAMhD,OAAOqE,WAAW;AACtClI,IAAAA,MAAK6G,MAAME,WAAWtE,YAAYzC,MAAK6G,MAAMhD,KAAK;AAGlD7D,IAAAA,MAAKoE,SAAQ;AACbpE,IAAAA,MAAKlB,SAAS,IAAI;AAGlB,QAAIkB,MAAKiF,QAAQlG,QAAQ;AACvBiB,MAAAA,MAAKiF,QAAQlG,OAAOgK,KAAK/I,KAAI;IAC/B;AAGA,QAAIA,MAAKC,IAAID,MAAK6D,OAAO,kBAAkB,MAAM,QAAQ;AACvD7D,MAAAA,MAAKC,IAAID,MAAK6D,OAAO;QACnB,oBAAoB;MACtB,CAAC;IACH;AAEAkB,gBAAY/E,KAAI;EAClB;EAEAgJ,UAAU;AACR,UAAMhJ,QAAO;AAEbkF,mBAAelF,KAAI;AAGnB,UAAMiJ,oBAAoBjJ,MAAK6D,MAAM0E,aAAa,+BAA+B;AACjFvI,IAAAA,MAAK6D,MAAMqF,gBAAgB,+BAA+B;AAE1D,QAAI,CAACD,mBAAmB;AACtBjJ,MAAAA,MAAK6D,MAAMqF,gBAAgB,OAAO;IACpC,OAAO;AACLlJ,MAAAA,MAAK6D,MAAM2E,aAAa,SAASS,iBAAiB;IACpD;AAEA,QAAIjJ,MAAK6G,MAAMG,WAAW;AAExB,YAAMmC,uBAAuBnJ,MAAK6G,MAAMhD,MAAM0E,aAAa,+BAA+B;AAC1FvI,MAAAA,MAAK6G,MAAMhD,MAAMqF,gBAAgB,+BAA+B;AAEhE,UAAI,CAACC,sBAAsB;AACzBnJ,QAAAA,MAAK6G,MAAMhD,MAAMqF,gBAAgB,OAAO;MAC1C,OAAO;AACLlJ,QAAAA,MAAK6G,MAAMhD,MAAM2E,aAAa,SAASS,iBAAiB;MAC1D;AAGA,UAAIjJ,MAAK6G,MAAMe,aAAa;AAC1B5H,QAAAA,MAAK6G,MAAMe,YAAYnF,YAAYzC,MAAK6G,MAAMhD,KAAK;MACrD;IACF;AAGA,QAAI7D,MAAK6G,MAAME,YAAY;AACzB/G,MAAAA,MAAK6G,MAAME,WAAWc,WAAWuB,YAAYpJ,MAAK6G,MAAME,UAAU;IACpE;AAGA,QAAI/G,MAAKiF,QAAQjG,WAAW;AAC1BgB,MAAAA,MAAKiF,QAAQjG,UAAU+J,KAAK/I,KAAI;IAClC;AAGA,WAAOA,MAAK6D,MAAMc;EACpB;EAEA0E,aAAa;AACX,UAAMrJ,QAAO;AAEb,UAAM;MAAEmD,QAAQhB;QAASc,cAAa;AACtC,UAAMqG,OAAOtJ,MAAK6G,MAAME,WAAWjD,sBAAqB;AACxD,UAAMyF,QAAQD,KAAKnG;AACnB,UAAM;MAAEhF;QAAU6B,MAAKiF;AACvB,UAAMuE,WAAWxJ,MAAKiF,QAAQ/G,SAAS,YAAY8B,MAAKiF,QAAQ/G,SAAS;AACzE,QAAIuL,aAAa;AACjB,QAAIC,UAAUH;AACd,QAAII,WAAW;AAGf,QAAIH,UAAU;AAEZ,UAAIrL,QAAQ,GAAG;AACbsL,qBAAatL,QAAQ+H,KAAKE,IAAImD,OAAOpH,KAAI;AAEzC,YAAIA,QAAOoH,OAAO;AAChBE,wBAActL,SAASoL,QAAQpH;QACjC;MACF,OAAO;AACLsH,qBAAatL,SAASoL,QAAQpH;MAChC;AAGA,UAAIhE,QAAQ,GAAG;AACbuL,kBAAUxD,KAAK0D,IAAIH,aAAatH,KAAI;MACtC,WAAWhE,QAAQ,GAAG;AACpBuL,kBAAUD,aAAatL,QAAQ+H,KAAK0D,IAAIH,UAAU;MACpD,OAAO;AACLC,oBAAYvH,QAAOoH,UAAU,IAAIpL;MACnC;AAEAsL,oBAAc;IAChB;AAGAzJ,IAAAA,MAAK6J,yBAAyBJ;AAG9B,QAAID,UAAU;AACZG,kBAAYxH,QAAOuH,WAAW;IAChC,OAAO;AACLC,kBAAYJ,QAAQG,WAAW;IACjC;AAGA1J,IAAAA,MAAKC,IAAID,MAAK6G,MAAMhD,OAAO;MACzBV,QAAS,GAAEuG,OAAQ;MACnBI,WAAY,GAAEH,QAAS;MACvB3B,MAAMhI,MAAK6G,MAAMI,aAAa,UAAW,GAAEqC,KAAKtB,IAAK,OAAM;MAC3D9E,OAAQ,GAAEoG,KAAKpG,KAAM;IACvB,CAAC;AAGD,QAAIlD,MAAKiF,QAAQhG,cAAc;AAC7Be,MAAAA,MAAKiF,QAAQhG,aAAa8J,KAAK/I,KAAI;IACrC;AAGA,WAAO;MACL6G,OAAO;QACL1D,QAAQuG;QACRI,WAAWH;;MAEbI,WAAWT;;EAEf;EAEA3F,YAAY;AACV,WAAO,KAAKiB,uBAAuB;EACrC;EAEA9F,SAASkL,OAAO;AACd,UAAMhK,QAAO;AAGb,QAAI,CAACgK,SAAS,CAAChK,MAAK2D,UAAS,GAAI;AAC/B;IACF;AAEA,UAAM;MAAER,QAAQhB;QAASc,cAAa;AACtC,UAAMqG,OAAOtJ,MAAK6D,MAAMC,sBAAqB;AAC7C,UAAMmG,QAAQX,KAAKtF;AACnB,UAAMuF,QAAQD,KAAKnG;AACnB,UAAMhD,SAAS,CAAA;AAGf,UAAM+J,YAAYhE,KAAKE,IAAI,GAAG6D,KAAK;AACnC,UAAME,eAAejE,KAAKE,IAAI,GAAGmD,QAAQU,KAAK;AAC9C,UAAMG,WAAWlE,KAAKE,IAAI,GAAG,CAAC6D,KAAK;AACnC,UAAMI,eAAenE,KAAKE,IAAI,GAAG6D,QAAQV,QAAQpH,KAAI;AACrD,UAAMmI,kBAAkBpE,KAAKE,IAAI,GAAGmD,SAASU,QAAQV,QAAQpH,MAAK;AAClE,UAAMoI,cAAcrE,KAAKE,IAAI,GAAG,CAAC6D,QAAQ9H,QAAOoH,KAAK;AACrD,UAAMiB,qBAAqB,IAAI,MAAMrI,QAAO8H,UAAU9H,QAAOoH;AAG7D,QAAIkB,iBAAiB;AACrB,QAAIlB,QAAQpH,OAAM;AAChBsI,uBAAiB,KAAKL,YAAYC,gBAAgBd;IACpD,WAAWY,gBAAgBhI,OAAM;AAC/BsI,uBAAiBN,eAAehI;IAClC,WAAWmI,mBAAmBnI,OAAM;AAClCsI,uBAAiBH,kBAAkBnI;IACrC;AAGA,QACEnC,MAAKiF,QAAQ/G,SAAS,aACtB8B,MAAKiF,QAAQ/G,SAAS,mBACtB8B,MAAKiF,QAAQ/G,SAAS,kBACtB;AACAiC,aAAO0I,YAAY;AACnB1I,aAAOuK,UAAUD;IACnB;AAGA,QAAIzK,MAAKiF,QAAQ/G,SAAS,WAAW8B,MAAKiF,QAAQ/G,SAAS,iBAAiB;AAC1E,UAAIyM,QAAQ;AACZ,UAAI3K,MAAKiF,QAAQ9G,QAAQ,GAAG;AAC1BwM,iBAAS3K,MAAKiF,QAAQ9G,QAAQsM;MAChC,OAAO;AACLE,iBAAS3K,MAAKiF,QAAQ9G,SAAS,IAAIsM;MACrC;AACAtK,aAAO0I,YAAa,SAAQ8B,KAAM;IACpC;AAGA,QAAI3K,MAAKiF,QAAQ/G,SAAS,YAAY8B,MAAKiF,QAAQ/G,SAAS,kBAAkB;AAC5E,UAAI0M,YAAY5K,MAAK6J,yBAAyBW;AAG9C,UAAIxK,MAAK6G,MAAMI,aAAa,YAAY;AACtC2D,qBAAaX;MACf;AAEA9J,aAAO0I,YAAa,iBAAgB+B,SAAU;IAChD;AAEA5K,IAAAA,MAAKC,IAAID,MAAK6G,MAAMhD,OAAO1D,MAAM;AAGjC,QAAIH,MAAKiF,QAAQnG,UAAU;AACzBkB,MAAAA,MAAKiF,QAAQnG,SAASiK,KAAK/I,OAAM;QAC/B6K,SAASvB;QAETY;QACAC;QACAC;QACAC;QACAC;QACAC;QAEAE;QACAD;MACF,CAAC;IACH;EACF;EAEApG,WAAW;AACT,SAAKiF,WAAU;EACjB;AACF;AAGA,IAAM1E,aAAW,SAAUmG,OAAO7F,YAAYpE,MAAM;AAGlD,MACE,OAAOkK,gBAAgB,WACnBD,iBAAiBC,cACjBD,SACA,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAM3J,aAAa,KACnB,OAAO2J,MAAME,aAAa,UAC9B;AACAF,YAAQ,CAACA,KAAK;EAChB;AAEA,QAAMG,MAAMH,MAAMxH;AAClB,MAAIE,IAAI;AACR,MAAI0H;AAEJ,OAAK1H,GAAGA,IAAIyH,KAAKzH,KAAK,GAAG;AACvB,QAAI,OAAOyB,YAAY,YAAY,OAAOA,YAAY,aAAa;AACjE,UAAI,CAAC6F,MAAMtH,CAAC,EAAEmB,UAAU;AACtBmG,cAAMtH,CAAC,EAAEmB,WAAW,IAAIW,SAASwF,MAAMtH,CAAC,GAAGyB,OAAO;MACpD;eACS6F,MAAMtH,CAAC,EAAEmB,UAAU;AAE5BuG,YAAMJ,MAAMtH,CAAC,EAAEmB,SAASM,OAAO,EAAEkG,MAAML,MAAMtH,CAAC,EAAEmB,UAAU9D,IAAI;IAChE;AACA,QAAI,OAAOqK,QAAQ,aAAa;AAC9B,aAAOA;IACT;EACF;AAEA,SAAOJ;AACT;AACAnG,WAASY,cAAcD;AChkBvB,IAAe,WAAA;EACb8F,UAAU;EACVC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,cAAc;EACdC,qBAAqB;;EAGrBC,WAAW;EACXC,SAAS;AACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPA,SAASC,gBAAcjH,YAAW5E,SAAO4E,UAAU;AACjD,MAAI,OAAOA,cAAa,aAAa;AACnC;EACF;AAEA,QAAMW,YAAWX,UAASY;AAG1B,QAAMsG,cAAcvG,UAASwG,UAAUhN;AACvCwG,EAAAA,UAASwG,UAAUhN,WAAW,WAAY;AACxC,UAAMkB,QAAO;AAEb6L,gBAAYV,MAAMnL,KAAI;AAEtB,UAAM+L,UACJ,CAAC/L,MAAKgM,mBACNhM,MAAKiM,UACJ,CAACjM,MAAKiF,QAAQxF,oBAAoBO,MAAK4E,wBACxC,CAAC5E,MAAKiF,QAAQvF,aAAY;AAE5B,QAAIqM,SAAS;AACX/L,MAAAA,MAAKgM,kBAAkB;AAEvBhM,MAAAA,MAAKiM,MAAMC,SAAUD,WAAU;AAC7B,cAAME,UAAUF,MAAMpE;AACtB7H,QAAAA,MAAKC,IAAIgM,OAAO;UACdhF,UAAUjH,MAAK6G,MAAMI;UACrBjD,KAAK;UACLgE,MAAM;UACNoE,OAAO;UACPnI,QAAQ;UACRf,OAAO;UACPC,QAAQ;UACRkJ,UAAU;UACVC,WAAW;UACXnE,eAAe;UACfC,gBAAgB;UAChBC,oBAAoB;UACpBkE,QAAQ;UACR3N,QAAQ;QACV,CAAC;AACDoB,QAAAA,MAAKwM,SAASP;AAGd,YAAIjM,MAAKiM,MAAM/N,SAAS,SAAS;AAC/B,cAAI8B,MAAK6G,MAAMC,KAAK;AAClB9G,YAAAA,MAAKwM,OAAOhE,aAAa,UAAUxI,MAAK6G,MAAMC,GAAG;qBAEjD9G,MAAK6G,MAAMhD,SACX7D,MAAK6G,MAAMhD,MAAM4I,YAAY,SAC7BzM,MAAK6G,MAAMhD,MAAMiD,KACjB;AACA9G,YAAAA,MAAKwM,OAAOhE,aAAa,UAAUxI,MAAK6G,MAAMhD,MAAMiD,GAAG;UACzD;QACF;AAGA,YAAI9G,MAAKiF,QAAQ/F,YAAY;AAC3Bc,UAAAA,MAAKwM,OAAOhE,aACV,SACC,GAAExI,MAAKiF,QAAQ/F,UAAW,IAAGc,MAAKiF,QAAQ/F,UAAW,IAAGc,MAAKiM,MAAM/N,IAAK,EAC3E;QACF;AAGA8B,QAAAA,MAAK6G,MAAME,WAAWtE,YAAYwJ,KAAK;AAGvCE,gBAAQtE,WAAWuB,YAAY+C,OAAO;AAGtC,YAAInM,MAAKiF,QAAQtF,eAAe;AAC9BK,UAAAA,MAAKiF,QAAQtF,cAAcoJ,KAAK/I,KAAI;QACtC;MACF,CAAC;IACH;;AAIF,QAAM0M,gBAAgBpH,UAASwG,UAAUzC;AACzC/D,EAAAA,UAASwG,UAAUzC,aAAa,WAAY;AAC1C,UAAMrJ,QAAO;AACb,UAAM2M,YAAYD,cAAcvB,MAAMnL,KAAI;AAC1C,UAAM4M,OAAO5M,MAAK6G,MAAMhD,QAAQ7D,MAAK6G,MAAMhD,MAAMmH,WAAW;AAE5D,QAAI2B,aAAa3M,MAAKiM,SAASW,SAASA,SAAS,YAAYA,SAAS,UAAU;AAC9E,UAAIC,IAAIF,UAAU9F,MAAM1D;AACxB,UAAI2J,IAAKD,IAAI7M,MAAK6G,MAAM3D,QAASlD,MAAK6G,MAAM1D;AAC5C,UAAI4J,MAAMJ,UAAU5C,UAAU7G,QAAQ4J,KAAK;AAC3C,UAAIE,KAAKL,UAAU9F,MAAMiD;AAEzB,UAAI6C,UAAU5C,UAAU7G,QAAQ4J,GAAG;AACjCA,YAAIH,UAAU5C,UAAU7G;AACxB2J,YAAKC,IAAI9M,MAAK6G,MAAM1D,SAAUnD,MAAK6G,MAAM3D;AACzC6J,aAAK;AACLC,eAAOL,UAAU9F,MAAM1D,SAAS0J,KAAK;MACvC;AAGA,UAAID,SAAS,UAAU;AACrBC,aAAK;AACLG,cAAM;MACR;AAEAhN,MAAAA,MAAKC,IAAID,MAAKwM,QAAQ;QACpBtJ,OAAQ,GAAE4J,CAAE;QACZG,YAAa,GAAEF,EAAG;QAClB5J,QAAS,GAAE0J,CAAE;QACb/C,WAAY,GAAEkD,EAAG;MACnB,CAAC;IACH;AAEA,WAAOL;;AAIT,QAAMO,aAAa5H,UAASwG,UAAU5E;AACtC5B,EAAAA,UAASwG,UAAU5E,UAAU,WAAY;AACvC,UAAMlH,QAAO;AACb,UAAMmN,gBAAgBD,WAAW/B,MAAMnL,KAAI;AAE3C,QAAI,CAACA,MAAKiF,QAAQ9F,UAAU;AAC1Ba,MAAAA,MAAKiF,QAAQ9F,WAAWa,MAAK6D,MAAM0E,aAAa,qBAAqB,KAAK;IAC5E;AAEA,QAAIvI,MAAKiF,QAAQ9F,UAAU;AACzBa,MAAAA,MAAKoN,uBAAuBD;AAC5B,aAAO;IACT;AAEA,WAAOA;;AAGT,QAAME,qBAAqB/H,UAASwG,UAAU3E;AAC9C7B,EAAAA,UAASwG,UAAU3E,kBAAkB,WAAY;AAC/C,UAAMnH,QAAO;AACb,QAAImN,gBAAgBE,mBAAmBlC,MAAMnL,KAAI;AAEjD,QAAI,CAACA,MAAKiF,QAAQ9F,UAAU;AAC1B,aAAOgO;IACT;AAGA,UAAMlB,QAAQ,IAAIqB,YAAYtN,MAAKiF,QAAQ9F,UAAU;MACnDiM,UAAU;MACVC,MAAMrL,MAAKiF,QAAQ1F;MACnBiM,cAAc;MACdC,qBAAqB;MACrBC,WAAW1L,MAAKiF,QAAQ7F,kBAAkB;MAC1CuM,SAAS3L,MAAKiF,QAAQ5F,gBAAgB;MACtCiM,MAAM,CAACtL,MAAKiF,QAAQ3F;MACpBiM,QAAQvL,MAAKiF,QAAQ3F,eAAe;IACtC,CAAC;AAGD,QAAIU,MAAKiF,QAAQrF,mBAAmB;AAClCI,MAAAA,MAAKiF,QAAQrF,kBAAkBmJ,KAAK/I,OAAMiM,KAAK;IACjD;AAEA,aAASsB,oBAAoB;AAC3B,UAAIvN,MAAK6G,MAAM2G,eAAe;AAC5BxN,QAAAA,MAAK6G,MAAMhD,QAAQ7D,MAAK6G,MAAM2G;AAC9BxN,QAAAA,MAAK6G,MAAMhD,MAAMnD,MAAM+M,UAAU;AAGjCzN,QAAAA,MAAKqJ,WAAU;AACfrJ,QAAAA,MAAKlB,SAAQ;MACf;IACF;AAEA,QAAImN,MAAMyB,QAAO,GAAI;AAInB,UAAI,KAAKzI,QAAQpG,gBAAe,GAAI;AAClCsO,wBAAgB;AAChBnN,QAAAA,MAAK6G,MAAMI,WAAW;AACtBjH,QAAAA,MAAKiF,QAAQ/G,OAAO;AACpB8B,QAAAA,MAAKiF,QAAQ9G,QAAQ;MACvB;AAGA,UAAI,CAACgP,eAAe;AAClB,YAAI,CAACnN,MAAKoN,sBAAsB;AAC9BnB,gBAAM0B,YAAaC,SAAQ;AAEzB,kBAAMtF,WAAWtI,MAAK6D,MAAM0E,aAAa,OAAO;AAChD,gBAAID,UAAU;AACZtI,cAAAA,MAAK6D,MAAM2E,aAAa,iCAAiCF,QAAQ;YACnE;AAGAtI,YAAAA,MAAKC,IAAID,MAAK6D,OAAO;cACnB,oBAAqB,QAAO+J,GAAI;cAChC,uBAAuB;cACvB,mBAAmB;YACrB,CAAC;UACH,CAAC;QACH;MAGF,OAAO;AACL3B,cAAM4B,GAAG,SAAS,MAAM;AACtB,cAAI7N,MAAKiF,QAAQzF,sBAAsB;AACrC,kBAAMsO,cAAc9N,MAAKlB;AACzBkB,YAAAA,MAAKlB,WAAW,WAAY;AAC1BgP,0BAAY3C,MAAMnL,KAAI;AACtB,kBACE,CAACA,MAAK+N,eACL/N,MAAKiF,QAAQ1F,aAAc,CAACS,MAAKiF,QAAQ1F,aAAa,CAACS,MAAKgO,aAC7D;AACA,oBAAIhO,MAAK2D,UAAS,GAAI;AACpBsI,wBAAMgC,KAAI;gBACZ,OAAO;AACLhC,wBAAMiC,MAAK;gBACb;cACF;;UAEJ,OAAO;AACLjC,kBAAMgC,KAAI;UACZ;QACF,CAAC;AACDhC,cAAM4B,GAAG,WAAW,MAAM;AACxB7N,UAAAA,MAAK6G,MAAM2G,gBAAgBxN,MAAK6G,MAAMhD;AACtC7D,UAAAA,MAAK6G,MAAMhD,QAAQ7D,MAAKwM;AAGxBxM,UAAAA,MAAK6G,MAAM3D,QAAQlD,MAAKiM,MAAMkC,cAAc;AAC5CnO,UAAAA,MAAK6G,MAAM1D,SAASnD,MAAKiM,MAAMmC,eAAe;AAC9CpO,UAAAA,MAAKqJ,WAAU;AACfrJ,UAAAA,MAAKlB,SAAQ;AAGb,cAAIkB,MAAK6G,MAAM2G,eAAe;AAC5BxN,YAAAA,MAAK6G,MAAM2G,cAAc9M,MAAM+M,UAAU;UAC3C;QACF,CAAC;AAEDxB,cAAM4B,GAAG,SAAS,MAAM;AACtB7N,UAAAA,MAAKgO,aAAa;AAElB,cAAI,CAAChO,MAAKiF,QAAQ1F,WAAW;AAE3BgO,8BAAiB;UACnB;QACF,CAAC;AACDtB,cAAM4B,GAAG,SAAS,MAAM;AACtB7N,UAAAA,MAAK+N,aAAa;AAGlBR,4BAAiB;QACnB,CAAC;AAEDvN,QAAAA,MAAKiM,QAAQA;AAGb,YAAI,CAACjM,MAAKoN,sBAAsB;AAE9BpN,UAAAA,MAAK6G,MAAMC,MACT;AAEF,cAAImF,MAAM/N,SAAS,SAAS;AAC1B+N,kBAAM0B,YAAaC,SAAQ;AACzB5N,cAAAA,MAAK6G,MAAMiB,UAAW,QAAO8F,GAAI;AACjC5N,cAAAA,MAAKoH,KAAI;YACX,CAAC;AAED,mBAAO;UACT;QACF;MACF;IACF;AAEA,WAAO+F;;AAIT,QAAMkB,aAAa/I,UAASwG,UAAU9C;AACtC1D,EAAAA,UAASwG,UAAU9C,UAAU,WAAY;AACvC,UAAMhJ,QAAO;AAEb,QAAIA,MAAK6G,MAAM2G,eAAe;AAC5BxN,MAAAA,MAAK6G,MAAMhD,QAAQ7D,MAAK6G,MAAM2G;AAC9B,aAAOxN,MAAK6G,MAAM2G;IACpB;AAEAa,eAAWlD,MAAMnL,KAAI;;AAEzB;AClSA,SAASsO,kBAAgB3J,YAAW5E,SAAO4E,UAAU;AAEnD4J,UAAQC,KACN,2QACF;AAEA,MAAI,OAAO7J,cAAa,aAAa;AACnC;EACF;AAEA,QAAMW,YAAWX,UAASY;AAG1B,GACE,WACA,mBACA,QACA,WACA,cACA,aACA,YACA,UAAU,EACV/E,QAASC,SAAQ;AACjB,UAAMgO,MAAMnJ,UAASwG,UAAUrL,GAAG;AAClC6E,IAAAA,UAASwG,UAAUrL,GAAG,IAAI,YAAaI,MAAM;AAC3C,YAAMb,QAAO;AAEb,UAAIS,QAAQ,aAAaT,MAAK6D,MAAM0E,aAAa,uBAAuB,MAAM,MAAM;AAClFvI,QAAAA,MAAKiF,QAAQ/G,OAAO;AACpB8B,QAAAA,MAAKiG,YAAY9H,QAAQ6B,MAAK6D,MAAM0E,aAAa,uBAAuB,KAAK;MAC/E;AACA,UAAIvI,MAAKiF,QAAQ/G,SAAS,WAAW;AACnC,eAAOuQ,IAAItD,MAAMnL,OAAMa,IAAI;MAC7B;AAEAb,MAAAA,MAAKiG,YAAYyI,YAAY1O,MAAK6D,MAAM0E,aAAa,gBAAgB,KAAK;AAE1E,cAAQ9H,KAAG;QACT,KAAK,QAAQ;AACX,gBAAMkO,WAAY,GAAE3O,MAAKiG,YAAY9H,KAAM,GAAEyQ,MAAM,GAAG;AACtD5O,UAAAA,MAAKiF,QAAQ9G,QAAQ6B,MAAKiG,YAAY9H,SAAS;AAC/C6B,UAAAA,MAAKiF,QAAQ4J,SAASF,SAAS,CAAC,IAAItI,WAAWsI,SAAS,CAAC,CAAC,IAAI;AAC9D3O,UAAAA,MAAKiF,QAAQ6J,SAASH,SAAS,CAAC,IAAItI,WAAWsI,SAAS,CAAC,CAAC,IAAI;AAE9D,gBAAMI,eAAe/O,MAAKiG,YAAYyI,UAAUE,MAAM,GAAG;AACzD5O,UAAAA,MAAKiF,QAAQ+J,aAAaD,aAAa,CAAC,IAAI1I,WAAW0I,aAAa,CAAC,CAAC,IAAI;AAC1E/O,UAAAA,MAAKiF,QAAQgK,aAAaF,aAAa,CAAC,IAAI1I,WAAW0I,aAAa,CAAC,CAAC,IAAI;AAE1EN,cAAItD,MAAMnL,OAAMa,IAAI;AAGpB,gBAAMoI,oBAAoBjJ,MAAK6D,MAAM0E,aAAa,+BAA+B;AACjF,cAAIU,mBAAmB;AACrBjJ,YAAAA,MAAK6D,MAAM2E,aAAa,SAASS,iBAAiB;UACpD;AAEA,iBAAO;QACT;QACA,KAAK,YAAY;AACf,gBAAMiG,eAAelP,MAAKC,IAAID,MAAK6D,OAAO,WAAW;AACrD7D,UAAAA,MAAKC,IAAID,MAAK6D,OAAO;YAAEgF,WAAW;UAAG,CAAC;AACtC,gBAAMS,OAAOtJ,MAAK6D,MAAMC,sBAAqB;AAC7C9D,UAAAA,MAAKmP,WAAW;YACdjM,OAAOoG,KAAKpG;YACZC,QAAQmG,KAAKnG;YACbmE,GAAGgC,KAAKtF,MAAMhE,MAAKqH,cAAa,EAAGC;YACnC8H,GAAG9F,KAAKtB;;AAEVhI,UAAAA,MAAKC,IAAID,MAAK6D,OAAO;YAAEgF,WAAWqG;UAAa,CAAC;AAChD;QACF;QACA,KAAK,YAAY;AACf,gBAAMG,MAAMrP,MAAKqH,cAAa;AAC9B,gBAAMiI,iBACHD,IAAI/H,IAAI+H,IAAIlM,SAAS,IAAInD,MAAKmP,SAAS7H,IAAItH,MAAKmP,SAAShM,SAAS,MAClEkM,IAAIlM,SAAS;AAChB,gBAAMoM,QAAQD,gBAAgBtP,MAAKiF,QAAQ4J;AAC3C,gBAAMW,QAAQF,gBAAgBtP,MAAKiF,QAAQ6J;AAC3C,cAAIW,KAAKF;AACT,cAAIG,KAAKF;AACT,cAAIxP,MAAKiF,QAAQ+J,eAAe,QAAQO,QAAQvP,MAAKiF,QAAQ+J,WAAYS,MAAK;AAC9E,cAAIzP,MAAKiF,QAAQgK,eAAe,QAAQO,QAAQxP,MAAKiF,QAAQgK,WAAYS,MAAK;AAC9E1P,UAAAA,MAAKC,IAAID,MAAK6D,OAAO;YAAEgF,WAAY,eAAc6G,EAAG,MAAKD,EAAG;UAAO,CAAC;AACpE;QACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO;MAEX;AACA,aAAOhB,IAAItD,MAAMnL,OAAMa,IAAI;;EAE/B,CAAC;AACH;AC5FO,IAAM8D,WAAWgL;IAEX/D,gBAAgB,SAASA,iBAAgB;AACpD,SAAOgE,gBAAiBjL,QAAQ;AAClC;IAEa2J,kBAAkB,SAASA,mBAAkB;AACxD,SAAOuB,kBAAmBlL,QAAQ;AACpC;", "names": ["type", "speed", "containerClass", "imgSrc", "imgElement", "imgSize", "imgPosition", "imgRepeat", "keepImg", "elementInViewport", "zIndex", "disableParallax", "onScroll", "onInit", "onDestroy", "onCoverImage", "videoClass", "videoSrc", "videoStartTime", "videoEndTime", "videoVolume", "videoLoop", "videoPlayOnlyVisible", "videoLazyLoading", "disable<PERSON><PERSON><PERSON>", "onVideoInsert", "onVideoWorkerInit", "win", "window", "global", "self", "css", "el", "styles", "getComputedStyle", "getPropertyValue", "Object", "keys", "for<PERSON>ach", "key", "style", "extend", "out", "args", "i", "getParents", "elem", "parents", "parentElement", "nodeType", "push", "ready", "callback", "document", "readyState", "addEventListener", "capture", "once", "passive", "navigator", "mobileAgent", "test", "userAgent", "isMobile", "wndW", "wndH", "$deviceHelper", "getDeviceHeight", "body", "createElement", "cssText", "append<PERSON><PERSON><PERSON>", "clientHeight", "innerHeight", "documentElement", "updateWindowHeight", "innerWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getWindowSize", "width", "height", "jarallaxList", "updateParallax", "length", "data", "k", "instance", "oldData", "isVisible", "clientRect", "$item", "getBoundingClientRect", "newData", "top", "bottom", "isResized", "isScrolled", "onResize", "requestAnimationFrame", "visibilityObserver", "IntersectionObserver", "entries", "entry", "target", "jarall<PERSON>", "isElementInViewport", "isIntersecting", "rootMargin", "addObserver", "observe", "options", "removeObserver", "instanceID", "splice", "unobserve", "Jarallax", "constructor", "item", "userOptions", "defaults", "dataOptions", "dataset", "pureDataOptions", "lowerCaseOption", "substr", "toLowerCase", "pureOptions", "Math", "min", "max", "parseFloat", "RegExp", "disableParallaxRegexp", "disableParallaxDefault", "disableVideoRegexp", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elementInVP", "Element", "image", "src", "$container", "useImgTag", "position", "initImg", "canInitParallax", "init", "getWindowData", "y", "scrollTop", "$imgElement", "querySelector", "Image", "cloneNode", "$itemParent", "parentNode", "bgImage", "containerStyles", "left", "overflow", "imageStyles", "pointerEvents", "transformStyle", "backfaceVisibility", "curStyle", "getAttribute", "setAttribute", "curImgStyle", "$parents", "filter", "parentTransform", "transform", "overflowRegex", "call", "destroy", "originalStylesTag", "removeAttribute", "originalStylesImgTag", "<PERSON><PERSON><PERSON><PERSON>", "coverImage", "rect", "contH", "isScroll", "scrollDist", "resultH", "resultMT", "abs", "parallaxScrollDistance", "marginTop", "container", "force", "contT", "beforeTop", "beforeTopEnd", "afterTop", "beforeBottom", "beforeBottomEnd", "afterBottom", "fromViewportCenter", "visiblePercent", "opacity", "scale", "positionY", "section", "items", "HTMLElement", "nodeName", "len", "ret", "apply", "autoplay", "loop", "mute", "volume", "showControls", "accessibilityHidden", "startTime", "endTime", "jarallaxVideo", "defOnScroll", "prototype", "isReady", "isVideoInserted", "video", "getVideo", "$parent", "right", "max<PERSON><PERSON><PERSON>", "maxHeight", "margin", "$video", "tagName", "defCoverImage", "imageData", "node", "h", "w", "ml", "mt", "marginLeft", "defInitImg", "defaultResult", "defaultInitImgResult", "defCanInitParallax", "VideoWorker", "resetDefaultImage", "$default_item", "display", "<PERSON><PERSON><PERSON><PERSON>", "getImageURL", "url", "on", "oldOnScroll", "videoError", "videoEnded", "play", "pause", "videoWidth", "videoHeight", "defDest<PERSON>", "jarallaxElement", "console", "warn", "def", "threshold", "speedArr", "split", "speedY", "speedX", "thresholdArr", "thresholdY", "thresholdX", "defTransform", "itemData", "x", "wnd", "centerPercent", "moveY", "moveX", "my", "mx", "jarallaxLib", "jarallaxVideoExt", "jarallaxElementExt"]}