import React, { useEffect, useRef } from "react";
import DOMPurify from "dompurify";
import hljs from "highlight.js";
// Note: Using custom CSS instead of highlight.js CSS to avoid conflicts

const HighlightedHTML = ({
  content,
  className = "",
  tag = "div",
  style = {},
}) => {
  const contentRef = useRef(null);

  // Sanitize HTML content for safe rendering
  const sanitizeHTML = (html) => {
    if (!html || typeof html !== "string") {
      return "";
    }

    // Configure DOMPurify to allow safe HTML elements and attributes
    const cleanHTML = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        "p",
        "br",
        "strong",
        "em",
        "u",
        "s",
        "sub",
        "sup",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "ul",
        "ol",
        "li",
        "blockquote",
        "pre",
        "code",
        "a",
        "img",
        "table",
        "thead",
        "tbody",
        "tr",
        "th",
        "td",
        "hr",
        "div",
        "span",
      ],
      ALLOWED_ATTR: [
        "href",
        "target",
        "rel",
        "src",
        "alt",
        "title",
        "class",
        "data-language",
        "colspan",
        "rowspan",
      ],
      ALLOW_DATA_ATTR: false,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      // Allow highlight.js classes for syntax highlighting
      ADD_ATTR: ["class"],
      // Add some additional security
      FORBID_TAGS: ["script", "object", "embed", "form", "input"],
      FORBID_ATTR: ["onerror", "onload", "onclick", "onmouseover"],
    });

    return cleanHTML;
  };

  // Function to apply inline styles - EXACT SAME AS TEST PAGE
  const applyInlineHighlightStyles = (codeBlock) => {
    console.log("🎨 Applying inline styles (same as test page)...");

    // Same colors as test page
    const highlightColors = {
      "hljs-keyword": "#569cd6",
      "hljs-string": "#ce9178",
      "hljs-number": "#b5cea8",
      "hljs-comment": "#6a9955",
      "hljs-tag": "#569cd6",
      "hljs-name": "#4fc1ff",
      "hljs-attr": "#9cdcfe",
      "hljs-function": "#dcdcaa",
      "hljs-title": "#dcdcaa",
      "hljs-variable": "#9cdcfe",
      "hljs-property": "#9cdcfe",
    };

    // Apply styles to highlighted spans - EXACT SAME AS TEST PAGE
    Object.keys(highlightColors).forEach((className) => {
      const elements = codeBlock.querySelectorAll(`.${className}`);
      elements.forEach((element) => {
        element.style.setProperty(
          "color",
          highlightColors[className],
          "important"
        );
        element.style.setProperty("background", "none", "important");
        element.style.setProperty("border", "1px solid red", "important"); // Debug border
        console.log(
          `🎨 Applied color ${highlightColors[className]} to .${className}`
        );
      });
    });

    // Style the pre element - EXACT SAME AS TEST PAGE
    const preElement = codeBlock.closest("pre");
    if (preElement) {
      preElement.style.setProperty("background", "#1e1e1e", "important");
      preElement.style.setProperty("color", "#d4d4d4", "important");
      preElement.style.setProperty("padding", "16px", "important");
      preElement.style.setProperty("border-radius", "8px", "important");
      preElement.style.setProperty("border", "3px solid yellow", "important"); // Debug border
      preElement.style.setProperty(
        "font-family",
        'Consolas, "Courier New", monospace',
        "important"
      );
      preElement.style.setProperty("font-size", "14px", "important");
      preElement.style.setProperty("line-height", "1.5", "important");
      console.log("🎨 Applied styles to pre element");
    }
  };

  // Apply syntax highlighting after component mounts and updates
  useEffect(() => {
    if (contentRef.current) {
      console.log("🔍 HighlightedHTML - Content ref:", contentRef.current);
      console.log("🔍 HighlightedHTML - Raw content:", content);

      // Find all code blocks and apply highlighting
      const codeBlocks = contentRef.current.querySelectorAll("pre code");
      console.log("🔍 Found code blocks:", codeBlocks.length, codeBlocks);

      codeBlocks.forEach((block, index) => {
        console.log(`🔍 Processing code block ${index}:`, block);
        console.log(`🔍 Block innerHTML before:`, block.innerHTML);

        // FORCE re-highlighting by removing the highlighted flag
        if (block.dataset.highlighted === "yes") {
          console.log(
            `🔍 Block ${index} was already highlighted, forcing re-highlight`
          );
          delete block.dataset.highlighted;
        }

        // Remove existing highlighting classes
        block.className = block.className.replace(/hljs[^\s]*/g, "").trim();

        // Apply highlight.js
        hljs.highlightElement(block);

        console.log(`🔍 Block innerHTML after:`, block.innerHTML);
        console.log(`🔍 Block className after:`, block.className);

        // FORCE APPLY INLINE STYLES TO OVERRIDE THEME STYLES
        applyInlineHighlightStyles(block);

        // Log all the highlight.js classes that were applied
        const highlightClasses = Array.from(block.querySelectorAll("*"))
          .map((el) => el.className)
          .filter((cls) => cls.includes("hljs"));
        console.log(`🔍 Highlight classes found:`, highlightClasses);

        // Add language label if data-language attribute exists
        const pre = block.parentElement;
        const language = pre.getAttribute("data-language");
        if (language && !pre.querySelector(".language-label")) {
          const label = document.createElement("span");
          label.className = "language-label";
          label.textContent = language.toUpperCase();
          pre.style.position = "relative";
          label.style.position = "absolute";
          label.style.top = "8px";
          label.style.right = "12px";
          label.style.background = "rgba(255, 255, 255, 0.1)";
          label.style.color = "#d4d4d4";
          label.style.padding = "2px 8px";
          label.style.borderRadius = "4px";
          label.style.fontSize = "12px";
          label.style.fontWeight = "500";
          pre.appendChild(label);
        }
      });
    }
  }, [content]);

  const sanitizedContent = sanitizeHTML(content);

  console.log("🔍 HighlightedHTML - Original content:", content);
  console.log("🔍 HighlightedHTML - Sanitized content:", sanitizedContent);

  // Create the element with sanitized HTML
  const Element = tag;

  return (
    <div
      style={{
        // Create a completely isolated container that breaks CSS inheritance
        position: "relative",
        isolation: "isolate", // Creates new stacking context
        contain: "style layout", // CSS containment
      }}
    >
      <div
        className="syntax-highlight-isolated"
        ref={contentRef}
        style={{
          // NUCLEAR OPTION: Reset everything and rebuild from scratch
          all: "initial",
          display: "block",
          fontFamily: "Arial, sans-serif",
          fontSize: "16px",
          lineHeight: "1.8",
          color: "#ffffff",
          background: "transparent",
          margin: "0",
          padding: "0",
          border: "none",
          outline: "none",
          textDecoration: "none",
          textTransform: "none",
          letterSpacing: "normal",
          wordSpacing: "normal",
          whiteSpace: "normal",
          direction: "ltr",
          unicodeBidi: "normal",
          // Override any possible inheritance
          colorScheme: "dark",
        }}
      >
        <Element
          className={className}
          style={{
            ...style,
            // Additional reset for the content element
            all: "unset",
            display: "block",
            fontFamily: "inherit",
            fontSize: "inherit",
            lineHeight: "inherit",
            color: "inherit",
          }}
          dangerouslySetInnerHTML={{ __html: sanitizedContent }}
        />
      </div>
    </div>
  );
};

export default HighlightedHTML;
