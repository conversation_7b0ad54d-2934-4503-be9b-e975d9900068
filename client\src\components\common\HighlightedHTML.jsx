import React, { useEffect, useRef } from "react";
import DOMPurify from "dompurify";
import hljs from "highlight.js";
// Note: Using custom CSS instead of highlight.js CSS to avoid conflicts

const HighlightedHTML = ({
  content,
  className = "",
  tag = "div",
  style = {},
}) => {
  const contentRef = useRef(null);

  // Sanitize HTML content for safe rendering
  const sanitizeHTML = (html) => {
    if (!html || typeof html !== "string") {
      return "";
    }

    // Configure DOMPurify to allow safe HTML elements and attributes
    const cleanHTML = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        "p",
        "br",
        "strong",
        "em",
        "u",
        "s",
        "sub",
        "sup",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "ul",
        "ol",
        "li",
        "blockquote",
        "pre",
        "code",
        "a",
        "img",
        "table",
        "thead",
        "tbody",
        "tr",
        "th",
        "td",
        "hr",
        "div",
        "span",
      ],
      ALLOWED_ATTR: [
        "href",
        "target",
        "rel",
        "src",
        "alt",
        "title",
        "class",
        "data-language",
        "colspan",
        "rowspan",
      ],
      ALLOW_DATA_ATTR: false,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      // Allow highlight.js classes for syntax highlighting
      ADD_ATTR: ["class"],
      // Add some additional security
      FORBID_TAGS: ["script", "object", "embed", "form", "input"],
      FORBID_ATTR: ["onerror", "onload", "onclick", "onmouseover"],
    });

    return cleanHTML;
  };

  // Function to apply inline styles directly to highlighted elements
  const applyInlineHighlightStyles = (codeBlock) => {
    console.log("🎨 Applying inline highlight styles...");

    // Define VS Code dark theme colors
    const highlightColors = {
      "hljs-keyword": "#569cd6",
      "hljs-meta": "#569cd6",
      "hljs-tag": "#569cd6",
      "hljs-name": "#4fc1ff",
      "hljs-attr": "#9cdcfe",
      "hljs-string": "#ce9178",
      "hljs-number": "#b5cea8",
      "hljs-title.function_": "#dcdcaa",
      "hljs-variable.language_": "#9cdcfe",
      "hljs-property": "#9cdcfe",
      "hljs-comment": "#6a9955",
      "hljs-function": "#dcdcaa",
      "hljs-variable": "#9cdcfe",
      "hljs-type": "#4ec9b0",
      "hljs-operator": "#d4d4d4",
      "hljs-punctuation": "#d4d4d4",
      "hljs-value": "#ce9178",
      "hljs-built_in": "#4ec9b0",
      "hljs-literal": "#569cd6",
      "hljs-title": "#dcdcaa",
      "hljs-params": "#d4d4d4",
    };

    // Apply styles to all highlighted spans with !important via setProperty
    Object.keys(highlightColors).forEach((className) => {
      const elements = codeBlock.querySelectorAll(`.${className}`);
      elements.forEach((element) => {
        // Use setProperty with priority to force !important
        element.style.setProperty(
          "color",
          highlightColors[className],
          "important"
        );
        element.style.setProperty("background", "none", "important");
        element.style.setProperty("border", "1px solid red", "important"); // Debug border
        element.style.setProperty("font-weight", "inherit", "important");
        element.style.setProperty("font-family", "inherit", "important");
        console.log(
          `🎨 Applied color ${highlightColors[className]} to .${className} with !important`
        );
      });
    });

    // Also set the code block background with !important
    const preElement = codeBlock.closest("pre");
    if (preElement) {
      preElement.style.setProperty("background", "#1e1e1e", "important");
      preElement.style.setProperty("color", "#d4d4d4", "important");
      preElement.style.setProperty("padding", "16px", "important");
      preElement.style.setProperty("border-radius", "8px", "important");
      preElement.style.setProperty("border", "2px solid yellow", "important"); // Debug border for pre
      preElement.style.setProperty(
        "font-family",
        'Consolas, "Courier New", monospace',
        "important"
      );
      preElement.style.setProperty("font-size", "14px", "important");
      preElement.style.setProperty("line-height", "1.5", "important");
      console.log("🎨 Applied dark background to pre element with !important");
    }
  };

  // Apply syntax highlighting after component mounts and updates
  useEffect(() => {
    if (contentRef.current) {
      console.log("🔍 HighlightedHTML - Content ref:", contentRef.current);
      console.log("🔍 HighlightedHTML - Raw content:", content);

      // Find all code blocks and apply highlighting
      const codeBlocks = contentRef.current.querySelectorAll("pre code");
      console.log("🔍 Found code blocks:", codeBlocks.length, codeBlocks);

      codeBlocks.forEach((block, index) => {
        console.log(`🔍 Processing code block ${index}:`, block);
        console.log(`🔍 Block innerHTML before:`, block.innerHTML);

        // FORCE re-highlighting by removing the highlighted flag
        if (block.dataset.highlighted === "yes") {
          console.log(
            `🔍 Block ${index} was already highlighted, forcing re-highlight`
          );
          delete block.dataset.highlighted;
        }

        // Remove existing highlighting classes
        block.className = block.className.replace(/hljs[^\s]*/g, "").trim();

        // Apply highlight.js
        hljs.highlightElement(block);

        console.log(`🔍 Block innerHTML after:`, block.innerHTML);
        console.log(`🔍 Block className after:`, block.className);

        // FORCE APPLY INLINE STYLES TO OVERRIDE THEME STYLES
        applyInlineHighlightStyles(block);

        // Log all the highlight.js classes that were applied
        const highlightClasses = Array.from(block.querySelectorAll("*"))
          .map((el) => el.className)
          .filter((cls) => cls.includes("hljs"));
        console.log(`🔍 Highlight classes found:`, highlightClasses);

        // Add language label if data-language attribute exists
        const pre = block.parentElement;
        const language = pre.getAttribute("data-language");
        if (language && !pre.querySelector(".language-label")) {
          const label = document.createElement("span");
          label.className = "language-label";
          label.textContent = language.toUpperCase();
          pre.style.position = "relative";
          label.style.position = "absolute";
          label.style.top = "8px";
          label.style.right = "12px";
          label.style.background = "rgba(255, 255, 255, 0.1)";
          label.style.color = "#d4d4d4";
          label.style.padding = "2px 8px";
          label.style.borderRadius = "4px";
          label.style.fontSize = "12px";
          label.style.fontWeight = "500";
          pre.appendChild(label);
        }
      });
    }
  }, [content]);

  const sanitizedContent = sanitizeHTML(content);

  console.log("🔍 HighlightedHTML - Original content:", content);
  console.log("🔍 HighlightedHTML - Sanitized content:", sanitizedContent);

  // Create the element with sanitized HTML
  const Element = tag;

  return (
    <div
      className="syntax-highlight-container"
      style={{
        // Aggressive style isolation
        all: "initial",
        fontFamily: 'Consolas, "Courier New", monospace !important',
        fontSize: "16px !important",
        lineHeight: "1.8 !important",
        color: "#d4d4d4 !important",
        background: "transparent !important",
        // Override any potential theme interference
        textAlign: "left",
        direction: "ltr",
        unicodeBidi: "normal",
        whiteSpace: "normal",
        wordSpacing: "normal",
        wordBreak: "normal",
        wordWrap: "normal",
        tabSize: "4",
        hyphens: "none",
      }}
    >
      <Element
        ref={contentRef}
        className={className}
        style={{
          ...style,
          // Additional isolation
          color: "inherit",
          fontFamily: "inherit",
        }}
        dangerouslySetInnerHTML={{ __html: sanitizedContent }}
      />
    </div>
  );
};

export default HighlightedHTML;
