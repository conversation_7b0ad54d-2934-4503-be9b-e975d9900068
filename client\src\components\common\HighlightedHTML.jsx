import React, { useEffect, useRef } from "react";
import DOMPurify from "dompurify";
import hljs from "highlight.js";
// Import highlight.js CSS for styling
import "highlight.js/styles/vs2015.css";

const HighlightedHTML = ({
  content,
  className = "",
  tag = "div",
  style = {},
}) => {
  const contentRef = useRef(null);

  // Sanitize HTML content for safe rendering
  const sanitizeHTML = (html) => {
    if (!html || typeof html !== "string") {
      return "";
    }

    // Configure DOMPurify to allow safe HTML elements and attributes
    const cleanHTML = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        "p",
        "br",
        "strong",
        "em",
        "u",
        "s",
        "sub",
        "sup",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "ul",
        "ol",
        "li",
        "blockquote",
        "pre",
        "code",
        "a",
        "img",
        "table",
        "thead",
        "tbody",
        "tr",
        "th",
        "td",
        "hr",
        "div",
        "span",
      ],
      ALLOWED_ATTR: [
        "href",
        "target",
        "rel",
        "src",
        "alt",
        "title",
        "class",
        "data-language",
        "colspan",
        "rowspan",
      ],
      ALLOW_DATA_ATTR: false,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      // Allow highlight.js classes for syntax highlighting
      ADD_ATTR: ["class"],
      // Add some additional security
      FORBID_TAGS: ["script", "object", "embed", "form", "input"],
      FORBID_ATTR: ["onerror", "onload", "onclick", "onmouseover"],
    });

    return cleanHTML;
  };

  // Apply syntax highlighting after component mounts and updates
  useEffect(() => {
    if (contentRef.current) {
      // Find all code blocks and apply highlighting
      const codeBlocks = contentRef.current.querySelectorAll("pre code");
      codeBlocks.forEach((block) => {
        // Remove existing highlighting classes
        block.className = block.className.replace(/hljs[^\s]*/g, "").trim();

        // Apply highlight.js
        hljs.highlightElement(block);

        // Add language label if data-language attribute exists
        const pre = block.parentElement;
        const language = pre.getAttribute("data-language");
        if (language && !pre.querySelector(".language-label")) {
          const label = document.createElement("span");
          label.className = "language-label";
          label.textContent = language.toUpperCase();
          pre.style.position = "relative";
          label.style.position = "absolute";
          label.style.top = "8px";
          label.style.right = "12px";
          label.style.background = "rgba(255, 255, 255, 0.1)";
          label.style.color = "#d4d4d4";
          label.style.padding = "2px 8px";
          label.style.borderRadius = "4px";
          label.style.fontSize = "12px";
          label.style.fontWeight = "500";
          pre.appendChild(label);
        }
      });
    }
  }, [content]);

  const sanitizedContent = sanitizeHTML(content);

  // Create the element with sanitized HTML
  const Element = tag;

  return (
    <Element
      ref={contentRef}
      className={className}
      style={style}
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
    />
  );
};

export default HighlightedHTML;
