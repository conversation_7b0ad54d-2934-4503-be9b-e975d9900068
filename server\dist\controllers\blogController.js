"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateBlogPost = exports.toggleBlogPostVisibility = exports.deleteBlogPost = exports.createBlogPost = exports.getBlogPost = exports.getBlogPosts = void 0;
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const slugify_1 = __importDefault(require("slugify"));
const upload_1 = require("../middleware/upload");
const path_1 = __importDefault(require("path"));
const prisma = new client_1.PrismaClient();
const createBlogPostSchema = joi_1.default.object({
    slug: joi_1.default.string().optional(),
    featured: joi_1.default.boolean().default(false),
    published: joi_1.default.boolean().default(false),
    scheduledAt: joi_1.default.date().optional().allow(null),
    publishedAt: joi_1.default.date().optional().allow(null),
    featuredImageAlt: joi_1.default.string().optional().allow(""),
    readTime: joi_1.default.number().integer().min(1).optional(),
    categoryIds: joi_1.default.array().items(joi_1.default.string()).default([]),
    tagIds: joi_1.default.array().items(joi_1.default.string()).default([]),
    translations: joi_1.default.object()
        .pattern(joi_1.default.string().valid("en", "et", "fi", "sv", "de"), joi_1.default.object({
        title: joi_1.default.string().allow(""),
        excerpt: joi_1.default.string().optional().allow(""),
        content: joi_1.default.string().allow(""),
        metaTitle: joi_1.default.string().optional().allow(""),
        metaDesc: joi_1.default.string().optional().allow(""),
        keywords: joi_1.default.array().items(joi_1.default.string()).default([]),
    }))
        .required(),
});
const updateBlogPostSchema = createBlogPostSchema.keys({
    id: joi_1.default.string().required(),
});
const getBlogPosts = async (req, res) => {
    try {
        const { page = 1, limit = 10, language = "en", category, tag, featured, published = "true", search, } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const now = new Date();
        const where = {};
        if (published === "true") {
            where.published = true;
            where.OR = [{ scheduledAt: null }, { scheduledAt: { lte: now } }];
        }
        if (featured === "true") {
            where.featured = true;
        }
        if (search) {
            where.translations = {
                some: {
                    language: language,
                    OR: [
                        { title: { contains: search, mode: "insensitive" } },
                        { excerpt: { contains: search, mode: "insensitive" } },
                        { content: { contains: search, mode: "insensitive" } },
                    ],
                },
            };
        }
        if (category) {
            where.categories = {
                some: {
                    category: {
                        slug: category,
                    },
                },
            };
        }
        if (tag) {
            where.tags = {
                some: {
                    tag: {
                        slug: tag,
                    },
                },
            };
        }
        const [posts, total] = await Promise.all([
            prisma.blogPost.findMany({
                where,
                include: {
                    author: {
                        select: { id: true, name: true, email: true },
                    },
                    translations: true,
                    categories: {
                        include: { category: true },
                    },
                    tags: {
                        include: { tag: true },
                    },
                    _count: {
                        select: { comments: { where: { approved: true } } },
                    },
                },
                orderBy: [
                    { featured: "desc" },
                    { publishedAt: "desc" },
                    { createdAt: "desc" },
                ],
                skip,
                take: Number(limit),
            }),
            prisma.blogPost.count({ where }),
        ]);
        const formattedPosts = posts.map((post) => ({
            id: post.id,
            slug: post.slug,
            published: post.published,
            featured: post.featured,
            publishedAt: post.publishedAt,
            scheduledAt: post.scheduledAt,
            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
            featuredImage: post.featuredImage ? (0, upload_1.getFileUrl)(post.featuredImage) : null,
            featuredImageAlt: post.featuredImageAlt,
            readTime: post.readTime,
            viewCount: post.viewCount,
            author: post.author,
            translations: post.translations,
            categories: post.categories.map((c) => c.category),
            tags: post.tags.map((t) => t.tag),
            commentCount: post._count.comments,
        }));
        res.json({
            success: true,
            data: {
                data: formattedPosts,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total,
                    pages: Math.ceil(total / Number(limit)),
                    totalPages: Math.ceil(total / Number(limit)),
                },
            },
        });
    }
    catch (error) {
        console.error("Get blog posts error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.getBlogPosts = getBlogPosts;
const getBlogPost = async (req, res) => {
    try {
        const { slug } = req.params;
        const { language = "en" } = req.query;
        const now = new Date();
        const post = await prisma.blogPost.findUnique({
            where: { slug },
            include: {
                author: {
                    select: { id: true, name: true, email: true },
                },
                translations: true,
                categories: {
                    include: { category: true },
                },
                tags: {
                    include: { tag: true },
                },
                comments: {
                    where: { approved: true, parentId: null },
                    include: {
                        replies: {
                            where: { approved: true },
                            orderBy: { createdAt: "asc" },
                        },
                    },
                    orderBy: { createdAt: "desc" },
                },
            },
        });
        if (!post) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found",
            });
        }
        if (!post.published || (post.scheduledAt && post.scheduledAt > now)) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found",
            });
        }
        await prisma.blogPost.update({
            where: { id: post.id },
            data: { viewCount: { increment: 1 } },
        });
        const translation = post.translations.find((t) => t.language === language) ||
            post.translations.find((t) => t.language === "en") ||
            post.translations[0];
        const formattedPost = {
            id: post.id,
            slug: post.slug,
            published: post.published,
            featured: post.featured,
            publishedAt: post.publishedAt,
            scheduledAt: post.scheduledAt,
            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
            featuredImage: post.featuredImage ? (0, upload_1.getFileUrl)(post.featuredImage) : null,
            featuredImageAlt: post.featuredImageAlt,
            gallery: post.gallery.map((img) => (0, upload_1.getFileUrl)(img)),
            readTime: post.readTime,
            viewCount: post.viewCount,
            author: post.author,
            translation,
            translations: post.translations,
            categories: post.categories.map((c) => c.category),
            tags: post.tags.map((t) => t.tag),
            comments: post.comments,
        };
        res.json({
            success: true,
            data: formattedPost,
        });
    }
    catch (error) {
        console.error("Get blog post error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.getBlogPost = getBlogPost;
const createBlogPost = async (req, res) => {
    try {
        const formData = { ...req.body };
        try {
            if (formData.categoryIds) {
                formData.categoryIds = JSON.parse(formData.categoryIds);
            }
            if (formData.tagIds) {
                formData.tagIds = JSON.parse(formData.tagIds);
            }
            if (formData.translations) {
                formData.translations = JSON.parse(formData.translations);
            }
            if (formData.featured) {
                formData.featured = formData.featured === "true";
            }
            if (formData.published) {
                formData.published = formData.published === "true";
            }
            if (formData.readTime) {
                formData.readTime = parseInt(formData.readTime, 10);
            }
        }
        catch (parseError) {
            return res.status(400).json({
                success: false,
                message: "Invalid JSON in form data",
            });
        }
        const { error, value } = createBlogPostSchema.validate(formData);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message,
            });
        }
        const { slug: providedSlug, featured, published, scheduledAt, publishedAt, featuredImageAlt, readTime, categoryIds, tagIds, translations, } = value;
        if (!translations.en ||
            !translations.en.title ||
            !translations.en.content) {
            return res.status(400).json({
                success: false,
                message: "English title and content are required",
            });
        }
        const validTranslations = Object.entries(translations).reduce((acc, [lang, translation]) => {
            const typedTranslation = translation;
            if (typedTranslation.title && typedTranslation.content) {
                acc[lang] = translation;
            }
            return acc;
        }, {});
        const slug = providedSlug ||
            (0, slugify_1.default)(translations.en.title, { lower: true, strict: true });
        const existingPost = await prisma.blogPost.findUnique({
            where: { slug },
        });
        if (existingPost) {
            return res.status(400).json({
                success: false,
                message: "A blog post with this slug already exists",
            });
        }
        let featuredImage = null;
        if (req.file) {
            featuredImage = req.file.filename;
        }
        const blogPost = await prisma.blogPost.create({
            data: {
                slug,
                featured,
                published,
                scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
                publishedAt: published
                    ? publishedAt
                        ? new Date(publishedAt)
                        : new Date()
                    : null,
                featuredImage,
                featuredImageAlt,
                readTime,
                authorId: req.user.id,
                translations: {
                    create: Object.entries(validTranslations).map(([language, translation]) => ({
                        language,
                        title: translation.title,
                        excerpt: translation.excerpt || "",
                        content: translation.content,
                        metaTitle: translation.metaTitle || translation.title,
                        metaDesc: translation.metaDesc || translation.excerpt || "",
                        keywords: translation.keywords || [],
                    })),
                },
                categories: {
                    create: categoryIds.map((categoryId) => ({
                        categoryId,
                    })),
                },
                tags: {
                    create: tagIds.map((tagId) => ({
                        tagId,
                    })),
                },
            },
            include: {
                author: {
                    select: { id: true, name: true, email: true },
                },
                translations: true,
                categories: {
                    include: { category: true },
                },
                tags: {
                    include: { tag: true },
                },
            },
        });
        res.status(201).json({
            success: true,
            data: {
                ...blogPost,
                featuredImage: blogPost.featuredImage
                    ? (0, upload_1.getFileUrl)(blogPost.featuredImage)
                    : null,
            },
        });
    }
    catch (error) {
        console.error("Create blog post error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.createBlogPost = createBlogPost;
const deleteBlogPost = async (req, res) => {
    try {
        const { id } = req.params;
        const post = await prisma.blogPost.findUnique({
            where: { id },
        });
        if (!post) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found",
            });
        }
        if (post.featuredImage) {
            try {
                await (0, upload_1.deleteFile)(path_1.default.join(process.cwd(), "uploads", "blog-images", post.featuredImage));
            }
            catch (error) {
                console.error("Error deleting image:", error);
            }
        }
        if (post.gallery && post.gallery.length > 0) {
            for (const image of post.gallery) {
                try {
                    await (0, upload_1.deleteFile)(path_1.default.join(process.cwd(), "uploads", "blog-images", image));
                }
                catch (error) {
                    console.error("Error deleting gallery image:", error);
                }
            }
        }
        await prisma.blogPost.delete({
            where: { id },
        });
        res.json({
            success: true,
            message: "Blog post deleted successfully",
        });
    }
    catch (error) {
        console.error("Delete blog post error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.deleteBlogPost = deleteBlogPost;
const toggleBlogPostVisibility = async (req, res) => {
    try {
        const { id } = req.params;
        const post = await prisma.blogPost.findUnique({
            where: { id },
        });
        if (!post) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found",
            });
        }
        const updatedPost = await prisma.blogPost.update({
            where: { id },
            data: {
                published: !post.published,
                publishedAt: !post.published ? new Date() : null,
            },
        });
        res.json({
            success: true,
            data: updatedPost,
            message: `Blog post ${updatedPost.published ? "published" : "unpublished"} successfully`,
        });
    }
    catch (error) {
        console.error("Toggle visibility error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.toggleBlogPostVisibility = toggleBlogPostVisibility;
const updateBlogPost = async (req, res) => {
    try {
        const { id } = req.params;
        const formData = { ...req.body, id };
        try {
            if (formData.categoryIds) {
                formData.categoryIds = JSON.parse(formData.categoryIds);
            }
            if (formData.tagIds) {
                formData.tagIds = JSON.parse(formData.tagIds);
            }
            if (formData.translations) {
                formData.translations = JSON.parse(formData.translations);
            }
            if (formData.featured) {
                formData.featured = formData.featured === "true";
            }
            if (formData.published) {
                formData.published = formData.published === "true";
            }
            if (formData.readTime) {
                formData.readTime = parseInt(formData.readTime, 10);
            }
        }
        catch (parseError) {
            return res.status(400).json({
                success: false,
                message: "Invalid JSON in form data",
            });
        }
        const { error, value } = updateBlogPostSchema.validate(formData);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message,
            });
        }
        const { slug: providedSlug, featured, published, scheduledAt, publishedAt, featuredImageAlt, readTime, categoryIds, tagIds, translations, } = value;
        if (!translations.en ||
            !translations.en.title ||
            !translations.en.content) {
            return res.status(400).json({
                success: false,
                message: "English title and content are required",
            });
        }
        const validTranslations = Object.entries(translations).reduce((acc, [lang, translation]) => {
            const typedTranslation = translation;
            if (typedTranslation.title && typedTranslation.content) {
                acc[lang] = translation;
            }
            return acc;
        }, {});
        const existingPost = await prisma.blogPost.findUnique({
            where: { id },
            include: { translations: true },
        });
        if (!existingPost) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found",
            });
        }
        let slug = existingPost.slug;
        if (providedSlug && providedSlug !== existingPost.slug) {
            slug = (0, slugify_1.default)(providedSlug || translations.en.title, {
                lower: true,
                strict: true,
            });
            const slugExists = await prisma.blogPost.findUnique({
                where: { slug },
            });
            if (slugExists && slugExists.id !== id) {
                return res.status(400).json({
                    success: false,
                    message: "A blog post with this slug already exists",
                });
            }
        }
        let featuredImage = existingPost.featuredImage;
        if (req.file) {
            if (existingPost.featuredImage) {
                try {
                    await (0, upload_1.deleteFile)(path_1.default.join(process.cwd(), "uploads", "blog-images", existingPost.featuredImage));
                }
                catch (error) {
                    console.error("Error deleting old image:", error);
                }
            }
            featuredImage = req.file.filename;
        }
        const updatedPost = await prisma.$transaction(async (tx) => {
            await tx.blogPostTranslation.deleteMany({
                where: { blogPostId: id },
            });
            await tx.blogPostCategory.deleteMany({
                where: { blogPostId: id },
            });
            await tx.blogPostTag.deleteMany({
                where: { blogPostId: id },
            });
            return await tx.blogPost.update({
                where: { id },
                data: {
                    slug,
                    featured,
                    published,
                    scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
                    publishedAt: published
                        ? publishedAt
                            ? new Date(publishedAt)
                            : new Date()
                        : null,
                    featuredImage,
                    featuredImageAlt,
                    readTime,
                    translations: {
                        create: Object.entries(validTranslations).map(([language, translation]) => ({
                            language,
                            title: translation.title,
                            excerpt: translation.excerpt || "",
                            content: translation.content,
                            metaTitle: translation.metaTitle || translation.title,
                            metaDesc: translation.metaDesc || translation.excerpt || "",
                            keywords: translation.keywords || [],
                        })),
                    },
                    categories: {
                        create: categoryIds.map((categoryId) => ({
                            categoryId,
                        })),
                    },
                    tags: {
                        create: tagIds.map((tagId) => ({
                            tagId,
                        })),
                    },
                },
                include: {
                    author: {
                        select: { id: true, name: true, email: true },
                    },
                    translations: true,
                    categories: {
                        include: { category: true },
                    },
                    tags: {
                        include: { tag: true },
                    },
                },
            });
        });
        res.json({
            success: true,
            data: {
                ...updatedPost,
                featuredImage: updatedPost.featuredImage
                    ? (0, upload_1.getFileUrl)(updatedPost.featuredImage)
                    : null,
            },
        });
    }
    catch (error) {
        console.error("Update blog post error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.updateBlogPost = updateBlogPost;
