{"version": 3, "file": "css-gradient.js", "sources": ["../../../src/js/css-gradient.ts"], "sourcesContent": ["/**\n * css-gradient\n */\n\nimport { CacheItem, createCache<PERSON><PERSON>, getCache, setCache } from './cache';\nimport { isString } from './common';\nimport { MatchedRegExp, Options } from './typedef';\nimport { isColor, splitValue } from './util';\n\n/* constants */\nimport {\n  ANGLE,\n  CS_HUE,\n  CS_RECT,\n  LENGTH,\n  NUM,\n  NUM_POSITIVE,\n  PCT\n} from './constant';\nconst NAMESPACE = 'css-gradient';\nconst DIM_ANGLE = `${NUM}(?:${ANGLE})`;\nconst DIM_ANGLE_PCT = `${DIM_ANGLE}|${PCT}`;\nconst DIM_LEN = `${NUM}(?:${LENGTH})|0`;\nconst DIM_LEN_PCT = `${DIM_LEN}|${PCT}`;\nconst DIM_LEN_PCT_POSI = `${NUM_POSITIVE}(?:${LENGTH}|%)|0`;\nconst DIM_LEN_POSI = `${NUM_POSITIVE}(?:${LENGTH})|0`;\nconst CTR = 'center';\nconst L_R = 'left|right';\nconst T_B = 'top|bottom';\nconst S_E = 'start|end';\nconst AXIS_X = `${L_R}|x-(?:${S_E})`;\nconst AXIS_Y = `${T_B}|y-(?:${S_E})`;\nconst BLOCK = `block-(?:${S_E})`;\nconst INLINE = `inline-(?:${S_E})`;\nconst POS_1 = `${CTR}|${AXIS_X}|${AXIS_Y}|${BLOCK}|${INLINE}|${DIM_LEN_PCT}`;\nconst POS_2 = [\n  `(?:${CTR}|${AXIS_X})\\\\s+(?:${CTR}|${AXIS_Y})`,\n  `(?:${CTR}|${AXIS_Y})\\\\s+(?:${CTR}|${AXIS_X})`,\n  `(?:${CTR}|${AXIS_X}|${DIM_LEN_PCT})\\\\s+(?:${CTR}|${AXIS_Y}|${DIM_LEN_PCT})`,\n  `(?:${CTR}|${BLOCK})\\\\s+(?:${CTR}|${INLINE})`,\n  `(?:${CTR}|${INLINE})\\\\s+(?:${CTR}|${BLOCK})`,\n  `(?:${CTR}|${S_E})\\\\s+(?:${CTR}|${S_E})`\n].join('|');\nconst POS_4 = [\n  `(?:${AXIS_X})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${AXIS_Y})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${AXIS_Y})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${AXIS_X})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${BLOCK})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${INLINE})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${INLINE})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${BLOCK})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${S_E})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${S_E})\\\\s+(?:${DIM_LEN_PCT})`\n].join('|');\nconst RAD_EXTENT = '(?:clos|farth)est-(?:corner|side)';\nconst RAD_SIZE = [\n  `${RAD_EXTENT}(?:\\\\s+${RAD_EXTENT})?`,\n  `${DIM_LEN_POSI}`,\n  `(?:${DIM_LEN_PCT_POSI})\\\\s+(?:${DIM_LEN_PCT_POSI})`\n].join('|');\nconst RAD_SHAPE = 'circle|ellipse';\nconst FROM_ANGLE = `from\\\\s+${DIM_ANGLE}`;\nconst AT_POSITION = `at\\\\s+(?:${POS_1}|${POS_2}|${POS_4})`;\nconst TO_SIDE_CORNER = `to\\\\s+(?:(?:${L_R})(?:\\\\s(?:${T_B}))?|(?:${T_B})(?:\\\\s(?:${L_R}))?)`;\nconst IN_COLOR_SPACE = `in\\\\s+(?:${CS_RECT}|${CS_HUE})`;\n\n/* type definitions */\n/**\n * @type ColorStopList - list of color stops\n */\ntype ColorStopList = [string, string, ...string[]];\n\n/**\n * @typedef Gradient - parsed CSS gradient\n * @property value - input value\n * @property type - gradient type\n * @property [gradientLine] - gradient line\n * @property colorStopList - list of color stops\n */\ninterface Gradient {\n  value: string;\n  type: string;\n  gradientLine?: string;\n  colorStopList: ColorStopList;\n}\n\n/* regexp */\nconst REG_GRAD = /^(?:repeating-)?(?:conic|linear|radial)-gradient\\(/;\nconst REG_GRAD_CAPT = /^((?:repeating-)?(?:conic|linear|radial)-gradient)\\(/;\n\n/**\n * get gradient type\n * @param value - gradient value\n * @returns gradient type\n */\nexport const getGradientType = (value: string): string => {\n  if (isString(value)) {\n    value = value.trim();\n    if (REG_GRAD.test(value)) {\n      const [, type] = value.match(REG_GRAD_CAPT) as MatchedRegExp;\n      return type;\n    }\n  }\n  return '';\n};\n\n/**\n * validate gradient line\n * @param value - gradient line value\n * @param type - gradient type\n * @returns result\n */\nexport const validateGradientLine = (value: string, type: string): boolean => {\n  if (isString(value) && isString(type)) {\n    value = value.trim();\n    type = type.trim();\n    let lineSyntax = '';\n    if (/^(?:repeating-)?linear-gradient$/.test(type)) {\n      /*\n       * <linear-gradient-line> = [\n       *   [ <angle> | to <side-or-corner> ] ||\n       *   <color-interpolation-method>\n       * ]\n       */\n      lineSyntax = [\n        `(?:${DIM_ANGLE}|${TO_SIDE_CORNER})(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+(?:${DIM_ANGLE}|${TO_SIDE_CORNER}))?`\n      ].join('|');\n    } else if (/^(?:repeating-)?radial-gradient$/.test(type)) {\n      /*\n       * <radial-gradient-line> = [\n       *   [ [ <radial-shape> || <radial-size> ]? [ at <position> ]? ] ||\n       *   <color-interpolation-method>]?\n       */\n      lineSyntax = [\n        `(?:${RAD_SHAPE})(?:\\\\s+(?:${RAD_SIZE}))?(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `(?:${RAD_SIZE})(?:\\\\s+(?:${RAD_SHAPE}))?(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${AT_POSITION}(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${RAD_SHAPE})(?:\\\\s+(?:${RAD_SIZE}))?(?:\\\\s+${AT_POSITION})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${RAD_SIZE})(?:\\\\s+(?:${RAD_SHAPE}))?(?:\\\\s+${AT_POSITION})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${AT_POSITION})?`\n      ].join('|');\n    } else if (/^(?:repeating-)?conic-gradient$/.test(type)) {\n      /*\n       * <conic-gradient-line> = [\n       *   [ [ from <angle> ]? [ at <position> ]? ] ||\n       *   <color-interpolation-method>\n       * ]\n       */\n      lineSyntax = [\n        `${FROM_ANGLE}(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${AT_POSITION}(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${FROM_ANGLE})?(?:\\\\s+${AT_POSITION})?`\n      ].join('|');\n    }\n    if (lineSyntax) {\n      const reg = new RegExp(`^(?:${lineSyntax})$`);\n      return reg.test(value);\n    }\n  }\n  return false;\n};\n\n/**\n * validate color stop list\n * @param list\n * @param type\n * @param [opt]\n * @returns result\n */\nexport const validateColorStopList = (\n  list: string[],\n  type: string,\n  opt: Options = {}\n): boolean => {\n  if (Array.isArray(list) && list.length > 1) {\n    const dimension = /^(?:repeating-)?conic-gradient$/.test(type)\n      ? DIM_ANGLE_PCT\n      : DIM_LEN_PCT;\n    const regColorHint = new RegExp(`^(?:${dimension})$`);\n    const regDimension = new RegExp(`(?:\\\\s+(?:${dimension})){1,2}$`);\n    const arr = [];\n    for (const item of list) {\n      if (isString(item)) {\n        if (regColorHint.test(item)) {\n          arr.push('hint');\n        } else {\n          const color = item.replace(regDimension, '');\n          if (isColor(color, opt)) {\n            arr.push('color');\n          } else {\n            return false;\n          }\n        }\n      }\n    }\n    const value = arr.join(',');\n    return /^color(?:,(?:hint,)?color)+$/.test(value);\n  }\n  return false;\n};\n\n/**\n * parse CSS gradient\n * @param value - gradient value\n * @param [opt] - options\n * @returns parsed result\n */\nexport const parseGradient = (\n  value: string,\n  opt: Options = {}\n): Gradient | null => {\n  if (isString(value)) {\n    value = value.trim();\n    const cacheKey: string = createCacheKey(\n      {\n        namespace: NAMESPACE,\n        name: 'parseGradient',\n        value\n      },\n      opt\n    );\n    const cachedResult = getCache(cacheKey);\n    if (cachedResult instanceof CacheItem) {\n      if (cachedResult.isNull) {\n        return null;\n      }\n      return cachedResult.item as Gradient;\n    }\n    const type = getGradientType(value);\n    const gradValue = value.replace(REG_GRAD, '').replace(/\\)$/, '');\n    if (type && gradValue) {\n      const [lineOrColorStop = '', ...colorStops] = splitValue(gradValue, {\n        delimiter: ','\n      });\n      const dimension = /^(?:repeating-)?conic-gradient$/.test(type)\n        ? DIM_ANGLE_PCT\n        : DIM_LEN_PCT;\n      const regDimension = new RegExp(`(?:\\\\s+(?:${dimension})){1,2}$`);\n      let isColorStop = false;\n      if (regDimension.test(lineOrColorStop)) {\n        const colorStop = lineOrColorStop.replace(regDimension, '');\n        if (isColor(colorStop, opt)) {\n          isColorStop = true;\n        }\n      } else if (isColor(lineOrColorStop, opt)) {\n        isColorStop = true;\n      }\n      if (isColorStop) {\n        colorStops.unshift(lineOrColorStop);\n        const valid = validateColorStopList(colorStops, type, opt);\n        if (valid) {\n          const res: Gradient = {\n            value,\n            type,\n            colorStopList: colorStops as ColorStopList\n          };\n          setCache(cacheKey, res);\n          return res;\n        }\n      } else if (colorStops.length > 1) {\n        const gradientLine = lineOrColorStop;\n        const valid =\n          validateGradientLine(gradientLine, type) &&\n          validateColorStopList(colorStops, type, opt);\n        if (valid) {\n          const res: Gradient = {\n            value,\n            type,\n            gradientLine,\n            colorStopList: colorStops as ColorStopList\n          };\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    }\n    setCache(cacheKey, null);\n    return null;\n  }\n  return null;\n};\n\n/**\n * is CSS gradient\n * @param value - CSS value\n * @param [opt] - options\n * @returns result\n */\nexport const isGradient = (value: string, opt: Options = {}): boolean => {\n  const gradient = parseGradient(value, opt);\n  return gradient !== null;\n};\n"], "names": [], "mappings": ";;;;AAmBA,MAAM,YAAY;AAClB,MAAM,YAAY,GAAG,GAAG,MAAM,KAAK;AACnC,MAAM,gBAAgB,GAAG,SAAS,IAAI,GAAG;AACzC,MAAM,UAAU,GAAG,GAAG,MAAM,MAAM;AAClC,MAAM,cAAc,GAAG,OAAO,IAAI,GAAG;AACrC,MAAM,mBAAmB,GAAG,YAAY,MAAM,MAAM;AACpD,MAAM,eAAe,GAAG,YAAY,MAAM,MAAM;AAChD,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,SAAS,GAAG,GAAG,SAAS,GAAG;AACjC,MAAM,SAAS,GAAG,GAAG,SAAS,GAAG;AACjC,MAAM,QAAQ,YAAY,GAAG;AAC7B,MAAM,SAAS,aAAa,GAAG;AAC/B,MAAM,QAAQ,GAAG,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,WAAW;AAC1E,MAAM,QAAQ;AAAA,EACZ,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM;AAAA,EAC3C,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM;AAAA,EAC3C,MAAM,GAAG,IAAI,MAAM,IAAI,WAAW,WAAW,GAAG,IAAI,MAAM,IAAI,WAAW;AAAA,EACzE,MAAM,GAAG,IAAI,KAAK,WAAW,GAAG,IAAI,MAAM;AAAA,EAC1C,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,KAAK;AAAA,EAC1C,MAAM,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG;AACvC,EAAE,KAAK,GAAG;AACV,MAAM,QAAQ;AAAA,EACZ,MAAM,MAAM,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACzE,MAAM,MAAM,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACzE,MAAM,KAAK,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACxE,MAAM,MAAM,WAAW,WAAW,WAAW,KAAK,WAAW,WAAW;AAAA,EACxE,MAAM,GAAG,WAAW,WAAW,WAAW,GAAG,WAAW,WAAW;AACrE,EAAE,KAAK,GAAG;AACV,MAAM,aAAa;AACnB,MAAM,WAAW;AAAA,EACf,GAAG,UAAU,UAAU,UAAU;AAAA,EACjC,GAAG,YAAY;AAAA,EACf,MAAM,gBAAgB,WAAW,gBAAgB;AACnD,EAAE,KAAK,GAAG;AACV,MAAM,YAAY;AAClB,MAAM,aAAa,WAAW,SAAS;AACvC,MAAM,cAAc,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK;AACvD,MAAM,iBAAiB,eAAe,GAAG,aAAa,GAAG,UAAU,GAAG,aAAa,GAAG;AACtF,MAAM,iBAAiB,YAAY,OAAO,IAAI,MAAM;AAuBpD,MAAM,WAAW;AACjB,MAAM,gBAAgB;AAOT,MAAA,kBAAkB,CAAC,UAA0B;AACpD,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACf,QAAA,SAAS,KAAK,KAAK,GAAG;AACxB,YAAM,CAAG,EAAA,IAAI,IAAI,MAAM,MAAM,aAAa;AACnC,aAAA;AAAA,IAAA;AAAA,EACT;AAEK,SAAA;AACT;AAQa,MAAA,uBAAuB,CAAC,OAAe,SAA0B;AAC5E,MAAI,SAAS,KAAK,KAAK,SAAS,IAAI,GAAG;AACrC,YAAQ,MAAM,KAAK;AACnB,WAAO,KAAK,KAAK;AACjB,QAAI,aAAa;AACb,QAAA,mCAAmC,KAAK,IAAI,GAAG;AAOpC,mBAAA;AAAA,QACX,MAAM,SAAS,IAAI,cAAc,WAAW,cAAc;AAAA,QAC1D,GAAG,cAAc,aAAa,SAAS,IAAI,cAAc;AAAA,MAAA,EACzD,KAAK,GAAG;AAAA,IACD,WAAA,mCAAmC,KAAK,IAAI,GAAG;AAM3C,mBAAA;AAAA,QACX,MAAM,SAAS,cAAc,QAAQ,aAAa,WAAW,YAAY,cAAc;AAAA,QACvF,MAAM,QAAQ,cAAc,SAAS,aAAa,WAAW,YAAY,cAAc;AAAA,QACvF,GAAG,WAAW,UAAU,cAAc;AAAA,QACtC,GAAG,cAAc,UAAU,SAAS,cAAc,QAAQ,aAAa,WAAW;AAAA,QAClF,GAAG,cAAc,UAAU,QAAQ,cAAc,SAAS,aAAa,WAAW;AAAA,QAClF,GAAG,cAAc,UAAU,WAAW;AAAA,MAAA,EACtC,KAAK,GAAG;AAAA,IACD,WAAA,kCAAkC,KAAK,IAAI,GAAG;AAO1C,mBAAA;AAAA,QACX,GAAG,UAAU,UAAU,WAAW,YAAY,cAAc;AAAA,QAC5D,GAAG,WAAW,UAAU,cAAc;AAAA,QACtC,GAAG,cAAc,UAAU,UAAU,YAAY,WAAW;AAAA,MAAA,EAC5D,KAAK,GAAG;AAAA,IAAA;AAEZ,QAAI,YAAY;AACd,YAAM,MAAM,IAAI,OAAO,OAAO,UAAU,IAAI;AACrC,aAAA,IAAI,KAAK,KAAK;AAAA,IAAA;AAAA,EACvB;AAEK,SAAA;AACT;AASO,MAAM,wBAAwB,CACnC,MACA,MACA,MAAe,CAAA,MACH;AACZ,MAAI,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AAC1C,UAAM,YAAY,kCAAkC,KAAK,IAAI,IACzD,gBACA;AACJ,UAAM,eAAe,IAAI,OAAO,OAAO,SAAS,IAAI;AACpD,UAAM,eAAe,IAAI,OAAO,aAAa,SAAS,UAAU;AAChE,UAAM,MAAM,CAAC;AACb,eAAW,QAAQ,MAAM;AACnB,UAAA,SAAS,IAAI,GAAG;AACd,YAAA,aAAa,KAAK,IAAI,GAAG;AAC3B,cAAI,KAAK,MAAM;AAAA,QAAA,OACV;AACL,gBAAM,QAAQ,KAAK,QAAQ,cAAc,EAAE;AACvC,cAAA,QAAQ,OAAO,GAAG,GAAG;AACvB,gBAAI,KAAK,OAAO;AAAA,UAAA,OACX;AACE,mBAAA;AAAA,UAAA;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEI,UAAA,QAAQ,IAAI,KAAK,GAAG;AACnB,WAAA,+BAA+B,KAAK,KAAK;AAAA,EAAA;AAE3C,SAAA;AACT;AAQO,MAAM,gBAAgB,CAC3B,OACA,MAAe,OACK;AAChB,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,UAAM,WAAmB;AAAA,MACvB;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACM,UAAA,eAAe,SAAS,QAAQ;AACtC,QAAI,wBAAwB,WAAW;AACrC,UAAI,aAAa,QAAQ;AAChB,eAAA;AAAA,MAAA;AAET,aAAO,aAAa;AAAA,IAAA;AAEhB,UAAA,OAAO,gBAAgB,KAAK;AAC5B,UAAA,YAAY,MAAM,QAAQ,UAAU,EAAE,EAAE,QAAQ,OAAO,EAAE;AAC/D,QAAI,QAAQ,WAAW;AACrB,YAAM,CAAC,kBAAkB,IAAI,GAAG,UAAU,IAAI,WAAW,WAAW;AAAA,QAClE,WAAW;AAAA,MAAA,CACZ;AACD,YAAM,YAAY,kCAAkC,KAAK,IAAI,IACzD,gBACA;AACJ,YAAM,eAAe,IAAI,OAAO,aAAa,SAAS,UAAU;AAChE,UAAI,cAAc;AACd,UAAA,aAAa,KAAK,eAAe,GAAG;AACtC,cAAM,YAAY,gBAAgB,QAAQ,cAAc,EAAE;AACtD,YAAA,QAAQ,WAAW,GAAG,GAAG;AACb,wBAAA;AAAA,QAAA;AAAA,MAEP,WAAA,QAAQ,iBAAiB,GAAG,GAAG;AAC1B,sBAAA;AAAA,MAAA;AAEhB,UAAI,aAAa;AACf,mBAAW,QAAQ,eAAe;AAClC,cAAM,QAAQ,sBAAsB,YAAY,MAAM,GAAG;AACzD,YAAI,OAAO;AACT,gBAAM,MAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UACjB;AACA,mBAAS,UAAU,GAAG;AACf,iBAAA;AAAA,QAAA;AAAA,MACT,WACS,WAAW,SAAS,GAAG;AAChC,cAAM,eAAe;AACf,cAAA,QACJ,qBAAqB,cAAc,IAAI,KACvC,sBAAsB,YAAY,MAAM,GAAG;AAC7C,YAAI,OAAO;AACT,gBAAM,MAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UACjB;AACA,mBAAS,UAAU,GAAG;AACf,iBAAA;AAAA,QAAA;AAAA,MACT;AAAA,IACF;AAEF,aAAS,UAAU,IAAI;AAChB,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAgB;AACjE,QAAA,WAAW,cAAc,OAAO,GAAG;AACzC,SAAO,aAAa;AACtB;"}