import React from "react";
import PropTypes from "prop-types";
import addScrollspy from "@/utils/addScrollSpy";
import { init_classic_menu_resize } from "@/utils/menuToggle";
import { scrollToElement } from "@/utils/scrollToElement";
import { closeMobileMenu } from "@/utils/toggleMobileMenu";
import { Link, useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { productsSubmenu } from "@/data/menu";

export default function Nav({ links, animateY = false }) {
  const { t: translate } = useTranslation();
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const { pathname } = useLocation();

  useEffect(() => {
    setTimeout(() => {
      scrollToElement();
    }, 1000);
    init_classic_menu_resize();
    window.addEventListener("scroll", addScrollspy);
    window.addEventListener("resize", init_classic_menu_resize);

    // Close products dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (!event.target.closest(".products-menu")) {
        setIsProductsOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);

    return () => {
      window.removeEventListener("scroll", addScrollspy);
      window.removeEventListener("resize", init_classic_menu_resize);
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const handleProductsClick = (e) => {
    // Only handle clicks on mobile
    if (window.innerWidth <= 1024) {
      e.preventDefault();
      setIsProductsOpen(!isProductsOpen);
    }
  };

  return (
    <>
      {links[0]?.href?.includes("/") &&
        links.map((link, index) => {
          // Special handling for Products menu item
          if (link.text === "Products") {
            return (
              <li
                key={index}
                className={`products-menu ${isProductsOpen ? "js-opened" : ""}`}
              >
                <a
                  href="#"
                  onClick={handleProductsClick}
                  className={`mn-has-sub ${
                    pathname.startsWith("/products") ? "active" : ""
                  }`}
                >
                  {animateY ? (
                    <span className="btn-animate-y">
                      <span className="btn-animate-y-1">
                        {translate(`menu.${link.text.toLowerCase()}`)}
                      </span>
                      <span className="btn-animate-y-2" aria-hidden="true">
                        {translate(`menu.${link.text.toLowerCase()}`)}
                      </span>
                    </span>
                  ) : (
                    <>
                      {translate(`menu.${link.text.toLowerCase()}`)}
                      <i
                        className={`mi-chevron-down ${
                          isProductsOpen ? "rotate-180" : ""
                        }`}
                      />
                    </>
                  )}
                </a>
                <ul
                  className={`mn-sub ${
                    isProductsOpen ? "mobile-sub-active" : ""
                  }`}
                >
                  <li>
                    <Link
                      to={productsSubmenu.bms.href}
                      onClick={() => setIsProductsOpen(false)}
                    >
                      {translate(productsSubmenu.bms.title)} -{" "}
                      {translate("products.bms.overview")}
                    </Link>
                  </li>
                  <li className="mn-sub-multi">
                    <span className="mn-group-title">
                      {translate("products.bms.modules")}
                    </span>
                    <ul>
                      {productsSubmenu.bms.modules.map((module, index) => (
                        <li key={index}>
                          <Link
                            to={module.href}
                            onClick={() => setIsProductsOpen(false)}
                          >
                            {translate(module.text)}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </li>
                </ul>
              </li>
            );
          }

          // Regular menu items
          return (
            <li key={index}>
              <Link
                className={
                  pathname.split("/")[1] === link.href.split("/")[1]
                    ? "active"
                    : ""
                }
                to={link.href}
              >
                {animateY ? (
                  <span className="btn-animate-y">
                    <span className="btn-animate-y-1">
                      {translate(`menu.${link.text.toLowerCase()}`)}
                    </span>
                    <span className="btn-animate-y-2" aria-hidden="true">
                      {translate(`menu.${link.text.toLowerCase()}`)}
                    </span>
                  </span>
                ) : (
                  translate(`menu.${link.text.toLowerCase()}`)
                )}
              </Link>
            </li>
          );
        })}
      {!links[0]?.href?.includes("/") &&
        links.map((link, index) => (
          <li className="scrollspy-link" key={index}>
            <a onClick={() => closeMobileMenu()} className="" href={link.href}>
              {animateY ? (
                <span className="btn-animate-y">
                  <span className="btn-animate-y-1">
                    {translate(`menu.${link.text.toLowerCase()}`)}
                  </span>
                  <span className="btn-animate-y-2" aria-hidden="true">
                    {translate(`menu.${link.text.toLowerCase()}`)}
                  </span>
                </span>
              ) : (
                translate(`menu.${link.text.toLowerCase()}`)
              )}
            </a>
          </li>
        ))}
    </>
  );
}

Nav.propTypes = {
  links: PropTypes.arrayOf(
    PropTypes.shape({
      href: PropTypes.string.isRequired,
      text: PropTypes.string.isRequired,
    })
  ).isRequired,
  animateY: PropTypes.bool,
};
