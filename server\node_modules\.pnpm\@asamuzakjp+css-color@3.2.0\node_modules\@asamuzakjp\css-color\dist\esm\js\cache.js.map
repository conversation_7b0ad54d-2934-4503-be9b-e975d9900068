{"version": 3, "file": "cache.js", "sources": ["../../../src/js/cache.ts"], "sourcesContent": ["/**\n * cache\n */\n\nimport { LRUCache } from 'lru-cache';\nimport { Options } from './typedef';\nimport { valueToJsonString } from './util';\n\n/* numeric constants */\nconst MAX_CACHE = 4096;\n\n/**\n * CacheItem\n */\nexport class CacheItem {\n  /* private */\n  #isNull: boolean;\n  #item: unknown;\n\n  /**\n   * constructor\n   */\n  constructor(item: unknown, isNull: boolean = false) {\n    this.#item = item;\n    this.#isNull = !!isNull;\n  }\n\n  get item() {\n    return this.#item;\n  }\n\n  get isNull() {\n    return this.#isNull;\n  }\n}\n\n/**\n * NullObject\n */\nexport class NullObject extends CacheItem {\n  /**\n   * constructor\n   */\n  constructor() {\n    super(Symbol('null'), true);\n  }\n}\n\n/*\n * lru cache\n */\nexport const lruCache = new LRUCache({\n  max: MAX_CACHE\n});\n\n/**\n * set cache\n * @param key - cache key\n * @param value - value to cache\n * @returns void\n */\nexport const setCache = (key: string, value: unknown): void => {\n  if (key) {\n    if (value === null) {\n      lruCache.set(key, new NullObject());\n    } else if (value instanceof CacheItem) {\n      lruCache.set(key, value);\n    } else {\n      lruCache.set(key, new CacheItem(value));\n    }\n  }\n};\n\n/**\n * get cache\n * @param key - cache key\n * @returns cached item or false otherwise\n */\nexport const getCache = (key: string): CacheItem | boolean => {\n  if (key && lruCache.has(key)) {\n    const item = lruCache.get(key);\n    if (item instanceof CacheItem) {\n      return item;\n    }\n    // delete unexpected cached item\n    lruCache.delete(key);\n    return false;\n  }\n  return false;\n};\n\n/**\n * create cache key\n * @param keyData - key data\n * @param [opt] - options\n * @returns cache key\n */\nexport const createCacheKey = (\n  keyData: Record<string, string>,\n  opt: Options = {}\n): string => {\n  const { customProperty = {}, dimension = {} } = opt;\n  let cacheKey = '';\n  if (\n    keyData &&\n    Object.keys(keyData).length &&\n    typeof customProperty.callback !== 'function' &&\n    typeof dimension.callback !== 'function'\n  ) {\n    keyData.opt = valueToJsonString(opt);\n    cacheKey = valueToJsonString(keyData);\n  }\n  return cacheKey;\n};\n"], "names": [], "mappings": ";;;;;;;;;;AASA,MAAM,YAAY;AAKX,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA,EAQrB,YAAY,MAAe,SAAkB,OAAO;AANpD;AAAA;AACA;AAME,uBAAK,OAAQ;AACR,uBAAA,SAAU,CAAC,CAAC;AAAA,EAAA;AAAA,EAGnB,IAAI,OAAO;AACT,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAEhB;AAlBE;AACA;AAsBK,MAAM,mBAAmB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIxC,cAAc;AACN,UAAA,OAAO,MAAM,GAAG,IAAI;AAAA,EAAA;AAE9B;AAKa,MAAA,WAAW,IAAI,SAAS;AAAA,EACnC,KAAK;AACP,CAAC;AAQY,MAAA,WAAW,CAAC,KAAa,UAAyB;AAC7D,MAAI,KAAK;AACP,QAAI,UAAU,MAAM;AAClB,eAAS,IAAI,KAAK,IAAI,WAAA,CAAY;AAAA,IAAA,WACzB,iBAAiB,WAAW;AAC5B,eAAA,IAAI,KAAK,KAAK;AAAA,IAAA,OAClB;AACL,eAAS,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC;AAAA,IAAA;AAAA,EACxC;AAEJ;AAOa,MAAA,WAAW,CAAC,QAAqC;AAC5D,MAAI,OAAO,SAAS,IAAI,GAAG,GAAG;AACtB,UAAA,OAAO,SAAS,IAAI,GAAG;AAC7B,QAAI,gBAAgB,WAAW;AACtB,aAAA;AAAA,IAAA;AAGT,aAAS,OAAO,GAAG;AACZ,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AAQO,MAAM,iBAAiB,CAC5B,SACA,MAAe,OACJ;AACX,QAAM,EAAE,iBAAiB,CAAA,GAAI,YAAY,CAAA,EAAO,IAAA;AAChD,MAAI,WAAW;AACf,MACE,WACA,OAAO,KAAK,OAAO,EAAE,UACrB,OAAO,eAAe,aAAa,cACnC,OAAO,UAAU,aAAa,YAC9B;AACQ,YAAA,MAAM,kBAAkB,GAAG;AACnC,eAAW,kBAAkB,OAAO;AAAA,EAAA;AAE/B,SAAA;AACT;"}