import{n as Ne,o as De}from"./vendor-misc-C-eNx6BD.js";/*!
  * PhotoSwipe 5.4.4 - https://photoswipe.com
  * (c) 2024 D<PERSON><PERSON>ov
  */function v(s,t,e){const i=document.createElement(t);return s&&(i.className=s),e&&e.appendChild(i),i}function f(s,t){return s.x=t.x,s.y=t.y,t.id!==void 0&&(s.id=t.id),s}function $e(s){s.x=Math.round(s.x),s.y=Math.round(s.y)}function Zt(s,t){const e=Math.abs(s.x-t.x),i=Math.abs(s.y-t.y);return Math.sqrt(e*e+i*i)}function nt(s,t){return s.x===t.x&&s.y===t.y}function rt(s,t,e){return Math.min(Math.max(s,t),e)}function ot(s,t,e){let i=`translate3d(${s}px,${t||0}px,0)`;return e!==void 0&&(i+=` scale3d(${e},${e},1)`),i}function z(s,t,e,i){s.style.transform=ot(t,e,i)}const gi="cubic-bezier(.4,0,.22,1)";function xe(s,t,e,i){s.style.transition=t?`${t} ${e}ms ${i||gi}`:"none"}function Bt(s,t,e){s.style.width=typeof t=="number"?`${t}px`:t,s.style.height=typeof e=="number"?`${e}px`:e}function Ei(s){xe(s)}function vi(s){return"decode"in s?s.decode().catch(()=>{}):s.complete?Promise.resolve(s):new Promise((t,e)=>{s.onload=()=>t(s),s.onerror=e})}const y={IDLE:"idle",LOADING:"loading",LOADED:"loaded",ERROR:"error"};function yi(s){return"button"in s&&s.button===1||s.ctrlKey||s.metaKey||s.altKey||s.shiftKey}function Ai(s,t,e=document){let i=[];if(s instanceof Element)i=[s];else if(s instanceof NodeList||Array.isArray(s))i=Array.from(s);else{const n=typeof s=="string"?s:t;n&&(i=Array.from(e.querySelectorAll(n)))}return i}function Qt(){return!!(navigator.vendor&&navigator.vendor.match(/apple/i))}let Me=!1;try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>{Me=!0}}))}catch{}class Ti{constructor(){this._pool=[]}add(t,e,i,n){this._toggleListener(t,e,i,n)}remove(t,e,i,n){this._toggleListener(t,e,i,n,!0)}removeAll(){this._pool.forEach(t=>{this._toggleListener(t.target,t.type,t.listener,t.passive,!0,!0)}),this._pool=[]}_toggleListener(t,e,i,n,o,r){if(!t)return;const a=o?"removeEventListener":"addEventListener";e.split(" ").forEach(c=>{if(c){r||(o?this._pool=this._pool.filter(p=>p.type!==c||p.listener!==i||p.target!==t):this._pool.push({target:t,type:c,listener:i,passive:n}));const d=Me?{passive:n||!1}:!1;t[a](c,i,d)}})}}function Re(s,t){if(s.getViewportSizeFn){const e=s.getViewportSizeFn(s,t);if(e)return e}return{x:document.documentElement.clientWidth,y:window.innerHeight}}function st(s,t,e,i,n){let o=0;if(t.paddingFn)o=t.paddingFn(e,i,n)[s];else if(t.padding)o=t.padding[s];else{const r="padding"+s[0].toUpperCase()+s.slice(1);t[r]&&(o=t[r])}return Number(o)||0}function ze(s,t,e,i){return{x:t.x-st("left",s,t,e,i)-st("right",s,t,e,i),y:t.y-st("top",s,t,e,i)-st("bottom",s,t,e,i)}}class bi{constructor(t){this.slide=t,this.currZoomLevel=1,this.center={x:0,y:0},this.max={x:0,y:0},this.min={x:0,y:0}}update(t){this.currZoomLevel=t,this.slide.width?(this._updateAxis("x"),this._updateAxis("y"),this.slide.pswp.dispatch("calcBounds",{slide:this.slide})):this.reset()}_updateAxis(t){const{pswp:e}=this.slide,i=this.slide[t==="x"?"width":"height"]*this.currZoomLevel,o=st(t==="x"?"left":"top",e.options,e.viewportSize,this.slide.data,this.slide.index),r=this.slide.panAreaSize[t];this.center[t]=Math.round((r-i)/2)+o,this.max[t]=i>r?Math.round(r-i)+o:this.center[t],this.min[t]=i>r?o:this.center[t]}reset(){this.center.x=0,this.center.y=0,this.max.x=0,this.max.y=0,this.min.x=0,this.min.y=0}correctPan(t,e){return rt(e,this.max[t],this.min[t])}}const Jt=4e3;class ke{constructor(t,e,i,n){this.pswp=n,this.options=t,this.itemData=e,this.index=i,this.panAreaSize=null,this.elementSize=null,this.fit=1,this.fill=1,this.vFill=1,this.initial=1,this.secondary=1,this.max=1,this.min=1}update(t,e,i){const n={x:t,y:e};this.elementSize=n,this.panAreaSize=i;const o=i.x/n.x,r=i.y/n.y;this.fit=Math.min(1,o<r?o:r),this.fill=Math.min(1,o>r?o:r),this.vFill=Math.min(1,r),this.initial=this._getInitial(),this.secondary=this._getSecondary(),this.max=Math.max(this.initial,this.secondary,this._getMax()),this.min=Math.min(this.fit,this.initial,this.secondary),this.pswp&&this.pswp.dispatch("zoomLevelsUpdate",{zoomLevels:this,slideData:this.itemData})}_parseZoomLevelOption(t){const e=t+"ZoomLevel",i=this.options[e];if(i)return typeof i=="function"?i(this):i==="fill"?this.fill:i==="fit"?this.fit:Number(i)}_getSecondary(){let t=this._parseZoomLevelOption("secondary");return t||(t=Math.min(1,this.fit*3),this.elementSize&&t*this.elementSize.x>Jt&&(t=Jt/this.elementSize.x),t)}_getInitial(){return this._parseZoomLevelOption("initial")||this.fit}_getMax(){return this._parseZoomLevelOption("max")||Math.max(1,this.fit*4)}}class Si{constructor(t,e,i){this.data=t,this.index=e,this.pswp=i,this.isActive=e===i.currIndex,this.currentResolution=0,this.panAreaSize={x:0,y:0},this.pan={x:0,y:0},this.isFirstSlide=this.isActive&&!i.opener.isOpen,this.zoomLevels=new ke(i.options,t,e,i),this.pswp.dispatch("gettingData",{slide:this,data:this.data,index:e}),this.content=this.pswp.contentLoader.getContentBySlide(this),this.container=v("pswp__zoom-wrap","div"),this.holderElement=null,this.currZoomLevel=1,this.width=this.content.width,this.height=this.content.height,this.heavyAppended=!1,this.bounds=new bi(this),this.prevDisplayedWidth=-1,this.prevDisplayedHeight=-1,this.pswp.dispatch("slideInit",{slide:this})}setIsActive(t){t&&!this.isActive?this.activate():!t&&this.isActive&&this.deactivate()}append(t){this.holderElement=t,this.container.style.transformOrigin="0 0",this.data&&(this.calculateSize(),this.load(),this.updateContentSize(),this.appendHeavy(),this.holderElement.appendChild(this.container),this.zoomAndPanToInitial(),this.pswp.dispatch("firstZoomPan",{slide:this}),this.applyCurrentZoomPan(),this.pswp.dispatch("afterSetContent",{slide:this}),this.isActive&&this.activate())}load(){this.content.load(!1),this.pswp.dispatch("slideLoad",{slide:this})}appendHeavy(){const{pswp:t}=this;this.heavyAppended||!t.opener.isOpen||t.mainScroll.isShifted()||!this.isActive&&!1||this.pswp.dispatch("appendHeavy",{slide:this}).defaultPrevented||(this.heavyAppended=!0,this.content.append(),this.pswp.dispatch("appendHeavyContent",{slide:this}))}activate(){this.isActive=!0,this.appendHeavy(),this.content.activate(),this.pswp.dispatch("slideActivate",{slide:this})}deactivate(){this.isActive=!1,this.content.deactivate(),this.currZoomLevel!==this.zoomLevels.initial&&this.calculateSize(),this.currentResolution=0,this.zoomAndPanToInitial(),this.applyCurrentZoomPan(),this.updateContentSize(),this.pswp.dispatch("slideDeactivate",{slide:this})}destroy(){this.content.hasSlide=!1,this.content.remove(),this.container.remove(),this.pswp.dispatch("slideDestroy",{slide:this})}resize(){this.currZoomLevel===this.zoomLevels.initial||!this.isActive?(this.calculateSize(),this.currentResolution=0,this.zoomAndPanToInitial(),this.applyCurrentZoomPan(),this.updateContentSize()):(this.calculateSize(),this.bounds.update(this.currZoomLevel),this.panTo(this.pan.x,this.pan.y))}updateContentSize(t){const e=this.currentResolution||this.zoomLevels.initial;if(!e)return;const i=Math.round(this.width*e)||this.pswp.viewportSize.x,n=Math.round(this.height*e)||this.pswp.viewportSize.y;!this.sizeChanged(i,n)&&!t||this.content.setDisplayedSize(i,n)}sizeChanged(t,e){return t!==this.prevDisplayedWidth||e!==this.prevDisplayedHeight?(this.prevDisplayedWidth=t,this.prevDisplayedHeight=e,!0):!1}getPlaceholderElement(){var t;return(t=this.content.placeholder)===null||t===void 0?void 0:t.element}zoomTo(t,e,i,n){const{pswp:o}=this;if(!this.isZoomable()||o.mainScroll.isShifted())return;o.dispatch("beforeZoomTo",{destZoomLevel:t,centerPoint:e,transitionDuration:i}),o.animations.stopAllPan();const r=this.currZoomLevel;n||(t=rt(t,this.zoomLevels.min,this.zoomLevels.max)),this.setZoomLevel(t),this.pan.x=this.calculateZoomToPanOffset("x",e,r),this.pan.y=this.calculateZoomToPanOffset("y",e,r),$e(this.pan);const a=()=>{this._setResolution(t),this.applyCurrentZoomPan()};i?o.animations.startTransition({isPan:!0,name:"zoomTo",target:this.container,transform:this.getCurrentTransform(),onComplete:a,duration:i,easing:o.options.easing}):a()}toggleZoom(t){this.zoomTo(this.currZoomLevel===this.zoomLevels.initial?this.zoomLevels.secondary:this.zoomLevels.initial,t,this.pswp.options.zoomAnimationDuration)}setZoomLevel(t){this.currZoomLevel=t,this.bounds.update(this.currZoomLevel)}calculateZoomToPanOffset(t,e,i){if(this.bounds.max[t]-this.bounds.min[t]===0)return this.bounds.center[t];e||(e=this.pswp.getViewportCenterPoint()),i||(i=this.zoomLevels.initial);const o=this.currZoomLevel/i;return this.bounds.correctPan(t,(this.pan[t]-e[t])*o+e[t])}panTo(t,e){this.pan.x=this.bounds.correctPan("x",t),this.pan.y=this.bounds.correctPan("y",e),this.applyCurrentZoomPan()}isPannable(){return!!this.width&&this.currZoomLevel>this.zoomLevels.fit}isZoomable(){return!!this.width&&this.content.isZoomable()}applyCurrentZoomPan(){this._applyZoomTransform(this.pan.x,this.pan.y,this.currZoomLevel),this===this.pswp.currSlide&&this.pswp.dispatch("zoomPanUpdate",{slide:this})}zoomAndPanToInitial(){this.currZoomLevel=this.zoomLevels.initial,this.bounds.update(this.currZoomLevel),f(this.pan,this.bounds.center),this.pswp.dispatch("initialZoomPan",{slide:this})}_applyZoomTransform(t,e,i){i/=this.currentResolution||this.zoomLevels.initial,z(this.container,t,e,i)}calculateSize(){const{pswp:t}=this;f(this.panAreaSize,ze(t.options,t.viewportSize,this.data,this.index)),this.zoomLevels.update(this.width,this.height,this.panAreaSize),t.dispatch("calcSlideSize",{slide:this})}getCurrentTransform(){const t=this.currZoomLevel/(this.currentResolution||this.zoomLevels.initial);return ot(this.pan.x,this.pan.y,t)}_setResolution(t){t!==this.currentResolution&&(this.currentResolution=t,this.updateContentSize(),this.pswp.dispatch("resolutionChanged"))}}const wi=.35,Ci=.6,Li=.4,Ii=.5;function Oi(s,t){return s*t/(1-t)}class Pi{constructor(t){this.gestures=t,this.pswp=t.pswp,this.startPan={x:0,y:0}}start(){this.pswp.currSlide&&f(this.startPan,this.pswp.currSlide.pan),this.pswp.animations.stopAll()}change(){const{p1:t,prevP1:e,dragAxis:i}=this.gestures,{currSlide:n}=this.pswp;if(i==="y"&&this.pswp.options.closeOnVerticalDrag&&n&&n.currZoomLevel<=n.zoomLevels.fit&&!this.gestures.isMultitouch){const o=n.pan.y+(t.y-e.y);if(!this.pswp.dispatch("verticalDrag",{panY:o}).defaultPrevented){this._setPanWithFriction("y",o,Ci);const r=1-Math.abs(this._getVerticalDragRatio(n.pan.y));this.pswp.applyBgOpacity(r),n.applyCurrentZoomPan()}}else this._panOrMoveMainScroll("x")||(this._panOrMoveMainScroll("y"),n&&($e(n.pan),n.applyCurrentZoomPan()))}end(){const{velocity:t}=this.gestures,{mainScroll:e,currSlide:i}=this.pswp;let n=0;if(this.pswp.animations.stopAll(),e.isShifted()){const r=(e.x-e.getCurrSlideX())/this.pswp.viewportSize.x;t.x<-.5&&r<0||t.x<.1&&r<-.5?(n=1,t.x=Math.min(t.x,0)):(t.x>Ii&&r>0||t.x>-.1&&r>.5)&&(n=-1,t.x=Math.max(t.x,0)),e.moveIndexBy(n,!0,t.x)}i&&i.currZoomLevel>i.zoomLevels.max||this.gestures.isMultitouch?this.gestures.zoomLevels.correctZoomPan(!0):(this._finishPanGestureForAxis("x"),this._finishPanGestureForAxis("y"))}_finishPanGestureForAxis(t){const{velocity:e}=this.gestures,{currSlide:i}=this.pswp;if(!i)return;const{pan:n,bounds:o}=i,r=n[t],a=this.pswp.bgOpacity<1&&t==="y",c=r+Oi(e[t],.995);if(a){const C=this._getVerticalDragRatio(r),E=this._getVerticalDragRatio(c);if(C<0&&E<-.4||C>0&&E>Li){this.pswp.close();return}}const d=o.correctPan(t,c);if(r===d)return;const p=d===c?1:.82,_=this.pswp.bgOpacity,m=d-r;this.pswp.animations.startSpring({name:"panGesture"+t,isPan:!0,start:r,end:d,velocity:e[t],dampingRatio:p,onUpdate:C=>{if(a&&this.pswp.bgOpacity<1){const E=1-(d-C)/m;this.pswp.applyBgOpacity(rt(_+(1-_)*E,0,1))}n[t]=Math.floor(C),i.applyCurrentZoomPan()}})}_panOrMoveMainScroll(t){const{p1:e,dragAxis:i,prevP1:n,isMultitouch:o}=this.gestures,{currSlide:r,mainScroll:a}=this.pswp,h=e[t]-n[t],c=a.x+h;if(!h||!r)return!1;if(t==="x"&&!r.isPannable()&&!o)return a.moveTo(c,!0),!0;const{bounds:d}=r,p=r.pan[t]+h;if(this.pswp.options.allowPanToNext&&i==="x"&&t==="x"&&!o){const _=a.getCurrSlideX(),m=a.x-_,C=h>0,E=!C;if(p>d.min[t]&&C){if(d.min[t]<=this.startPan[t])return a.moveTo(c,!0),!0;this._setPanWithFriction(t,p)}else if(p<d.max[t]&&E){if(this.startPan[t]<=d.max[t])return a.moveTo(c,!0),!0;this._setPanWithFriction(t,p)}else if(m!==0){if(m>0)return a.moveTo(Math.max(c,_),!0),!0;if(m<0)return a.moveTo(Math.min(c,_),!0),!0}else this._setPanWithFriction(t,p)}else t==="y"?!a.isShifted()&&d.min.y!==d.max.y&&this._setPanWithFriction(t,p):this._setPanWithFriction(t,p);return!1}_getVerticalDragRatio(t){var e,i;return(t-((e=(i=this.pswp.currSlide)===null||i===void 0?void 0:i.bounds.center.y)!==null&&e!==void 0?e:0))/(this.pswp.viewportSize.y/3)}_setPanWithFriction(t,e,i){const{currSlide:n}=this.pswp;if(!n)return;const{pan:o,bounds:r}=n;if(r.correctPan(t,e)!==e||i){const h=Math.round(e-o[t]);o[t]+=h*(i||wi)}else o[t]=e}}const Ni=.05,Di=.15;function te(s,t,e){return s.x=(t.x+e.x)/2,s.y=(t.y+e.y)/2,s}class $i{constructor(t){this.gestures=t,this._startPan={x:0,y:0},this._startZoomPoint={x:0,y:0},this._zoomPoint={x:0,y:0},this._wasOverFitZoomLevel=!1,this._startZoomLevel=1}start(){const{currSlide:t}=this.gestures.pswp;t&&(this._startZoomLevel=t.currZoomLevel,f(this._startPan,t.pan)),this.gestures.pswp.animations.stopAllPan(),this._wasOverFitZoomLevel=!1}change(){const{p1:t,startP1:e,p2:i,startP2:n,pswp:o}=this.gestures,{currSlide:r}=o;if(!r)return;const a=r.zoomLevels.min,h=r.zoomLevels.max;if(!r.isZoomable()||o.mainScroll.isShifted())return;te(this._startZoomPoint,e,n),te(this._zoomPoint,t,i);let c=1/Zt(e,n)*Zt(t,i)*this._startZoomLevel;if(c>r.zoomLevels.initial+r.zoomLevels.initial/15&&(this._wasOverFitZoomLevel=!0),c<a)if(o.options.pinchToClose&&!this._wasOverFitZoomLevel&&this._startZoomLevel<=r.zoomLevels.initial){const d=1-(a-c)/(a/1.2);o.dispatch("pinchClose",{bgOpacity:d}).defaultPrevented||o.applyBgOpacity(d)}else c=a-(a-c)*Di;else c>h&&(c=h+(c-h)*Ni);r.pan.x=this._calculatePanForZoomLevel("x",c),r.pan.y=this._calculatePanForZoomLevel("y",c),r.setZoomLevel(c),r.applyCurrentZoomPan()}end(){const{pswp:t}=this.gestures,{currSlide:e}=t;(!e||e.currZoomLevel<e.zoomLevels.initial)&&!this._wasOverFitZoomLevel&&t.options.pinchToClose?t.close():this.correctZoomPan()}_calculatePanForZoomLevel(t,e){const i=e/this._startZoomLevel;return this._zoomPoint[t]-(this._startZoomPoint[t]-this._startPan[t])*i}correctZoomPan(t){const{pswp:e}=this.gestures,{currSlide:i}=e;if(!(i!=null&&i.isZoomable()))return;this._zoomPoint.x===0&&(t=!0);const n=i.currZoomLevel;let o,r=!0;n<i.zoomLevels.initial?o=i.zoomLevels.initial:n>i.zoomLevels.max?o=i.zoomLevels.max:(r=!1,o=n);const a=e.bgOpacity,h=e.bgOpacity<1,c=f({x:0,y:0},i.pan);let d=f({x:0,y:0},c);t&&(this._zoomPoint.x=0,this._zoomPoint.y=0,this._startZoomPoint.x=0,this._startZoomPoint.y=0,this._startZoomLevel=n,f(this._startPan,c)),r&&(d={x:this._calculatePanForZoomLevel("x",o),y:this._calculatePanForZoomLevel("y",o)}),i.setZoomLevel(o),d={x:i.bounds.correctPan("x",d.x),y:i.bounds.correctPan("y",d.y)},i.setZoomLevel(n);const p=!nt(d,c);if(!p&&!r&&!h){i._setResolution(o),i.applyCurrentZoomPan();return}e.animations.stopAllPan(),e.animations.startSpring({isPan:!0,start:0,end:1e3,velocity:0,dampingRatio:1,naturalFrequency:40,onUpdate:_=>{if(_/=1e3,p||r){if(p&&(i.pan.x=c.x+(d.x-c.x)*_,i.pan.y=c.y+(d.y-c.y)*_),r){const m=n+(o-n)*_;i.setZoomLevel(m)}i.applyCurrentZoomPan()}h&&e.bgOpacity<1&&e.applyBgOpacity(rt(a+(1-a)*_,0,1))},onComplete:()=>{i._setResolution(o),i.applyCurrentZoomPan()}})}}function ee(s){return!!s.target.closest(".pswp__container")}class xi{constructor(t){this.gestures=t}click(t,e){const i=e.target.classList,n=i.contains("pswp__img"),o=i.contains("pswp__item")||i.contains("pswp__zoom-wrap");n?this._doClickOrTapAction("imageClick",t,e):o&&this._doClickOrTapAction("bgClick",t,e)}tap(t,e){ee(e)&&this._doClickOrTapAction("tap",t,e)}doubleTap(t,e){ee(e)&&this._doClickOrTapAction("doubleTap",t,e)}_doClickOrTapAction(t,e,i){var n;const{pswp:o}=this.gestures,{currSlide:r}=o,a=t+"Action",h=o.options[a];if(!o.dispatch(a,{point:e,originalEvent:i}).defaultPrevented){if(typeof h=="function"){h.call(o,e,i);return}switch(h){case"close":case"next":o[h]();break;case"zoom":r==null||r.toggleZoom(e);break;case"zoom-or-close":r!=null&&r.isZoomable()&&r.zoomLevels.secondary!==r.zoomLevels.initial?r.toggleZoom(e):o.options.clickToCloseNonZoomable&&o.close();break;case"toggle-controls":(n=this.gestures.pswp.element)===null||n===void 0||n.classList.toggle("pswp--ui-visible");break}}}}const Mi=10,Ri=300,zi=25;class ki{constructor(t){this.pswp=t,this.dragAxis=null,this.p1={x:0,y:0},this.p2={x:0,y:0},this.prevP1={x:0,y:0},this.prevP2={x:0,y:0},this.startP1={x:0,y:0},this.startP2={x:0,y:0},this.velocity={x:0,y:0},this._lastStartP1={x:0,y:0},this._intervalP1={x:0,y:0},this._numActivePoints=0,this._ongoingPointers=[],this._touchEventEnabled="ontouchstart"in window,this._pointerEventEnabled=!!window.PointerEvent,this.supportsTouch=this._touchEventEnabled||this._pointerEventEnabled&&navigator.maxTouchPoints>1,this._numActivePoints=0,this._intervalTime=0,this._velocityCalculated=!1,this.isMultitouch=!1,this.isDragging=!1,this.isZooming=!1,this.raf=null,this._tapTimer=null,this.supportsTouch||(t.options.allowPanToNext=!1),this.drag=new Pi(this),this.zoomLevels=new $i(this),this.tapHandler=new xi(this),t.on("bindEvents",()=>{t.events.add(t.scrollWrap,"click",this._onClick.bind(this)),this._pointerEventEnabled?this._bindEvents("pointer","down","up","cancel"):this._touchEventEnabled?(this._bindEvents("touch","start","end","cancel"),t.scrollWrap&&(t.scrollWrap.ontouchmove=()=>{},t.scrollWrap.ontouchend=()=>{})):this._bindEvents("mouse","down","up")})}_bindEvents(t,e,i,n){const{pswp:o}=this,{events:r}=o,a=n?t+n:"";r.add(o.scrollWrap,t+e,this.onPointerDown.bind(this)),r.add(window,t+"move",this.onPointerMove.bind(this)),r.add(window,t+i,this.onPointerUp.bind(this)),a&&r.add(o.scrollWrap,a,this.onPointerUp.bind(this))}onPointerDown(t){const e=t.type==="mousedown"||t.pointerType==="mouse";if(e&&t.button>0)return;const{pswp:i}=this;if(!i.opener.isOpen){t.preventDefault();return}i.dispatch("pointerDown",{originalEvent:t}).defaultPrevented||(e&&(i.mouseDetected(),this._preventPointerEventBehaviour(t,"down")),i.animations.stopAll(),this._updatePoints(t,"down"),this._numActivePoints===1&&(this.dragAxis=null,f(this.startP1,this.p1)),this._numActivePoints>1?(this._clearTapTimer(),this.isMultitouch=!0):this.isMultitouch=!1)}onPointerMove(t){this._preventPointerEventBehaviour(t,"move"),this._numActivePoints&&(this._updatePoints(t,"move"),!this.pswp.dispatch("pointerMove",{originalEvent:t}).defaultPrevented&&(this._numActivePoints===1&&!this.isDragging?(this.dragAxis||this._calculateDragDirection(),this.dragAxis&&!this.isDragging&&(this.isZooming&&(this.isZooming=!1,this.zoomLevels.end()),this.isDragging=!0,this._clearTapTimer(),this._updateStartPoints(),this._intervalTime=Date.now(),this._velocityCalculated=!1,f(this._intervalP1,this.p1),this.velocity.x=0,this.velocity.y=0,this.drag.start(),this._rafStopLoop(),this._rafRenderLoop())):this._numActivePoints>1&&!this.isZooming&&(this._finishDrag(),this.isZooming=!0,this._updateStartPoints(),this.zoomLevels.start(),this._rafStopLoop(),this._rafRenderLoop())))}_finishDrag(){this.isDragging&&(this.isDragging=!1,this._velocityCalculated||this._updateVelocity(!0),this.drag.end(),this.dragAxis=null)}onPointerUp(t){this._numActivePoints&&(this._updatePoints(t,"up"),!this.pswp.dispatch("pointerUp",{originalEvent:t}).defaultPrevented&&(this._numActivePoints===0&&(this._rafStopLoop(),this.isDragging?this._finishDrag():!this.isZooming&&!this.isMultitouch&&this._finishTap(t)),this._numActivePoints<2&&this.isZooming&&(this.isZooming=!1,this.zoomLevels.end(),this._numActivePoints===1&&(this.dragAxis=null,this._updateStartPoints()))))}_rafRenderLoop(){(this.isDragging||this.isZooming)&&(this._updateVelocity(),this.isDragging?nt(this.p1,this.prevP1)||this.drag.change():(!nt(this.p1,this.prevP1)||!nt(this.p2,this.prevP2))&&this.zoomLevels.change(),this._updatePrevPoints(),this.raf=requestAnimationFrame(this._rafRenderLoop.bind(this)))}_updateVelocity(t){const e=Date.now(),i=e-this._intervalTime;i<50&&!t||(this.velocity.x=this._getVelocity("x",i),this.velocity.y=this._getVelocity("y",i),this._intervalTime=e,f(this._intervalP1,this.p1),this._velocityCalculated=!0)}_finishTap(t){const{mainScroll:e}=this.pswp;if(e.isShifted()){e.moveIndexBy(0,!0);return}if(t.type.indexOf("cancel")>0)return;if(t.type==="mouseup"||t.pointerType==="mouse"){this.tapHandler.click(this.startP1,t);return}const i=this.pswp.options.doubleTapAction?Ri:0;this._tapTimer?(this._clearTapTimer(),Zt(this._lastStartP1,this.startP1)<zi&&this.tapHandler.doubleTap(this.startP1,t)):(f(this._lastStartP1,this.startP1),this._tapTimer=setTimeout(()=>{this.tapHandler.tap(this.startP1,t),this._clearTapTimer()},i))}_clearTapTimer(){this._tapTimer&&(clearTimeout(this._tapTimer),this._tapTimer=null)}_getVelocity(t,e){const i=this.p1[t]-this._intervalP1[t];return Math.abs(i)>1&&e>5?i/e:0}_rafStopLoop(){this.raf&&(cancelAnimationFrame(this.raf),this.raf=null)}_preventPointerEventBehaviour(t,e){this.pswp.applyFilters("preventPointerEvent",!0,t,e)&&t.preventDefault()}_updatePoints(t,e){if(this._pointerEventEnabled){const i=t,n=this._ongoingPointers.findIndex(o=>o.id===i.pointerId);e==="up"&&n>-1?this._ongoingPointers.splice(n,1):e==="down"&&n===-1?this._ongoingPointers.push(this._convertEventPosToPoint(i,{x:0,y:0})):n>-1&&this._convertEventPosToPoint(i,this._ongoingPointers[n]),this._numActivePoints=this._ongoingPointers.length,this._numActivePoints>0&&f(this.p1,this._ongoingPointers[0]),this._numActivePoints>1&&f(this.p2,this._ongoingPointers[1])}else{const i=t;this._numActivePoints=0,i.type.indexOf("touch")>-1?i.touches&&i.touches.length>0&&(this._convertEventPosToPoint(i.touches[0],this.p1),this._numActivePoints++,i.touches.length>1&&(this._convertEventPosToPoint(i.touches[1],this.p2),this._numActivePoints++)):(this._convertEventPosToPoint(t,this.p1),e==="up"?this._numActivePoints=0:this._numActivePoints++)}}_updatePrevPoints(){f(this.prevP1,this.p1),f(this.prevP2,this.p2)}_updateStartPoints(){f(this.startP1,this.p1),f(this.startP2,this.p2),this._updatePrevPoints()}_calculateDragDirection(){if(this.pswp.mainScroll.isShifted())this.dragAxis="x";else{const t=Math.abs(this.p1.x-this.startP1.x)-Math.abs(this.p1.y-this.startP1.y);if(t!==0){const e=t>0?"x":"y";Math.abs(this.p1[e]-this.startP1[e])>=Mi&&(this.dragAxis=e)}}}_convertEventPosToPoint(t,e){return e.x=t.pageX-this.pswp.offset.x,e.y=t.pageY-this.pswp.offset.y,"pointerId"in t?e.id=t.pointerId:t.identifier!==void 0&&(e.id=t.identifier),e}_onClick(t){this.pswp.mainScroll.isShifted()&&(t.preventDefault(),t.stopPropagation())}}const Fi=.35;class Vi{constructor(t){this.pswp=t,this.x=0,this.slideWidth=0,this._currPositionIndex=0,this._prevPositionIndex=0,this._containerShiftIndex=-1,this.itemHolders=[]}resize(t){const{pswp:e}=this,i=Math.round(e.viewportSize.x+e.viewportSize.x*e.options.spacing),n=i!==this.slideWidth;n&&(this.slideWidth=i,this.moveTo(this.getCurrSlideX())),this.itemHolders.forEach((o,r)=>{n&&z(o.el,(r+this._containerShiftIndex)*this.slideWidth),t&&o.slide&&o.slide.resize()})}resetPosition(){this._currPositionIndex=0,this._prevPositionIndex=0,this.slideWidth=0,this._containerShiftIndex=-1}appendHolders(){this.itemHolders=[];for(let t=0;t<3;t++){const e=v("pswp__item","div",this.pswp.container);e.setAttribute("role","group"),e.setAttribute("aria-roledescription","slide"),e.setAttribute("aria-hidden","true"),e.style.display=t===1?"block":"none",this.itemHolders.push({el:e})}}canBeSwiped(){return this.pswp.getNumItems()>1}moveIndexBy(t,e,i){const{pswp:n}=this;let o=n.potentialIndex+t;const r=n.getNumItems();if(n.canLoop()){o=n.getLoopedIndex(o);const h=(t+r)%r;h<=r/2?t=h:t=h-r}else o<0?o=0:o>=r&&(o=r-1),t=o-n.potentialIndex;n.potentialIndex=o,this._currPositionIndex-=t,n.animations.stopMainScroll();const a=this.getCurrSlideX();if(!e)this.moveTo(a),this.updateCurrItem();else{n.animations.startSpring({isMainScroll:!0,start:this.x,end:a,velocity:i||0,naturalFrequency:30,dampingRatio:1,onUpdate:c=>{this.moveTo(c)},onComplete:()=>{this.updateCurrItem(),n.appendHeavy()}});let h=n.potentialIndex-n.currIndex;if(n.canLoop()){const c=(h+r)%r;c<=r/2?h=c:h=c-r}Math.abs(h)>1&&this.updateCurrItem()}return!!t}getCurrSlideX(){return this.slideWidth*this._currPositionIndex}isShifted(){return this.x!==this.getCurrSlideX()}updateCurrItem(){var t;const{pswp:e}=this,i=this._prevPositionIndex-this._currPositionIndex;if(!i)return;this._prevPositionIndex=this._currPositionIndex,e.currIndex=e.potentialIndex;let n=Math.abs(i),o;n>=3&&(this._containerShiftIndex+=i+(i>0?-3:3),n=3,this.itemHolders.forEach(r=>{var a;(a=r.slide)===null||a===void 0||a.destroy(),r.slide=void 0}));for(let r=0;r<n;r++)i>0?(o=this.itemHolders.shift(),o&&(this.itemHolders[2]=o,this._containerShiftIndex++,z(o.el,(this._containerShiftIndex+2)*this.slideWidth),e.setContent(o,e.currIndex-n+r+2))):(o=this.itemHolders.pop(),o&&(this.itemHolders.unshift(o),this._containerShiftIndex--,z(o.el,this._containerShiftIndex*this.slideWidth),e.setContent(o,e.currIndex+n-r-2)));Math.abs(this._containerShiftIndex)>50&&!this.isShifted()&&(this.resetPosition(),this.resize()),e.animations.stopAllPan(),this.itemHolders.forEach((r,a)=>{r.slide&&r.slide.setIsActive(a===1)}),e.currSlide=(t=this.itemHolders[1])===null||t===void 0?void 0:t.slide,e.contentLoader.updateLazy(i),e.currSlide&&e.currSlide.applyCurrentZoomPan(),e.dispatch("change")}moveTo(t,e){if(!this.pswp.canLoop()&&e){let i=(this.slideWidth*this._currPositionIndex-t)/this.slideWidth;i+=this.pswp.currIndex;const n=Math.round(t-this.x);(i<0&&n>0||i>=this.pswp.getNumItems()-1&&n<0)&&(t=this.x+n*Fi)}this.x=t,this.pswp.container&&z(this.pswp.container,t),this.pswp.dispatch("moveMainScroll",{x:t,dragging:e??!1})}}const Hi={Escape:27,z:90,ArrowLeft:37,ArrowUp:38,ArrowRight:39,ArrowDown:40,Tab:9},R=(s,t)=>t?s:Hi[s];class Zi{constructor(t){this.pswp=t,this._wasFocused=!1,t.on("bindEvents",()=>{t.options.trapFocus&&(t.options.initialPointerPos||this._focusRoot(),t.events.add(document,"focusin",this._onFocusIn.bind(this))),t.events.add(document,"keydown",this._onKeyDown.bind(this))});const e=document.activeElement;t.on("destroy",()=>{t.options.returnFocus&&e&&this._wasFocused&&e.focus()})}_focusRoot(){!this._wasFocused&&this.pswp.element&&(this.pswp.element.focus(),this._wasFocused=!0)}_onKeyDown(t){const{pswp:e}=this;if(e.dispatch("keydown",{originalEvent:t}).defaultPrevented||yi(t))return;let i,n,o=!1;const r="key"in t;switch(r?t.key:t.keyCode){case R("Escape",r):e.options.escKey&&(i="close");break;case R("z",r):i="toggleZoom";break;case R("ArrowLeft",r):n="x";break;case R("ArrowUp",r):n="y";break;case R("ArrowRight",r):n="x",o=!0;break;case R("ArrowDown",r):o=!0,n="y";break;case R("Tab",r):this._focusRoot();break}if(n){t.preventDefault();const{currSlide:a}=e;e.options.arrowKeys&&n==="x"&&e.getNumItems()>1?i=o?"next":"prev":a&&a.currZoomLevel>a.zoomLevels.fit&&(a.pan[n]+=o?-80:80,a.panTo(a.pan.x,a.pan.y))}i&&(t.preventDefault(),e[i]())}_onFocusIn(t){const{template:e}=this.pswp;e&&document!==t.target&&e!==t.target&&!e.contains(t.target)&&e.focus()}}const Bi="cubic-bezier(.4,0,.22,1)";class Wi{constructor(t){var e;this.props=t;const{target:i,onComplete:n,transform:o,onFinish:r=()=>{},duration:a=333,easing:h=Bi}=t;this.onFinish=r;const c=o?"transform":"opacity",d=(e=t[c])!==null&&e!==void 0?e:"";this._target=i,this._onComplete=n,this._finished=!1,this._onTransitionEnd=this._onTransitionEnd.bind(this),this._helperTimeout=setTimeout(()=>{xe(i,c,a,h),this._helperTimeout=setTimeout(()=>{i.addEventListener("transitionend",this._onTransitionEnd,!1),i.addEventListener("transitioncancel",this._onTransitionEnd,!1),this._helperTimeout=setTimeout(()=>{this._finalizeAnimation()},a+500),i.style[c]=d},30)},0)}_onTransitionEnd(t){t.target===this._target&&this._finalizeAnimation()}_finalizeAnimation(){this._finished||(this._finished=!0,this.onFinish(),this._onComplete&&this._onComplete())}destroy(){this._helperTimeout&&clearTimeout(this._helperTimeout),Ei(this._target),this._target.removeEventListener("transitionend",this._onTransitionEnd,!1),this._target.removeEventListener("transitioncancel",this._onTransitionEnd,!1),this._finished||this._finalizeAnimation()}}const Ki=12,Ui=.75;class Yi{constructor(t,e,i){this.velocity=t*1e3,this._dampingRatio=e||Ui,this._naturalFrequency=i||Ki,this._dampedFrequency=this._naturalFrequency,this._dampingRatio<1&&(this._dampedFrequency*=Math.sqrt(1-this._dampingRatio*this._dampingRatio))}easeFrame(t,e){let i=0,n;e/=1e3;const o=Math.E**(-this._dampingRatio*this._naturalFrequency*e);if(this._dampingRatio===1)n=this.velocity+this._naturalFrequency*t,i=(t+n*e)*o,this.velocity=i*-this._naturalFrequency+n*o;else if(this._dampingRatio<1){n=1/this._dampedFrequency*(this._dampingRatio*this._naturalFrequency*t+this.velocity);const r=Math.cos(this._dampedFrequency*e),a=Math.sin(this._dampedFrequency*e);i=o*(t*r+n*a),this.velocity=i*-this._naturalFrequency*this._dampingRatio+o*(-this._dampedFrequency*t*a+this._dampedFrequency*n*r)}return i}}class Gi{constructor(t){this.props=t,this._raf=0;const{start:e,end:i,velocity:n,onUpdate:o,onComplete:r,onFinish:a=()=>{},dampingRatio:h,naturalFrequency:c}=t;this.onFinish=a;const d=new Yi(n,h,c);let p=Date.now(),_=e-i;const m=()=>{this._raf&&(_=d.easeFrame(_,Date.now()-p),Math.abs(_)<1&&Math.abs(d.velocity)<50?(o(i),r&&r(),this.onFinish()):(p=Date.now(),o(_+i),this._raf=requestAnimationFrame(m)))};this._raf=requestAnimationFrame(m)}destroy(){this._raf>=0&&cancelAnimationFrame(this._raf),this._raf=0}}class ji{constructor(){this.activeAnimations=[]}startSpring(t){this._start(t,!0)}startTransition(t){this._start(t)}_start(t,e){const i=e?new Gi(t):new Wi(t);return this.activeAnimations.push(i),i.onFinish=()=>this.stop(i),i}stop(t){t.destroy();const e=this.activeAnimations.indexOf(t);e>-1&&this.activeAnimations.splice(e,1)}stopAll(){this.activeAnimations.forEach(t=>{t.destroy()}),this.activeAnimations=[]}stopAllPan(){this.activeAnimations=this.activeAnimations.filter(t=>t.props.isPan?(t.destroy(),!1):!0)}stopMainScroll(){this.activeAnimations=this.activeAnimations.filter(t=>t.props.isMainScroll?(t.destroy(),!1):!0)}isPanRunning(){return this.activeAnimations.some(t=>t.props.isPan)}}class qi{constructor(t){this.pswp=t,t.events.add(t.element,"wheel",this._onWheel.bind(this))}_onWheel(t){t.preventDefault();const{currSlide:e}=this.pswp;let{deltaX:i,deltaY:n}=t;if(e&&!this.pswp.dispatch("wheel",{originalEvent:t}).defaultPrevented)if(t.ctrlKey||this.pswp.options.wheelToZoom){if(e.isZoomable()){let o=-n;t.deltaMode===1?o*=.05:o*=t.deltaMode?1:.002,o=2**o;const r=e.currZoomLevel*o;e.zoomTo(r,{x:t.clientX,y:t.clientY})}}else e.isPannable()&&(t.deltaMode===1&&(i*=18,n*=18),e.panTo(e.pan.x-i,e.pan.y-n))}}function Xi(s){if(typeof s=="string")return s;if(!s||!s.isCustomSVG)return"";const t=s;let e='<svg aria-hidden="true" class="pswp__icn" viewBox="0 0 %d %d" width="%d" height="%d">';return e=e.split("%d").join(t.size||32),t.outlineID&&(e+='<use class="pswp__icn-shadow" xlink:href="#'+t.outlineID+'"/>'),e+=t.inner,e+="</svg>",e}class Qi{constructor(t,e){var i;const n=e.name||e.className;let o=e.html;if(t.options[n]===!1)return;typeof t.options[n+"SVG"]=="string"&&(o=t.options[n+"SVG"]),t.dispatch("uiElementCreate",{data:e});let r="";e.isButton?(r+="pswp__button ",r+=e.className||`pswp__button--${e.name}`):r+=e.className||`pswp__${e.name}`;let a=e.isButton?e.tagName||"button":e.tagName||"div";a=a.toLowerCase();const h=v(r,a);if(e.isButton){a==="button"&&(h.type="button");let{title:p}=e;const{ariaLabel:_}=e;typeof t.options[n+"Title"]=="string"&&(p=t.options[n+"Title"]),p&&(h.title=p);const m=_||p;m&&h.setAttribute("aria-label",m)}h.innerHTML=Xi(o),e.onInit&&e.onInit(h,t),e.onClick&&(h.onclick=p=>{typeof e.onClick=="string"?t[e.onClick]():typeof e.onClick=="function"&&e.onClick(p,h,t)});const c=e.appendTo||"bar";let d=t.element;c==="bar"?(t.topBar||(t.topBar=v("pswp__top-bar pswp__hide-on-close","div",t.scrollWrap)),d=t.topBar):(h.classList.add("pswp__hide-on-close"),c==="wrapper"&&(d=t.scrollWrap)),(i=d)===null||i===void 0||i.appendChild(t.applyFilters("uiElement",h,e))}}function Fe(s,t,e){s.classList.add("pswp__button--arrow"),s.setAttribute("aria-controls","pswp__items"),t.on("change",()=>{t.options.loop||(e?s.disabled=!(t.currIndex<t.getNumItems()-1):s.disabled=!(t.currIndex>0))})}const Ji={name:"arrowPrev",className:"pswp__button--arrow--prev",title:"Previous",order:10,isButton:!0,appendTo:"wrapper",html:{isCustomSVG:!0,size:60,inner:'<path d="M29 43l-3 3-16-16 16-16 3 3-13 13 13 13z" id="pswp__icn-arrow"/>',outlineID:"pswp__icn-arrow"},onClick:"prev",onInit:Fe},ts={name:"arrowNext",className:"pswp__button--arrow--next",title:"Next",order:11,isButton:!0,appendTo:"wrapper",html:{isCustomSVG:!0,size:60,inner:'<use xlink:href="#pswp__icn-arrow"/>',outlineID:"pswp__icn-arrow"},onClick:"next",onInit:(s,t)=>{Fe(s,t,!0)}},es={name:"close",title:"Close",order:20,isButton:!0,html:{isCustomSVG:!0,inner:'<path d="M24 10l-2-2-6 6-6-6-2 2 6 6-6 6 2 2 6-6 6 6 2-2-6-6z" id="pswp__icn-close"/>',outlineID:"pswp__icn-close"},onClick:"close"},is={name:"zoom",title:"Zoom",order:10,isButton:!0,html:{isCustomSVG:!0,inner:'<path d="M17.426 19.926a6 6 0 1 1 1.5-1.5L23 22.5 21.5 24l-4.074-4.074z" id="pswp__icn-zoom"/><path fill="currentColor" class="pswp__zoom-icn-bar-h" d="M11 16v-2h6v2z"/><path fill="currentColor" class="pswp__zoom-icn-bar-v" d="M13 12h2v6h-2z"/>',outlineID:"pswp__icn-zoom"},onClick:"toggleZoom"},ss={name:"preloader",appendTo:"bar",order:7,html:{isCustomSVG:!0,inner:'<path fill-rule="evenodd" clip-rule="evenodd" d="M21.2 16a5.2 5.2 0 1 1-5.2-5.2V8a8 8 0 1 0 8 8h-2.8Z" id="pswp__icn-loading"/>',outlineID:"pswp__icn-loading"},onInit:(s,t)=>{let e,i=null;const n=(a,h)=>{s.classList.toggle("pswp__preloader--"+a,h)},o=a=>{e!==a&&(e=a,n("active",a))},r=()=>{var a;if(!((a=t.currSlide)!==null&&a!==void 0&&a.content.isLoading())){o(!1),i&&(clearTimeout(i),i=null);return}i||(i=setTimeout(()=>{var h;o(!!(!((h=t.currSlide)===null||h===void 0)&&h.content.isLoading())),i=null},t.options.preloaderDelay))};t.on("change",r),t.on("loadComplete",a=>{t.currSlide===a.slide&&r()}),t.ui&&(t.ui.updatePreloaderVisibility=r)}},ns={name:"counter",order:5,onInit:(s,t)=>{t.on("change",()=>{s.innerText=t.currIndex+1+t.options.indexIndicatorSep+t.getNumItems()})}};function ie(s,t){s.classList.toggle("pswp--zoomed-in",t)}class os{constructor(t){this.pswp=t,this.isRegistered=!1,this.uiElementsData=[],this.items=[],this.updatePreloaderVisibility=()=>{},this._lastUpdatedZoomLevel=void 0}init(){const{pswp:t}=this;this.isRegistered=!1,this.uiElementsData=[es,Ji,ts,is,ss,ns],t.dispatch("uiRegister"),this.uiElementsData.sort((e,i)=>(e.order||0)-(i.order||0)),this.items=[],this.isRegistered=!0,this.uiElementsData.forEach(e=>{this.registerElement(e)}),t.on("change",()=>{var e;(e=t.element)===null||e===void 0||e.classList.toggle("pswp--one-slide",t.getNumItems()===1)}),t.on("zoomPanUpdate",()=>this._onZoomPanUpdate())}registerElement(t){this.isRegistered?this.items.push(new Qi(this.pswp,t)):this.uiElementsData.push(t)}_onZoomPanUpdate(){const{template:t,currSlide:e,options:i}=this.pswp;if(this.pswp.opener.isClosing||!t||!e)return;let{currZoomLevel:n}=e;if(this.pswp.opener.isOpen||(n=e.zoomLevels.initial),n===this._lastUpdatedZoomLevel)return;this._lastUpdatedZoomLevel=n;const o=e.zoomLevels.initial-e.zoomLevels.secondary;if(Math.abs(o)<.01||!e.isZoomable()){ie(t,!1),t.classList.remove("pswp--zoom-allowed");return}t.classList.add("pswp--zoom-allowed");const r=n===e.zoomLevels.initial?e.zoomLevels.secondary:e.zoomLevels.initial;ie(t,r<=n),(i.imageClickAction==="zoom"||i.imageClickAction==="zoom-or-close")&&t.classList.add("pswp--click-to-zoom")}}function rs(s){const t=s.getBoundingClientRect();return{x:t.left,y:t.top,w:t.width}}function as(s,t,e){const i=s.getBoundingClientRect(),n=i.width/t,o=i.height/e,r=n>o?n:o,a=(i.width-t*r)/2,h=(i.height-e*r)/2,c={x:i.left+a,y:i.top+h,w:t*r};return c.innerRect={w:i.width,h:i.height,x:a,y:h},c}function ls(s,t,e){const i=e.dispatch("thumbBounds",{index:s,itemData:t,instance:e});if(i.thumbBounds)return i.thumbBounds;const{element:n}=t;let o,r;if(n&&e.options.thumbSelector!==!1){const a=e.options.thumbSelector||"img";r=n.matches(a)?n:n.querySelector(a)}return r=e.applyFilters("thumbEl",r,t,s),r&&(t.thumbCropped?o=as(r,t.width||t.w||0,t.height||t.h||0):o=rs(r)),e.applyFilters("thumbBounds",o,t,s)}class hs{constructor(t,e){this.type=t,this.defaultPrevented=!1,e&&Object.assign(this,e)}preventDefault(){this.defaultPrevented=!0}}class cs{constructor(){this._listeners={},this._filters={},this.pswp=void 0,this.options=void 0}addFilter(t,e,i=100){var n,o,r;this._filters[t]||(this._filters[t]=[]),(n=this._filters[t])===null||n===void 0||n.push({fn:e,priority:i}),(o=this._filters[t])===null||o===void 0||o.sort((a,h)=>a.priority-h.priority),(r=this.pswp)===null||r===void 0||r.addFilter(t,e,i)}removeFilter(t,e){this._filters[t]&&(this._filters[t]=this._filters[t].filter(i=>i.fn!==e)),this.pswp&&this.pswp.removeFilter(t,e)}applyFilters(t,...e){var i;return(i=this._filters[t])===null||i===void 0||i.forEach(n=>{e[0]=n.fn.apply(this,e)}),e[0]}on(t,e){var i,n;this._listeners[t]||(this._listeners[t]=[]),(i=this._listeners[t])===null||i===void 0||i.push(e),(n=this.pswp)===null||n===void 0||n.on(t,e)}off(t,e){var i;this._listeners[t]&&(this._listeners[t]=this._listeners[t].filter(n=>e!==n)),(i=this.pswp)===null||i===void 0||i.off(t,e)}dispatch(t,e){var i;if(this.pswp)return this.pswp.dispatch(t,e);const n=new hs(t,e);return(i=this._listeners[t])===null||i===void 0||i.forEach(o=>{o.call(this,n)}),n}}class ds{constructor(t,e){if(this.element=v("pswp__img pswp__img--placeholder",t?"img":"div",e),t){const i=this.element;i.decoding="async",i.alt="",i.src=t,i.setAttribute("role","presentation")}this.element.setAttribute("aria-hidden","true")}setDisplayedSize(t,e){this.element&&(this.element.tagName==="IMG"?(Bt(this.element,250,"auto"),this.element.style.transformOrigin="0 0",this.element.style.transform=ot(0,0,t/250)):Bt(this.element,t,e))}destroy(){var t;(t=this.element)!==null&&t!==void 0&&t.parentNode&&this.element.remove(),this.element=null}}class us{constructor(t,e,i){this.instance=e,this.data=t,this.index=i,this.element=void 0,this.placeholder=void 0,this.slide=void 0,this.displayedImageWidth=0,this.displayedImageHeight=0,this.width=Number(this.data.w)||Number(this.data.width)||0,this.height=Number(this.data.h)||Number(this.data.height)||0,this.isAttached=!1,this.hasSlide=!1,this.isDecoding=!1,this.state=y.IDLE,this.data.type?this.type=this.data.type:this.data.src?this.type="image":this.type="html",this.instance.dispatch("contentInit",{content:this})}removePlaceholder(){this.placeholder&&!this.keepPlaceholder()&&setTimeout(()=>{this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0)},1e3)}load(t,e){if(this.slide&&this.usePlaceholder())if(this.placeholder){const i=this.placeholder.element;i&&!i.parentElement&&this.slide.container.prepend(i)}else{const i=this.instance.applyFilters("placeholderSrc",this.data.msrc&&this.slide.isFirstSlide?this.data.msrc:!1,this);this.placeholder=new ds(i,this.slide.container)}this.element&&!e||this.instance.dispatch("contentLoad",{content:this,isLazy:t}).defaultPrevented||(this.isImageContent()?(this.element=v("pswp__img","img"),this.displayedImageWidth&&this.loadImage(t)):(this.element=v("pswp__content","div"),this.element.innerHTML=this.data.html||""),e&&this.slide&&this.slide.updateContentSize(!0))}loadImage(t){var e,i;if(!this.isImageContent()||!this.element||this.instance.dispatch("contentLoadImage",{content:this,isLazy:t}).defaultPrevented)return;const n=this.element;this.updateSrcsetSizes(),this.data.srcset&&(n.srcset=this.data.srcset),n.src=(e=this.data.src)!==null&&e!==void 0?e:"",n.alt=(i=this.data.alt)!==null&&i!==void 0?i:"",this.state=y.LOADING,n.complete?this.onLoaded():(n.onload=()=>{this.onLoaded()},n.onerror=()=>{this.onError()})}setSlide(t){this.slide=t,this.hasSlide=!0,this.instance=t.pswp}onLoaded(){this.state=y.LOADED,this.slide&&this.element&&(this.instance.dispatch("loadComplete",{slide:this.slide,content:this}),this.slide.isActive&&this.slide.heavyAppended&&!this.element.parentNode&&(this.append(),this.slide.updateContentSize(!0)),(this.state===y.LOADED||this.state===y.ERROR)&&this.removePlaceholder())}onError(){this.state=y.ERROR,this.slide&&(this.displayError(),this.instance.dispatch("loadComplete",{slide:this.slide,isError:!0,content:this}),this.instance.dispatch("loadError",{slide:this.slide,content:this}))}isLoading(){return this.instance.applyFilters("isContentLoading",this.state===y.LOADING,this)}isError(){return this.state===y.ERROR}isImageContent(){return this.type==="image"}setDisplayedSize(t,e){if(this.element&&(this.placeholder&&this.placeholder.setDisplayedSize(t,e),!this.instance.dispatch("contentResize",{content:this,width:t,height:e}).defaultPrevented&&(Bt(this.element,t,e),this.isImageContent()&&!this.isError()))){const i=!this.displayedImageWidth&&t;this.displayedImageWidth=t,this.displayedImageHeight=e,i?this.loadImage(!1):this.updateSrcsetSizes(),this.slide&&this.instance.dispatch("imageSizeChange",{slide:this.slide,width:t,height:e,content:this})}}isZoomable(){return this.instance.applyFilters("isContentZoomable",this.isImageContent()&&this.state!==y.ERROR,this)}updateSrcsetSizes(){if(!this.isImageContent()||!this.element||!this.data.srcset)return;const t=this.element,e=this.instance.applyFilters("srcsetSizesWidth",this.displayedImageWidth,this);(!t.dataset.largestUsedSize||e>parseInt(t.dataset.largestUsedSize,10))&&(t.sizes=e+"px",t.dataset.largestUsedSize=String(e))}usePlaceholder(){return this.instance.applyFilters("useContentPlaceholder",this.isImageContent(),this)}lazyLoad(){this.instance.dispatch("contentLazyLoad",{content:this}).defaultPrevented||this.load(!0)}keepPlaceholder(){return this.instance.applyFilters("isKeepingPlaceholder",this.isLoading(),this)}destroy(){this.hasSlide=!1,this.slide=void 0,!this.instance.dispatch("contentDestroy",{content:this}).defaultPrevented&&(this.remove(),this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0),this.isImageContent()&&this.element&&(this.element.onload=null,this.element.onerror=null,this.element=void 0))}displayError(){if(this.slide){var t,e;let i=v("pswp__error-msg","div");i.innerText=(t=(e=this.instance.options)===null||e===void 0?void 0:e.errorMsg)!==null&&t!==void 0?t:"",i=this.instance.applyFilters("contentErrorElement",i,this),this.element=v("pswp__content pswp__error-msg-container","div"),this.element.appendChild(i),this.slide.container.innerText="",this.slide.container.appendChild(this.element),this.slide.updateContentSize(!0),this.removePlaceholder()}}append(){if(this.isAttached||!this.element)return;if(this.isAttached=!0,this.state===y.ERROR){this.displayError();return}if(this.instance.dispatch("contentAppend",{content:this}).defaultPrevented)return;const t="decode"in this.element;this.isImageContent()?t&&this.slide&&(!this.slide.isActive||Qt())?(this.isDecoding=!0,this.element.decode().catch(()=>{}).finally(()=>{this.isDecoding=!1,this.appendImage()})):this.appendImage():this.slide&&!this.element.parentNode&&this.slide.container.appendChild(this.element)}activate(){this.instance.dispatch("contentActivate",{content:this}).defaultPrevented||!this.slide||(this.isImageContent()&&this.isDecoding&&!Qt()?this.appendImage():this.isError()&&this.load(!1,!0),this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","false"))}deactivate(){this.instance.dispatch("contentDeactivate",{content:this}),this.slide&&this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","true")}remove(){this.isAttached=!1,!this.instance.dispatch("contentRemove",{content:this}).defaultPrevented&&(this.element&&this.element.parentNode&&this.element.remove(),this.placeholder&&this.placeholder.element&&this.placeholder.element.remove())}appendImage(){this.isAttached&&(this.instance.dispatch("contentAppendImage",{content:this}).defaultPrevented||(this.slide&&this.element&&!this.element.parentNode&&this.slide.container.appendChild(this.element),(this.state===y.LOADED||this.state===y.ERROR)&&this.removePlaceholder()))}}const ps=5;function Ve(s,t,e){const i=t.createContentFromData(s,e);let n;const{options:o}=t;if(o){n=new ke(o,s,-1);let r;t.pswp?r=t.pswp.viewportSize:r=Re(o,t);const a=ze(o,r,s,e);n.update(i.width,i.height,a)}return i.lazyLoad(),n&&i.setDisplayedSize(Math.ceil(i.width*n.initial),Math.ceil(i.height*n.initial)),i}function _s(s,t){const e=t.getItemData(s);if(!t.dispatch("lazyLoadSlide",{index:s,itemData:e}).defaultPrevented)return Ve(e,t,s)}class fs{constructor(t){this.pswp=t,this.limit=Math.max(t.options.preload[0]+t.options.preload[1]+1,ps),this._cachedItems=[]}updateLazy(t){const{pswp:e}=this;if(e.dispatch("lazyLoad").defaultPrevented)return;const{preload:i}=e.options,n=t===void 0?!0:t>=0;let o;for(o=0;o<=i[1];o++)this.loadSlideByIndex(e.currIndex+(n?o:-o));for(o=1;o<=i[0];o++)this.loadSlideByIndex(e.currIndex+(n?-o:o))}loadSlideByIndex(t){const e=this.pswp.getLoopedIndex(t);let i=this.getContentByIndex(e);i||(i=_s(e,this.pswp),i&&this.addToCache(i))}getContentBySlide(t){let e=this.getContentByIndex(t.index);return e||(e=this.pswp.createContentFromData(t.data,t.index),this.addToCache(e)),e.setSlide(t),e}addToCache(t){if(this.removeByIndex(t.index),this._cachedItems.push(t),this._cachedItems.length>this.limit){const e=this._cachedItems.findIndex(i=>!i.isAttached&&!i.hasSlide);e!==-1&&this._cachedItems.splice(e,1)[0].destroy()}}removeByIndex(t){const e=this._cachedItems.findIndex(i=>i.index===t);e!==-1&&this._cachedItems.splice(e,1)}getContentByIndex(t){return this._cachedItems.find(e=>e.index===t)}destroy(){this._cachedItems.forEach(t=>t.destroy()),this._cachedItems=[]}}class ms extends cs{getNumItems(){var t;let e=0;const i=(t=this.options)===null||t===void 0?void 0:t.dataSource;i&&"length"in i?e=i.length:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),i.items&&(e=i.items.length));const n=this.dispatch("numItems",{dataSource:i,numItems:e});return this.applyFilters("numItems",n.numItems,i)}createContentFromData(t,e){return new us(t,this,e)}getItemData(t){var e;const i=(e=this.options)===null||e===void 0?void 0:e.dataSource;let n={};Array.isArray(i)?n=i[t]:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),n=i.items[t]);let o=n;o instanceof Element&&(o=this._domElementToItemData(o));const r=this.dispatch("itemData",{itemData:o||{},index:t});return this.applyFilters("itemData",r.itemData,t)}_getGalleryDOMElements(t){var e,i;return(e=this.options)!==null&&e!==void 0&&e.children||(i=this.options)!==null&&i!==void 0&&i.childSelector?Ai(this.options.children,this.options.childSelector,t)||[]:[t]}_domElementToItemData(t){const e={element:t},i=t.tagName==="A"?t:t.querySelector("a");if(i){e.src=i.dataset.pswpSrc||i.href,i.dataset.pswpSrcset&&(e.srcset=i.dataset.pswpSrcset),e.width=i.dataset.pswpWidth?parseInt(i.dataset.pswpWidth,10):0,e.height=i.dataset.pswpHeight?parseInt(i.dataset.pswpHeight,10):0,e.w=e.width,e.h=e.height,i.dataset.pswpType&&(e.type=i.dataset.pswpType);const o=t.querySelector("img");if(o){var n;e.msrc=o.currentSrc||o.src,e.alt=(n=o.getAttribute("alt"))!==null&&n!==void 0?n:""}(i.dataset.pswpCropped||i.dataset.cropped)&&(e.thumbCropped=!0)}return this.applyFilters("domItemData",e,t,i)}lazyLoadData(t,e){return Ve(t,this,e)}}const tt=.003;class gs{constructor(t){this.pswp=t,this.isClosed=!0,this.isOpen=!1,this.isClosing=!1,this.isOpening=!1,this._duration=void 0,this._useAnimation=!1,this._croppedZoom=!1,this._animateRootOpacity=!1,this._animateBgOpacity=!1,this._placeholder=void 0,this._opacityElement=void 0,this._cropContainer1=void 0,this._cropContainer2=void 0,this._thumbBounds=void 0,this._prepareOpen=this._prepareOpen.bind(this),t.on("firstZoomPan",this._prepareOpen)}open(){this._prepareOpen(),this._start()}close(){if(this.isClosed||this.isClosing||this.isOpening)return;const t=this.pswp.currSlide;this.isOpen=!1,this.isOpening=!1,this.isClosing=!0,this._duration=this.pswp.options.hideAnimationDuration,t&&t.currZoomLevel*t.width>=this.pswp.options.maxWidthToAnimate&&(this._duration=0),this._applyStartProps(),setTimeout(()=>{this._start()},this._croppedZoom?30:0)}_prepareOpen(){if(this.pswp.off("firstZoomPan",this._prepareOpen),!this.isOpening){const t=this.pswp.currSlide;this.isOpening=!0,this.isClosing=!1,this._duration=this.pswp.options.showAnimationDuration,t&&t.zoomLevels.initial*t.width>=this.pswp.options.maxWidthToAnimate&&(this._duration=0),this._applyStartProps()}}_applyStartProps(){const{pswp:t}=this,e=this.pswp.currSlide,{options:i}=t;if(i.showHideAnimationType==="fade"?(i.showHideOpacity=!0,this._thumbBounds=void 0):i.showHideAnimationType==="none"?(i.showHideOpacity=!1,this._duration=0,this._thumbBounds=void 0):this.isOpening&&t._initialThumbBounds?this._thumbBounds=t._initialThumbBounds:this._thumbBounds=this.pswp.getThumbBounds(),this._placeholder=e==null?void 0:e.getPlaceholderElement(),t.animations.stopAll(),this._useAnimation=!!(this._duration&&this._duration>50),this._animateZoom=!!this._thumbBounds&&(e==null?void 0:e.content.usePlaceholder())&&(!this.isClosing||!t.mainScroll.isShifted()),!this._animateZoom)this._animateRootOpacity=!0,this.isOpening&&e&&(e.zoomAndPanToInitial(),e.applyCurrentZoomPan());else{var n;this._animateRootOpacity=(n=i.showHideOpacity)!==null&&n!==void 0?n:!1}if(this._animateBgOpacity=!this._animateRootOpacity&&this.pswp.options.bgOpacity>tt,this._opacityElement=this._animateRootOpacity?t.element:t.bg,!this._useAnimation){this._duration=0,this._animateZoom=!1,this._animateBgOpacity=!1,this._animateRootOpacity=!0,this.isOpening&&(t.element&&(t.element.style.opacity=String(tt)),t.applyBgOpacity(1));return}if(this._animateZoom&&this._thumbBounds&&this._thumbBounds.innerRect){var o;this._croppedZoom=!0,this._cropContainer1=this.pswp.container,this._cropContainer2=(o=this.pswp.currSlide)===null||o===void 0?void 0:o.holderElement,t.container&&(t.container.style.overflow="hidden",t.container.style.width=t.viewportSize.x+"px")}else this._croppedZoom=!1;this.isOpening?(this._animateRootOpacity?(t.element&&(t.element.style.opacity=String(tt)),t.applyBgOpacity(1)):(this._animateBgOpacity&&t.bg&&(t.bg.style.opacity=String(tt)),t.element&&(t.element.style.opacity="1")),this._animateZoom&&(this._setClosedStateZoomPan(),this._placeholder&&(this._placeholder.style.willChange="transform",this._placeholder.style.opacity=String(tt)))):this.isClosing&&(t.mainScroll.itemHolders[0]&&(t.mainScroll.itemHolders[0].el.style.display="none"),t.mainScroll.itemHolders[2]&&(t.mainScroll.itemHolders[2].el.style.display="none"),this._croppedZoom&&t.mainScroll.x!==0&&(t.mainScroll.resetPosition(),t.mainScroll.resize()))}_start(){this.isOpening&&this._useAnimation&&this._placeholder&&this._placeholder.tagName==="IMG"?new Promise(t=>{let e=!1,i=!0;vi(this._placeholder).finally(()=>{e=!0,i||t(!0)}),setTimeout(()=>{i=!1,e&&t(!0)},50),setTimeout(t,250)}).finally(()=>this._initiate()):this._initiate()}_initiate(){var t,e;(t=this.pswp.element)===null||t===void 0||t.style.setProperty("--pswp-transition-duration",this._duration+"ms"),this.pswp.dispatch(this.isOpening?"openingAnimationStart":"closingAnimationStart"),this.pswp.dispatch("initialZoom"+(this.isOpening?"In":"Out")),(e=this.pswp.element)===null||e===void 0||e.classList.toggle("pswp--ui-visible",this.isOpening),this.isOpening?(this._placeholder&&(this._placeholder.style.opacity="1"),this._animateToOpenState()):this.isClosing&&this._animateToClosedState(),this._useAnimation||this._onAnimationComplete()}_onAnimationComplete(){const{pswp:t}=this;if(this.isOpen=this.isOpening,this.isClosed=this.isClosing,this.isOpening=!1,this.isClosing=!1,t.dispatch(this.isOpen?"openingAnimationEnd":"closingAnimationEnd"),t.dispatch("initialZoom"+(this.isOpen?"InEnd":"OutEnd")),this.isClosed)t.destroy();else if(this.isOpen){var e;this._animateZoom&&t.container&&(t.container.style.overflow="visible",t.container.style.width="100%"),(e=t.currSlide)===null||e===void 0||e.applyCurrentZoomPan()}}_animateToOpenState(){const{pswp:t}=this;this._animateZoom&&(this._croppedZoom&&this._cropContainer1&&this._cropContainer2&&(this._animateTo(this._cropContainer1,"transform","translate3d(0,0,0)"),this._animateTo(this._cropContainer2,"transform","none")),t.currSlide&&(t.currSlide.zoomAndPanToInitial(),this._animateTo(t.currSlide.container,"transform",t.currSlide.getCurrentTransform()))),this._animateBgOpacity&&t.bg&&this._animateTo(t.bg,"opacity",String(t.options.bgOpacity)),this._animateRootOpacity&&t.element&&this._animateTo(t.element,"opacity","1")}_animateToClosedState(){const{pswp:t}=this;this._animateZoom&&this._setClosedStateZoomPan(!0),this._animateBgOpacity&&t.bgOpacity>.01&&t.bg&&this._animateTo(t.bg,"opacity","0"),this._animateRootOpacity&&t.element&&this._animateTo(t.element,"opacity","0")}_setClosedStateZoomPan(t){if(!this._thumbBounds)return;const{pswp:e}=this,{innerRect:i}=this._thumbBounds,{currSlide:n,viewportSize:o}=e;if(this._croppedZoom&&i&&this._cropContainer1&&this._cropContainer2){const r=-o.x+(this._thumbBounds.x-i.x)+i.w,a=-o.y+(this._thumbBounds.y-i.y)+i.h,h=o.x-i.w,c=o.y-i.h;t?(this._animateTo(this._cropContainer1,"transform",ot(r,a)),this._animateTo(this._cropContainer2,"transform",ot(h,c))):(z(this._cropContainer1,r,a),z(this._cropContainer2,h,c))}n&&(f(n.pan,i||this._thumbBounds),n.currZoomLevel=this._thumbBounds.w/n.width,t?this._animateTo(n.container,"transform",n.getCurrentTransform()):n.applyCurrentZoomPan())}_animateTo(t,e,i){if(!this._duration){t.style[e]=i;return}const{animations:n}=this.pswp,o={duration:this._duration,easing:this.pswp.options.easing,onComplete:()=>{n.activeAnimations.length||this._onAnimationComplete()},target:t};o[e]=i,n.startTransition(o)}}const Es={allowPanToNext:!0,spacing:.1,loop:!0,pinchToClose:!0,closeOnVerticalDrag:!0,hideAnimationDuration:333,showAnimationDuration:333,zoomAnimationDuration:333,escKey:!0,arrowKeys:!0,trapFocus:!0,returnFocus:!0,maxWidthToAnimate:4e3,clickToCloseNonZoomable:!0,imageClickAction:"zoom-or-close",bgClickAction:"close",tapAction:"toggle-controls",doubleTapAction:"zoom",indexIndicatorSep:" / ",preloaderDelay:2e3,bgOpacity:.8,index:0,errorMsg:"The image cannot be loaded",preload:[1,2],easing:"cubic-bezier(.4,0,.22,1)"};class Ra extends ms{constructor(t){super(),this.options=this._prepareOptions(t||{}),this.offset={x:0,y:0},this._prevViewportSize={x:0,y:0},this.viewportSize={x:0,y:0},this.bgOpacity=1,this.currIndex=0,this.potentialIndex=0,this.isOpen=!1,this.isDestroying=!1,this.hasMouse=!1,this._initialItemData={},this._initialThumbBounds=void 0,this.topBar=void 0,this.element=void 0,this.template=void 0,this.container=void 0,this.scrollWrap=void 0,this.currSlide=void 0,this.events=new Ti,this.animations=new ji,this.mainScroll=new Vi(this),this.gestures=new ki(this),this.opener=new gs(this),this.keyboard=new Zi(this),this.contentLoader=new fs(this)}init(){if(this.isOpen||this.isDestroying)return!1;this.isOpen=!0,this.dispatch("init"),this.dispatch("beforeOpen"),this._createMainStructure();let t="pswp--open";return this.gestures.supportsTouch&&(t+=" pswp--touch"),this.options.mainClass&&(t+=" "+this.options.mainClass),this.element&&(this.element.className+=" "+t),this.currIndex=this.options.index||0,this.potentialIndex=this.currIndex,this.dispatch("firstUpdate"),this.scrollWheel=new qi(this),(Number.isNaN(this.currIndex)||this.currIndex<0||this.currIndex>=this.getNumItems())&&(this.currIndex=0),this.gestures.supportsTouch||this.mouseDetected(),this.updateSize(),this.offset.y=window.pageYOffset,this._initialItemData=this.getItemData(this.currIndex),this.dispatch("gettingData",{index:this.currIndex,data:this._initialItemData,slide:void 0}),this._initialThumbBounds=this.getThumbBounds(),this.dispatch("initialLayout"),this.on("openingAnimationEnd",()=>{const{itemHolders:e}=this.mainScroll;e[0]&&(e[0].el.style.display="block",this.setContent(e[0],this.currIndex-1)),e[2]&&(e[2].el.style.display="block",this.setContent(e[2],this.currIndex+1)),this.appendHeavy(),this.contentLoader.updateLazy(),this.events.add(window,"resize",this._handlePageResize.bind(this)),this.events.add(window,"scroll",this._updatePageScrollOffset.bind(this)),this.dispatch("bindEvents")}),this.mainScroll.itemHolders[1]&&this.setContent(this.mainScroll.itemHolders[1],this.currIndex),this.dispatch("change"),this.opener.open(),this.dispatch("afterInit"),!0}getLoopedIndex(t){const e=this.getNumItems();return this.options.loop&&(t>e-1&&(t-=e),t<0&&(t+=e)),rt(t,0,e-1)}appendHeavy(){this.mainScroll.itemHolders.forEach(t=>{var e;(e=t.slide)===null||e===void 0||e.appendHeavy()})}goTo(t){this.mainScroll.moveIndexBy(this.getLoopedIndex(t)-this.potentialIndex)}next(){this.goTo(this.potentialIndex+1)}prev(){this.goTo(this.potentialIndex-1)}zoomTo(...t){var e;(e=this.currSlide)===null||e===void 0||e.zoomTo(...t)}toggleZoom(){var t;(t=this.currSlide)===null||t===void 0||t.toggleZoom()}close(){!this.opener.isOpen||this.isDestroying||(this.isDestroying=!0,this.dispatch("close"),this.events.removeAll(),this.opener.close())}destroy(){var t;if(!this.isDestroying){this.options.showHideAnimationType="none",this.close();return}this.dispatch("destroy"),this._listeners={},this.scrollWrap&&(this.scrollWrap.ontouchmove=null,this.scrollWrap.ontouchend=null),(t=this.element)===null||t===void 0||t.remove(),this.mainScroll.itemHolders.forEach(e=>{var i;(i=e.slide)===null||i===void 0||i.destroy()}),this.contentLoader.destroy(),this.events.removeAll()}refreshSlideContent(t){this.contentLoader.removeByIndex(t),this.mainScroll.itemHolders.forEach((e,i)=>{var n,o;let r=((n=(o=this.currSlide)===null||o===void 0?void 0:o.index)!==null&&n!==void 0?n:0)-1+i;if(this.canLoop()&&(r=this.getLoopedIndex(r)),r===t&&(this.setContent(e,t,!0),i===1)){var a;this.currSlide=e.slide,(a=e.slide)===null||a===void 0||a.setIsActive(!0)}}),this.dispatch("change")}setContent(t,e,i){if(this.canLoop()&&(e=this.getLoopedIndex(e)),t.slide){if(t.slide.index===e&&!i)return;t.slide.destroy(),t.slide=void 0}if(!this.canLoop()&&(e<0||e>=this.getNumItems()))return;const n=this.getItemData(e);t.slide=new Si(n,e,this),e===this.currIndex&&(this.currSlide=t.slide),t.slide.append(t.el)}getViewportCenterPoint(){return{x:this.viewportSize.x/2,y:this.viewportSize.y/2}}updateSize(t){if(this.isDestroying)return;const e=Re(this.options,this);!t&&nt(e,this._prevViewportSize)||(f(this._prevViewportSize,e),this.dispatch("beforeResize"),f(this.viewportSize,this._prevViewportSize),this._updatePageScrollOffset(),this.dispatch("viewportSize"),this.mainScroll.resize(this.opener.isOpen),!this.hasMouse&&window.matchMedia("(any-hover: hover)").matches&&this.mouseDetected(),this.dispatch("resize"))}applyBgOpacity(t){this.bgOpacity=Math.max(t,0),this.bg&&(this.bg.style.opacity=String(this.bgOpacity*this.options.bgOpacity))}mouseDetected(){if(!this.hasMouse){var t;this.hasMouse=!0,(t=this.element)===null||t===void 0||t.classList.add("pswp--has_mouse")}}_handlePageResize(){this.updateSize(),/iPhone|iPad|iPod/i.test(window.navigator.userAgent)&&setTimeout(()=>{this.updateSize()},500)}_updatePageScrollOffset(){this.setScrollOffset(0,window.pageYOffset)}setScrollOffset(t,e){this.offset.x=t,this.offset.y=e,this.dispatch("updateScrollOffset")}_createMainStructure(){this.element=v("pswp","div"),this.element.setAttribute("tabindex","-1"),this.element.setAttribute("role","dialog"),this.template=this.element,this.bg=v("pswp__bg","div",this.element),this.scrollWrap=v("pswp__scroll-wrap","section",this.element),this.container=v("pswp__container","div",this.scrollWrap),this.scrollWrap.setAttribute("aria-roledescription","carousel"),this.container.setAttribute("aria-live","off"),this.container.setAttribute("id","pswp__items"),this.mainScroll.appendHolders(),this.ui=new os(this),this.ui.init(),(this.options.appendToEl||document.body).appendChild(this.element)}getThumbBounds(){return ls(this.currIndex,this.currSlide?this.currSlide.data:this._initialItemData,this)}canLoop(){return this.options.loop&&this.getNumItems()>2}_prepareOptions(t){return window.matchMedia("(prefers-reduced-motion), (update: slow)").matches&&(t.showHideAnimationType="none",t.zoomAnimationDuration=0),{...Es,...t}}}/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const N=new Map,Lt={set(s,t,e){N.has(s)||N.set(s,new Map);const i=N.get(s);if(!i.has(t)&&i.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(i.keys())[0]}.`);return}i.set(t,e)},get(s,t){return N.has(s)&&N.get(s).get(t)||null},remove(s,t){if(!N.has(s))return;const e=N.get(s);e.delete(t),e.size===0&&N.delete(s)}},vs=1e6,ys=1e3,Wt="transitionend",He=s=>(s&&window.CSS&&window.CSS.escape&&(s=s.replace(/#([^\s"#']+)/g,(t,e)=>`#${CSS.escape(e)}`)),s),As=s=>s==null?`${s}`:Object.prototype.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase(),Ts=s=>{do s+=Math.floor(Math.random()*vs);while(document.getElementById(s));return s},bs=s=>{if(!s)return 0;let{transitionDuration:t,transitionDelay:e}=window.getComputedStyle(s);const i=Number.parseFloat(t),n=Number.parseFloat(e);return!i&&!n?0:(t=t.split(",")[0],e=e.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(e))*ys)},Ze=s=>{s.dispatchEvent(new Event(Wt))},L=s=>!s||typeof s!="object"?!1:(typeof s.jquery<"u"&&(s=s[0]),typeof s.nodeType<"u"),D=s=>L(s)?s.jquery?s[0]:s:typeof s=="string"&&s.length>0?document.querySelector(He(s)):null,X=s=>{if(!L(s)||s.getClientRects().length===0)return!1;const t=getComputedStyle(s).getPropertyValue("visibility")==="visible",e=s.closest("details:not([open])");if(!e)return t;if(e!==s){const i=s.closest("summary");if(i&&i.parentNode!==e||i===null)return!1}return t},$=s=>!s||s.nodeType!==Node.ELEMENT_NODE||s.classList.contains("disabled")?!0:typeof s.disabled<"u"?s.disabled:s.hasAttribute("disabled")&&s.getAttribute("disabled")!=="false",Be=s=>{if(!document.documentElement.attachShadow)return null;if(typeof s.getRootNode=="function"){const t=s.getRootNode();return t instanceof ShadowRoot?t:null}return s instanceof ShadowRoot?s:s.parentNode?Be(s.parentNode):null},Tt=()=>{},at=s=>{s.offsetHeight},We=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,It=[],Ss=s=>{document.readyState==="loading"?(It.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of It)t()}),It.push(s)):s()},A=()=>document.documentElement.dir==="rtl",b=s=>{Ss(()=>{const t=We();if(t){const e=s.NAME,i=t.fn[e];t.fn[e]=s.jQueryInterface,t.fn[e].Constructor=s,t.fn[e].noConflict=()=>(t.fn[e]=i,s.jQueryInterface)}})},g=(s,t=[],e=s)=>typeof s=="function"?s(...t):e,Ke=(s,t,e=!0)=>{if(!e){g(s);return}const n=bs(t)+5;let o=!1;const r=({target:a})=>{a===t&&(o=!0,t.removeEventListener(Wt,r),g(s))};t.addEventListener(Wt,r),setTimeout(()=>{o||Ze(t)},n)},Gt=(s,t,e,i)=>{const n=s.length;let o=s.indexOf(t);return o===-1?!e&&i?s[n-1]:s[0]:(o+=e?1:-1,i&&(o=(o+n)%n),s[Math.max(0,Math.min(o,n-1))])},ws=/[^.]*(?=\..*)\.|.*/,Cs=/\..*/,Ls=/::\d+$/,Ot={};let se=1;const Ue={mouseenter:"mouseover",mouseleave:"mouseout"},Is=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Ye(s,t){return t&&`${t}::${se++}`||s.uidEvent||se++}function Ge(s){const t=Ye(s);return s.uidEvent=t,Ot[t]=Ot[t]||{},Ot[t]}function Os(s,t){return function e(i){return jt(i,{delegateTarget:s}),e.oneOff&&l.off(s,i.type,t),t.apply(s,[i])}}function Ps(s,t,e){return function i(n){const o=s.querySelectorAll(t);for(let{target:r}=n;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return jt(n,{delegateTarget:r}),i.oneOff&&l.off(s,n.type,t,e),e.apply(r,[n])}}function je(s,t,e=null){return Object.values(s).find(i=>i.callable===t&&i.delegationSelector===e)}function qe(s,t,e){const i=typeof t=="string",n=i?e:t||e;let o=Xe(s);return Is.has(o)||(o=s),[i,n,o]}function ne(s,t,e,i,n){if(typeof t!="string"||!s)return;let[o,r,a]=qe(t,e,i);t in Ue&&(r=(C=>function(E){if(!E.relatedTarget||E.relatedTarget!==E.delegateTarget&&!E.delegateTarget.contains(E.relatedTarget))return C.call(this,E)})(r));const h=Ge(s),c=h[a]||(h[a]={}),d=je(c,r,o?e:null);if(d){d.oneOff=d.oneOff&&n;return}const p=Ye(r,t.replace(ws,"")),_=o?Ps(s,e,r):Os(s,r);_.delegationSelector=o?e:null,_.callable=r,_.oneOff=n,_.uidEvent=p,c[p]=_,s.addEventListener(a,_,o)}function Kt(s,t,e,i,n){const o=je(t[e],i,n);o&&(s.removeEventListener(e,o,!!n),delete t[e][o.uidEvent])}function Ns(s,t,e,i){const n=t[e]||{};for(const[o,r]of Object.entries(n))o.includes(i)&&Kt(s,t,e,r.callable,r.delegationSelector)}function Xe(s){return s=s.replace(Cs,""),Ue[s]||s}const l={on(s,t,e,i){ne(s,t,e,i,!1)},one(s,t,e,i){ne(s,t,e,i,!0)},off(s,t,e,i){if(typeof t!="string"||!s)return;const[n,o,r]=qe(t,e,i),a=r!==t,h=Ge(s),c=h[r]||{},d=t.startsWith(".");if(typeof o<"u"){if(!Object.keys(c).length)return;Kt(s,h,r,o,n?e:null);return}if(d)for(const p of Object.keys(h))Ns(s,h,p,t.slice(1));for(const[p,_]of Object.entries(c)){const m=p.replace(Ls,"");(!a||t.includes(m))&&Kt(s,h,r,_.callable,_.delegationSelector)}},trigger(s,t,e){if(typeof t!="string"||!s)return null;const i=We(),n=Xe(t),o=t!==n;let r=null,a=!0,h=!0,c=!1;o&&i&&(r=i.Event(t,e),i(s).trigger(r),a=!r.isPropagationStopped(),h=!r.isImmediatePropagationStopped(),c=r.isDefaultPrevented());const d=jt(new Event(t,{bubbles:a,cancelable:!0}),e);return c&&d.preventDefault(),h&&s.dispatchEvent(d),d.defaultPrevented&&r&&r.preventDefault(),d}};function jt(s,t={}){for(const[e,i]of Object.entries(t))try{s[e]=i}catch{Object.defineProperty(s,e,{configurable:!0,get(){return i}})}return s}function oe(s){if(s==="true")return!0;if(s==="false")return!1;if(s===Number(s).toString())return Number(s);if(s===""||s==="null")return null;if(typeof s!="string")return s;try{return JSON.parse(decodeURIComponent(s))}catch{return s}}function Pt(s){return s.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const I={setDataAttribute(s,t,e){s.setAttribute(`data-bs-${Pt(t)}`,e)},removeDataAttribute(s,t){s.removeAttribute(`data-bs-${Pt(t)}`)},getDataAttributes(s){if(!s)return{};const t={},e=Object.keys(s.dataset).filter(i=>i.startsWith("bs")&&!i.startsWith("bsConfig"));for(const i of e){let n=i.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),t[n]=oe(s.dataset[i])}return t},getDataAttribute(s,t){return oe(s.getAttribute(`data-bs-${Pt(t)}`))}};class lt{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=L(e)?I.getDataAttribute(e,"config"):{};return{...this.constructor.Default,...typeof i=="object"?i:{},...L(e)?I.getDataAttributes(e):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[i,n]of Object.entries(e)){const o=t[i],r=L(o)?"element":As(o);if(!new RegExp(n).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${r}" but expected type "${n}".`)}}}const Ds="5.3.3";class w extends lt{constructor(t,e){super(),t=D(t),t&&(this._element=t,this._config=this._getConfig(e),Lt.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Lt.remove(this._element,this.constructor.DATA_KEY),l.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){Ke(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return Lt.get(D(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,typeof e=="object"?e:null)}static get VERSION(){return Ds}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Nt=s=>{let t=s.getAttribute("data-bs-target");if(!t||t==="#"){let e=s.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),t=e&&e!=="#"?e.trim():null}return t?t.split(",").map(e=>He(e)).join(","):null},u={find(s,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,s))},findOne(s,t=document.documentElement){return Element.prototype.querySelector.call(t,s)},children(s,t){return[].concat(...s.children).filter(e=>e.matches(t))},parents(s,t){const e=[];let i=s.parentNode.closest(t);for(;i;)e.push(i),i=i.parentNode.closest(t);return e},prev(s,t){let e=s.previousElementSibling;for(;e;){if(e.matches(t))return[e];e=e.previousElementSibling}return[]},next(s,t){let e=s.nextElementSibling;for(;e;){if(e.matches(t))return[e];e=e.nextElementSibling}return[]},focusableChildren(s){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,s).filter(e=>!$(e)&&X(e))},getSelectorFromElement(s){const t=Nt(s);return t&&u.findOne(t)?t:null},getElementFromSelector(s){const t=Nt(s);return t?u.findOne(t):null},getMultipleElementsFromSelector(s){const t=Nt(s);return t?u.find(t):[]}},wt=(s,t="hide")=>{const e=`click.dismiss${s.EVENT_KEY}`,i=s.NAME;l.on(document,e,`[data-bs-dismiss="${i}"]`,function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),$(this))return;const o=u.getElementFromSelector(this)||this.closest(`.${i}`);s.getOrCreateInstance(o)[t]()})},$s="alert",xs="bs.alert",Qe=`.${xs}`,Ms=`close${Qe}`,Rs=`closed${Qe}`,zs="fade",ks="show";class ht extends w{static get NAME(){return $s}close(){if(l.trigger(this._element,Ms).defaultPrevented)return;this._element.classList.remove(ks);const e=this._element.classList.contains(zs);this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),l.trigger(this._element,Rs),this.dispose()}static jQueryInterface(t){return this.each(function(){const e=ht.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}wt(ht,"close");b(ht);const Fs="button",Vs="bs.button",Hs=`.${Vs}`,Zs=".data-api",Bs="active",re='[data-bs-toggle="button"]',Ws=`click${Hs}${Zs}`;class ct extends w{static get NAME(){return Fs}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(Bs))}static jQueryInterface(t){return this.each(function(){const e=ct.getOrCreateInstance(this);t==="toggle"&&e[t]()})}}l.on(document,Ws,re,s=>{s.preventDefault();const t=s.target.closest(re);ct.getOrCreateInstance(t).toggle()});b(ct);const Ks="swipe",Q=".bs.swipe",Us=`touchstart${Q}`,Ys=`touchmove${Q}`,Gs=`touchend${Q}`,js=`pointerdown${Q}`,qs=`pointerup${Q}`,Xs="touch",Qs="pen",Js="pointer-event",tn=40,en={endCallback:null,leftCallback:null,rightCallback:null},sn={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class bt extends lt{constructor(t,e){super(),this._element=t,!(!t||!bt.isSupported())&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return en}static get DefaultType(){return sn}static get NAME(){return Ks}dispose(){l.off(this._element,Q)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=tn)return;const e=t/this._deltaX;this._deltaX=0,e&&g(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(l.on(this._element,js,t=>this._start(t)),l.on(this._element,qs,t=>this._end(t)),this._element.classList.add(Js)):(l.on(this._element,Us,t=>this._start(t)),l.on(this._element,Ys,t=>this._move(t)),l.on(this._element,Gs,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===Qs||t.pointerType===Xs)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const nn="carousel",on="bs.carousel",x=`.${on}`,Je=".data-api",rn="ArrowLeft",an="ArrowRight",ln=500,et="next",K="prev",Y="left",yt="right",hn=`slide${x}`,Dt=`slid${x}`,cn=`keydown${x}`,dn=`mouseenter${x}`,un=`mouseleave${x}`,pn=`dragstart${x}`,_n=`load${x}${Je}`,fn=`click${x}${Je}`,ti="carousel",_t="active",mn="slide",gn="carousel-item-end",En="carousel-item-start",vn="carousel-item-next",yn="carousel-item-prev",ei=".active",ii=".carousel-item",An=ei+ii,Tn=".carousel-item img",bn=".carousel-indicators",Sn="[data-bs-slide], [data-bs-slide-to]",wn='[data-bs-ride="carousel"]',Cn={[rn]:yt,[an]:Y},Ln={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},In={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class J extends w{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=u.findOne(bn,this._element),this._addEventListeners(),this._config.ride===ti&&this.cycle()}static get Default(){return Ln}static get DefaultType(){return In}static get NAME(){return nn}next(){this._slide(et)}nextWhenVisible(){!document.hidden&&X(this._element)&&this.next()}prev(){this._slide(K)}pause(){this._isSliding&&Ze(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){l.one(this._element,Dt,()=>this.cycle());return}this.cycle()}}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding){l.one(this._element,Dt,()=>this.to(t));return}const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?et:K;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&l.on(this._element,cn,t=>this._keydown(t)),this._config.pause==="hover"&&(l.on(this._element,dn,()=>this.pause()),l.on(this._element,un,()=>this._maybeEnableCycle())),this._config.touch&&bt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const i of u.find(Tn,this._element))l.on(i,pn,n=>n.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(Y)),rightCallback:()=>this._slide(this._directionToOrder(yt)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),ln+this._config.interval))}};this._swipeHelper=new bt(this._element,e)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Cn[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=u.findOne(ei,this._indicatorsElement);e.classList.remove(_t),e.removeAttribute("aria-current");const i=u.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(_t),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===et,o=e||Gt(this._getItems(),i,n,this._config.wrap);if(o===i)return;const r=this._getItemIndex(o),a=m=>l.trigger(this._element,m,{relatedTarget:o,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:r});if(a(hn).defaultPrevented||!i||!o)return;const c=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=o;const d=n?En:gn,p=n?vn:yn;o.classList.add(p),at(o),i.classList.add(d),o.classList.add(d);const _=()=>{o.classList.remove(d,p),o.classList.add(_t),i.classList.remove(_t,p,d),this._isSliding=!1,a(Dt)};this._queueCallback(_,i,this._isAnimated()),c&&this.cycle()}_isAnimated(){return this._element.classList.contains(mn)}_getActive(){return u.findOne(An,this._element)}_getItems(){return u.find(ii,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return A()?t===Y?K:et:t===Y?et:K}_orderToDirection(t){return A()?t===K?Y:yt:t===K?yt:Y}static jQueryInterface(t){return this.each(function(){const e=J.getOrCreateInstance(this,t);if(typeof t=="number"){e.to(t);return}if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}l.on(document,fn,Sn,function(s){const t=u.getElementFromSelector(this);if(!t||!t.classList.contains(ti))return;s.preventDefault();const e=J.getOrCreateInstance(t),i=this.getAttribute("data-bs-slide-to");if(i){e.to(i),e._maybeEnableCycle();return}if(I.getDataAttribute(this,"slide")==="next"){e.next(),e._maybeEnableCycle();return}e.prev(),e._maybeEnableCycle()});l.on(window,_n,()=>{const s=u.find(wn);for(const t of s)J.getOrCreateInstance(t)});b(J);const On="collapse",Pn="bs.collapse",dt=`.${Pn}`,Nn=".data-api",Dn=`show${dt}`,$n=`shown${dt}`,xn=`hide${dt}`,Mn=`hidden${dt}`,Rn=`click${dt}${Nn}`,$t="show",j="collapse",ft="collapsing",zn="collapsed",kn=`:scope .${j} .${j}`,Fn="collapse-horizontal",Vn="width",Hn="height",Zn=".collapse.show, .collapse.collapsing",Ut='[data-bs-toggle="collapse"]',Bn={parent:null,toggle:!0},Wn={parent:"(null|element)",toggle:"boolean"};class q extends w{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=u.find(Ut);for(const n of i){const o=u.getSelectorFromElement(n),r=u.find(o).filter(a=>a===this._element);o!==null&&r.length&&this._triggerArray.push(n)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Bn}static get DefaultType(){return Wn}static get NAME(){return On}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(Zn).filter(a=>a!==this._element).map(a=>q.getOrCreateInstance(a,{toggle:!1}))),t.length&&t[0]._isTransitioning||l.trigger(this._element,Dn).defaultPrevented)return;for(const a of t)a.hide();const i=this._getDimension();this._element.classList.remove(j),this._element.classList.add(ft),this._element.style[i]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=()=>{this._isTransitioning=!1,this._element.classList.remove(ft),this._element.classList.add(j,$t),this._element.style[i]="",l.trigger(this._element,$n)},r=`scroll${i[0].toUpperCase()+i.slice(1)}`;this._queueCallback(n,this._element,!0),this._element.style[i]=`${this._element[r]}px`}hide(){if(this._isTransitioning||!this._isShown()||l.trigger(this._element,xn).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,at(this._element),this._element.classList.add(ft),this._element.classList.remove(j,$t);for(const n of this._triggerArray){const o=u.getElementFromSelector(n);o&&!this._isShown(o)&&this._addAriaAndCollapsedClass([n],!1)}this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(ft),this._element.classList.add(j),l.trigger(this._element,Mn)};this._element.style[e]="",this._queueCallback(i,this._element,!0)}_isShown(t=this._element){return t.classList.contains($t)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=D(t.parent),t}_getDimension(){return this._element.classList.contains(Fn)?Vn:Hn}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Ut);for(const e of t){const i=u.getElementFromSelector(e);i&&this._addAriaAndCollapsedClass([e],this._isShown(i))}}_getFirstLevelChildren(t){const e=u.find(kn,this._config.parent);return u.find(t,this._config.parent).filter(i=>!e.includes(i))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle(zn,!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return typeof t=="string"&&/show|hide/.test(t)&&(e.toggle=!1),this.each(function(){const i=q.getOrCreateInstance(this,e);if(typeof t=="string"){if(typeof i[t]>"u")throw new TypeError(`No method named "${t}"`);i[t]()}})}}l.on(document,Rn,Ut,function(s){(s.target.tagName==="A"||s.delegateTarget&&s.delegateTarget.tagName==="A")&&s.preventDefault();for(const t of u.getMultipleElementsFromSelector(this))q.getOrCreateInstance(t,{toggle:!1}).toggle()});b(q);const ae="dropdown",Kn="bs.dropdown",Z=`.${Kn}`,qt=".data-api",Un="Escape",le="Tab",Yn="ArrowUp",he="ArrowDown",Gn=2,jn=`hide${Z}`,qn=`hidden${Z}`,Xn=`show${Z}`,Qn=`shown${Z}`,si=`click${Z}${qt}`,ni=`keydown${Z}${qt}`,Jn=`keyup${Z}${qt}`,G="show",to="dropup",eo="dropend",io="dropstart",so="dropup-center",no="dropdown-center",k='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',oo=`${k}.${G}`,At=".dropdown-menu",ro=".navbar",ao=".navbar-nav",lo=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",ho=A()?"top-end":"top-start",co=A()?"top-start":"top-end",uo=A()?"bottom-end":"bottom-start",po=A()?"bottom-start":"bottom-end",_o=A()?"left-start":"right-start",fo=A()?"right-start":"left-start",mo="top",go="bottom",Eo={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},vo={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class S extends w{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=u.next(this._element,At)[0]||u.prev(this._element,At)[0]||u.findOne(At,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Eo}static get DefaultType(){return vo}static get NAME(){return ae}toggle(){return this._isShown()?this.hide():this.show()}show(){if($(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!l.trigger(this._element,Xn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(ao))for(const i of[].concat(...document.body.children))l.on(i,"mouseover",Tt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(G),this._element.classList.add(G),l.trigger(this._element,Qn,t)}}hide(){if($(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!l.trigger(this._element,jn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))l.off(i,"mouseover",Tt);this._popper&&this._popper.destroy(),this._menu.classList.remove(G),this._element.classList.remove(G),this._element.setAttribute("aria-expanded","false"),I.removeDataAttribute(this._menu,"popper"),l.trigger(this._element,qn,t)}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!L(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${ae.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof Ne>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:L(this._config.reference)?t=D(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=De(t,this._menu,e)}_isShown(){return this._menu.classList.contains(G)}_getPlacement(){const t=this._parent;if(t.classList.contains(eo))return _o;if(t.classList.contains(io))return fo;if(t.classList.contains(so))return mo;if(t.classList.contains(no))return go;const e=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(to)?e?co:ho:e?po:uo}_detectNavbar(){return this._element.closest(ro)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(I.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...g(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const i=u.find(lo,this._menu).filter(n=>X(n));i.length&&Gt(i,e,t===he,!i.includes(e)).focus()}static jQueryInterface(t){return this.each(function(){const e=S.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}static clearMenus(t){if(t.button===Gn||t.type==="keyup"&&t.key!==le)return;const e=u.find(oo);for(const i of e){const n=S.getInstance(i);if(!n||n._config.autoClose===!1)continue;const o=t.composedPath(),r=o.includes(n._menu);if(o.includes(n._element)||n._config.autoClose==="inside"&&!r||n._config.autoClose==="outside"&&r||n._menu.contains(t.target)&&(t.type==="keyup"&&t.key===le||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const a={relatedTarget:n._element};t.type==="click"&&(a.clickEvent=t),n._completeHide(a)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i=t.key===Un,n=[Yn,he].includes(t.key);if(!n&&!i||e&&!i)return;t.preventDefault();const o=this.matches(k)?this:u.prev(this,k)[0]||u.next(this,k)[0]||u.findOne(k,t.delegateTarget.parentNode),r=S.getOrCreateInstance(o);if(n){t.stopPropagation(),r.show(),r._selectMenuItem(t);return}r._isShown()&&(t.stopPropagation(),r.hide(),o.focus())}}l.on(document,ni,k,S.dataApiKeydownHandler);l.on(document,ni,At,S.dataApiKeydownHandler);l.on(document,si,S.clearMenus);l.on(document,Jn,S.clearMenus);l.on(document,si,k,function(s){s.preventDefault(),S.getOrCreateInstance(this).toggle()});b(S);const oi="backdrop",yo="fade",ce="show",de=`mousedown.bs.${oi}`,Ao={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},To={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class ri extends lt{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Ao}static get DefaultType(){return To}static get NAME(){return oi}show(t){if(!this._config.isVisible){g(t);return}this._append();const e=this._getElement();this._config.isAnimated&&at(e),e.classList.add(ce),this._emulateAnimation(()=>{g(t)})}hide(t){if(!this._config.isVisible){g(t);return}this._getElement().classList.remove(ce),this._emulateAnimation(()=>{this.dispose(),g(t)})}dispose(){this._isAppended&&(l.off(this._element,de),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(yo),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=D(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),l.on(t,de,()=>{g(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){Ke(t,this._getElement(),this._config.isAnimated)}}const bo="focustrap",So="bs.focustrap",St=`.${So}`,wo=`focusin${St}`,Co=`keydown.tab${St}`,Lo="Tab",Io="forward",ue="backward",Oo={autofocus:!0,trapElement:null},Po={autofocus:"boolean",trapElement:"element"};class ai extends lt{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Oo}static get DefaultType(){return Po}static get NAME(){return bo}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),l.off(document,St),l.on(document,wo,t=>this._handleFocusin(t)),l.on(document,Co,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,l.off(document,St))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=u.focusableChildren(e);i.length===0?e.focus():this._lastTabNavDirection===ue?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){t.key===Lo&&(this._lastTabNavDirection=t.shiftKey?ue:Io)}}const pe=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",_e=".sticky-top",mt="padding-right",fe="margin-right";class Yt{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,mt,e=>e+t),this._setElementAttributes(pe,mt,e=>e+t),this._setElementAttributes(_e,fe,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,mt),this._resetElementAttributes(pe,mt),this._resetElementAttributes(_e,fe)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth(),o=r=>{if(r!==this._element&&window.innerWidth>r.clientWidth+n)return;this._saveInitialAttribute(r,e);const a=window.getComputedStyle(r).getPropertyValue(e);r.style.setProperty(e,`${i(Number.parseFloat(a))}px`)};this._applyManipulationCallback(t,o)}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&I.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){const i=n=>{const o=I.getDataAttribute(n,e);if(o===null){n.style.removeProperty(e);return}I.removeDataAttribute(n,e),n.style.setProperty(e,o)};this._applyManipulationCallback(t,i)}_applyManipulationCallback(t,e){if(L(t)){e(t);return}for(const i of u.find(t,this._element))e(i)}}const No="modal",Do="bs.modal",T=`.${Do}`,$o=".data-api",xo="Escape",Mo=`hide${T}`,Ro=`hidePrevented${T}`,li=`hidden${T}`,hi=`show${T}`,zo=`shown${T}`,ko=`resize${T}`,Fo=`click.dismiss${T}`,Vo=`mousedown.dismiss${T}`,Ho=`keydown.dismiss${T}`,Zo=`click${T}${$o}`,me="modal-open",Bo="fade",ge="show",xt="modal-static",Wo=".modal.show",Ko=".modal-dialog",Uo=".modal-body",Yo='[data-bs-toggle="modal"]',Go={backdrop:!0,focus:!0,keyboard:!0},jo={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class V extends w{constructor(t,e){super(t,e),this._dialog=u.findOne(Ko,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Yt,this._addEventListeners()}static get Default(){return Go}static get DefaultType(){return jo}static get NAME(){return No}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||l.trigger(this._element,hi,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(me),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||l.trigger(this._element,Mo).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(ge),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){l.off(window,T),l.off(this._dialog,T),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new ri({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ai({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=u.findOne(Uo,this._dialog);e&&(e.scrollTop=0),at(this._element),this._element.classList.add(ge);const i=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,l.trigger(this._element,zo,{relatedTarget:t})};this._queueCallback(i,this._dialog,this._isAnimated())}_addEventListeners(){l.on(this._element,Ho,t=>{if(t.key===xo){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),l.on(window,ko,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),l.on(this._element,Vo,t=>{l.one(this._element,Fo,e=>{if(!(this._element!==t.target||this._element!==e.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(me),this._resetAdjustments(),this._scrollBar.reset(),l.trigger(this._element,li)})}_isAnimated(){return this._element.classList.contains(Bo)}_triggerBackdropTransition(){if(l.trigger(this._element,Ro).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,i=this._element.style.overflowY;i==="hidden"||this._element.classList.contains(xt)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(xt),this._queueCallback(()=>{this._element.classList.remove(xt),this._queueCallback(()=>{this._element.style.overflowY=i},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const n=A()?"paddingLeft":"paddingRight";this._element.style[n]=`${e}px`}if(!i&&t){const n=A()?"paddingRight":"paddingLeft";this._element.style[n]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const i=V.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof i[t]>"u")throw new TypeError(`No method named "${t}"`);i[t](e)}})}}l.on(document,Zo,Yo,function(s){const t=u.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&s.preventDefault(),l.one(t,hi,n=>{n.defaultPrevented||l.one(t,li,()=>{X(this)&&this.focus()})});const e=u.findOne(Wo);e&&V.getInstance(e).hide(),V.getOrCreateInstance(t).toggle(this)});wt(V);b(V);const qo="offcanvas",Xo="bs.offcanvas",P=`.${Xo}`,ci=".data-api",Qo=`load${P}${ci}`,Jo="Escape",Ee="show",ve="showing",ye="hiding",tr="offcanvas-backdrop",di=".offcanvas.show",er=`show${P}`,ir=`shown${P}`,sr=`hide${P}`,Ae=`hidePrevented${P}`,ui=`hidden${P}`,nr=`resize${P}`,or=`click${P}${ci}`,rr=`keydown.dismiss${P}`,ar='[data-bs-toggle="offcanvas"]',lr={backdrop:!0,keyboard:!0,scroll:!1},hr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class O extends w{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return lr}static get DefaultType(){return hr}static get NAME(){return qo}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||l.trigger(this._element,er,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Yt().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ve);const i=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(Ee),this._element.classList.remove(ve),l.trigger(this._element,ir,{relatedTarget:t})};this._queueCallback(i,this._element,!0)}hide(){if(!this._isShown||l.trigger(this._element,sr).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(ye),this._backdrop.hide();const e=()=>{this._element.classList.remove(Ee,ye),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Yt().reset(),l.trigger(this._element,ui)};this._queueCallback(e,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){l.trigger(this._element,Ae);return}this.hide()},e=!!this._config.backdrop;return new ri({className:tr,isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?t:null})}_initializeFocusTrap(){return new ai({trapElement:this._element})}_addEventListeners(){l.on(this._element,rr,t=>{if(t.key===Jo){if(this._config.keyboard){this.hide();return}l.trigger(this._element,Ae)}})}static jQueryInterface(t){return this.each(function(){const e=O.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}l.on(document,or,ar,function(s){const t=u.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),$(this))return;l.one(t,ui,()=>{X(this)&&this.focus()});const e=u.findOne(di);e&&e!==t&&O.getInstance(e).hide(),O.getOrCreateInstance(t).toggle(this)});l.on(window,Qo,()=>{for(const s of u.find(di))O.getOrCreateInstance(s).show()});l.on(window,nr,()=>{for(const s of u.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(s).position!=="fixed"&&O.getOrCreateInstance(s).hide()});wt(O);b(O);const cr=/^aria-[\w-]*$/i,pi={"*":["class","dir","id","lang","role",cr],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},dr=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ur=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,pr=(s,t)=>{const e=s.nodeName.toLowerCase();return t.includes(e)?dr.has(e)?!!ur.test(s.nodeValue):!0:t.filter(i=>i instanceof RegExp).some(i=>i.test(e))};function _r(s,t,e){if(!s.length)return s;if(e&&typeof e=="function")return e(s);const n=new window.DOMParser().parseFromString(s,"text/html"),o=[].concat(...n.body.querySelectorAll("*"));for(const r of o){const a=r.nodeName.toLowerCase();if(!Object.keys(t).includes(a)){r.remove();continue}const h=[].concat(...r.attributes),c=[].concat(t["*"]||[],t[a]||[]);for(const d of h)pr(d,c)||r.removeAttribute(d.nodeName)}return n.body.innerHTML}const fr="TemplateFactory",mr={allowList:pi,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},gr={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Er={entry:"(string|element|function|null)",selector:"(string|element)"};class vr extends lt{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return mr}static get DefaultType(){return gr}static get NAME(){return fr}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[n,o]of Object.entries(this._config.content))this._setContent(t,o,n);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},Er)}_setContent(t,e,i){const n=u.findOne(i,t);if(n){if(e=this._resolvePossibleFunction(e),!e){n.remove();return}if(L(e)){this._putElementInTemplate(D(e),n);return}if(this._config.html){n.innerHTML=this._maybeSanitize(e);return}n.textContent=e}}_maybeSanitize(t){return this._config.sanitize?_r(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return g(t,[this])}_putElementInTemplate(t,e){if(this._config.html){e.innerHTML="",e.append(t);return}e.textContent=t.textContent}}const yr="tooltip",Ar=new Set(["sanitize","allowList","sanitizeFn"]),Mt="fade",Tr="modal",gt="show",br=".tooltip-inner",Te=`.${Tr}`,be="hide.bs.modal",it="hover",Rt="focus",Sr="click",wr="manual",Cr="hide",Lr="hidden",Ir="show",Or="shown",Pr="inserted",Nr="click",Dr="focusin",$r="focusout",xr="mouseenter",Mr="mouseleave",Rr={AUTO:"auto",TOP:"top",RIGHT:A()?"left":"right",BOTTOM:"bottom",LEFT:A()?"right":"left"},zr={allowList:pi,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},kr={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class B extends w{constructor(t,e){if(typeof Ne>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return zr}static get DefaultType(){return kr}static get NAME(){return yr}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),l.off(this._element.closest(Te),be,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=l.trigger(this._element,this.constructor.eventName(Ir)),i=(Be(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!i)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:o}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(o.append(n),l.trigger(this._element,this.constructor.eventName(Pr))),this._popper=this._createPopper(n),n.classList.add(gt),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))l.on(a,"mouseover",Tt);const r=()=>{l.trigger(this._element,this.constructor.eventName(Or)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(r,this.tip,this._isAnimated())}hide(){if(!this._isShown()||l.trigger(this._element,this.constructor.eventName(Cr)).defaultPrevented)return;if(this._getTipElement().classList.remove(gt),"ontouchstart"in document.documentElement)for(const n of[].concat(...document.body.children))l.off(n,"mouseover",Tt);this._activeTrigger[Sr]=!1,this._activeTrigger[Rt]=!1,this._activeTrigger[it]=!1,this._isHovered=null;const i=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),l.trigger(this._element,this.constructor.eventName(Lr)))};this._queueCallback(i,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Mt,gt),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=Ts(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(Mt),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new vr({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[br]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Mt)}_isShown(){return this.tip&&this.tip.classList.contains(gt)}_createPopper(t){const e=g(this._config.placement,[this,t,this._element]),i=Rr[e.toUpperCase()];return De(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_resolvePossibleFunction(t){return g(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:i=>{this._getTipElement().setAttribute("data-popper-placement",i.state.placement)}}]};return{...e,...g(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if(e==="click")l.on(this._element,this.constructor.eventName(Nr),this._config.selector,i=>{this._initializeOnDelegatedTarget(i).toggle()});else if(e!==wr){const i=e===it?this.constructor.eventName(xr):this.constructor.eventName(Dr),n=e===it?this.constructor.eventName(Mr):this.constructor.eventName($r);l.on(this._element,i,this._config.selector,o=>{const r=this._initializeOnDelegatedTarget(o);r._activeTrigger[o.type==="focusin"?Rt:it]=!0,r._enter()}),l.on(this._element,n,this._config.selector,o=>{const r=this._initializeOnDelegatedTarget(o);r._activeTrigger[o.type==="focusout"?Rt:it]=r._element.contains(o.relatedTarget),r._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},l.on(this._element.closest(Te),be,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=I.getDataAttributes(this._element);for(const i of Object.keys(e))Ar.has(i)&&delete e[i];return t={...e,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:D(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=B.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}}b(B);const Fr="popover",Vr=".popover-header",Hr=".popover-body",Zr={...B.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Br={...B.DefaultType,content:"(null|string|element|function)"};class Ct extends B{static get Default(){return Zr}static get DefaultType(){return Br}static get NAME(){return Fr}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Vr]:this._getTitle(),[Hr]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=Ct.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}}b(Ct);const Wr="scrollspy",Kr="bs.scrollspy",Xt=`.${Kr}`,Ur=".data-api",Yr=`activate${Xt}`,Se=`click${Xt}`,Gr=`load${Xt}${Ur}`,jr="dropdown-item",U="active",qr='[data-bs-spy="scroll"]',zt="[href]",Xr=".nav, .list-group",we=".nav-link",Qr=".nav-item",Jr=".list-group-item",ta=`${we}, ${Qr} > ${we}, ${Jr}`,ea=".dropdown",ia=".dropdown-toggle",sa={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},na={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class ut extends w{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return sa}static get DefaultType(){return na}static get NAME(){return Wr}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=D(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(e=>Number.parseFloat(e))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(l.off(this._config.target,Se),l.on(this._config.target,Se,zt,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo){i.scrollTo({top:n,behavior:"smooth"});return}i.scrollTop=n}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),t)}_observerCallback(t){const e=r=>this._targetLinks.get(`#${r.target.id}`),i=r=>{this._previousScrollData.visibleEntryTop=r.target.offsetTop,this._process(e(r))},n=(this._rootElement||document.documentElement).scrollTop,o=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const r of t){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(r));continue}const a=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&a){if(i(r),!n)return;continue}!o&&!a&&i(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=u.find(zt,this._config.target);for(const e of t){if(!e.hash||$(e))continue;const i=u.findOne(decodeURI(e.hash),this._element);X(i)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,i))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(U),this._activateParents(t),l.trigger(this._element,Yr,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(jr)){u.findOne(ia,t.closest(ea)).classList.add(U);return}for(const e of u.parents(t,Xr))for(const i of u.prev(e,ta))i.classList.add(U)}_clearActiveClass(t){t.classList.remove(U);const e=u.find(`${zt}.${U}`,t);for(const i of e)i.classList.remove(U)}static jQueryInterface(t){return this.each(function(){const e=ut.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}l.on(window,Gr,()=>{for(const s of u.find(qr))ut.getOrCreateInstance(s)});b(ut);const oa="tab",ra="bs.tab",W=`.${ra}`,aa=`hide${W}`,la=`hidden${W}`,ha=`show${W}`,ca=`shown${W}`,da=`click${W}`,ua=`keydown${W}`,pa=`load${W}`,_a="ArrowLeft",Ce="ArrowRight",fa="ArrowUp",Le="ArrowDown",kt="Home",Ie="End",F="active",Oe="fade",Ft="show",ma="dropdown",_i=".dropdown-toggle",ga=".dropdown-menu",Vt=`:not(${_i})`,Ea='.list-group, .nav, [role="tablist"]',va=".nav-item, .list-group-item",ya=`.nav-link${Vt}, .list-group-item${Vt}, [role="tab"]${Vt}`,fi='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Ht=`${ya}, ${fi}`,Aa=`.${F}[data-bs-toggle="tab"], .${F}[data-bs-toggle="pill"], .${F}[data-bs-toggle="list"]`;class H extends w{constructor(t){super(t),this._parent=this._element.closest(Ea),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),l.on(this._element,ua,e=>this._keydown(e)))}static get NAME(){return oa}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?l.trigger(e,aa,{relatedTarget:t}):null;l.trigger(t,ha,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(F),this._activate(u.getElementFromSelector(t));const i=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(Ft);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),l.trigger(t,ca,{relatedTarget:e})};this._queueCallback(i,t,t.classList.contains(Oe))}_deactivate(t,e){if(!t)return;t.classList.remove(F),t.blur(),this._deactivate(u.getElementFromSelector(t));const i=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(Ft);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),l.trigger(t,la,{relatedTarget:e})};this._queueCallback(i,t,t.classList.contains(Oe))}_keydown(t){if(![_a,Ce,fa,Le,kt,Ie].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter(n=>!$(n));let i;if([kt,Ie].includes(t.key))i=e[t.key===kt?0:e.length-1];else{const n=[Ce,Le].includes(t.key);i=Gt(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),H.getOrCreateInstance(i).show())}_getChildren(){return u.find(Ht,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const i of e)this._setInitialAttributesOnChild(i)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=u.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains(ma))return;const n=(o,r)=>{const a=u.findOne(o,i);a&&a.classList.toggle(r,e)};n(_i,F),n(ga,Ft),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(F)}_getInnerElement(t){return t.matches(Ht)?t:u.findOne(Ht,t)}_getOuterElement(t){return t.closest(va)||t}static jQueryInterface(t){return this.each(function(){const e=H.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}l.on(document,da,fi,function(s){["A","AREA"].includes(this.tagName)&&s.preventDefault(),!$(this)&&H.getOrCreateInstance(this).show()});l.on(window,pa,()=>{for(const s of u.find(Aa))H.getOrCreateInstance(s)});b(H);const Ta="toast",ba="bs.toast",M=`.${ba}`,Sa=`mouseover${M}`,wa=`mouseout${M}`,Ca=`focusin${M}`,La=`focusout${M}`,Ia=`hide${M}`,Oa=`hidden${M}`,Pa=`show${M}`,Na=`shown${M}`,Da="fade",Pe="hide",Et="show",vt="showing",$a={animation:"boolean",autohide:"boolean",delay:"number"},xa={animation:!0,autohide:!0,delay:5e3};class pt extends w{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return xa}static get DefaultType(){return $a}static get NAME(){return Ta}show(){if(l.trigger(this._element,Pa).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(Da);const e=()=>{this._element.classList.remove(vt),l.trigger(this._element,Na),this._maybeScheduleHide()};this._element.classList.remove(Pe),at(this._element),this._element.classList.add(Et,vt),this._queueCallback(e,this._element,this._config.animation)}hide(){if(!this.isShown()||l.trigger(this._element,Ia).defaultPrevented)return;const e=()=>{this._element.classList.add(Pe),this._element.classList.remove(vt,Et),l.trigger(this._element,Oa)};this._element.classList.add(vt),this._queueCallback(e,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Et),super.dispose()}isShown(){return this._element.classList.contains(Et)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=e;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=e;break}}if(e){this._clearTimeout();return}const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){l.on(this._element,Sa,t=>this._onInteraction(t,!0)),l.on(this._element,wa,t=>this._onInteraction(t,!1)),l.on(this._element,Ca,t=>this._onInteraction(t,!0)),l.on(this._element,La,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const e=pt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}wt(pt);b(pt);const za=Object.freeze(Object.defineProperty({__proto__:null,Alert:ht,Button:ct,Carousel:J,Collapse:q,Dropdown:S,Modal:V,Offcanvas:O,Popover:Ct,ScrollSpy:ut,Tab:H,Toast:pt,Tooltip:B},Symbol.toStringTag,{value:"Module"}));export{Ra as P,za as b};
//# sourceMappingURL=vendor-ui-FmAuOtO-.js.map
