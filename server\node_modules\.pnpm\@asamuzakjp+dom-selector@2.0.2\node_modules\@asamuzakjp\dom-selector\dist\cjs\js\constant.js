var E=Object.defineProperty;var c=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var n=Object.prototype.hasOwnProperty;var _=(o,t)=>{for(var r in t)E(o,r,{get:t[r],enumerable:!0})},T=(o,t,r,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let e of x(t))!n.call(o,e)&&e!==r&&E(o,e,{get:()=>t[e],enumerable:!(s=c(t,e))||s.enumerable});return o};var p=o=>T(E({},"__esModule",{value:!0}),o);var rt={};_(rt,{ALPHA_NUM:()=>O,AN_PLUS_B:()=>S,BIT_01:()=>W,BIT_02:()=>a,BIT_04:()=>d,BIT_08:()=>i,BIT_16:()=>u,BIT_32:()=>Y,BIT_HYPHEN:()=>h,COMBINATOR:()=>N,DOCUMENT_FRAGMENT_NODE:()=>j,DOCUMENT_NODE:()=>Z,DOCUMENT_POSITION_CONTAINED_BY:()=>v,DOCUMENT_POSITION_CONTAINS:()=>q,DOCUMENT_POSITION_PRECEDING:()=>k,DUO:()=>X,ELEMENT_NODE:()=>y,HEX:()=>$,IDENTIFIER:()=>D,MAX_BIT_16:()=>b,NOT_SUPPORTED_ERR:()=>I,NTH:()=>R,RAW:()=>C,REG_LOGICAL_PSEUDO:()=>V,REG_SHADOW_HOST:()=>tt,REG_SHADOW_MODE:()=>ot,REG_SHADOW_PSEUDO:()=>et,SELECTOR:()=>A,SELECTOR_ATTR:()=>L,SELECTOR_CLASS:()=>P,SELECTOR_ID:()=>M,SELECTOR_LIST:()=>U,SELECTOR_PSEUDO_CLASS:()=>l,SELECTOR_PSEUDO_ELEMENT:()=>F,SELECTOR_TYPE:()=>H,SHOW_ALL:()=>z,SHOW_DOCUMENT:()=>J,SHOW_DOCUMENT_FRAGMENT:()=>K,SHOW_ELEMENT:()=>Q,STRING:()=>B,SYNTAX_ERR:()=>f,TEXT_NODE:()=>g,TYPE_FROM:()=>m,TYPE_TO:()=>w,U_FFFD:()=>G});module.exports=p(rt);const O="[A-Z\\d]+",S="AnPlusB",N="Combinator",D="Identifier",I="NotSupportedError",R="Nth",C="Raw",A="Selector",L="AttributeSelector",P="ClassSelector",M="IdSelector",U="SelectorList",l="PseudoClassSelector",F="PseudoElementSelector",H="TypeSelector",B="String",f="SyntaxError",G="\uFFFD",W=1,a=2,d=4,i=8,u=16,Y=32,h=45,X=2,$=16,b=65535,m=8,w=-1,y=1,g=3,Z=9,j=11,k=2,q=8,v=16,z=4294967295,J=256,K=1024,Q=1,V=/^(?:(?:ha|i)s|not|where)$/,tt=/^host(?:-context)?$/,ot=/^(?:close|open)$/,et=/^part|slotted$/;0&&(module.exports={ALPHA_NUM,AN_PLUS_B,BIT_01,BIT_02,BIT_04,BIT_08,BIT_16,BIT_32,BIT_HYPHEN,COMBINATOR,DOCUMENT_FRAGMENT_NODE,DOCUMENT_NODE,DOCUMENT_POSITION_CONTAINED_BY,DOCUMENT_POSITION_CONTAINS,DOCUMENT_POSITION_PRECEDING,DUO,ELEMENT_NODE,HEX,IDENTIFIER,MAX_BIT_16,NOT_SUPPORTED_ERR,NTH,RAW,REG_LOGICAL_PSEUDO,REG_SHADOW_HOST,REG_SHADOW_MODE,REG_SHADOW_PSEUDO,SELECTOR,SELECTOR_ATTR,SELECTOR_CLASS,SELECTOR_ID,SELECTOR_LIST,SELECTOR_PSEUDO_CLASS,SELECTOR_PSEUDO_ELEMENT,SELECTOR_TYPE,SHOW_ALL,SHOW_DOCUMENT,SHOW_DOCUMENT_FRAGMENT,SHOW_ELEMENT,STRING,SYNTAX_ERR,TEXT_NODE,TYPE_FROM,TYPE_TO,U_FFFD});
//# sourceMappingURL=constant.js.map
