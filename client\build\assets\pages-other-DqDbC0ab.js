import{j as e,L as U,u as W,r as l,d as G,c as O}from"./vendor-react-Cy9l5Slk.js";import{H as _,m as L,F as z}from"./components-layout-bSmHkFSl.js";import{H as ne,a as re,P as ce,M as oe,b as H,c as se,d as D,A as de}from"./components-home-CLoPu5ar.js";import{S as M,P as me,M as ae,f as te,g as he,h as ge,i as xe,j as ue,k as ee,R as je,C as pe,F as be,W as Ne,l as q}from"./components-common-BBpqRGpk.js";const fe={"@context":"https://schema.org","@type":"Organization",name:"Ultimation Studio",url:"https://devskills.ee",logo:{"@type":"ImageObject",url:"https://devskills.ee/logo.png",width:"180",height:"60"},description:"Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.",contactPoint:{"@type":"ContactPoint",telephone:"******-567-8901",contactType:"customer service",availableLanguage:["English","Estonian"]},sameAs:["https://www.facebook.com/devskillsee","https://www.linkedin.com/company/devskills-ee","https://twitter.com/DevSkillsEE"]},ve={"@context":"https://schema.org","@type":"WebSite",name:"Ultimation Studio",url:"https://devskills.ee",potentialAction:{"@type":"SearchAction",target:"https://devskills.ee/search?q={search_term_string}","query-input":"required name=search_term_string"}};function Oe(){return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Ultimation Studio - Business Management System",description:"Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.",canonical:"https://devskills.ee",image:"https://devskills.ee/og-images/home.png",imageAlt:"Ultimation Studio - Business Management System",imageWidth:"1200",imageHeight:"630",schema:[fe,ve],keywords:["business management system","BMS","productivity","operations","ultimation","studio"]}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsxs("main",{id:"main",children:[e.jsx(me,{className:"home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection",style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsx(ne,{})}),e.jsx(re,{dark:!0})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(z,{})})]})," "]})})]})}const ye={title:"Elegant Portfolio Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template",description:"Resonance &mdash; One & Multi Page Reactjs Creative Template"};function we(){return e.jsxs(e.Fragment,{children:[e.jsx(ae,{meta:ye}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/section-bg-1.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:"PORTFOLIO"}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-tiny mb-0 opacity-075",children:"Explore captivating web design solutions."})})})]})}),e.jsx("section",{className:"page-section pb-0  scrollSpysection  bg-dark-1 light-content ",id:"portfolio",children:e.jsx(ce,{})}),e.jsx("div",{className:"page-section overflow-hidden",children:e.jsx(oe,{})}),e.jsx("section",{className:"page-section bg-dark-1 light-content pt-0",children:e.jsx("div",{className:"container position-relative",children:e.jsx("div",{className:"row text-center wow fadeInUp",children:e.jsxs("div",{className:"col-md-10 offset-md-1 col-lg-6 offset-lg-3",children:[e.jsx("p",{className:"section-descr mb-50 mb-sm-30",children:"The power of design help us to solve complex problems and cultivate business solutions."}),e.jsx("div",{className:"local-scroll",children:e.jsx(U,{to:"/elegant-contact",className:"btn btn-mod btn-large btn-w btn-circle btn-hover-anim",children:e.jsx("span",{children:"Contact us"})})})]})})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(z,{})})]})," "]})})]})}const ze=Object.freeze(Object.defineProperty({__proto__:null,default:we},Symbol.toStringTag,{value:"Module"}));function ke(){const{i18n:b}=W(),w=b.language||"et",[f,c]=l.useState([]),[x,g]=l.useState(!0),[u,j]=l.useState(1),[v,n]=l.useState(1);l.useEffect(()=>{(async()=>{var d,h;try{g(!0);const t=await H.getBlogPosts(w,u,9);if(t.response.ok&&t.data){const y=((d=t.data.data)==null?void 0:d.data)||t.data.data||[],s=((h=t.data.data)==null?void 0:h.pagination)||t.data.pagination;console.log("Blog listing API response:",t.data),console.log("Posts array:",y),console.log("Pagination:",s),c(Array.isArray(y)?y:[]),n((s==null?void 0:s.totalPages)||1)}else console.error("Failed to fetch blog posts:",t.response.status),c([])}catch(t){console.error("Error fetching blog posts:",t),c([])}finally{g(!1)}})()},[w,u]);const o=(i,d)=>{var t,y,s;const h=(t=i.translations)==null?void 0:t.find(p=>p.language===w);return(h==null?void 0:h[d])||((s=(y=i.translations)==null?void 0:y.find(p=>p.language==="en"))==null?void 0:s[d])||""};return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:w==="et"?"Blogi - DevSkills":"Blog - DevSkills",description:w==="et"?"Eksperditeadmised tarkvaraarenduse, mobiilirakenduste, AI ja tehnoloogiasuundade kohta.":"Expert insights on software development, mobile apps, AI, and technology trends.",slug:"blog",type:"website",keywords:w==="et"?["blogi","tarkvaraarendus","mobiilirakendused","AI","tehnoloogia"]:["blog","software development","mobile apps","AI","technology"]}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/section-bg-1.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:"BLOG"}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-tiny mb-0 opacity-075",children:"nsights and inspiration at your fingertips."})})})]})}),e.jsxs(e.Fragment,{children:[e.jsx("section",{className:"page-section bg-dark-1 light-content",id:"blog",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"row mt-n50 mb-50 wow fadeInUp","data-wow-offset":0,children:[x&&e.jsx("div",{className:"col-12 text-center",children:e.jsx("div",{className:"text-gray",children:"Loading blog posts..."})}),!x&&f.length===0&&e.jsx("div",{className:"col-12 text-center",children:e.jsx("div",{className:"text-gray",children:"No blog posts available yet."})}),!x&&Array.isArray(f)&&f.map(i=>{var d;return e.jsx("div",{className:"post-prev col-md-6 col-lg-4 mt-50",children:e.jsxs("div",{className:"post-prev-container",children:[e.jsx("div",{className:"post-prev-img",children:e.jsx(U,{to:`/blog-single/${i.slug}`,children:e.jsx("img",{src:i.featuredImage||"/assets/images/demo-elegant/blog/1.jpg",width:607,height:358,alt:o(i,"title")})})}),e.jsx("h3",{className:"post-prev-title",children:e.jsx(U,{to:`/blog-single/${i.slug}`,children:o(i,"title")})}),e.jsx("div",{className:"post-prev-text",children:o(i,"excerpt")}),e.jsxs("div",{className:"post-prev-info clearfix",children:[e.jsxs("div",{className:"float-start",children:[e.jsx("a",{href:"#",className:"icon-author",children:e.jsx("i",{className:"mi-user size-14 align-middle"})}),e.jsx("a",{href:"#",children:((d=i.author)==null?void 0:d.name)||"DevSkills Team"})]}),e.jsxs("div",{className:"float-end",children:[e.jsx("i",{className:"mi-calendar size-14 align-middle"}),e.jsx("a",{href:"#",children:new Date(i.publishedAt||i.createdAt).toLocaleDateString()})]})]})]})},i.id)})]}),e.jsx(he,{})]})}),e.jsx("hr",{className:"mt-0 mb-0 white"}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container relative",children:e.jsxs("div",{className:"row mt-n60",children:[e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:"Categories"}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-menu",children:ge.map(i=>e.jsxs("li",{children:[e.jsx("a",{href:"#",title:"",children:i.name}),e.jsxs("small",{children:[" - ",i.count," "]})]},i.id))})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:"Tags"}),e.jsx("div",{className:"widget-body",children:e.jsx("div",{className:"tags",children:xe.map(i=>e.jsx("a",{href:"#",children:i.name},i.id))})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:"Archive"}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-menu",children:ue.map(i=>e.jsx("li",{children:e.jsx("a",{href:"#",title:"",children:i.date})},i.id))})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:"Text widget"}),e.jsx("div",{className:"widget-body",children:e.jsxs("div",{className:"widget-text clearfix",children:[e.jsx("img",{src:"/assets/images/blog/previews/post-prev-6.jpg",alt:"Image Description",height:140,style:{height:"fit-content"},width:100,className:"left img-left"}),"Consectetur adipiscing elit. Quisque magna ante eleifend eleifend. Purus ut dignissim consectetur, nulla erat ultrices purus, ut consequat sem elit non sem. Quisque magna ante eleifend eleifend."]})})]})})]})})})]})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(z,{})})]})," "]})})]})}const $e=Object.freeze(Object.defineProperty({__proto__:null,default:ke},Symbol.toStringTag,{value:"Module"})),Se={title:"Elegant Portfolio Single Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template",description:"Resonance &mdash; One & Multi Page Reactjs Creative Template"};function Ce(){let b=G();const w=ee.filter(f=>f.id==b.id)[0]||ee[0];return e.jsxs(e.Fragment,{children:[e.jsx(ae,{meta:Se}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/section-bg-1.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:w.title}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-tiny mb-0 opacity-075",children:"Branding, UI/UX Design, No-code Development"})})})]})}),e.jsxs(e.Fragment,{children:[e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsxs("div",{className:"container relative",children:[e.jsxs("div",{className:"row mb-80 mb-sm-40",children:[e.jsxs("div",{className:"col-md-6 mb-sm-40",children:[e.jsx("h2",{className:"section-title-small mb-20",children:"Project Details"}),e.jsx("hr",{className:"mb-20"}),e.jsxs("div",{className:"row text-gray",children:[e.jsx("div",{className:"col-sm-4",children:e.jsx("b",{children:"Date:"})}),e.jsx("div",{className:"col-sm-8",children:"May 1th, 2023"})]}),e.jsx("hr",{className:"mb-20"}),e.jsxs("div",{className:"row text-gray",children:[e.jsx("div",{className:"col-sm-4",children:e.jsx("b",{children:"Client:"})}),e.jsx("div",{className:"col-sm-8",children:"Envato Users"})]}),e.jsx("hr",{className:"mb-20"}),e.jsxs("div",{className:"row text-gray",children:[e.jsx("div",{className:"col-sm-4",children:e.jsx("b",{children:"Services:"})}),e.jsx("div",{className:"col-sm-8",children:"Branding, UI/UX Design, Front-end Development, Back-end Development"})]}),e.jsx("hr",{className:"mb-20"})]}),e.jsxs("div",{className:"col-md-6",children:[e.jsx("h2",{className:"section-title-small mb-20",children:"Description"}),e.jsx("hr",{className:"mb-20"}),e.jsx("p",{className:"text-gray mb-0",children:"Lorem ipsum dolor sit amet conseur adipisci inerene maximus ligula sempe metuse pelente mattis. Maecenas volutpat, diam eni sagittis quam porta quam. Sed id dolor consectetur fermentum volutpat accumsan purus iaculis libero. Donec vel ultricies purus iaculis libero. Etiam sit amet fringilla lacus susantebe sit ullamcorper pulvinar neque porttitor. Integere lectus. Praesent sede nisi eleifend fermum orci amet, iaculis libero. Donec vel ultricies purus quam."})]})]}),e.jsxs("div",{className:"row mb-n30",children:[e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/1-large.jpg",alt:"Image Description",width:970,height:1136})}),e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/6-large.jpg",alt:"Image Description",width:970,height:1136})}),e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/8-large.jpg",alt:"Image Description",width:970,height:1136})}),e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/3-large.jpg",alt:"Image Description",width:970,height:1136})})]})]})}),e.jsx("hr",{className:"mt-0 mb-0 white"})]}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx(je,{})}),e.jsxs(e.Fragment,{children:[e.jsx("hr",{className:"mt-0 mb-0 white"}),e.jsxs("div",{className:"work-navigation bg-dark-1 light-content clearfix z-index-1 position-relative",children:[e.jsx(U,{to:"/main-portfolio-single-1/1",className:"work-prev",children:e.jsxs("span",{children:[e.jsx("i",{className:"mi-arrow-left size-24 align-middle"})," ","Previous"]})}),e.jsx("a",{href:"#",className:"work-all",children:e.jsxs("span",{children:[e.jsx("i",{className:"mi-close size-24 align-middle"})," All works"]})}),e.jsx(U,{to:"/main-portfolio-single-3/1",className:"work-next",children:e.jsxs("span",{children:["Next ",e.jsx("i",{className:"mi-arrow-right size-24 align-middle"})]})})]})]})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(z,{})})]})," "]})})]})}const Re=Object.freeze(Object.defineProperty({__proto__:null,default:Ce},Symbol.toStringTag,{value:"Module"}));function Pe(){var i;let b=G();const{i18n:w}=W(),f=w.language||"et",[c,x]=l.useState(null),[g,u]=l.useState(!0),[j,v]=l.useState("");l.useEffect(()=>{const d=async()=>{try{u(!0);const h=await H.getPost(b.id);h.response.ok&&h.data?(console.log("Blog single API response:",h.data),x(h.data.data||h.data)):(console.error("Failed to fetch blog post:",h.response.status),v("Blog post not found"))}catch(h){console.error("Error fetching blog post:",h),v("Failed to load blog post")}finally{u(!1)}};b.id&&d()},[b.id]);const n=(d,h)=>{var y;if(!d||!d.translations)return"";const t=d.translations.find(s=>s.language===f);return(t==null?void 0:t[h])||((y=d.translations.find(s=>s.language==="en"))==null?void 0:y[h])||""};if(g)return e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{children:"Loading..."}),e.jsx("p",{children:"Please wait while we load the blog post."})]})})})})})]})})});if(!c||j)return e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{children:"Blog Post Not Found"}),e.jsx("p",{children:"The blog post you're looking for doesn't exist."}),e.jsx("a",{href:"/blog",className:"btn btn-mod btn-border btn-large btn-round",children:"Back to Blog"})]})})})})})]})})});const o={title:`${n(c,"title")} || DevSkills`,description:n(c,"excerpt")};return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:o.title,description:o.description}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(_,{links:L})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/section-bg-1.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-10 offset-lg-1",children:e.jsx("h1",{className:"hs-title-3a mb-0 wow fadeInUpShort","data-wow-duration":"0.6s",children:n(c,"title")})})}),e.jsxs("div",{className:"blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn","data-wow-delay":"0.2s",children:[e.jsx("div",{className:"d-inline-block me-3",children:e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"mi-clock size-16"}),e.jsx("span",{className:"visually-hidden",children:"Date:"})," ",new Date(c.publishedAt||c.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})}),e.jsx("div",{className:"d-inline-block me-3",children:e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"mi-user size-16"}),e.jsx("span",{className:"visually-hidden",children:"Author:"})," ",((i=c.author)==null?void 0:i.name)||"DevSkills Team"]})}),c.categories&&c.categories.length>0&&e.jsxs("div",{className:"d-inline-block me-3",children:[e.jsx("i",{className:"mi-folder size-16"}),e.jsx("span",{className:"visually-hidden",children:"Category:"}),e.jsx("a",{href:"#",children:c.categories[0].name})]}),e.jsxs("div",{className:"d-inline-block me-3",children:[e.jsx("i",{className:"mi-time size-16"}),e.jsx("span",{className:"visually-hidden",children:"Read time:"})," ",c.readTime||5," min"]})]})]})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container relative",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last",children:[e.jsx("div",{className:"blog-item mb-80 mb-xs-40",children:e.jsxs("div",{className:"blog-item-body",children:[c.featuredImage&&e.jsx("div",{className:"mb-40 mb-xs-30",children:e.jsx("img",{src:c.featuredImage,alt:n(c,"title"),width:1350,height:796})}),e.jsx("div",{className:"lead mb-40",children:n(c,"excerpt")}),e.jsx("div",{className:"blog-content",style:{lineHeight:"1.8",fontSize:"16px"},dangerouslySetInnerHTML:{__html:n(c,"content")}})]})}),e.jsxs("div",{className:"mb-80 mb-xs-40",children:[e.jsxs("h4",{className:"blog-page-title",children:["Comments ",e.jsx("small",{className:"number",children:"(3)"})]}),e.jsx("ul",{className:"media-list comment-list clearlist",children:e.jsx(pe,{})})]}),e.jsxs("div",{className:"mb-80 mb-xs-40",children:[e.jsx("h4",{className:"blog-page-title",children:"Leave a comment"}),e.jsx(be,{})]}),e.jsxs("div",{className:"clearfix mt-40",children:[e.jsxs("a",{href:"#",className:"blog-item-more left",children:[e.jsx("i",{className:"mi-chevron-left"})," Prev post"]}),e.jsxs("a",{href:"#",className:"blog-item-more right",children:["Next post ",e.jsx("i",{className:"mi-chevron-right"})]})]})]}),e.jsx("div",{className:"col-lg-4 col-xl-3",children:e.jsx(Ne,{searchInputClass:"form-control input-lg search-field round"})})]})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(z,{})})]})," "]})})]})}const qe=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"}));function Ie(){return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Page Not Found - 404",description:"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.",canonical:"https://devskills.ee/404",image:"https://devskills.ee/og-images/404.png"}),e.jsxs("div",{className:"theme-main",children:[e.jsxs("div",{className:"page",id:"top",children:[e.jsxs(e.Fragment,{children:[e.jsx("nav",{className:"main-nav dark light-after-scroll transparent stick-fixed wow-menubar wch-unset",children:e.jsxs("div",{className:"main-nav-sub full-wrapper",children:[e.jsx("div",{className:"nav-logo-wrap local-scroll",children:e.jsxs(U,{to:"/",className:"logo",children:[e.jsx("img",{src:"/assets/images/logo-white.svg",alt:"Your Company Logo",width:105,height:34,className:"logo-white"}),e.jsx("img",{src:"/assets/images/logo-dark.svg",alt:"Your Company Logo",width:105,height:34,className:"logo-dark"})]})}),e.jsxs("div",{className:"mobile-nav",role:"button",tabIndex:0,children:[e.jsx("i",{className:"mobile-nav-icon"}),e.jsx("span",{className:"visually-hidden",children:"Menu"})]}),e.jsx("div",{className:"inner-nav desktop-nav",children:e.jsxs("ul",{className:"clearlist scroll-nav local-scroll justify-content-end",children:[e.jsx("li",{className:"active",children:e.jsxs("a",{href:"mailto:<EMAIL>",children:[e.jsx("i",{className:"mi-email align-center"}),"<EMAIL>"]})}),e.jsx("li",{children:e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"mi-call align-center"})," 0307-567-890"]})})]})})]})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"home-section bg-dark-1 bg-dark-alpha-60 light-content parallax-5",style:{backgroundImage:"url(/assets/images/full-width-images/section-bg-3.jpg)"},id:"home",children:e.jsx("div",{className:"container min-height-100vh d-flex align-items-center pt-100 pb-100 pt-sm-120 pb-sm-120",children:e.jsx("div",{className:"home-content",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-sm-10 offset-sm-1 col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsxs("div",{className:"hs-wrap",children:[e.jsx("div",{className:"wow fadeInUp","data-wow-delay":".1s",children:e.jsx("h1",{className:"hs-title-12 mb-40 mb-sm-30",children:"404"})}),e.jsx("div",{className:"mb-40 mb-sm-30 wow fadeInUp","data-wow-delay":".2s",children:e.jsx("h2",{className:"section-descr mb-20",children:"The page you were looking for could not be found."})}),e.jsx("div",{className:"local-scroll wow fadeInUp","data-wow-delay":".3s",children:e.jsxs(U,{to:"/",className:"btn btn-mod btn-w btn-round btn-medium btn-hover-anim",children:[e.jsx("i",{className:"mi-arrow-left size-24 align-center"}),e.jsx("span",{children:"Back To Home Page"})]})})]})})})})})})})]}),e.jsx(z,{})]})," "]})]})}const He=Object.freeze(Object.defineProperty({__proto__:null,default:Ie},Symbol.toStringTag,{value:"Module"})),Te=()=>{const{t:b}=W(),w=O(),[f,c]=l.useState({email:"",password:""}),[x,g]=l.useState(!1),[u,j]=l.useState(""),v=o=>{c({...f,[o.target.name]:o.target.value}),u&&j("")},n=async o=>{o.preventDefault(),g(!0),j("");try{const{response:i,data:d}=await se.login(f);d.success?(localStorage.setItem("adminToken",d.token),localStorage.setItem("adminUser",JSON.stringify(d.user)),w("/admin/dashboard")):j(d.message||"Login failed")}catch(i){console.error("Login error:",i),j("Network error. Please try again.")}finally{g(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Admin Login - DevSkills",description:"Admin login page for DevSkills content management",noIndex:!0}),e.jsx("div",{id:"page",className:"page",children:e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section",id:"admin-login",children:e.jsx("div",{className:"container relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-md-6 offset-md-3 col-lg-4 offset-lg-4",children:e.jsxs("div",{className:"form-container",children:[e.jsxs("div",{className:"text-center mb-60 mb-sm-40",children:[e.jsxs("div",{className:"hs-line-4 font-alt black mb-20 mb-xs-10",children:[e.jsx("span",{className:"color-primary-1",children:"DevSkills"})," Admin"]}),e.jsx("p",{className:"section-descr mb-0",children:"Sign in to access the admin dashboard"})]}),u&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("i",{className:"mi-warning"}),u]}),e.jsxs("form",{className:"form contact-form",onSubmit:n,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email Address"}),e.jsx("input",{type:"email",name:"email",id:"email",className:"input-lg round form-control",placeholder:"Email Address",value:f.email,onChange:v,required:!0,autoComplete:"email"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsx("input",{type:"password",name:"password",id:"password",className:"input-lg round form-control",placeholder:"Password",value:f.password,onChange:v,required:!0,autoComplete:"current-password"})]}),e.jsx("div",{className:"form-group",children:e.jsx("button",{type:"submit",className:"btn btn-mod btn-color btn-large btn-round btn-full-width",disabled:x,children:x?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"fa fa-spinner fa-spin me-2"}),"Signing in..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-lock me-2"}),"Sign In"]})})})]}),e.jsx("div",{className:"text-center mt-40",children:e.jsx("p",{className:"small opacity-07",children:"© 2024 DevSkills. All rights reserved."})})]})})})})})})})]})},We=Object.freeze(Object.defineProperty({__proto__:null,default:Te},Symbol.toStringTag,{value:"Module"})),Ae=()=>{const b=O(),[w,f]=l.useState(null),[c,x]=l.useState(!0);return l.useEffect(()=>{(async()=>{const u=localStorage.getItem("adminToken"),j=localStorage.getItem("adminUser");if(!u||!j){b("/admin");return}try{const{response:v,data:n}=await se.getMe();v.ok&&n.success?f(n.user):(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),b("/admin"))}catch(v){console.error("Auth check failed:",v),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),b("/admin")}finally{x(!1)}})()},[b]),c?e.jsx("div",{id:"page",className:"page",children:e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section",children:e.jsx("div",{className:"container relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 text-center",children:e.jsxs("div",{className:"loading-animation",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"color-primary-1",style:{fontSize:"3rem",animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"mt-20",children:e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading..."})})]})})})})})})}):e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Admin Dashboard - DevSkills",description:"DevSkills admin dashboard for content management",noIndex:!0}),e.jsxs(q,{title:"Dashboard",children:[e.jsxs("div",{className:"row mb-40",children:[e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:document-text-bold"})}),e.jsx("div",{className:"number-2-title",children:"Total Posts"}),e.jsx("div",{className:"number-2-number",children:"0"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:folder-bold"})}),e.jsx("div",{className:"number-2-title",children:"Categories"}),e.jsx("div",{className:"number-2-number",children:"5"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:chat-round-bold"})}),e.jsx("div",{className:"number-2-title",children:"Comments"}),e.jsx("div",{className:"number-2-number",children:"0"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:eye-bold"})}),e.jsx("div",{className:"number-2-title",children:"Page Views"}),e.jsx("div",{className:"number-2-number",children:"-"})]})})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsx("div",{className:"mb-20",children:e.jsx("h3",{className:"hs-line-4 font-alt black mb-20 mb-xs-10",children:"Quick Actions"})}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:add-circle-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"New Blog Post"}),e.jsx("div",{className:"alt-features-descr",children:"Create a new multilingual blog post with rich content and scheduling."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>b("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:"Create Post"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:documents-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Manage Posts"}),e.jsx("div",{className:"alt-features-descr",children:"Edit, publish, schedule, and organize your existing blog posts."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>b("/admin/posts"),className:"btn btn-mod btn-color btn-round",children:"Manage Posts"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:folder-with-files-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Categories & Tags"}),e.jsx("div",{className:"alt-features-descr",children:"Organize your content with categories and tags for better navigation."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>b("/admin/categories"),className:"btn btn-mod btn-color btn-round",children:"Organize Content"})})]})})]})]})})]})]})},Je=Object.freeze(Object.defineProperty({__proto__:null,default:Ae},Symbol.toStringTag,{value:"Module"})),De=()=>{const b=O(),[w,f]=l.useState([]),[c,x]=l.useState(!0),[g,u]=l.useState(""),[j,v]=l.useState({page:1,limit:10,status:"all",search:""}),[n,o]=l.useState({});l.useEffect(()=>{i()},[j]);const i=async()=>{try{x(!0);const s={};Object.entries(j).forEach(([I,A])=>{A&&A!=="all"&&(s[I]=A)});const{response:p,data:T}=await D.getPosts(s);T.success?(f(T.data.posts),o(T.data.pagination)):u(T.message||"Failed to load posts")}catch(s){console.error("Load posts error:",s),u("Network error. Please try again.")}finally{x(!1)}},d=async s=>{if(confirm("Are you sure you want to delete this blog post? This action cannot be undone."))try{const p=localStorage.getItem("adminToken"),I=await(await fetch(`/api/blog/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${p}`}})).json();I.success?i():u(I.message||"Failed to delete post")}catch(p){console.error("Delete error:",p),u("Network error. Please try again.")}},h=async s=>{try{const p=localStorage.getItem("adminToken"),I=await(await fetch(`/api/blog/${s}/toggle-visibility`,{method:"PATCH",headers:{Authorization:`Bearer ${p}`}})).json();I.success?i():u(I.message||"Failed to toggle visibility")}catch(p){console.error("Toggle visibility error:",p),u("Network error. Please try again.")}},t=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),y=s=>s.published?s.scheduledAt&&new Date(s.scheduledAt)>new Date?e.jsxs("span",{className:"badge bg-warning",children:[e.jsx("i",{className:"mi-clock me-1"}),"Scheduled"]}):e.jsxs("span",{className:"badge bg-success",children:[e.jsx("i",{className:"mi-check me-1"}),"Published"]}):e.jsxs("span",{className:"badge bg-secondary",children:[e.jsx("i",{className:"mi-edit me-1"}),"Draft"]});return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Manage Blog Posts - Admin",description:"Manage blog posts in the admin panel",noIndex:!0}),e.jsxs(q,{title:"Blog Posts",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-md-6",children:e.jsx("p",{className:"section-descr mb-0",children:"Manage your blog posts, create new content, and organize your articles."})}),e.jsx("div",{className:"col-md-6 text-md-end",children:e.jsxs("button",{onClick:()=>b("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-plus me-2"}),"New Post"]})})]})}),e.jsx("div",{className:"admin-table mb-30",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-4 mb-20",children:[e.jsx("label",{className:"form-label",children:"Search Posts"}),e.jsx("input",{type:"text",value:j.search,onChange:s=>v(p=>({...p,search:s.target.value,page:1})),className:"form-control",placeholder:"Search by title..."})]}),e.jsxs("div",{className:"col-md-3 mb-20",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:j.status,onChange:s=>v(p=>({...p,status:s.target.value,page:1})),className:"form-control",children:[e.jsx("option",{value:"all",children:"All Posts"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"draft",children:"Drafts"})]})]}),e.jsxs("div",{className:"col-md-3 mb-20",children:[e.jsx("label",{className:"form-label",children:"Per Page"}),e.jsxs("select",{value:j.limit,onChange:s=>v(p=>({...p,limit:parseInt(s.target.value),page:1})),className:"form-control",children:[e.jsx("option",{value:10,children:"10"}),e.jsx("option",{value:25,children:"25"}),e.jsx("option",{value:50,children:"50"})]})]})]})}),g&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("i",{className:"mi-warning me-2"}),g]}),e.jsx("div",{className:"admin-table",children:c?e.jsxs("div",{className:"text-center py-60",children:[e.jsx("i",{className:"fa fa-spinner fa-spin fa-2x color-primary-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading posts..."})]}):w.length===0?e.jsxs("div",{className:"text-center py-60",children:[e.jsx("i",{className:"mi-edit fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No blog posts found"}),e.jsx("p",{className:"section-descr mb-30",children:j.search||j.status!=="all"?"Try adjusting your search filters or create your first blog post.":"Get started by creating your first blog post."}),e.jsxs("button",{onClick:()=>b("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-plus me-2"}),"Create First Post"]})]}):e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Title"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Author"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:w.map(s=>{const p=s.translations.find(T=>T.language==="en")||s.translations[0];return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[s.featuredImage&&e.jsx("img",{className:"rounded me-3",src:s.featuredImage,alt:"",style:{width:"50px",height:"50px",objectFit:"cover"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:(p==null?void 0:p.title)||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",s.slug]})]})]})}),e.jsxs("td",{children:[y(s),s.featured&&e.jsxs("span",{className:"badge bg-primary ms-2",children:[e.jsx("i",{className:"mi-star me-1"}),"Featured"]})]}),e.jsx("td",{children:s.author.name||s.author.email}),e.jsx("td",{children:t(s.createdAt)}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>b(`/admin/blog/edit/${s.id}`),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("i",{className:"mi-edit"})}),e.jsx("button",{onClick:()=>h(s.id),className:`btn btn-sm ${s.published?"btn-outline-warning":"btn-outline-success"}`,title:s.published?"Unpublish":"Publish",children:e.jsx("i",{className:s.published?"mi-eye-off":"mi-eye"})}),e.jsx("button",{onClick:()=>d(s.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("i",{className:"mi-trash"})})]})})]},s.id)})})]})})}),n.pages>1&&e.jsxs("div",{className:"row mt-30",children:[e.jsx("div",{className:"col-md-6",children:e.jsxs("p",{className:"small text-muted mb-0",children:["Showing ",(n.page-1)*n.limit+1," to"," ",Math.min(n.page*n.limit,n.total)," ","of ",n.total," results"]})}),e.jsx("div",{className:"col-md-6 text-md-end",children:e.jsx("nav",{"aria-label":"Blog posts pagination",children:e.jsxs("ul",{className:"pagination pagination-sm justify-content-md-end",children:[e.jsx("li",{className:`page-item ${n.page<=1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>v(s=>({...s,page:s.page-1})),disabled:n.page<=1,children:"Previous"})}),e.jsx("li",{className:"page-item active",children:e.jsxs("span",{className:"page-link",children:["Page ",n.page," of ",n.pages]})}),e.jsx("li",{className:`page-item ${n.page>=n.pages?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>v(s=>({...s,page:s.page+1})),disabled:n.page>=n.pages,children:"Next"})})]})})})]})]})]})},Ge=Object.freeze(Object.defineProperty({__proto__:null,default:De},Symbol.toStringTag,{value:"Module"})),Ee=()=>{var Y,Q,V,X,K;const{t:b,i18n:w}=W(),f=O(),{id:c}=G(),x=!!c,[g,u]=l.useState(!1),[j,v]=l.useState(!1),[n,o]=l.useState(""),[i,d]=l.useState(""),[h]=l.useState(()=>Object.keys(w.store.data)),[t,y]=l.useState(()=>{const r={};return h.forEach(S=>{r[S]={title:"",excerpt:"",content:"",metaTitle:"",metaDesc:"",keywords:[]}}),{slug:"",featured:!1,published:!1,scheduledAt:"",featuredImage:null,featuredImageAlt:"",readTime:"",categoryIds:[],tagIds:[],translations:r}}),[s,p]=l.useState("en"),[T,I]=l.useState([]),[A,a]=l.useState([]),[m,k]=l.useState(null);l.useEffect(()=>{(async()=>{try{u(!0),o("");const S=localStorage.getItem("adminToken");if(!S){o("Authentication required. Please log in to access this page."),u(!1);return}const[N,C]=await Promise.all([D.getCategories(),D.getTags()]);if(N.response.ok&&N.data)I(N.data.data||[]);else{if(console.error("Categories API failed:",N.response.status,N.response.statusText),N.response.status===401||N.response.status===403){o("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}I([])}if(C.response.ok&&C.data)a(C.data.data||[]);else{if(console.error("Tags API failed:",C.response.status,C.response.statusText),C.response.status===401||C.response.status===403){o("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}a([])}if(x){const E=await fetch(`${de}/admin/posts/${c}`,{headers:{Authorization:`Bearer ${S}`}});if(E.ok)try{const P=(await E.json()).data,Z={};P.translations&&Array.isArray(P.translations)&&P.translations.forEach(R=>{Z[R.language]=R}),y(R=>({...R,slug:P.slug||"",featured:P.featured||!1,published:P.published||!1,scheduledAt:P.scheduledAt?new Date(P.scheduledAt).toISOString().slice(0,16):"",featuredImage:null,featuredImageAlt:P.featuredImageAlt||"",readTime:P.readTime||"",categoryIds:P.categories?P.categories.map(J=>J.id):[],tagIds:P.tags?P.tags.map(J=>J.id):[],translations:{...R.translations,...Z}})),P.featuredImage&&k(P.featuredImage)}catch(F){console.error("Failed to parse post response:",F),o("Failed to load post data - invalid response format")}else console.error("Post API failed:",E.status,E.statusText),o(`Failed to load post: ${E.status} ${E.statusText}`)}}catch(S){console.error("Error loading data:",S),S.message&&S.message.includes("fetch")?o("Failed to connect to the server. Please check if the backend is running on localhost:4004"):o("Failed to load data. Please try again.")}finally{u(!1)}})()},[c,x]);const B=(r,S)=>{y(N=>({...N,[r]:S}))},$=(r,S,N)=>{y(C=>({...C,translations:{...C.translations,[r]:{...C.translations[r],[S]:N}}}))},le=r=>{const S=r.target.files[0];if(S){y(C=>({...C,featuredImage:S}));const N=new FileReader;N.onload=C=>{k(C.target.result)},N.readAsDataURL(S)}},ie=async r=>{r.preventDefault(),v(!0),o(""),d("");try{const S=localStorage.getItem("adminToken"),N=new FormData;N.append("slug",t.slug),N.append("featured",t.featured),N.append("published",t.published),t.scheduledAt&&N.append("scheduledAt",t.scheduledAt),N.append("featuredImageAlt",t.featuredImageAlt),t.readTime&&N.append("readTime",t.readTime),N.append("categoryIds",JSON.stringify(t.categoryIds)),N.append("tagIds",JSON.stringify(t.tagIds)),N.append("translations",JSON.stringify(t.translations)),t.featuredImage&&N.append("featuredImage",t.featuredImage);let C;x?C=await H.updatePost(c,N):C=await H.createPost(N);const{response:E,data:F}=C;if(E.ok&&F&&F.success)d(`Blog post ${x?"updated":"created"} successfully!`),setTimeout(()=>{f("/admin/posts")},2e3);else{const P=(F==null?void 0:F.message)||`Failed to ${x?"update":"create"} blog post`;o(P)}}catch(S){console.error("Save error:",S),o("Network error. Please try again.")}finally{v(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:`${x?"Edit":"Create"} Blog Post - Admin`,description:"Create or edit blog posts in the admin panel",noIndex:!0}),e.jsx(q,{title:x?"Edit Blog Post":"Create New Blog Post",children:e.jsxs("form",{onSubmit:ie,className:"admin-form",children:[n&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),n]}),i&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),i]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:settings-bold",className:"me-2 color-primary-1"}),"Basic Settings"]}),e.jsx("p",{className:"section-descr mb-0",children:"Configure the basic properties of your blog post"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"me-2"}),"Slug (URL)"]}),e.jsx("input",{type:"text",value:t.slug,onChange:r=>B("slug",r.target.value),className:"form-control",placeholder:"blog-post-url"}),e.jsx("small",{className:"form-text text-muted",children:"This will be the URL path for your blog post (e.g., /blog/your-slug)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:clock-circle-bold",className:"me-2"}),"Read Time (minutes)"]}),e.jsx("input",{type:"number",value:t.readTime,onChange:r=>B("readTime",r.target.value),className:"form-control",placeholder:"5",min:"1",max:"60"}),e.jsx("small",{className:"form-text text-muted",children:"Estimated reading time for this post"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:calendar-bold",className:"me-2"}),"Schedule Publication"]}),e.jsx("input",{type:"datetime-local",value:t.scheduledAt,onChange:r=>B("scheduledAt",r.target.value),className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Leave empty to publish immediately when published is checked"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-2"}),"Post Options"]}),e.jsxs("div",{className:"d-flex flex-column gap-2",children:[e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"featured",checked:t.featured,onChange:r=>B("featured",r.target.checked),className:"form-check-input"}),e.jsxs("label",{className:"form-check-label",htmlFor:"featured",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured Post"]}),e.jsx("small",{className:"form-text text-muted d-block",children:"Show this post prominently on the homepage"})]}),e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"published",checked:t.published,onChange:r=>B("published",r.target.checked),className:"form-check-input"}),e.jsxs("label",{className:"form-check-label",htmlFor:"published",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]}),e.jsx("small",{className:"form-text text-muted d-block",children:"Make this post visible to the public"})]})]})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("i",{className:"mi-image me-2 color-primary-1"}),"Featured Image"]}),e.jsx("p",{className:"section-descr mb-0",children:"Upload a featured image that will be displayed with your blog post"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-upload me-2"}),"Upload Image"]}),e.jsx("input",{type:"file",accept:"image/*",onChange:le,className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Recommended size: 1200x630px. Supported formats: JPG, PNG, WebP"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-accessibility me-2"}),"Alt Text"]}),e.jsx("input",{type:"text",value:t.featuredImageAlt,onChange:r=>B("featuredImageAlt",r.target.value),className:"form-control",placeholder:"Describe the image for accessibility"}),e.jsx("small",{className:"form-text text-muted",children:"Describe the image for screen readers and SEO"})]}),m&&e.jsxs("div",{className:"col-12",children:[e.jsx("div",{className:"mb-20",children:e.jsx("label",{className:"form-label",children:"Image Preview"})}),e.jsx("div",{className:"text-center",children:e.jsx("img",{src:m,alt:"Preview",className:"image-preview",style:{maxWidth:"400px",height:"auto"}})})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("i",{className:"mi-globe me-2 color-primary-1"}),"Content (Multi-language)"]}),e.jsx("p",{className:"section-descr mb-0",children:"Create content in multiple languages. At least English content is required."})]})}),e.jsx("div",{className:"language-tabs mb-30",children:h.map(r=>e.jsxs("button",{type:"button",onClick:()=>p(r),className:`language-tab ${s===r?"active":""}`,children:[e.jsx("i",{className:"mi-globe me-2"}),r.toUpperCase(),r==="en"&&e.jsx("span",{className:"ms-1 small",children:"(Required)"})]},r))}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Title (",s.toUpperCase(),")",s==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx("input",{type:"text",value:((Y=t.translations[s])==null?void 0:Y.title)||"",onChange:r=>$(s,"title",r.target.value),className:"form-control",placeholder:"Enter blog post title",required:s==="en"}),e.jsxs("small",{className:"form-text text-muted",children:["The main title of your blog post in"," ",s.toUpperCase()]})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-text me-2"}),"Excerpt (",s.toUpperCase(),")"]}),e.jsx("textarea",{value:((Q=t.translations[s])==null?void 0:Q.excerpt)||"",onChange:r=>$(s,"excerpt",r.target.value),rows:3,className:"form-control",placeholder:"Brief description of the blog post"}),e.jsx("small",{className:"form-text text-muted",children:"A short summary that will appear in blog listings and social media previews"})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-document me-2"}),"Content (",s.toUpperCase(),")",s==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx("textarea",{value:((V=t.translations[s])==null?void 0:V.content)||"",onChange:r=>$(s,"content",r.target.value),rows:20,className:"form-control content-editor",placeholder:"Write your blog post content here. You can paste formatted text from Word documents.",required:s==="en"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-info me-1"}),"Tip: You can paste formatted content from Word documents and the formatting will be preserved. Use Markdown syntax for additional formatting."]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Title (",s.toUpperCase(),")"]}),e.jsx("input",{type:"text",value:((X=t.translations[s])==null?void 0:X.metaTitle)||"",onChange:r=>$(s,"metaTitle",r.target.value),className:"form-control",placeholder:"SEO title (optional)",maxLength:"60"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-search me-1"}),"Title that appears in search engine results (max 60 characters)"]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-description me-2"}),"Meta Description (",s.toUpperCase(),")"]}),e.jsx("textarea",{value:((K=t.translations[s])==null?void 0:K.metaDesc)||"",onChange:r=>$(s,"metaDesc",r.target.value),rows:3,className:"form-control",placeholder:"SEO description (optional)",maxLength:"160"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-search me-1"}),"Description that appears in search engine results (max 160 characters)"]})]})]})]}),e.jsx("div",{className:"row mt-40",children:e.jsxs("div",{className:"col-12 text-end",children:[e.jsx("button",{type:"button",onClick:()=>f("/admin/posts"),className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:j,className:"btn btn-mod btn-color btn-round",children:j?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"fa fa-spinner fa-spin me-2"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-check me-2"}),x?"Update Post":"Create Post"]})})]})})]})})]})},Ye=Object.freeze(Object.defineProperty({__proto__:null,default:Ee},Symbol.toStringTag,{value:"Module"})),Fe=()=>{O();const[b,w]=l.useState([]),[f,c]=l.useState(!0),[x,g]=l.useState(""),[u,j]=l.useState(""),[v,n]=l.useState(!1),[o,i]=l.useState(null),[d,h]=l.useState({name:"",description:"",color:"#4567e7"});l.useEffect(()=>{t()},[]);const t=async()=>{try{c(!0);const{response:a,data:m}=await D.getCategories();m.success?w(m.data||[]):g(m.message||"Failed to load categories")}catch(a){console.error("Load categories error:",a),g("Network error. Please try again.")}finally{c(!1)}},y=(a,m)=>{h(k=>({...k,[a]:m}))},s=async a=>{a.preventDefault(),g(""),j("");try{let m,k;o?{response:m,data:k}=await D.updateCategory(o.id,d):{response:m,data:k}=await D.createCategory(d),k.success?(j(o?"Category updated successfully!":"Category created successfully!"),n(!1),i(null),h({name:"",description:"",color:"#4567e7"}),t()):g(k.message||"Failed to save category")}catch(m){console.error("Save category error:",m),g("Network error. Please try again.")}},p=a=>{i(a),h({name:a.name,description:a.description||"",color:a.color||"#4567e7"}),n(!0)},T=async a=>{if(window.confirm("Are you sure you want to delete this category? This action cannot be undone."))try{const{response:m,data:k}=await D.deleteCategory(a);k.success?(j("Category deleted successfully!"),t()):g(k.message||"Failed to delete category")}catch(m){console.error("Delete category error:",m),g("Network error. Please try again.")}},I=()=>{i(null),h({name:"",description:"",color:"#4567e7"}),n(!0)},A=()=>{n(!1),i(null),h({name:"",description:"",color:"#4567e7"}),g("")};return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Manage Categories - Admin",description:"Manage blog categories in the admin panel",noIndex:!0}),e.jsxs(q,{title:"Categories",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-md-6",children:e.jsx("p",{className:"section-descr mb-0",children:"Organize your blog posts with categories. Create, edit, and manage content categories."})}),e.jsx("div",{className:"col-md-6 text-md-end",children:e.jsxs("button",{onClick:I,className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-plus me-2"}),"New Category"]})})]})}),x&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("i",{className:"mi-warning me-2"}),x]}),u&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("i",{className:"mi-check me-2"}),u]}),e.jsx("div",{className:"admin-table",children:f?e.jsxs("div",{className:"text-center py-60",children:[e.jsx("i",{className:"fa fa-spinner fa-spin fa-2x color-primary-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading categories..."})]}):b.length===0?e.jsxs("div",{className:"text-center py-60",children:[e.jsx("i",{className:"mi-folder fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No categories found"}),e.jsx("p",{className:"section-descr mb-30",children:"Create your first category to start organizing your blog posts."}),e.jsxs("button",{onClick:I,className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-plus me-2"}),"Create First Category"]})]}):e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Category"}),e.jsx("th",{children:"Description"}),e.jsx("th",{children:"Posts"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:b.map(a=>{var m;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"rounded me-3",style:{width:"20px",height:"20px",backgroundColor:a.color||"#4567e7"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:a.name}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsx("td",{children:e.jsx("span",{className:"text-muted",children:a.description||"No description"})}),e.jsx("td",{children:e.jsxs("span",{className:"badge bg-secondary",children:[((m=a._count)==null?void 0:m.posts)||0," posts"]})}),e.jsx("td",{children:new Date(a.createdAt).toLocaleDateString()}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>p(a),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("i",{className:"mi-edit"})}),e.jsx("button",{onClick:()=>T(a.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("i",{className:"mi-trash"})})]})})]},a.id)})})]})})}),v&&e.jsx("div",{className:"modal-overlay",onClick:A,children:e.jsxs("div",{className:"modal-content",onClick:a=>a.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h4",{className:"modal-title",children:[e.jsx("i",{className:"mi-folder me-2"}),o?"Edit Category":"Create New Category"]}),e.jsx("button",{type:"button",className:"modal-close",onClick:A,children:e.jsx("i",{className:"mi-close"})})]}),e.jsxs("form",{onSubmit:s,children:[e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Category Name *"]}),e.jsx("input",{type:"text",value:d.name,onChange:a=>y("name",a.target.value),className:"form-control",placeholder:"Enter category name",required:!0})]}),e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-text me-2"}),"Description"]}),e.jsx("textarea",{value:d.description,onChange:a=>y("description",a.target.value),className:"form-control",rows:3,placeholder:"Brief description of this category"})]}),e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-palette me-2"}),"Color"]}),e.jsxs("div",{className:"d-flex align-items-center gap-3",children:[e.jsx("input",{type:"color",value:d.color,onChange:a=>y("color",a.target.value),className:"form-control form-control-color",style:{width:"60px",height:"40px"}}),e.jsx("input",{type:"text",value:d.color,onChange:a=>y("color",a.target.value),className:"form-control",placeholder:"#4567e7"})]}),e.jsx("small",{className:"form-text text-muted",children:"Choose a color to represent this category"})]})]})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",onClick:A,className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsxs("button",{type:"submit",className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-check me-2"}),o?"Update Category":"Create Category"]})]})]})]})})]})]})},Qe=Object.freeze(Object.defineProperty({__proto__:null,default:Fe},Symbol.toStringTag,{value:"Module"})),Me=()=>{O();const[b,w]=l.useState([]),[f,c]=l.useState(!0),[x,g]=l.useState(""),[u,j]=l.useState(""),[v,n]=l.useState(!1),[o,i]=l.useState(null),[d,h]=l.useState({name:""});l.useEffect(()=>{t()},[]);const t=async()=>{try{c(!0);const{response:a,data:m}=await D.getTags();m.success?w(m.data||[]):g(m.message||"Failed to load tags")}catch(a){console.error("Load tags error:",a),g("Network error. Please try again.")}finally{c(!1)}},y=(a,m)=>{h(k=>({...k,[a]:m}))},s=async a=>{a.preventDefault(),g(""),j("");try{let m,k;o?{response:m,data:k}=await D.updateTag(o.id,d):{response:m,data:k}=await D.createTag(d),k.success?(j(o?"Tag updated successfully!":"Tag created successfully!"),n(!1),i(null),h({name:""}),t()):g(k.message||"Failed to save tag")}catch(m){console.error("Save tag error:",m),g("Network error. Please try again.")}},p=a=>{i(a),h({name:a.name}),n(!0)},T=async a=>{if(window.confirm("Are you sure you want to delete this tag? This action cannot be undone."))try{const{response:m,data:k}=await D.deleteTag(a);k.success?(j("Tag deleted successfully!"),t()):g(k.message||"Failed to delete tag")}catch(m){console.error("Delete tag error:",m),g("Network error. Please try again.")}},I=()=>{i(null),h({name:""}),n(!0)},A=()=>{n(!1),i(null),h({name:""}),g("")};return e.jsxs(e.Fragment,{children:[e.jsx(M,{title:"Manage Tags - Admin",description:"Manage blog tags in the admin panel",noIndex:!0}),e.jsxs(q,{title:"Tags",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-md-6",children:e.jsx("p",{className:"section-descr mb-0",children:"Tag your blog posts for better organization and discoverability. Create and manage content tags."})}),e.jsx("div",{className:"col-md-6 text-md-end",children:e.jsxs("button",{onClick:I,className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-plus me-2"}),"New Tag"]})})]})}),x&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("i",{className:"mi-warning me-2"}),x]}),u&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("i",{className:"mi-check me-2"}),u]}),e.jsx("div",{className:"admin-table",children:f?e.jsxs("div",{className:"text-center py-60",children:[e.jsx("i",{className:"fa fa-spinner fa-spin fa-2x color-primary-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading tags..."})]}):b.length===0?e.jsxs("div",{className:"text-center py-60",children:[e.jsx("i",{className:"mi-tag fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No tags found"}),e.jsx("p",{className:"section-descr mb-30",children:"Create your first tag to start organizing your blog posts."}),e.jsxs("button",{onClick:I,className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-plus me-2"}),"Create First Tag"]})]}):e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Tag Name"}),e.jsx("th",{children:"Posts"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:b.map(a=>{var m;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("i",{className:"mi-tag me-3 color-primary-1"}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:a.name}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsx("td",{children:e.jsxs("span",{className:"badge bg-secondary",children:[((m=a._count)==null?void 0:m.posts)||0," posts"]})}),e.jsx("td",{children:new Date(a.createdAt).toLocaleDateString()}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>p(a),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("i",{className:"mi-edit"})}),e.jsx("button",{onClick:()=>T(a.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("i",{className:"mi-trash"})})]})})]},a.id)})})]})})}),v&&e.jsx("div",{className:"modal-overlay",onClick:A,children:e.jsxs("div",{className:"modal-content",onClick:a=>a.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h4",{className:"modal-title",children:[e.jsx("i",{className:"mi-tag me-2"}),o?"Edit Tag":"Create New Tag"]}),e.jsx("button",{type:"button",className:"modal-close",onClick:A,children:e.jsx("i",{className:"mi-close"})})]}),e.jsxs("form",{onSubmit:s,children:[e.jsx("div",{className:"modal-body",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Tag Name *"]}),e.jsx("input",{type:"text",value:d.name,onChange:a=>y("name",a.target.value),className:"form-control",placeholder:"Enter tag name",required:!0}),e.jsx("small",{className:"form-text text-muted",children:'Keep it short and descriptive (e.g., "JavaScript", "Tutorial", "News")'})]})})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",onClick:A,className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsxs("button",{type:"submit",className:"btn btn-mod btn-color btn-round",children:[e.jsx("i",{className:"mi-check me-2"}),o?"Update Tag":"Create Tag"]})]})]})]})})]})]})},Ve=Object.freeze(Object.defineProperty({__proto__:null,default:Me},Symbol.toStringTag,{value:"Module"}));export{We as A,Oe as H,$e as a,Re as b,qe as c,He as d,Je as e,Ge as f,Ye as g,Qe as h,Ve as i,ze as p};
//# sourceMappingURL=pages-other-DqDbC0ab.js.map
