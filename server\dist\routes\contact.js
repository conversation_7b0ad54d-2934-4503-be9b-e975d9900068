"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const contactController_1 = require("../controllers/contactController");
const router = express_1.default.Router();
const contactLimiter = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"),
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "5"),
    message: {
        success: false,
        message: "Too many contact form submissions, please try again later.",
    },
    standardHeaders: true,
    legacyHeaders: false,
});
const validateApiKey = (req, res, next) => {
    const apiKey = req.headers["x-api-key"];
    if (!apiKey || apiKey !== process.env.API_KEY) {
        return res.status(401).json({
            success: false,
            message: "Invalid or missing API key",
        });
    }
    next();
};
router.post("/", contactLimiter, validateApiKey, contactController_1.sendContactForm);
router.post("/v1/communication/public/contact-form", contactLimiter, validateApiKey, contactController_1.sendContactForm);
exports.default = router;
