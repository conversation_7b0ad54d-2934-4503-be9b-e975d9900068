import Footer from "@/components/footers/Footer";

import Header from "@/components/headers/Header";

import { Link } from "react-router-dom";
import React, { useState, useEffect } from "react";
import { menuItems } from "@/data/menu";
import { categories } from "@/data/categories";
import { tags } from "@/data/tags";
import { archiveLinks } from "@/data/archeve";
import Pagination from "@/components/common/Pagination";
import { blogAPI } from "@/utils/api";
import { useTranslation } from "react-i18next";
import MultilingualSEO from "@/components/common/MultilingualSEO";

export default function ElegantBlogPageDark() {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setLoading(true);
        const result = await blogAPI.getBlogPosts(
          currentLanguage,
          currentPage,
          9
        );

        if (result.response.ok && result.data) {
          // Extract the posts array from the nested response structure
          const posts = result.data.data?.data || result.data.data || [];
          const pagination =
            result.data.data?.pagination || result.data.pagination;
          console.log("Blog listing API response:", result.data);
          console.log("Posts array:", posts);
          console.log("Pagination:", pagination);
          setBlogPosts(Array.isArray(posts) ? posts : []);
          setTotalPages(pagination?.totalPages || 1);
        } else {
          console.error("Failed to fetch blog posts:", result.response.status);
          setBlogPosts([]);
        }
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        setBlogPosts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, [currentLanguage, currentPage]);

  // Helper function to get translation for current language
  const getTranslation = (post, field) => {
    const translation = post.translations?.find(
      (t) => t.language === currentLanguage
    );
    return (
      translation?.[field] ||
      post.translations?.find((t) => t.language === "en")?.[field] ||
      ""
    );
  };

  return (
    <>
      <MultilingualSEO
        title={
          currentLanguage === "et" ? "Blogi - DevSkills" : "Blog - DevSkills"
        }
        description={
          currentLanguage === "et"
            ? "Eksperditeadmised tarkvaraarenduse, mobiilirakenduste, AI ja tehnoloogiasuundade kohta."
            : "Expert insights on software development, mobile apps, AI, and technology trends."
        }
        slug="blog"
        type="website"
        keywords={
          currentLanguage === "et"
            ? [
                "blogi",
                "tarkvaraarendus",
                "mobiilirakendused",
                "AI",
                "tehnoloogia",
              ]
            : [
                "blog",
                "software development",
                "mobile apps",
                "AI",
                "technology",
              ]
        }
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-elegant/section-bg-1.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    BLOG
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        nsights and inspiration at your fingertips.
                      </p>
                    </div>
                  </div>
                </div>
              </section>
              <>
                <section
                  className="page-section bg-dark-1 light-content"
                  id="blog"
                >
                  <div className="container">
                    {/* Blog Posts Grid */}
                    <div
                      className="row mt-n50 mb-50 wow fadeInUp"
                      data-wow-offset={0}
                    >
                      {/* Loading State */}
                      {loading && (
                        <div className="col-12 text-center">
                          <div className="text-gray">Loading blog posts...</div>
                        </div>
                      )}

                      {/* Empty State */}
                      {!loading && blogPosts.length === 0 && (
                        <div className="col-12 text-center">
                          <div className="text-gray">
                            No blog posts available yet.
                          </div>
                        </div>
                      )}

                      {/* Post Items */}
                      {!loading &&
                        Array.isArray(blogPosts) &&
                        blogPosts.map((post) => (
                          <div
                            key={post.id}
                            className="post-prev col-md-6 col-lg-4 mt-50"
                          >
                            <div className="post-prev-container">
                              <div className="post-prev-img">
                                <Link to={`/blog-single/${post.slug}`}>
                                  <img
                                    src={
                                      post.featuredImage ||
                                      "/assets/images/demo-elegant/blog/1.jpg"
                                    }
                                    width={607}
                                    height={358}
                                    alt={getTranslation(post, "title")}
                                  />
                                </Link>
                              </div>
                              <h3 className="post-prev-title">
                                <Link to={`/blog-single/${post.slug}`}>
                                  {getTranslation(post, "title")}
                                </Link>
                              </h3>
                              <div className="post-prev-text">
                                {getTranslation(post, "excerpt")}
                              </div>
                              <div className="post-prev-info clearfix">
                                <div className="float-start">
                                  <a href="#" className="icon-author">
                                    <i className="mi-user size-14 align-middle" />
                                  </a>
                                  <a href="#">
                                    {post.author?.name || "DevSkills Team"}
                                  </a>
                                </div>
                                <div className="float-end">
                                  <i className="mi-calendar size-14 align-middle" />
                                  <a href="#">
                                    {new Date(
                                      post.publishedAt || post.createdAt
                                    ).toLocaleDateString()}
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      {/* End Post Item */}

                      {/* End Post Item */}
                    </div>
                    {/* End Blog Posts Grid */}
                    {/* Pagination */}
                    <Pagination />
                    {/* End Pagination */}
                  </div>
                </section>
                {/* End Blog Section */}
                {/* Divider */}
                <hr className="mt-0 mb-0 white" />
                {/* End Divider */}
                {/* Section */}
                <section className="page-section bg-dark-1 light-content">
                  <div className="container relative">
                    <div className="row mt-n60">
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">Categories</h3>
                          <div className="widget-body">
                            <ul className="clearlist widget-menu">
                              {categories.map((category) => (
                                <li key={category.id}>
                                  <a href="#" title="">
                                    {category.name}
                                  </a>
                                  <small> - {category.count} </small>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">Tags</h3>
                          <div className="widget-body">
                            <div className="tags">
                              {tags.map((tag) => (
                                <a href="#" key={tag.id}>
                                  {tag.name}
                                </a>
                              ))}
                            </div>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">Archive</h3>
                          <div className="widget-body">
                            <ul className="clearlist widget-menu">
                              {archiveLinks.map((link) => (
                                <li key={link.id}>
                                  <a href="#" title="">
                                    {link.date}
                                  </a>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">Text widget</h3>
                          <div className="widget-body">
                            <div className="widget-text clearfix">
                              <img
                                src="/assets/images/blog/previews/post-prev-6.jpg"
                                alt="Image Description"
                                height={140}
                                style={{ height: "fit-content" }}
                                width={100}
                                className="left img-left"
                              />
                              Consectetur adipiscing elit. Quisque magna ante
                              eleifend eleifend. Purus ut dignissim consectetur,
                              nulla erat ultrices purus, ut consequat sem elit
                              non sem. Quisque magna ante eleifend eleifend.
                            </div>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                    </div>
                  </div>
                </section>
              </>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
