import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/auth';
export declare const getTags: (req: Request, res: Response) => Promise<void>;
export declare const createTag: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateTag: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteTag: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=tagController.d.ts.map