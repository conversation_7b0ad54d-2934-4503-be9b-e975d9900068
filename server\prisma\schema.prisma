// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      Role     @default(ADMIN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  blogPosts BlogPost[]
  comments  Comment[]

  @@map("users")
}

model BlogPost {
  id          String   @id @default(cuid())
  slug        String   @unique
  published   Boolean  @default(false)
  featured    <PERSON><PERSON><PERSON>  @default(false)
  publishedAt DateTime?
  scheduledAt DateTime? // When the post should become visible
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Author relation
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  // Content in multiple languages
  translations BlogPostTranslation[]

  // Categories and tags
  categories BlogPostCategory[]
  tags       BlogPostTag[]

  // Comments
  comments Comment[]

  // Images and media
  featuredImage    String? // Main blog post image
  featuredImageAlt String? // Alt text for featured image
  gallery          String[] // Array of additional image URLs

  // SEO and metadata
  metaImage String? // Custom OG image (if different from featured)
  readTime  Int?    // in minutes
  viewCount Int     @default(0) // Track page views

  @@map("blog_posts")
}

model BlogPostTranslation {
  id       String @id @default(cuid())
  language String // 'en', 'et', 'fi', 'sv', 'de'

  // Content fields
  title       String
  excerpt     String?
  content     String @db.Text // Rich HTML content - changed to Text for larger content
  contentType String @default("html") // Track content format (html/plain)
  metaTitle   String?
  metaDesc    String?
  keywords    String[] // Array of keywords

  // Relations
  blogPostId String
  blogPost   BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([blogPostId, language])
  @@map("blog_post_translations")
}

model Category {
  id          String @id @default(cuid())
  slug        String @unique
  name        String
  description String?
  color       String? // Hex color for UI
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  blogPosts BlogPostCategory[]

  @@map("categories")
}

model Tag {
  id        String @id @default(cuid())
  slug      String @unique
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  blogPosts BlogPostTag[]

  @@map("tags")
}

model BlogPostCategory {
  blogPostId String
  categoryId String

  blogPost BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)
  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([blogPostId, categoryId])
  @@map("blog_post_categories")
}

model BlogPostTag {
  blogPostId String
  tagId      String

  blogPost BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)
  tag      Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([blogPostId, tagId])
  @@map("blog_post_tags")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  author    String   // Name of commenter
  email     String   // Email of commenter
  website   String?  // Optional website
  approved  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  blogPostId String
  blogPost   BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)

  // Optional user relation (for registered users)
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Self-referencing for replies
  parentId String?
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

enum Role {
  ADMIN
  EDITOR
  USER
}
