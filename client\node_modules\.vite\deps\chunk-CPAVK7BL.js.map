{"version": 3, "sources": ["../../.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/php.js"], "sourcesContent": ["/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const regex = hljs.regex;\n  // negative look-ahead tries to avoid matching patterns that are not\n  // Perl at all like $ident$, @ident@, etc.\n  const NOT_PERL_ETC = /(?![A-Za-z0-9])(?![$])/;\n  const IDENT_RE = regex.concat(\n    /[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/,\n    NOT_PERL_ETC);\n  // Will not detect camelCase classes\n  const PASCAL_CASE_CLASS_NAME_RE = regex.concat(\n    /(\\\\?[A-Z][a-z0-9_\\x7f-\\xff]+|\\\\?[A-Z]+(?=[A-Z][a-z0-9_\\x7f-\\xff])){1,}/,\n    NOT_PERL_ETC);\n  const UPCASE_NAME_RE = regex.concat(\n    /[A-Z]+/,\n    NOT_PERL_ETC);\n  const VARIABLE = {\n    scope: 'variable',\n    match: '\\\\$+' + IDENT_RE,\n  };\n  const PREPROCESSOR = {\n    scope: \"meta\",\n    variants: [\n      { begin: /<\\?php/, relevance: 10 }, // boost for obvious PHP\n      { begin: /<\\?=/ },\n      // less relevant per PSR-1 which says not to use short-tags\n      { begin: /<\\?/, relevance: 0.1 },\n      { begin: /\\?>/ } // end php tag\n    ]\n  };\n  const SUBST = {\n    scope: 'subst',\n    variants: [\n      { begin: /\\$\\w+/ },\n      {\n        begin: /\\{\\$/,\n        end: /\\}/\n      }\n    ]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, { illegal: null, });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n\n  const HEREDOC = {\n    begin: /<<<[ \\t]*(?:(\\w+)|\"(\\w+)\")\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n    'on:begin': (m, resp) => { resp.data._beginMatch = m[1] || m[2]; },\n    'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); },\n  };\n\n  const NOWDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*'(\\w+)'\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n  });\n  // list of valid whitespaces because non-breaking space might be part of a IDENT_RE\n  const WHITESPACE = '[ \\t\\n]';\n  const STRING = {\n    scope: 'string',\n    variants: [\n      DOUBLE_QUOTED,\n      SINGLE_QUOTED,\n      HEREDOC,\n      NOWDOC\n    ]\n  };\n  const NUMBER = {\n    scope: 'number',\n    variants: [\n      { begin: `\\\\b0[bB][01]+(?:_[01]+)*\\\\b` }, // Binary w/ underscore support\n      { begin: `\\\\b0[oO][0-7]+(?:_[0-7]+)*\\\\b` }, // Octals w/ underscore support\n      { begin: `\\\\b0[xX][\\\\da-fA-F]+(?:_[\\\\da-fA-F]+)*\\\\b` }, // Hex w/ underscore support\n      // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n      { begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:[eE][+-]?\\\\d+)?` }\n    ],\n    relevance: 0\n  };\n  const LITERALS = [\n    \"false\",\n    \"null\",\n    \"true\"\n  ];\n  const KWS = [\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__FUNCTION__\",\n    \"__COMPILER_HALT_OFFSET__\",\n    \"__LINE__\",\n    \"__METHOD__\",\n    \"__NAMESPACE__\",\n    \"__TRAIT__\",\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    \"die\",\n    \"echo\",\n    \"exit\",\n    \"include\",\n    \"include_once\",\n    \"print\",\n    \"require\",\n    \"require_once\",\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    \"array\",\n    \"abstract\",\n    \"and\",\n    \"as\",\n    \"binary\",\n    \"bool\",\n    \"boolean\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"double\",\n    \"else\",\n    \"elseif\",\n    \"empty\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"enum\",\n    \"eval\",\n    \"extends\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"instanceof\",\n    \"insteadof\",\n    \"int\",\n    \"integer\",\n    \"interface\",\n    \"isset\",\n    \"iterable\",\n    \"list\",\n    \"match|0\",\n    \"mixed\",\n    \"new\",\n    \"never\",\n    \"object\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"real\",\n    \"return\",\n    \"string\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"unset\",\n    \"use\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"xor\",\n    \"yield\"\n  ];\n\n  const BUILT_INS = [\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    \"Error|0\",\n    \"AppendIterator\",\n    \"ArgumentCountError\",\n    \"ArithmeticError\",\n    \"ArrayIterator\",\n    \"ArrayObject\",\n    \"AssertionError\",\n    \"BadFunctionCallException\",\n    \"BadMethodCallException\",\n    \"CachingIterator\",\n    \"CallbackFilterIterator\",\n    \"CompileError\",\n    \"Countable\",\n    \"DirectoryIterator\",\n    \"DivisionByZeroError\",\n    \"DomainException\",\n    \"EmptyIterator\",\n    \"ErrorException\",\n    \"Exception\",\n    \"FilesystemIterator\",\n    \"FilterIterator\",\n    \"GlobIterator\",\n    \"InfiniteIterator\",\n    \"InvalidArgumentException\",\n    \"IteratorIterator\",\n    \"LengthException\",\n    \"LimitIterator\",\n    \"LogicException\",\n    \"MultipleIterator\",\n    \"NoRewindIterator\",\n    \"OutOfBoundsException\",\n    \"OutOfRangeException\",\n    \"OuterIterator\",\n    \"OverflowException\",\n    \"ParentIterator\",\n    \"ParseError\",\n    \"RangeException\",\n    \"RecursiveArrayIterator\",\n    \"RecursiveCachingIterator\",\n    \"RecursiveCallbackFilterIterator\",\n    \"RecursiveDirectoryIterator\",\n    \"RecursiveFilterIterator\",\n    \"RecursiveIterator\",\n    \"RecursiveIteratorIterator\",\n    \"RecursiveRegexIterator\",\n    \"RecursiveTreeIterator\",\n    \"RegexIterator\",\n    \"RuntimeException\",\n    \"SeekableIterator\",\n    \"SplDoublyLinkedList\",\n    \"SplFileInfo\",\n    \"SplFileObject\",\n    \"SplFixedArray\",\n    \"SplHeap\",\n    \"SplMaxHeap\",\n    \"SplMinHeap\",\n    \"SplObjectStorage\",\n    \"SplObserver\",\n    \"SplPriorityQueue\",\n    \"SplQueue\",\n    \"SplStack\",\n    \"SplSubject\",\n    \"SplTempFileObject\",\n    \"TypeError\",\n    \"UnderflowException\",\n    \"UnexpectedValueException\",\n    \"UnhandledMatchError\",\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    \"ArrayAccess\",\n    \"BackedEnum\",\n    \"Closure\",\n    \"Fiber\",\n    \"Generator\",\n    \"Iterator\",\n    \"IteratorAggregate\",\n    \"Serializable\",\n    \"Stringable\",\n    \"Throwable\",\n    \"Traversable\",\n    \"UnitEnum\",\n    \"WeakReference\",\n    \"WeakMap\",\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    \"Directory\",\n    \"__PHP_Incomplete_Class\",\n    \"parent\",\n    \"php_user_filter\",\n    \"self\",\n    \"static\",\n    \"stdClass\"\n  ];\n\n  /** Dual-case keywords\n   *\n   * [\"then\",\"FILE\"] =>\n   *     [\"then\", \"THEN\", \"FILE\", \"file\"]\n   *\n   * @param {string[]} items */\n  const dualCase = (items) => {\n    /** @type string[] */\n    const result = [];\n    items.forEach(item => {\n      result.push(item);\n      if (item.toLowerCase() === item) {\n        result.push(item.toUpperCase());\n      } else {\n        result.push(item.toLowerCase());\n      }\n    });\n    return result;\n  };\n\n  const KEYWORDS = {\n    keyword: KWS,\n    literal: dualCase(LITERALS),\n    built_in: BUILT_INS,\n  };\n\n  /**\n   * @param {string[]} items */\n  const normalizeKeywords = (items) => {\n    return items.map(item => {\n      return item.replace(/\\|\\d+$/, \"\");\n    });\n  };\n\n  const CONSTRUCTOR_CALL = { variants: [\n    {\n      match: [\n        /new/,\n        regex.concat(WHITESPACE, \"+\"),\n        // to prevent built ins from being confused as the class constructor call\n        regex.concat(\"(?!\", normalizeKeywords(BUILT_INS).join(\"\\\\b|\"), \"\\\\b)\"),\n        PASCAL_CASE_CLASS_NAME_RE,\n      ],\n      scope: {\n        1: \"keyword\",\n        4: \"title.class\",\n      },\n    }\n  ] };\n\n  const CONSTANT_REFERENCE = regex.concat(IDENT_RE, \"\\\\b(?!\\\\()\");\n\n  const LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON = { variants: [\n    {\n      match: [\n        regex.concat(\n          /::/,\n          regex.lookahead(/(?!class\\b)/)\n        ),\n        CONSTANT_REFERENCE,\n      ],\n      scope: { 2: \"variable.constant\", },\n    },\n    {\n      match: [\n        /::/,\n        /class/,\n      ],\n      scope: { 2: \"variable.language\", },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        regex.concat(\n          /::/,\n          regex.lookahead(/(?!class\\b)/)\n        ),\n        CONSTANT_REFERENCE,\n      ],\n      scope: {\n        1: \"title.class\",\n        3: \"variable.constant\",\n      },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        regex.concat(\n          \"::\",\n          regex.lookahead(/(?!class\\b)/)\n        ),\n      ],\n      scope: { 1: \"title.class\", },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        /::/,\n        /class/,\n      ],\n      scope: {\n        1: \"title.class\",\n        3: \"variable.language\",\n      },\n    }\n  ] };\n\n  const NAMED_ARGUMENT = {\n    scope: 'attr',\n    match: regex.concat(IDENT_RE, regex.lookahead(':'), regex.lookahead(/(?!::)/)),\n  };\n  const PARAMS_MODE = {\n    relevance: 0,\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      NAMED_ARGUMENT,\n      VARIABLE,\n      LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n      hljs.C_BLOCK_COMMENT_MODE,\n      STRING,\n      NUMBER,\n      CONSTRUCTOR_CALL,\n    ],\n  };\n  const FUNCTION_INVOKE = {\n    relevance: 0,\n    match: [\n      /\\b/,\n      // to prevent keywords from being confused as the function title\n      regex.concat(\"(?!fn\\\\b|function\\\\b|\", normalizeKeywords(KWS).join(\"\\\\b|\"), \"|\", normalizeKeywords(BUILT_INS).join(\"\\\\b|\"), \"\\\\b)\"),\n      IDENT_RE,\n      regex.concat(WHITESPACE, \"*\"),\n      regex.lookahead(/(?=\\()/)\n    ],\n    scope: { 3: \"title.function.invoke\", },\n    contains: [ PARAMS_MODE ]\n  };\n  PARAMS_MODE.contains.push(FUNCTION_INVOKE);\n\n  const ATTRIBUTE_CONTAINS = [\n    NAMED_ARGUMENT,\n    LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING,\n    NUMBER,\n    CONSTRUCTOR_CALL,\n  ];\n\n  const ATTRIBUTES = {\n    begin: regex.concat(/#\\[\\s*\\\\?/,\n      regex.either(\n        PASCAL_CASE_CLASS_NAME_RE,\n        UPCASE_NAME_RE\n      )\n    ),\n    beginScope: \"meta\",\n    end: /]/,\n    endScope: \"meta\",\n    keywords: {\n      literal: LITERALS,\n      keyword: [\n        'new',\n        'array',\n      ]\n    },\n    contains: [\n      {\n        begin: /\\[/,\n        end: /]/,\n        keywords: {\n          literal: LITERALS,\n          keyword: [\n            'new',\n            'array',\n          ]\n        },\n        contains: [\n          'self',\n          ...ATTRIBUTE_CONTAINS,\n        ]\n      },\n      ...ATTRIBUTE_CONTAINS,\n      {\n        scope: 'meta',\n        variants: [\n          { match: PASCAL_CASE_CLASS_NAME_RE },\n          { match: UPCASE_NAME_RE }\n        ]\n      }\n    ]\n  };\n\n  return {\n    case_insensitive: false,\n    keywords: KEYWORDS,\n    contains: [\n      ATTRIBUTES,\n      hljs.HASH_COMMENT_MODE,\n      hljs.COMMENT('//', '$'),\n      hljs.COMMENT(\n        '/\\\\*',\n        '\\\\*/',\n        { contains: [\n          {\n            scope: 'doctag',\n            match: '@[A-Za-z]+'\n          }\n        ] }\n      ),\n      {\n        match: /__halt_compiler\\(\\);/,\n        keywords: '__halt_compiler',\n        starts: {\n          scope: \"comment\",\n          end: hljs.MATCH_NOTHING_RE,\n          contains: [\n            {\n              match: /\\?>/,\n              scope: \"meta\",\n              endsParent: true\n            }\n          ]\n        }\n      },\n      PREPROCESSOR,\n      {\n        scope: 'variable.language',\n        match: /\\$this\\b/\n      },\n      VARIABLE,\n      FUNCTION_INVOKE,\n      LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n      {\n        match: [\n          /const/,\n          /\\s/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"variable.constant\",\n        },\n      },\n      CONSTRUCTOR_CALL,\n      {\n        scope: 'function',\n        relevance: 0,\n        beginKeywords: 'fn function',\n        end: /[;{]/,\n        excludeEnd: true,\n        illegal: '[$%\\\\[]',\n        contains: [\n          { beginKeywords: 'use', },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: '=>', // No markup, just a relevance booster\n            endsParent: true\n          },\n          {\n            scope: 'params',\n            begin: '\\\\(',\n            end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              ATTRIBUTES,\n              VARIABLE,\n              LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          },\n        ]\n      },\n      {\n        scope: 'class',\n        variants: [\n          {\n            beginKeywords: \"enum\",\n            illegal: /[($\"]/\n          },\n          {\n            beginKeywords: \"class interface trait\",\n            illegal: /[:($\"]/\n          }\n        ],\n        relevance: 0,\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      // both use and namespace still use \"old style\" rules (vs multi-match)\n      // because the namespace name can include `\\` and we still want each\n      // element to be treated as its own *individual* title\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: ';',\n        illegal: /[.']/,\n        contains: [ hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, { scope: \"title.class\" }) ]\n      },\n      {\n        beginKeywords: 'use',\n        relevance: 0,\n        end: ';',\n        contains: [\n          // TODO: title.function vs title.class\n          {\n            match: /\\b(as|const|function)\\b/,\n            scope: \"keyword\"\n          },\n          // TODO: could be title.class or title.function\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      STRING,\n      NUMBER,\n    ]\n  };\n}\n\nexport { php as default };\n"], "mappings": ";AAYA,SAAS,IAAI,MAAM;AACjB,QAAM,QAAQ,KAAK;AAGnB,QAAM,eAAe;AACrB,QAAM,WAAW,MAAM;AAAA,IACrB;AAAA,IACA;AAAA,EAAY;AAEd,QAAM,4BAA4B,MAAM;AAAA,IACtC;AAAA,IACA;AAAA,EAAY;AACd,QAAM,iBAAiB,MAAM;AAAA,IAC3B;AAAA,IACA;AAAA,EAAY;AACd,QAAM,WAAW;AAAA,IACf,OAAO;AAAA,IACP,OAAO,SAAS;AAAA,EAClB;AACA,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,UAAU;AAAA,MACR,EAAE,OAAO,UAAU,WAAW,GAAG;AAAA;AAAA,MACjC,EAAE,OAAO,OAAO;AAAA;AAAA,MAEhB,EAAE,OAAO,OAAO,WAAW,IAAI;AAAA,MAC/B,EAAE,OAAO,MAAM;AAAA;AAAA,IACjB;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ,OAAO;AAAA,IACP,UAAU;AAAA,MACR,EAAE,OAAO,QAAQ;AAAA,MACjB;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,KAAK,QAAQ,KAAK,kBAAkB,EAAE,SAAS,KAAM,CAAC;AAC5E,QAAM,gBAAgB,KAAK,QAAQ,KAAK,mBAAmB;AAAA,IACzD,SAAS;AAAA,IACT,UAAU,KAAK,kBAAkB,SAAS,OAAO,KAAK;AAAA,EACxD,CAAC;AAED,QAAM,UAAU;AAAA,IACd,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU,KAAK,kBAAkB,SAAS,OAAO,KAAK;AAAA,IACtD,YAAY,CAAC,GAAG,SAAS;AAAE,WAAK,KAAK,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,IAAG;AAAA,IACjE,UAAU,CAAC,GAAG,SAAS;AAAE,UAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC,EAAG,MAAK,YAAY;AAAA,IAAG;AAAA,EACnF;AAEA,QAAM,SAAS,KAAK,kBAAkB;AAAA,IACpC,OAAO;AAAA,IACP,KAAK;AAAA,EACP,CAAC;AAED,QAAM,aAAa;AACnB,QAAM,SAAS;AAAA,IACb,OAAO;AAAA,IACP,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,OAAO;AAAA,IACP,UAAU;AAAA,MACR,EAAE,OAAO,8BAA8B;AAAA;AAAA,MACvC,EAAE,OAAO,gCAAgC;AAAA;AAAA,MACzC,EAAE,OAAO,4CAA4C;AAAA;AAAA;AAAA,MAErD,EAAE,OAAO,6EAA6E;AAAA,IACxF;AAAA,IACA,WAAW;AAAA,EACb;AACA,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,MAAM;AAAA;AAAA;AAAA,IAGV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,YAAY;AAAA;AAAA;AAAA,IAGhB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAQA,QAAM,WAAW,CAAC,UAAU;AAE1B,UAAM,SAAS,CAAC;AAChB,UAAM,QAAQ,UAAQ;AACpB,aAAO,KAAK,IAAI;AAChB,UAAI,KAAK,YAAY,MAAM,MAAM;AAC/B,eAAO,KAAK,KAAK,YAAY,CAAC;AAAA,MAChC,OAAO;AACL,eAAO,KAAK,KAAK,YAAY,CAAC;AAAA,MAChC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAEA,QAAM,WAAW;AAAA,IACf,SAAS;AAAA,IACT,SAAS,SAAS,QAAQ;AAAA,IAC1B,UAAU;AAAA,EACZ;AAIA,QAAM,oBAAoB,CAAC,UAAU;AACnC,WAAO,MAAM,IAAI,UAAQ;AACvB,aAAO,KAAK,QAAQ,UAAU,EAAE;AAAA,IAClC,CAAC;AAAA,EACH;AAEA,QAAM,mBAAmB,EAAE,UAAU;AAAA,IACnC;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA,MAAM,OAAO,YAAY,GAAG;AAAA;AAAA,QAE5B,MAAM,OAAO,OAAO,kBAAkB,SAAS,EAAE,KAAK,MAAM,GAAG,MAAM;AAAA,QACrE;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE;AAEF,QAAM,qBAAqB,MAAM,OAAO,UAAU,YAAY;AAE9D,QAAM,sCAAsC,EAAE,UAAU;AAAA,IACtD;AAAA,MACE,OAAO;AAAA,QACL,MAAM;AAAA,UACJ;AAAA,UACA,MAAM,UAAU,aAAa;AAAA,QAC/B;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO,EAAE,GAAG,oBAAqB;AAAA,IACnC;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO,EAAE,GAAG,oBAAqB;AAAA,IACnC;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,UACJ;AAAA,UACA,MAAM,UAAU,aAAa;AAAA,QAC/B;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,UACJ;AAAA,UACA,MAAM,UAAU,aAAa;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,OAAO,EAAE,GAAG,cAAe;AAAA,IAC7B;AAAA,IACA;AAAA,MACE,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE;AAEF,QAAM,iBAAiB;AAAA,IACrB,OAAO;AAAA,IACP,OAAO,MAAM,OAAO,UAAU,MAAM,UAAU,GAAG,GAAG,MAAM,UAAU,QAAQ,CAAC;AAAA,EAC/E;AACA,QAAM,cAAc;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB,WAAW;AAAA,IACX,OAAO;AAAA,MACL;AAAA;AAAA,MAEA,MAAM,OAAO,yBAAyB,kBAAkB,GAAG,EAAE,KAAK,MAAM,GAAG,KAAK,kBAAkB,SAAS,EAAE,KAAK,MAAM,GAAG,MAAM;AAAA,MACjI;AAAA,MACA,MAAM,OAAO,YAAY,GAAG;AAAA,MAC5B,MAAM,UAAU,QAAQ;AAAA,IAC1B;AAAA,IACA,OAAO,EAAE,GAAG,wBAAyB;AAAA,IACrC,UAAU,CAAE,WAAY;AAAA,EAC1B;AACA,cAAY,SAAS,KAAK,eAAe;AAEzC,QAAM,qBAAqB;AAAA,IACzB;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,aAAa;AAAA,IACjB,OAAO,MAAM;AAAA,MAAO;AAAA,MAClB,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,YACP;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA,GAAG;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAG;AAAA,MACH;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,UACR,EAAE,OAAO,0BAA0B;AAAA,UACnC,EAAE,OAAO,eAAe;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,MACR;AAAA,MACA,KAAK;AAAA,MACL,KAAK,QAAQ,MAAM,GAAG;AAAA,MACtB,KAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA,EAAE,UAAU;AAAA,UACV;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF,EAAE;AAAA,MACJ;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,KAAK,KAAK;AAAA,UACV,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,OAAO;AAAA,cACP,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,UAAU;AAAA,UACR,EAAE,eAAe,MAAO;AAAA,UACxB,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA;AAAA,YACP,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,YACE,eAAe;AAAA,YACf,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,WAAW;AAAA,QACX,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,UACR,EAAE,eAAe,qBAAqB;AAAA,UACtC,KAAK;AAAA,QACP;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,QACE,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAAE,KAAK,QAAQ,KAAK,uBAAuB,EAAE,OAAO,cAAc,CAAC,CAAE;AAAA,MACjF;AAAA,MACA;AAAA,QACE,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA;AAAA,UAEA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": []}