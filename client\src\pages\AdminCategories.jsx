import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO from '../components/common/SEO';
import AdminLayout from '../components/admin/AdminLayout';
import { adminAPI } from '../utils/api';

const AdminCategories = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#4567e7'
  });

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const { response, data } = await adminAPI.getCategories();

      if (data.success) {
        setCategories(data.data || []);
      } else {
        setError(data.message || 'Failed to load categories');
      }
    } catch (error) {
      console.error('Load categories error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      let response, data;

      if (editingCategory) {
        ({ response, data } = await adminAPI.updateCategory(editingCategory.id, formData));
      } else {
        ({ response, data } = await adminAPI.createCategory(formData));
      }

      if (data.success) {
        setSuccess(editingCategory ? 'Category updated successfully!' : 'Category created successfully!');
        setShowModal(false);
        setEditingCategory(null);
        setFormData({ name: '', description: '', color: '#4567e7' });
        loadCategories();
      } else {
        setError(data.message || 'Failed to save category');
      }
    } catch (error) {
      console.error('Save category error:', error);
      setError('Network error. Please try again.');
    }
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || '',
      color: category.color || '#4567e7'
    });
    setShowModal(true);
  };

  const handleDelete = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    try {
      const { response, data } = await adminAPI.deleteCategory(categoryId);

      if (data.success) {
        setSuccess('Category deleted successfully!');
        loadCategories();
      } else {
        setError(data.message || 'Failed to delete category');
      }
    } catch (error) {
      console.error('Delete category error:', error);
      setError('Network error. Please try again.');
    }
  };

  const openCreateModal = () => {
    setEditingCategory(null);
    setFormData({ name: '', description: '', color: '#4567e7' });
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingCategory(null);
    setFormData({ name: '', description: '', color: '#4567e7' });
    setError('');
  };

  return (
    <>
      <SEO 
        title="Manage Categories - Admin"
        description="Manage blog categories in the admin panel"
        noIndex={true}
      />
      
      <AdminLayout title="Categories">
        
        {/* Action Bar */}
        <div className="mb-30">
          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="section-descr mb-0">
                Organize your blog posts with categories. Create, edit, and manage content categories.
              </p>
            </div>
            <div className="col-md-6 text-md-end">
              <button
                onClick={openCreateModal}
                className="btn btn-mod btn-color btn-round"
              >
                <i className="mi-plus me-2"></i>
                New Category
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="alert alert-danger mb-30" role="alert">
            <i className="mi-warning me-2"></i>
            {error}
          </div>
        )}
        
        {success && (
          <div className="alert alert-success mb-30" role="alert">
            <i className="mi-check me-2"></i>
            {success}
          </div>
        )}

        {/* Categories Table */}
        <div className="admin-table">
          {loading ? (
            <div className="text-center py-60">
              <i className="fa fa-spinner fa-spin fa-2x color-primary-1 mb-20"></i>
              <div className="hs-line-4 font-alt black">Loading categories...</div>
            </div>
          ) : categories.length === 0 ? (
            <div className="text-center py-60">
              <i className="mi-folder fa-3x color-gray-light-1 mb-20"></i>
              <div className="hs-line-4 font-alt black mb-10">No categories found</div>
              <p className="section-descr mb-30">
                Create your first category to start organizing your blog posts.
              </p>
              <button
                onClick={openCreateModal}
                className="btn btn-mod btn-color btn-round"
              >
                <i className="mi-plus me-2"></i>
                Create First Category
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Category</th>
                    <th>Description</th>
                    <th>Posts</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {categories.map((category) => (
                    <tr key={category.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <div 
                            className="rounded me-3"
                            style={{ 
                              width: '20px', 
                              height: '20px', 
                              backgroundColor: category.color || '#4567e7' 
                            }}
                          ></div>
                          <div>
                            <div className="fw-bold">{category.name}</div>
                            <small className="text-muted">/{category.slug}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className="text-muted">
                          {category.description || 'No description'}
                        </span>
                      </td>
                      <td>
                        <span className="badge bg-secondary">
                          {category._count?.posts || 0} posts
                        </span>
                      </td>
                      <td>
                        {new Date(category.createdAt).toLocaleDateString()}
                      </td>
                      <td>
                        <div className="btn-group" role="group">
                          <button
                            onClick={() => handleEdit(category)}
                            className="btn btn-sm btn-outline-primary"
                            title="Edit"
                          >
                            <i className="mi-edit"></i>
                          </button>
                          <button
                            onClick={() => handleDelete(category.id)}
                            className="btn btn-sm btn-outline-danger"
                            title="Delete"
                          >
                            <i className="mi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="modal-overlay" onClick={closeModal}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h4 className="modal-title">
                  <i className="mi-folder me-2"></i>
                  {editingCategory ? 'Edit Category' : 'Create New Category'}
                </h4>
                <button 
                  type="button" 
                  className="modal-close"
                  onClick={closeModal}
                >
                  <i className="mi-close"></i>
                </button>
              </div>
              
              <form onSubmit={handleSubmit}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-12 mb-20">
                      <label className="form-label">
                        <i className="mi-edit me-2"></i>
                        Category Name *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="form-control"
                        placeholder="Enter category name"
                        required
                      />
                    </div>
                    
                    <div className="col-12 mb-20">
                      <label className="form-label">
                        <i className="mi-text me-2"></i>
                        Description
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        className="form-control"
                        rows={3}
                        placeholder="Brief description of this category"
                      />
                    </div>
                    
                    <div className="col-12 mb-20">
                      <label className="form-label">
                        <i className="mi-palette me-2"></i>
                        Color
                      </label>
                      <div className="d-flex align-items-center gap-3">
                        <input
                          type="color"
                          value={formData.color}
                          onChange={(e) => handleInputChange('color', e.target.value)}
                          className="form-control form-control-color"
                          style={{ width: '60px', height: '40px' }}
                        />
                        <input
                          type="text"
                          value={formData.color}
                          onChange={(e) => handleInputChange('color', e.target.value)}
                          className="form-control"
                          placeholder="#4567e7"
                        />
                      </div>
                      <small className="form-text text-muted">
                        Choose a color to represent this category
                      </small>
                    </div>
                  </div>
                </div>
                
                <div className="modal-footer">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="btn btn-mod btn-gray btn-round me-3"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-mod btn-color btn-round"
                  >
                    <i className="mi-check me-2"></i>
                    {editingCategory ? 'Update Category' : 'Create Category'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
        
      </AdminLayout>
    </>
  );
};

export default AdminCategories;
