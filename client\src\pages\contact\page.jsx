import React from "react";
import Footer from "@/components/footers/Footer";
import Header from "@/components/headers/Header";
import { menuItems } from "@/data/menu";
import Contact from "@/components/home/<USER>";
import MarqueeDark from "@/components/home/<USER>";
import Map from "@/components/common/Map";
import { useTranslation } from "react-i18next";

import MetaComponent from "@/components/common/MetaComponent";

const dark = true;
export default function ElegantContactPageDark() {
  const { t, currentLanguage } = useTranslation();

  const metadata = {
    title:
      currentLanguage === "en"
        ? "Contact | DevSkills - Get in Touch With Our Team"
        : "Kontakt | DevSkills - Võta Meie Meeskonnaga Ühendust",
    description:
      currentLanguage === "en"
        ? "Contact DevSkills for business transformation, software development, and AI implementation services."
        : "Võta ühendust DevSkills-iga äri transformatsiooni, tarkvara arenduse ja tehisintellekti rakendamise teenuste osas.",
  };

  return (
    <>
      <MetaComponent meta={metadata} />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-elegant/section-bg-1.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("contact.page.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("contact.page.subtitle")}
                      </p>
                    </div>
                  </div>
                </div>
              </section>
              <section
                className={`page-section  scrollSpysection mb-0 pb-0  ${
                  dark ? "bg-dark-1 light-content" : ""
                } `}
                id="contact"
              >
                <Contact />
              </section>
              <div className="page-section overflow-hidden">
                <MarqueeDark />
              </div>
              <div className="google-map light-content">
                <Map />
              </div>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
