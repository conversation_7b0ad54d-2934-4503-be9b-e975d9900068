import {
  Node,
  mergeAttributes
} from "./chunk-W6QQZAON.js";
import "./chunk-DC5AMYBS.js";

// node_modules/.pnpm/@tiptap+extension-table-cell@2.22.3_@tiptap+core@2.22.3_@tiptap+pm@2.22.3_/node_modules/@tiptap/extension-table-cell/dist/index.js
var TableCell = Node.create({
  name: "tableCell",
  addOptions() {
    return {
      HTMLAttributes: {}
    };
  },
  content: "block+",
  addAttributes() {
    return {
      colspan: {
        default: 1
      },
      rowspan: {
        default: 1
      },
      colwidth: {
        default: null,
        parseHTML: (element) => {
          const colwidth = element.getAttribute("colwidth");
          const value = colwidth ? colwidth.split(",").map((width) => parseInt(width, 10)) : null;
          return value;
        }
      }
    };
  },
  tableRole: "cell",
  isolating: true,
  parseHTML() {
    return [
      { tag: "td" }
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ["td", mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
  }
});
export {
  TableCell,
  TableCell as default
};
//# sourceMappingURL=@tiptap_extension-table-cell.js.map
