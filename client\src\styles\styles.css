@import "swiper/css";
@import "../../public/assets/css/styles.css";
@import "jarallax/dist/jarallax.min.css";
@import "swiper/css/effect-fade";
@import "react-modal-video/css/modal-video.css";
@import "photoswipe/dist/photoswipe.css";

@import "tippy.js/dist/tippy.css";

/* Custom styles */
@import "./admin.css";

/* TipTap Blog Content Styles */
.blog-content pre {
  background: #1e1e1e !important;
  color: #d4d4d4 !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin: 16px 0 !important;
  overflow-x: auto !important;
  font-family: "Consolas", "Monaco", "Courier New", monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  position: relative !important;
}

.blog-content pre code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit !important;
}

/* NUCLEAR OPTION: COMPLETELY OVERRIDE .light-content INHERITANCE */
/* Block .light-content { color: #fff !important; } with maximum specificity */
.light-content .syntax-highlight-isolated,
.light-content .syntax-highlight-isolated *,
.page-section.light-content .syntax-highlight-isolated,
.page-section.light-content .syntax-highlight-isolated *,
.theme-elegant
  .dark-mode
  .page
  .page-section.light-content
  .syntax-highlight-isolated,
.theme-elegant
  .dark-mode
  .page
  .page-section.light-content
  .syntax-highlight-isolated
  * {
  color: unset !important; /* Remove the white color inheritance */
  font-family: Arial, sans-serif !important;
  font-size: 16px !important;
  line-height: 1.8 !important;
  background: transparent !important;
}

/* Block inheritance for text elements but preserve their original colors */
.syntax-highlight-isolated p {
  margin: 1em 0 !important;
  font-size: 16px !important;
  line-height: 1.8 !important;
  /* DO NOT set color - let TipTap handle it */
}

.syntax-highlight-isolated h1,
.syntax-highlight-isolated h2,
.syntax-highlight-isolated h3,
.syntax-highlight-isolated h4,
.syntax-highlight-isolated h5,
.syntax-highlight-isolated h6 {
  font-weight: bold !important;
  margin: 1.2em 0 0.6em 0 !important;
  /* DO NOT set color - let TipTap handle it */
}

.syntax-highlight-isolated h1 {
  font-size: 2em !important;
}
.syntax-highlight-isolated h2 {
  font-size: 1.5em !important;
}
.syntax-highlight-isolated h3 {
  font-size: 1.3em !important;
}

.syntax-highlight-isolated strong,
.syntax-highlight-isolated b {
  font-weight: bold !important;
  /* DO NOT set color - let TipTap handle it */
}

.syntax-highlight-isolated em,
.syntax-highlight-isolated i {
  font-style: italic !important;
  /* DO NOT set color - let TipTap handle it */
}

.syntax-highlight-isolated ul,
.syntax-highlight-isolated ol {
  margin: 1em 0 !important;
  padding-left: 2em !important;
  /* DO NOT set color - let TipTap handle it */
}

.syntax-highlight-isolated li {
  margin: 0.5em 0 !important;
  /* DO NOT set color - let TipTap handle it */
}

/* Force syntax highlighting colors with maximum specificity */
.syntax-highlight-isolated .hljs-keyword {
  color: #569cd6 !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-string {
  color: #ce9178 !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-number {
  color: #b5cea8 !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-comment {
  color: #6a9955 !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-tag {
  color: #569cd6 !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-name {
  color: #4fc1ff !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-attr {
  color: #9cdcfe !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-function {
  color: #dcdcaa !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-title {
  color: #dcdcaa !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-variable {
  color: #9cdcfe !important;
  border: 1px solid red !important;
}
.syntax-highlight-isolated .hljs-property {
  color: #9cdcfe !important;
  border: 1px solid red !important;
}

/* Force pre element styling */
.syntax-highlight-isolated pre {
  background: #1e1e1e !important;
  color: #d4d4d4 !important;
  padding: 16px !important;
  border-radius: 8px !important;
  border: 3px solid yellow !important;
  font-family: Consolas, "Courier New", monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin: 16px 0 !important;
  overflow-x: auto !important;
}

div.blog-content span.hljs-meta,
span.hljs-meta {
  color: #569cd6 !important;
  background: none !important;
}

.theme-elegant .dark-mode .page .blog-content span.hljs-tag,
.theme-elegant .dark-mode .blog-content span.hljs-tag,
.theme-elegant .blog-content span.hljs-tag,
.dark-mode .blog-content span.hljs-tag,
div.blog-content span.hljs-tag,
span.hljs-tag {
  color: #569cd6 !important;
  background: rgba(86, 156, 214, 0.1) !important; /* Debug background */
  border: 1px solid blue !important; /* Debug border */
}

.theme-elegant .dark-mode .page .blog-content span.hljs-name,
.theme-elegant .dark-mode .blog-content span.hljs-name,
.theme-elegant .blog-content span.hljs-name,
.dark-mode .blog-content span.hljs-name,
div.blog-content span.hljs-name,
span.hljs-name {
  color: #4fc1ff !important;
  background: rgba(79, 193, 255, 0.1) !important; /* Debug background */
  border: 1px solid cyan !important; /* Debug border */
}

.theme-elegant .dark-mode .page .blog-content span.hljs-attr,
.theme-elegant .dark-mode .blog-content span.hljs-attr,
.theme-elegant .blog-content span.hljs-attr,
.dark-mode .blog-content span.hljs-attr,
div.blog-content span.hljs-attr,
span.hljs-attr {
  color: #9cdcfe !important;
  background: rgba(156, 220, 254, 0.1) !important; /* Debug background */
  border: 1px solid lightblue !important; /* Debug border */
}

.theme-elegant .dark-mode .page .blog-content span.hljs-string,
.theme-elegant .dark-mode .blog-content span.hljs-string,
.theme-elegant .blog-content span.hljs-string,
.dark-mode .blog-content span.hljs-string,
div.blog-content span.hljs-string,
span.hljs-string {
  color: #ce9178 !important;
  background: rgba(206, 145, 120, 0.2) !important; /* Debug background */
  border: 2px solid green !important; /* Debug border */
}

div.blog-content span.hljs-number,
span.hljs-number {
  color: #b5cea8 !important;
  background: none !important;
}

div.blog-content span.hljs-title.function_,
span.hljs-title.function_ {
  color: #dcdcaa !important;
  background: none !important;
}

div.blog-content span.hljs-variable.language_,
span.hljs-variable.language_ {
  color: #9cdcfe !important;
  background: none !important;
}

div.blog-content span.hljs-property,
span.hljs-property {
  color: #9cdcfe !important;
  background: none !important;
}
.blog-content .hljs-string {
  color: #ce9178 !important;
}
.blog-content .hljs-number {
  color: #b5cea8 !important;
}
.blog-content .hljs-comment {
  color: #6a9955 !important;
}
.blog-content .hljs-function {
  color: #dcdcaa !important;
}
.blog-content .hljs-variable {
  color: #9cdcfe !important;
}
.blog-content .hljs-type {
  color: #4ec9b0 !important;
}
.blog-content .hljs-property {
  color: #9cdcfe !important;
}
.blog-content .hljs-operator {
  color: #d4d4d4 !important;
}
.blog-content .hljs-punctuation {
  color: #d4d4d4 !important;
}
.blog-content .hljs-tag {
  color: #569cd6 !important;
}
.blog-content .hljs-attr {
  color: #9cdcfe !important;
}
.blog-content .hljs-value {
  color: #ce9178 !important;
}
.blog-content .hljs-built_in {
  color: #4ec9b0 !important;
}
.blog-content .hljs-literal {
  color: #569cd6 !important;
}
.blog-content .hljs-title {
  color: #dcdcaa !important;
}
.blog-content .hljs-params {
  color: #d4d4d4 !important;
}

/* Language labels for code blocks in blog content */
.blog-content pre[data-language]::before {
  content: attr(data-language);
  position: absolute;
  top: 8px;
  right: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #d4d4d4;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
}

/* Ensure other TipTap elements display correctly in blog content */
.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  margin-top: 24px !important;
  margin-bottom: 16px !important;
}

.blog-content ul,
.blog-content ol {
  margin: 16px 0 !important;
  padding-left: 24px !important;
}

.blog-content blockquote {
  border-left: 4px solid #569cd6 !important;
  padding-left: 16px !important;
  margin: 16px 0 !important;
  font-style: italic !important;
  color: #888 !important;
}

.blog-content a {
  color: #569cd6 !important;
  text-decoration: underline !important;
}

.blog-content strong {
  font-weight: bold !important;
}

.blog-content em {
  font-style: italic !important;
}
