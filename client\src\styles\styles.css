@import "swiper/css";
@import "../../public/assets/css/styles.css";
@import "jarallax/dist/jarallax.min.css";
@import "swiper/css/effect-fade";
@import "react-modal-video/css/modal-video.css";
@import "photoswipe/dist/photoswipe.css";

@import "tippy.js/dist/tippy.css";

/* Custom styles */
@import "./admin.css";

/* TipTap Blog Content Styles */
.blog-content pre {
  background: #1e1e1e !important;
  color: #d4d4d4 !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin: 16px 0 !important;
  overflow-x: auto !important;
  font-family: "Consolas", "Monaco", "Courier New", monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  position: relative !important;
}

.blog-content pre code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit !important;
}

/* VS Code Dark Theme Syntax Highlighting - FORCE COLORS WITH DEBUG */
/* Use very high specificity to override any conflicting styles */
div.blog-content span.hljs-keyword,
span.hljs-keyword {
  color: #569cd6 !important;
  background: rgba(86, 156, 214, 0.2) !important; /* Debug background */
  border: 2px solid red !important; /* Debug border */
}

div.blog-content span.hljs-meta,
span.hljs-meta {
  color: #569cd6 !important;
  background: none !important;
}

div.blog-content span.hljs-tag,
span.hljs-tag {
  color: #569cd6 !important;
  background: none !important;
}

div.blog-content span.hljs-name,
span.hljs-name {
  color: #4fc1ff !important;
  background: none !important;
}

div.blog-content span.hljs-attr,
span.hljs-attr {
  color: #9cdcfe !important;
  background: none !important;
}

div.blog-content span.hljs-string,
span.hljs-string {
  color: #ce9178 !important;
  background: rgba(206, 145, 120, 0.2) !important; /* Debug background */
  border: 2px solid green !important; /* Debug border */
}

div.blog-content span.hljs-number,
span.hljs-number {
  color: #b5cea8 !important;
  background: none !important;
}

div.blog-content span.hljs-title.function_,
span.hljs-title.function_ {
  color: #dcdcaa !important;
  background: none !important;
}

div.blog-content span.hljs-variable.language_,
span.hljs-variable.language_ {
  color: #9cdcfe !important;
  background: none !important;
}

div.blog-content span.hljs-property,
span.hljs-property {
  color: #9cdcfe !important;
  background: none !important;
}
.blog-content .hljs-string {
  color: #ce9178 !important;
}
.blog-content .hljs-number {
  color: #b5cea8 !important;
}
.blog-content .hljs-comment {
  color: #6a9955 !important;
}
.blog-content .hljs-function {
  color: #dcdcaa !important;
}
.blog-content .hljs-variable {
  color: #9cdcfe !important;
}
.blog-content .hljs-type {
  color: #4ec9b0 !important;
}
.blog-content .hljs-property {
  color: #9cdcfe !important;
}
.blog-content .hljs-operator {
  color: #d4d4d4 !important;
}
.blog-content .hljs-punctuation {
  color: #d4d4d4 !important;
}
.blog-content .hljs-tag {
  color: #569cd6 !important;
}
.blog-content .hljs-attr {
  color: #9cdcfe !important;
}
.blog-content .hljs-value {
  color: #ce9178 !important;
}
.blog-content .hljs-built_in {
  color: #4ec9b0 !important;
}
.blog-content .hljs-literal {
  color: #569cd6 !important;
}
.blog-content .hljs-title {
  color: #dcdcaa !important;
}
.blog-content .hljs-params {
  color: #d4d4d4 !important;
}

/* Language labels for code blocks in blog content */
.blog-content pre[data-language]::before {
  content: attr(data-language);
  position: absolute;
  top: 8px;
  right: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #d4d4d4;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
}

/* Ensure other TipTap elements display correctly in blog content */
.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  margin-top: 24px !important;
  margin-bottom: 16px !important;
}

.blog-content ul,
.blog-content ol {
  margin: 16px 0 !important;
  padding-left: 24px !important;
}

.blog-content blockquote {
  border-left: 4px solid #569cd6 !important;
  padding-left: 16px !important;
  margin: 16px 0 !important;
  font-style: italic !important;
  color: #888 !important;
}

.blog-content a {
  color: #569cd6 !important;
  text-decoration: underline !important;
}

.blog-content strong {
  font-weight: bold !important;
}

.blog-content em {
  font-style: italic !important;
}
