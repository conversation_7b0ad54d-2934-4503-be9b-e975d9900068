"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupTempFiles = exports.getFileUrl = exports.deleteFile = exports.moveFromTemp = exports.uploadTemp = exports.uploadBlogImages = exports.uploadBlogImage = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const uploadDir = path_1.default.join(process.cwd(), "uploads");
const blogImagesDir = path_1.default.join(uploadDir, "blog-images");
const tempDir = path_1.default.join(uploadDir, "temp");
const ensureDirectories = () => {
    [uploadDir, blogImagesDir, tempDir].forEach((dir) => {
        try {
            if (!fs_1.default.existsSync(dir)) {
                fs_1.default.mkdirSync(dir, { recursive: true, mode: 0o755 });
                console.log(`✅ Created directory: ${dir}`);
            }
            else {
                console.log(`📁 Directory already exists: ${dir}`);
            }
        }
        catch (error) {
            if (error.code === "EACCES") {
                console.warn(`⚠️ Permission denied creating ${dir} - assuming Docker volume mount`);
            }
            else {
                console.error(`❌ Failed to create directory ${dir}:`, error);
            }
        }
    });
};
ensureDirectories();
const fileFilter = (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    if (mimetype && extname) {
        return cb(null, true);
    }
    else {
        cb(new Error("Only image files (JPEG, JPG, PNG, GIF, WebP) are allowed!"));
    }
};
const blogImageStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, blogImagesDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        const ext = path_1.default.extname(file.originalname);
        const name = file.fieldname + "-" + uniqueSuffix + ext;
        cb(null, name);
    },
});
const tempStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, tempDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        const ext = path_1.default.extname(file.originalname);
        const name = "temp-" + uniqueSuffix + ext;
        cb(null, name);
    },
});
exports.uploadBlogImage = (0, multer_1.default)({
    storage: blogImageStorage,
    fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024,
        files: 1,
    },
});
exports.uploadBlogImages = (0, multer_1.default)({
    storage: blogImageStorage,
    fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024,
        files: 10,
    },
});
exports.uploadTemp = (0, multer_1.default)({
    storage: tempStorage,
    fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024,
    },
});
const moveFromTemp = (tempFilename, permanentPath) => {
    return new Promise((resolve, reject) => {
        const tempFilePath = path_1.default.join(tempDir, tempFilename);
        const permanentFilePath = path_1.default.join(blogImagesDir, permanentPath);
        fs_1.default.rename(tempFilePath, permanentFilePath, (err) => {
            if (err) {
                reject(err);
            }
            else {
                resolve(permanentFilePath);
            }
        });
    });
};
exports.moveFromTemp = moveFromTemp;
const deleteFile = (filePath) => {
    return new Promise((resolve, reject) => {
        fs_1.default.unlink(filePath, (err) => {
            if (err && err.code !== "ENOENT") {
                reject(err);
            }
            else {
                resolve();
            }
        });
    });
};
exports.deleteFile = deleteFile;
const getFileUrl = (filename, type = "blog-images") => {
    const baseUrl = process.env.BASE_URL || "http://localhost:4004";
    return `${baseUrl}/uploads/${type}/${filename}`;
};
exports.getFileUrl = getFileUrl;
const cleanupTempFiles = (maxAgeHours = 24) => {
    fs_1.default.readdir(tempDir, (err, files) => {
        if (err)
            return;
        const now = Date.now();
        const maxAge = maxAgeHours * 60 * 60 * 1000;
        files.forEach((file) => {
            const filePath = path_1.default.join(tempDir, file);
            fs_1.default.stat(filePath, (err, stats) => {
                if (err)
                    return;
                if (now - stats.mtime.getTime() > maxAge) {
                    fs_1.default.unlink(filePath, (err) => {
                        if (err)
                            console.error("Error deleting temp file:", err);
                    });
                }
            });
        });
    });
};
exports.cleanupTempFiles = cleanupTempFiles;
