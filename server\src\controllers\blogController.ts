import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";
import slugify from "slugify";
import { AuthRequest } from "../middleware/auth";
import { getFileUrl, deleteFile } from "../middleware/upload";
import path from "path";
import DOMPurify from "dompurify";
import { JSDOM } from "jsdom";

const prisma = new PrismaClient();

// Initialize DOMPurify with JSD<PERSON>
const window = new JSDOM("").window;
const purify = DOMPurify(window);

// HTML sanitization function
const sanitizeHTML = (html: string): string => {
  if (!html || typeof html !== "string") {
    return "";
  }

  // Configure DOMPurify to allow safe HTML elements and attributes
  const cleanHTML = purify.sanitize(html, {
    ALLOWED_TAGS: [
      "p",
      "br",
      "strong",
      "em",
      "u",
      "s",
      "sub",
      "sup",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "ul",
      "ol",
      "li",
      "blockquote",
      "pre",
      "code",
      "a",
      "img",
      "table",
      "thead",
      "tbody",
      "tr",
      "th",
      "td",
      "hr",
      "div",
      "span",
    ],
    ALLOWED_ATTR: [
      "href",
      "target",
      "rel",
      "src",
      "alt",
      "title",
      "class",
      "data-language",
      "colspan",
      "rowspan",
    ],
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false,
    SANITIZE_DOM: true,
    KEEP_CONTENT: true,
  });

  return cleanHTML;
};

// Validation schemas
const createBlogPostSchema = Joi.object({
  slug: Joi.string().optional(),
  featured: Joi.boolean().default(false),
  published: Joi.boolean().default(false),
  scheduledAt: Joi.date().optional().allow(null),
  publishedAt: Joi.date().optional().allow(null),
  featuredImageAlt: Joi.string().optional().allow(""),
  readTime: Joi.number().integer().min(1).optional(),
  categoryIds: Joi.array().items(Joi.string()).default([]),
  tagIds: Joi.array().items(Joi.string()).default([]),
  translations: Joi.object()
    .pattern(
      Joi.string().valid("en", "et", "fi", "sv", "de"),
      Joi.object({
        title: Joi.string().allow(""),
        excerpt: Joi.string().optional().allow(""),
        content: Joi.string().allow(""),
        metaTitle: Joi.string().optional().allow(""),
        metaDesc: Joi.string().optional().allow(""),
        keywords: Joi.array().items(Joi.string()).default([]),
      })
    )
    .required(),
});

const updateBlogPostSchema = createBlogPostSchema.keys({
  id: Joi.string().required(),
});

// @desc    Get all blog posts with filters
// @route   GET /api/blog
// @access  Public
export const getBlogPosts = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 10,
      language = "en",
      category,
      tag,
      featured,
      published = "true",
      search,
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const now = new Date();

    // Build where clause
    const where: any = {};

    // Only show published posts that are scheduled to be visible
    if (published === "true") {
      where.published = true;
      where.OR = [{ scheduledAt: null }, { scheduledAt: { lte: now } }];
    }

    if (featured === "true") {
      where.featured = true;
    }

    // Search in translations
    if (search) {
      where.translations = {
        some: {
          language: language as string,
          OR: [
            { title: { contains: search as string, mode: "insensitive" } },
            { excerpt: { contains: search as string, mode: "insensitive" } },
            { content: { contains: search as string, mode: "insensitive" } },
          ],
        },
      };
    }

    // Filter by category
    if (category) {
      where.categories = {
        some: {
          category: {
            slug: category as string,
          },
        },
      };
    }

    // Filter by tag
    if (tag) {
      where.tags = {
        some: {
          tag: {
            slug: tag as string,
          },
        },
      };
    }

    const [posts, total] = await Promise.all([
      prisma.blogPost.findMany({
        where,
        include: {
          author: {
            select: { id: true, name: true, email: true },
          },
          translations: true, // Include all translations, let frontend choose
          categories: {
            include: { category: true },
          },
          tags: {
            include: { tag: true },
          },
          _count: {
            select: { comments: { where: { approved: true } } },
          },
        },
        orderBy: [
          { featured: "desc" },
          { publishedAt: "desc" },
          { createdAt: "desc" },
        ],
        skip,
        take: Number(limit),
      }),
      prisma.blogPost.count({ where }),
    ]);

    // Format response
    const formattedPosts = posts.map((post) => ({
      id: post.id,
      slug: post.slug,
      published: post.published,
      featured: post.featured,
      publishedAt: post.publishedAt,
      scheduledAt: post.scheduledAt,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      featuredImage: post.featuredImage ? getFileUrl(post.featuredImage) : null,
      featuredImageAlt: post.featuredImageAlt,
      readTime: post.readTime,
      viewCount: post.viewCount,
      author: post.author,
      translations: post.translations, // Return all translations for frontend to choose
      categories: post.categories.map((c) => c.category),
      tags: post.tags.map((t) => t.tag),
      commentCount: post._count.comments,
    }));

    res.json({
      success: true,
      data: {
        data: formattedPosts, // Posts array nested in data.data
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
          totalPages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    console.error("Get blog posts error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
};

// @desc    Get single blog post by slug
// @route   GET /api/blog/:slug
// @access  Public
export const getBlogPost = async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const { language = "en" } = req.query;
    const now = new Date();

    const post = await prisma.blogPost.findUnique({
      where: { slug },
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
        translations: true,
        categories: {
          include: { category: true },
        },
        tags: {
          include: { tag: true },
        },
        comments: {
          where: { approved: true, parentId: null },
          include: {
            replies: {
              where: { approved: true },
              orderBy: { createdAt: "asc" },
            },
          },
          orderBy: { createdAt: "desc" },
        },
      },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: "Blog post not found",
      });
    }

    // Check if post should be visible
    if (!post.published || (post.scheduledAt && post.scheduledAt > now)) {
      return res.status(404).json({
        success: false,
        message: "Blog post not found",
      });
    }

    // Increment view count
    await prisma.blogPost.update({
      where: { id: post.id },
      data: { viewCount: { increment: 1 } },
    });

    // Get translation for requested language or fallback to English
    const translation =
      post.translations.find((t) => t.language === language) ||
      post.translations.find((t) => t.language === "en") ||
      post.translations[0];

    const formattedPost = {
      id: post.id,
      slug: post.slug,
      published: post.published,
      featured: post.featured,
      publishedAt: post.publishedAt,
      scheduledAt: post.scheduledAt,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      featuredImage: post.featuredImage ? getFileUrl(post.featuredImage) : null,
      featuredImageAlt: post.featuredImageAlt,
      gallery: post.gallery.map((img) => getFileUrl(img)),
      readTime: post.readTime,
      viewCount: post.viewCount,
      author: post.author,
      translation,
      translations: post.translations,
      categories: post.categories.map((c) => c.category),
      tags: post.tags.map((t) => t.tag),
      comments: post.comments,
    };

    res.json({
      success: true,
      data: formattedPost,
    });
  } catch (error) {
    console.error("Get blog post error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
};

// @desc    Create new blog post
// @route   POST /api/blog
// @access  Private/Admin
export const createBlogPost = async (req: AuthRequest, res: Response) => {
  try {
    // Parse FormData fields (multer puts form fields in req.body)
    const formData = { ...req.body };

    // Parse JSON strings from FormData
    try {
      if (formData.categoryIds) {
        formData.categoryIds = JSON.parse(formData.categoryIds);
      }
      if (formData.tagIds) {
        formData.tagIds = JSON.parse(formData.tagIds);
      }
      if (formData.translations) {
        formData.translations = JSON.parse(formData.translations);
      }

      // Convert boolean strings to actual booleans
      if (formData.featured) {
        formData.featured = formData.featured === "true";
      }
      if (formData.published) {
        formData.published = formData.published === "true";
      }

      // Convert readTime to number if provided
      if (formData.readTime) {
        formData.readTime = parseInt(formData.readTime, 10);
      }
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: "Invalid JSON in form data",
      });
    }

    // Validate input
    const { error, value } = createBlogPostSchema.validate(formData);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    const {
      slug: providedSlug,
      featured,
      published,
      scheduledAt,
      publishedAt,
      featuredImageAlt,
      readTime,
      categoryIds,
      tagIds,
      translations,
    } = value;

    // Custom validation: Ensure English content is provided
    if (
      !translations.en ||
      !translations.en.title ||
      !translations.en.content
    ) {
      return res.status(400).json({
        success: false,
        message: "English title and content are required",
      });
    }

    // Filter out empty translations (keep only languages with content)
    const validTranslations = Object.entries(translations).reduce(
      (acc, [lang, translation]) => {
        const typedTranslation = translation as any;
        if (typedTranslation.title && typedTranslation.content) {
          acc[lang] = translation;
        }
        return acc;
      },
      {} as any
    );

    // Generate slug from English title if not provided
    const slug =
      providedSlug ||
      slugify(translations.en.title, { lower: true, strict: true });

    // Check if slug already exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug },
    });

    if (existingPost) {
      return res.status(400).json({
        success: false,
        message: "A blog post with this slug already exists",
      });
    }

    // Handle featured image from request
    let featuredImage = null;
    if (req.file) {
      featuredImage = req.file.filename;
    }

    // Create blog post with translations
    const blogPost = await prisma.blogPost.create({
      data: {
        slug,
        featured,
        published,
        scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
        publishedAt: published
          ? publishedAt
            ? new Date(publishedAt)
            : new Date()
          : null,
        featuredImage,
        featuredImageAlt,
        readTime,
        authorId: req.user!.id,
        translations: {
          create: Object.entries(validTranslations).map(
            ([language, translation]: [string, any]) => ({
              language,
              title: translation.title,
              excerpt: translation.excerpt || "",
              content: sanitizeHTML(translation.content), // Sanitize HTML content
              contentType: "html", // Mark as HTML content
              metaTitle: translation.metaTitle || translation.title,
              metaDesc: translation.metaDesc || translation.excerpt || "",
              keywords: translation.keywords || [],
            })
          ),
        },
        categories: {
          create: categoryIds.map((categoryId: string) => ({
            categoryId,
          })),
        },
        tags: {
          create: tagIds.map((tagId: string) => ({
            tagId,
          })),
        },
      },
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
        translations: true,
        categories: {
          include: { category: true },
        },
        tags: {
          include: { tag: true },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: {
        ...blogPost,
        featuredImage: blogPost.featuredImage
          ? getFileUrl(blogPost.featuredImage)
          : null,
      },
    });
  } catch (error) {
    console.error("Create blog post error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
};

// @desc    Delete blog post (with all translations)
// @route   DELETE /api/blog/:id
// @access  Private/Admin
export const deleteBlogPost = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const post = await prisma.blogPost.findUnique({
      where: { id },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: "Blog post not found",
      });
    }

    // Delete featured image if exists
    if (post.featuredImage) {
      try {
        await deleteFile(
          path.join(process.cwd(), "uploads", "blog-images", post.featuredImage)
        );
      } catch (error) {
        console.error("Error deleting image:", error);
      }
    }

    // Delete gallery images
    if (post.gallery && post.gallery.length > 0) {
      for (const image of post.gallery) {
        try {
          await deleteFile(
            path.join(process.cwd(), "uploads", "blog-images", image)
          );
        } catch (error) {
          console.error("Error deleting gallery image:", error);
        }
      }
    }

    // Delete blog post (cascade will handle translations, categories, tags, comments)
    await prisma.blogPost.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: "Blog post deleted successfully",
    });
  } catch (error) {
    console.error("Delete blog post error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
};

// @desc    Toggle blog post visibility
// @route   PATCH /api/blog/:id/toggle-visibility
// @access  Private/Admin
export const toggleBlogPostVisibility = async (
  req: AuthRequest,
  res: Response
) => {
  try {
    const { id } = req.params;

    const post = await prisma.blogPost.findUnique({
      where: { id },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: "Blog post not found",
      });
    }

    const updatedPost = await prisma.blogPost.update({
      where: { id },
      data: {
        published: !post.published,
        publishedAt: !post.published ? new Date() : null,
      },
    });

    res.json({
      success: true,
      data: updatedPost,
      message: `Blog post ${
        updatedPost.published ? "published" : "unpublished"
      } successfully`,
    });
  } catch (error) {
    console.error("Toggle visibility error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
};

// @desc    Update blog post
// @route   PUT /api/blog/:id
// @access  Private/Admin
export const updateBlogPost = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Parse FormData fields (multer puts form fields in req.body)
    const formData = { ...req.body, id };

    // Parse JSON strings from FormData
    try {
      if (formData.categoryIds) {
        formData.categoryIds = JSON.parse(formData.categoryIds);
      }
      if (formData.tagIds) {
        formData.tagIds = JSON.parse(formData.tagIds);
      }
      if (formData.translations) {
        formData.translations = JSON.parse(formData.translations);
      }

      // Convert boolean strings to actual booleans
      if (formData.featured) {
        formData.featured = formData.featured === "true";
      }
      if (formData.published) {
        formData.published = formData.published === "true";
      }

      // Convert readTime to number if provided
      if (formData.readTime) {
        formData.readTime = parseInt(formData.readTime, 10);
      }
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: "Invalid JSON in form data",
      });
    }

    // Validate input
    const { error, value } = updateBlogPostSchema.validate(formData);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    const {
      slug: providedSlug,
      featured,
      published,
      scheduledAt,
      publishedAt,
      featuredImageAlt,
      readTime,
      categoryIds,
      tagIds,
      translations,
    } = value;

    // Custom validation: Ensure English content is provided
    if (
      !translations.en ||
      !translations.en.title ||
      !translations.en.content
    ) {
      return res.status(400).json({
        success: false,
        message: "English title and content are required",
      });
    }

    // Filter out empty translations (keep only languages with content)
    const validTranslations = Object.entries(translations).reduce(
      (acc, [lang, translation]) => {
        const typedTranslation = translation as any;
        if (typedTranslation.title && typedTranslation.content) {
          acc[lang] = translation;
        }
        return acc;
      },
      {} as any
    );

    // Check if post exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { id },
      include: { translations: true },
    });

    if (!existingPost) {
      return res.status(404).json({
        success: false,
        message: "Blog post not found",
      });
    }

    // Generate slug if provided or keep existing
    let slug = existingPost.slug;
    if (providedSlug && providedSlug !== existingPost.slug) {
      slug = slugify(providedSlug || translations.en.title, {
        lower: true,
        strict: true,
      });

      // Check if new slug already exists
      const slugExists = await prisma.blogPost.findUnique({
        where: { slug },
      });

      if (slugExists && slugExists.id !== id) {
        return res.status(400).json({
          success: false,
          message: "A blog post with this slug already exists",
        });
      }
    }

    // Handle featured image
    let featuredImage = existingPost.featuredImage;
    if (req.file) {
      // Delete old image if exists
      if (existingPost.featuredImage) {
        try {
          await deleteFile(
            path.join(
              process.cwd(),
              "uploads",
              "blog-images",
              existingPost.featuredImage
            )
          );
        } catch (error) {
          console.error("Error deleting old image:", error);
        }
      }
      featuredImage = req.file.filename;
    }

    // Update blog post
    const updatedPost = await prisma.$transaction(async (tx) => {
      // Delete existing translations, categories, and tags
      await tx.blogPostTranslation.deleteMany({
        where: { blogPostId: id },
      });
      await tx.blogPostCategory.deleteMany({
        where: { blogPostId: id },
      });
      await tx.blogPostTag.deleteMany({
        where: { blogPostId: id },
      });

      // Update the blog post
      return await tx.blogPost.update({
        where: { id },
        data: {
          slug,
          featured,
          published,
          scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
          publishedAt: published
            ? publishedAt
              ? new Date(publishedAt)
              : new Date()
            : null,
          featuredImage,
          featuredImageAlt,
          readTime,
          translations: {
            create: Object.entries(validTranslations).map(
              ([language, translation]: [string, any]) => ({
                language,
                title: translation.title,
                excerpt: translation.excerpt || "",
                content: sanitizeHTML(translation.content), // Sanitize HTML content
                contentType: "html", // Mark as HTML content
                metaTitle: translation.metaTitle || translation.title,
                metaDesc: translation.metaDesc || translation.excerpt || "",
                keywords: translation.keywords || [],
              })
            ),
          },
          categories: {
            create: categoryIds.map((categoryId: string) => ({
              categoryId,
            })),
          },
          tags: {
            create: tagIds.map((tagId: string) => ({
              tagId,
            })),
          },
        },
        include: {
          author: {
            select: { id: true, name: true, email: true },
          },
          translations: true,
          categories: {
            include: { category: true },
          },
          tags: {
            include: { tag: true },
          },
        },
      });
    });

    res.json({
      success: true,
      data: {
        ...updatedPost,
        featuredImage: updatedPost.featuredImage
          ? getFileUrl(updatedPost.featuredImage)
          : null,
      },
    });
  } catch (error) {
    console.error("Update blog post error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
};
