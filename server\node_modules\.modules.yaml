hoistPattern:
  - '*'
hoistedDependencies:
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': private
  '@asamuzakjp/dom-selector@2.0.2':
    '@asamuzakjp/dom-selector': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@prisma/config@6.10.1':
    '@prisma/config': private
  '@prisma/debug@6.10.1':
    '@prisma/debug': private
  '@prisma/engines-version@6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c':
    '@prisma/engines-version': private
  '@prisma/engines@6.10.1':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.10.1':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.10.1':
    '@prisma/get-platform': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  accepts@1.3.8:
    accepts: private
  agent-base@7.1.3:
    agent-base: private
  anymatch@3.1.3:
    anymatch: private
  append-field@1.0.0:
    append-field: private
  argparse@1.0.10:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@1.0.2:
    balanced-match: private
  basic-auth@2.0.1:
    basic-auth: private
  bidi-js@1.0.3:
    bidi-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  chokidar@3.6.0:
    chokidar: private
  combined-stream@1.0.8:
    combined-stream: private
  compressible@2.0.18:
    compressible: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  css-tree@2.3.1:
    css-tree: private
  cssstyle@4.5.0:
    cssstyle: private
  data-urls@5.0.0:
    data-urls: private
  debug@2.6.9:
    debug: private
  decimal.js@10.5.0:
    decimal.js: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  encodeurl@2.0.0:
    encodeurl: private
  entities@6.0.1:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.5:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  esprima@4.0.1:
    esprima: private
  etag@1.8.1:
    etag: private
  extend-shallow@2.0.1:
    extend-shallow: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  form-data@4.0.3:
    form-data: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@5.1.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  jiti@2.4.2:
    jiti: private
  js-yaml@3.14.1:
    js-yaml: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  kind-of@6.0.3:
    kind-of: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.once@4.1.1:
    lodash.once: private
  lru-cache@10.4.3:
    lru-cache: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.0.30:
    mdn-data: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  methods@1.1.2:
    methods: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  negotiator@0.6.4:
    negotiator: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picomatch@2.3.1:
    picomatch: private
  proxy-addr@2.0.7:
    proxy-addr: private
  psl@1.15.0:
    psl: private
  pstree.remy@1.1.8:
    pstree.remy: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  rrweb-cssom@0.6.0:
    rrweb-cssom: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  section-matter@1.0.0:
    section-matter: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  source-map-js@1.2.1:
    source-map-js: private
  sprintf-js@1.0.3:
    sprintf-js: private
  statuses@2.0.1:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-bom-string@1.0.0:
    strip-bom-string: private
  supports-color@5.5.0:
    supports-color: private
  symbol-tree@3.2.4:
    symbol-tree: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  type-is@1.6.18:
    type-is: private
  typedarray@0.0.6:
    typedarray: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  universalify@0.2.0:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  url-parse@1.5.10:
    url-parse: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  vary@1.1.2:
    vary: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.2.0:
    whatwg-url: private
  ws@8.18.2:
    ws: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.4
pendingBuilds: []
prunedAt: Tue, 24 Jun 2025 11:19:23 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\dev-skills\server\node_modules\.pnpm
virtualStoreDirMaxLength: 120
