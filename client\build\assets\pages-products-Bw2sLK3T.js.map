{"version": 3, "file": "pages-products-Bw2sLK3T.js", "sources": ["../../src/pages/products/bms/overview/page.jsx", "../../src/pages/products/bms/module/page.jsx", "../../src/pages/products/bms/core/page.jsx", "../../src/pages/products/bms/accounting/page.jsx", "../../src/pages/products/bms/budget/page.jsx", "../../src/pages/products/bms/hr/page.jsx", "../../src/pages/products/bms/recruitment/page.jsx", "../../src/pages/products/bms/production/page.jsx", "../../src/pages/products/bms/sales/page.jsx", "../../src/pages/products/bms/quality/page.jsx", "../../src/pages/products/bms/communication/page.jsx", "../../src/pages/products/bms/companies/page.jsx", "../../src/pages/products/ultimation/overview/page.jsx"], "sourcesContent": ["import React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MetaComponent from \"@/components/common/MetaComponent\";\n\nconst metadata = {\n  title: \"Ultimation Studio Overview\",\n  description: \"Overview of the Ultimation Studio modules and features\",\n};\n\nexport default function BMSOverviewPage() {\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-alpha-50 light-content\">\n                <div className=\"container\">\n                  <h1>BMS Overview</h1>\n                  {/* Add your BMS overview content here */}\n                </div>\n              </section>\n            </main>\n            <Footer />\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React, { useEffect } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MetaComponent from \"@/components/common/MetaComponent\";\n\nconst metadata = {\n  title: \"Ultimation Studio Module\",\n  description: \"Ultimation Studio module details\",\n};\n\nexport default function BMSModulePage() {\n  const { moduleId } = useParams();\n\n  // If the module is 'core', redirect to the Core module page\n  useEffect(() => {\n    if (moduleId === \"core\") {\n      window.location.href = \"/products/bms/core\";\n    }\n  }, [moduleId]);\n\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-alpha-50 light-content\">\n                <div className=\"container\">\n                  <h1>BMS Module: {moduleId}</h1>\n                  {/* Add your module-specific content here */}\n                </div>\n              </section>\n            </main>\n            <Footer />\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/products/bms/core/page.jsx\n\nimport React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Core Module page\nconst coreModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Core Module\",\n  description:\n    \"The Core Module is the command center of your business empire, providing essential framework for managing your entire business ecosystem.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function CoreModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"core.page.title\")} - Ultimation Studio`}\n        description={t(\"core.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/core\"\n        image=\"https://devskills.ee/og-images/module-core.png\"\n        type=\"product\"\n        schema={coreModuleSchema}\n        keywords={[\n          \"core module\",\n          \"business management\",\n          \"Ultimation Studio\",\n          \"business framework\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"core.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"core.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"core.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"core.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"core.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-lock\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"core.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"core.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-settings\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"core.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"core.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-link\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"core.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"core.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-layers\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"core.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"core.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"core.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"core.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"core.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"core.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"core.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"core.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"core.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/products/bms/accounting/page.jsx\n\nimport React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Accounting Module page\nconst accountingModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Business Comanager - Accounting Module\",\n  description:\n    \"The Accounting Module transforms your financial operations from a reactive burden into a strategic advantage with real-time visibility and control.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkill<PERSON>\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Business Comanager\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function AccountingModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"accounting.page.title\")} - Ultimation Studio`}\n        description={t(\"accounting.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/accounting\"\n        image=\"https://devskills.ee/og-images/module-accounting.png\"\n        type=\"product\"\n        schema={accountingModuleSchema}\n        keywords={[\n          \"accounting module\",\n          \"financial management\",\n          \"Ultimation Studio\",\n          \"automated bookkeeping\",\n          \"financial reporting\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"accounting.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"accounting.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"accounting.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"accounting.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"accounting.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-book\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"accounting.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"accounting.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"accounting.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"accounting.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-document\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"accounting.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"accounting.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-check\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"accounting.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"accounting.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"accounting.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"accounting.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-link\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"accounting.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"accounting.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"accounting.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"accounting.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"accounting.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/products/bms/budget/page.jsx\n\nimport React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Budget Module page\nconst budgetModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Business Comanager - Budget Module\",\n  description:\n    \"The Budget Module transforms your budgeting process from a periodic guessing game into a dynamic strategic tool with intelligent forecasting and real-time adjustments.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Business Comanager\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function BudgetModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"budget.page.title\")} - Ultimation Studio`}\n        description={t(\"budget.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/budget\"\n        image=\"https://devskills.ee/og-images/module-budget.png\"\n        type=\"product\"\n        schema={budgetModuleSchema}\n        keywords={[\n          \"budget module\",\n          \"financial planning\",\n          \"Ultimation Studio\",\n          \"forecasting\",\n          \"scenario planning\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"budget.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"budget.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"budget.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"budget.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"budget.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"budget.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"budget.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-map\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"budget.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"budget.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-users\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"budget.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"budget.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"budget.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"budget.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-flag\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"budget.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"budget.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-refresh\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"budget.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"budget.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"budget.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"budget.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"budget.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/products/bms/hr/page.jsx\n\nimport React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the HR Module page\nconst hrModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Business Comanager - Human Resources Module\",\n  description:\n    \"The HR Module transforms your human resources from a cost center into your greatest competitive advantage with cutting-edge technology and human-centered design.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Business Comanager\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function HRModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"hr.page.title\")} - Ultimation Studio`}\n        description={t(\"hr.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/hr\"\n        image=\"https://devskills.ee/og-images/module-hr.png\"\n        type=\"product\"\n        schema={hrModuleSchema}\n        keywords={[\n          \"human resources module\",\n          \"talent management\",\n          \"Ultimation Studio\",\n          \"recruitment\",\n          \"employee development\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"hr.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"hr.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"hr.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"hr.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"hr.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"hr.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"hr.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-users\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"hr.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"hr.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-star\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"hr.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"hr.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"hr.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"hr.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-heart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"hr.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"hr.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-check\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"hr.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"hr.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"hr.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"hr.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    className=\"ultimation-cta-btn btn-large\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {t(\"hr.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Recruitment Module page\nconst recruitmentModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Recruitment Module\",\n  description:\n    \"The Recruitment Module transforms your hiring process from a time-consuming guessing game into a strategic competitive advantage with AI-powered matching and data-driven insights.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function RecruitmentModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"recruitment.page.title\")} - Ultimation Studio`}\n        description={t(\"recruitment.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/recruitment\"\n        image=\"https://devskills.ee/og-images/module-recruitment.png\"\n        type=\"product\"\n        schema={recruitmentModuleSchema}\n        keywords={[\n          \"recruitment module\",\n          \"talent acquisition\",\n          \"Ultimation Studio\",\n          \"AI recruitment\",\n          \"hiring platform\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"recruitment.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"recruitment.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"recruitment.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"recruitment.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"recruitment.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"recruitment.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"recruitment.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-filter\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"recruitment.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"recruitment.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-users\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"recruitment.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"recruitment.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-star\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"recruitment.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"recruitment.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"recruitment.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"recruitment.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-check\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"recruitment.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"recruitment.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"recruitment.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"recruitment.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"recruitment.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Production Module page\nconst productionModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Production Module\",\n  description:\n    \"The Production Module transforms your production operations from a complex logistical challenge into a streamlined competitive advantage with real-time monitoring and predictive maintenance.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function ProductionModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"production.page.title\")} - Ultimation Studio`}\n        description={t(\"production.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/production\"\n        image=\"https://devskills.ee/og-images/module-production.png\"\n        type=\"product\"\n        schema={productionModuleSchema}\n        keywords={[\n          \"production module\",\n          \"manufacturing management\",\n          \"Ultimation Studio\",\n          \"predictive maintenance\",\n          \"inventory optimization\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"production.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"production.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"production.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"production.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"production.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"production.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"production.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-settings\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"production.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"production.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-box\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"production.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"production.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-check\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"production.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"production.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-users\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"production.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"production.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"production.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"production.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"production.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"production.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"production.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Sales Module page\nconst salesModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Sales Module\",\n  description:\n    \"The Sales Module transforms your sales process from a high-pressure guessing game into a strategic, data-driven revenue engine with AI-powered lead scoring and automated follow-ups.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function SalesModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"sales.page.title\")} - Ultimation Studio`}\n        description={t(\"sales.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/sales\"\n        image=\"https://devskills.ee/og-images/module-sales.png\"\n        type=\"product\"\n        schema={salesModuleSchema}\n        keywords={[\n          \"sales module\",\n          \"revenue generation\",\n          \"Ultimation Studio\",\n          \"lead scoring\",\n          \"sales pipeline\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"sales.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"sales.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"sales.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"sales.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"sales.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"sales.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"sales.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"sales.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"sales.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-mail\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"sales.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"sales.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-stats\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"sales.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"sales.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-document\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"sales.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"sales.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-link\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"sales.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"sales.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"sales.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"sales.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"sales.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Quality Control Module page\nconst qualityModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Quality Control Module\",\n  description:\n    \"The Quality Control Module transforms your quality control from a reactive inspection process into a proactive strategic advantage with real-time monitoring and predictive analytics.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function QualityControlModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"quality.page.title\")} - Ultimation Studio`}\n        description={t(\"quality.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/quality\"\n        image=\"https://devskills.ee/og-images/module-quality.png\"\n        type=\"product\"\n        schema={qualityModuleSchema}\n        keywords={[\n          \"quality control module\",\n          \"excellence assurance\",\n          \"Ultimation Studio\",\n          \"quality monitoring\",\n          \"predictive analytics\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"quality.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"quality.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"quality.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"quality.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"quality.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"quality.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"quality.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-search\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"quality.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"quality.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-check\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"quality.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"quality.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-document\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"quality.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"quality.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-box\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"quality.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"quality.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-refresh\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"quality.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"quality.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"quality.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"quality.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"quality.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/products/bms/communication/page.jsx\n\nimport React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Communication Module page\nconst communicationModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Communication Module\",\n  description:\n    \"The Communication Module transforms your business communication from fragmented channels into a unified strategic advantage with email management, real-time chat, and custom API endpoints.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function CommunicationModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"communication.page.title\")} - Ultimation Studio`}\n        description={t(\"communication.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/communication\"\n        image=\"https://devskills.ee/og-images/module-communication.png\"\n        type=\"product\"\n        schema={communicationModuleSchema}\n        keywords={[\n          \"communication module\",\n          \"unified messaging\",\n          \"Ultimation Studio\",\n          \"email management\",\n          \"real-time chat\",\n          \"API endpoints\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"communication.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"communication.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"communication.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"communication.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"communication.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-inbox\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"communication.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"communication.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-mail\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"communication.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"communication.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-chat\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"communication.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"communication.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-link\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"communication.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"communication.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bell\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"communication.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"communication.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"communication.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"communication.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"communication.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"communication.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"communication.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/products/bms/companies/page.jsx\n\nimport React from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Companies Module page\nconst companiesModuleSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"SoftwareApplication\",\n  name: \"Ultimation Studio - Companies Module\",\n  description:\n    \"The Companies Module transforms your multi-entity operations from a complex administrative burden into a streamlined strategic advantage with centralized control and cross-company analytics.\",\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: \"https://devskills.ee/logo.png\",\n      width: \"180\",\n      height: \"60\",\n    },\n  },\n  isPartOf: {\n    \"@type\": \"SoftwareApplication\",\n    name: \"Ultimation Studio\",\n    url: \"https://devskills.ee/products/bms\",\n  },\n};\n\nexport default function CompaniesModulePage() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <SEO\n        title={`${t(\"companies.page.title\")} - Ultimation Studio`}\n        description={t(\"companies.page.intro\")}\n        canonical=\"https://devskills.ee/products/bms/companies\"\n        image=\"https://devskills.ee/og-images/module-companies.png\"\n        type=\"product\"\n        schema={companiesModuleSchema}\n        keywords={[\n          \"companies module\",\n          \"multi-entity management\",\n          \"Ultimation Studio\",\n          \"entity management\",\n          \"cross-entity reporting\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"companies.page.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"companies.page.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"companies.page.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"companies.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Features Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"companies.page.description\")}\n                </h3>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-home\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"companies.feature1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"companies.feature1.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 1 */}\n\n              {/* Feature 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-settings\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"companies.feature2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"companies.feature2.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 2 */}\n\n              {/* Feature 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-document\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"companies.feature3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"companies.feature3.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 3 */}\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Feature 4 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-refresh\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"companies.feature4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"companies.feature4.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 4 */}\n\n              {/* Feature 5 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-lock\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"companies.feature5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"companies.feature5.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 5 */}\n\n              {/* Feature 6 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"companies.feature6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"companies.feature6.description\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Feature 6 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Features Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"companies.cta.title\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"companies.cta.subtitle\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://comanager.ee\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"companies.cta.button\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n", "import React, { useEffect } from \"react\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { menuItems } from \"@/data/menu\";\nimport { Link } from \"react-router-dom\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport SEO from \"@/components/common/SEO\";\nimport \"@/styles/ultimation-styles.css\";\nimport { useTranslation } from \"react-i18next\";\n\n// JSON-LD structured data for the Ultimation Studio Overview page\nconst ultimationSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"Product\",\n  name: \"Ultimation Studio\",\n  description:\n    \"The Ultimate Business Management System that combines AI-driven automation with modular flexibility.\",\n  brand: {\n    \"@type\": \"Brand\",\n    name: \"DevSkills\",\n  },\n  offers: {\n    \"@type\": \"Offer\",\n    price: \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n    availability: \"https://schema.org/InStock\",\n  },\n  category: \"Business Management Software\",\n};\n\nexport default function UltimationStudioOverviewPage() {\n  const { t } = useTranslation();\n\n  // Load testimonial slider script\n  useEffect(() => {\n    // Dynamically import the testimonial slider script\n    const script = document.createElement(\"script\");\n    script.src = \"/scripts/simple-slider.js\";\n    script.async = true;\n    document.body.appendChild(script);\n\n    // Log to confirm script loading\n    console.log(\"Loading slider script\");\n\n    // Cleanup function to remove the script when component unmounts\n    return () => {\n      document.body.removeChild(script);\n    };\n  }, []);\n\n  return (\n    <>\n      <SEO\n        title=\"Ultimation Studio - Overview | The Ultimate Business Management System\"\n        description=\"Discover Ultimation Studio - the next-generation business management system that combines AI-driven automation with modular flexibility to transform your business operations.\"\n        canonical=\"https://devskills.ee/products/ultimation/overview\"\n        image=\"https://devskills.ee/og-images/ultimation-overview.png\"\n        type=\"product\"\n        schema={ultimationSchema}\n        keywords={[\n          \"Ultimation Studio\",\n          \"business management system\",\n          \"AI automation\",\n          \"modular BMS\",\n          \"enterprise software\",\n          \"DEVSKILLS\",\n        ]}\n      />\n      <div className=\"appear-animate\">\n        {/* Navigation panel */}\n        <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n          <Header links={menuItems} />\n        </nav>\n        {/* End Navigation panel */}\n\n        {/* Hero Section */}\n        <section className=\"page-section bg-dark-1 light-content\" id=\"home\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center\">\n                <h2\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.1s\"\n                >\n                  {t(\"ultimation.overview.title\")}\n                </h2>\n                <h3\n                  className=\"hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.2s\"\n                >\n                  {t(\"ultimation.overview.subtitle\")}\n                </h3>\n                <p\n                  className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\"\n                  data-wow-delay=\"0.3s\"\n                >\n                  {t(\"ultimation.overview.intro\")}\n                </p>\n                <div\n                  className=\"local-scroll mb-40 mb-xs-20 wow fadeInUpShort\"\n                  data-wow-delay=\"0.4s\"\n                >\n                  <a\n                    href=\"https://ultimation.devskills.ee/auth\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"ultimation.overview.cta\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Hero Section */}\n\n        {/* Marquee Section */}\n        <div className=\"page-section overflow-hidden bg-dark-alpha-90\">\n          <MarqueeDark />\n        </div>\n        {/* End Marquee Section */}\n\n        {/* Key Benefits Section */}\n        <section className=\"page-section bg-dark-1 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"ultimation.overview.transform\")}\n                </h3>\n                <p className=\"section-descr wow fadeInUpShort\">\n                  {t(\"ultimation.overview.transform.desc\")}\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Benefit 1 */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-layers\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.benefit1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.benefit1.text\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Benefit 1 */}\n\n              {/* Benefit 2 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-settings\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.benefit2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.benefit2.text\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Benefit 2 */}\n\n              {/* Benefit 3 */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <div className=\"ultimation-icon mb-20\">\n                      <i className=\"mi-bar-chart\"></i>\n                    </div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.benefit3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.benefit3.text\")}</p>\n                  </div>\n                </div>\n              </div>\n              {/* End Benefit 3 */}\n            </div>\n          </div>\n        </section>\n        {/* End Key Benefits Section */}\n\n        {/* Modules Overview Section */}\n        <section className=\"page-section bg-dark-2 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50\">\n                <h3 className=\"section-title-small mb-20 wow fadeInUpShort\">\n                  {t(\"ultimation.overview.modules\")}\n                </h3>\n                <p className=\"section-descr wow fadeInUpShort\">\n                  {t(\"ultimation.overview.modules.desc\")}\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row multi-columns-row\">\n              {/* Module 1 - Core */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.module1.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.module1.text\")}</p>\n                  </div>\n                  <div className=\"mt-20\">\n                    <Link\n                      to=\"/products/ultimation/core\"\n                      className=\"ultimation-cta-btn\"\n                    >\n                      {t(\"ultimation.overview.learnMore\")}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n\n              {/* Module 2 - Finance */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.module2.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.module2.text\")}</p>\n                  </div>\n                  <div className=\"mt-20\">\n                    <Link\n                      to=\"/products/ultimation/finance\"\n                      className=\"ultimation-cta-btn\"\n                    >\n                      {t(\"ultimation.overview.learnMore\")}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n\n              {/* Module 3 - CRM */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.module3.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.module3.text\")}</p>\n                  </div>\n                  <div className=\"mt-20\">\n                    <Link\n                      to=\"/products/ultimation/crm\"\n                      className=\"ultimation-cta-btn\"\n                    >\n                      {t(\"ultimation.overview.learnMore\")}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n\n              {/* Module 4 - HR */}\n              <div className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\">\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.module4.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.module4.text\")}</p>\n                  </div>\n                  <div className=\"mt-20\">\n                    <Link\n                      to=\"/products/ultimation/hr\"\n                      className=\"ultimation-cta-btn\"\n                    >\n                      {t(\"ultimation.overview.learnMore\")}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n\n              {/* Module 5 - Project Management */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.1s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.module5.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.module5.text\")}</p>\n                  </div>\n                  <div className=\"mt-20\">\n                    <Link\n                      to=\"/products/ultimation/projects\"\n                      className=\"ultimation-cta-btn\"\n                    >\n                      {t(\"ultimation.overview.learnMore\")}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n\n              {/* Module 6 - Communication */}\n              <div\n                className=\"col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort\"\n                data-wow-delay=\"0.2s\"\n              >\n                <div className=\"alt-features-item text-center ultimation-feature-box\">\n                  <div>\n                    <h3 className=\"alt-features-title\">\n                      {t(\"ultimation.overview.module6.title\")}\n                    </h3>\n                  </div>\n                  <div className=\"alt-features-descr\">\n                    <p>{t(\"ultimation.overview.module6.text\")}</p>\n                  </div>\n                  <div className=\"mt-20\">\n                    <Link\n                      to=\"/products/ultimation/communication\"\n                      className=\"ultimation-cta-btn\"\n                    >\n                      {t(\"ultimation.overview.learnMore\")}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-lg-8 offset-lg-2 text-center mt-40\">\n                <Link\n                  to=\"/products/ultimation/modules\"\n                  className=\"ultimation-cta-btn btn-large\"\n                >\n                  {t(\"ultimation.overview.exploreModules\")}\n                </Link>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Modules Overview Section */}\n\n        {/* Testimonial Section */}\n        <section className=\"page-section bg-dark-2 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-12 text-center\">\n                <h3 className=\"section-title-small mb-70 mb-sm-40 wow fadeInUpShort\">\n                  {t(\"ultimation.overview.testimonials\")}\n                </h3>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"testimonial-marquee\" id=\"testimonial-marquee\">\n            <div className=\"testimonial-marquee-track\" id=\"testimonial-track\">\n              {/* Original testimonials */}\n              <div className=\"testimonial-marquee-item\">\n                <blockquote>\n                  <p>{t(\"ultimation.overview.testimonial1\")}</p>\n                  <footer>\n                    {t(\"ultimation.overview.testimonial1.author\")}\n                  </footer>\n                </blockquote>\n              </div>\n\n              <div className=\"testimonial-marquee-item\">\n                <blockquote>\n                  <p>{t(\"ultimation.overview.testimonial2\")}</p>\n                  <footer>\n                    {t(\"ultimation.overview.testimonial2.author\")}\n                  </footer>\n                </blockquote>\n              </div>\n\n              <div className=\"testimonial-marquee-item\">\n                <blockquote>\n                  <p>{t(\"ultimation.overview.testimonial3\")}</p>\n                  <footer>\n                    {t(\"ultimation.overview.testimonial3.author\")}\n                  </footer>\n                </blockquote>\n              </div>\n\n              <div className=\"testimonial-marquee-item\">\n                <blockquote>\n                  <p>{t(\"ultimation.overview.testimonial4\")}</p>\n                  <footer>\n                    {t(\"ultimation.overview.testimonial4.author\")}\n                  </footer>\n                </blockquote>\n              </div>\n\n              {/* Cloned testimonials for continuous loop */}\n              <div className=\"testimonial-clone\">\n                <div className=\"testimonial-marquee-item\">\n                  <blockquote>\n                    <p>{t(\"ultimation.overview.testimonial1\")}</p>\n                    <footer>\n                      {t(\"ultimation.overview.testimonial1.author\")}\n                    </footer>\n                  </blockquote>\n                </div>\n\n                <div className=\"testimonial-marquee-item\">\n                  <blockquote>\n                    <p>{t(\"ultimation.overview.testimonial2\")}</p>\n                    <footer>\n                      {t(\"ultimation.overview.testimonial2.author\")}\n                    </footer>\n                  </blockquote>\n                </div>\n\n                <div className=\"testimonial-marquee-item\">\n                  <blockquote>\n                    <p>{t(\"ultimation.overview.testimonial3\")}</p>\n                    <footer>\n                      {t(\"ultimation.overview.testimonial3.author\")}\n                    </footer>\n                  </blockquote>\n                </div>\n\n                <div className=\"testimonial-marquee-item\">\n                  <blockquote>\n                    <p>{t(\"ultimation.overview.testimonial4\")}</p>\n                    <footer>\n                      {t(\"ultimation.overview.testimonial4.author\")}\n                    </footer>\n                  </blockquote>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End Testimonial Section */}\n\n        {/* CTA Section */}\n        <section className=\"page-section bg-dark-alpha-90 light-content\">\n          <div className=\"container relative\">\n            <div className=\"row\">\n              <div className=\"col-md-8 offset-md-2 text-center\">\n                <h3 className=\"section-title-small mb-30 mb-sm-20 wow fadeInUpShort\">\n                  {t(\"ultimation.overview.ready\")}\n                </h3>\n                <p className=\"section-descr mb-50 mb-sm-30 wow fadeInUpShort\">\n                  {t(\"ultimation.overview.ready.desc\")}\n                </p>\n                <div className=\"local-scroll wow fadeInUpShort\">\n                  <a\n                    href=\"https://ultimation.devskills.ee/auth\"\n                    className=\"ultimation-cta-btn btn-large\"\n                  >\n                    {t(\"ultimation.overview.getStarted\")}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n        {/* End CTA Section */}\n\n        {/* Footer */}\n        <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n          <Footer />\n        </footer>\n        {/* End Footer */}\n      </div>\n    </>\n  );\n}\n"], "names": ["metadata", "BMSOverviewPage", "jsxs", "Fragment", "jsx", "MetaComponent", "Header", "menuItems", "Footer", "BMSModulePage", "moduleId", "useParams", "useEffect", "coreModuleSchema", "CoreModulePage", "t", "useTranslation", "SEO", "MarqueeDark", "accountingModuleSchema", "AccountingModulePage", "budgetModuleSchema", "BudgetModulePage", "hrModuleSchema", "HRModulePage", "recruitmentModuleSchema", "RecruitmentModulePage", "productionModuleSchema", "ProductionModulePage", "salesModuleSchema", "SalesModulePage", "qualityModuleSchema", "QualityControlModulePage", "communicationModuleSchema", "CommunicationModulePage", "companiesModuleSchema", "CompaniesModulePage", "ultimationSchema", "UltimationStudioOverviewPage", "script", "Link"], "mappings": "0PAMA,MAAMA,EAAW,CACf,MAAO,6BACP,YAAa,wDACf,EAEA,SAAwBC,GAAkB,CACxC,OAEIC,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAc,KAAML,CAAU,CAAA,EAC9BI,MAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QACC,OAAK,CAAA,GAAG,OACP,SAAAH,MAAC,WAAQ,UAAU,8CACjB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAA,EAAA,IAAC,MAAG,SAAY,cAAA,CAAA,EAElB,EACF,CACF,CAAA,QACCI,EAAO,CAAA,CAAA,CAAA,CACV,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,8GC5BMR,EAAW,CACf,MAAO,2BACP,YAAa,kCACf,EAEA,SAAwBS,GAAgB,CAChC,KAAA,CAAE,SAAAC,CAAS,EAAIC,EAAU,EAG/BC,OAAAA,EAAAA,UAAU,IAAM,CACVF,IAAa,SACf,OAAO,SAAS,KAAO,qBACzB,EACC,CAACA,CAAQ,CAAC,EAITR,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAc,KAAML,CAAU,CAAA,EAC9BI,MAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACCH,MAAA,OAAA,CAAK,GAAG,OACP,SAACA,EAAA,IAAA,UAAA,CAAQ,UAAU,8CACjB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,gBAAC,KAAG,CAAA,SAAA,CAAA,eAAaM,CAAA,EAAS,CAAA,CAE5B,CACF,CAAA,EACF,QACCF,EAAO,CAAA,CAAA,CAAA,CACV,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,8GCjCMK,EAAmB,CACvB,WAAY,qBACZ,QAAS,sBACT,KAAM,kCACN,YACE,4IACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAiB,CACjC,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,iBAAiB,CAAC,uBAC9B,YAAaA,EAAE,iBAAiB,EAChC,UAAU,yCACV,MAAM,iDACN,KAAK,UACL,OAAQF,EACR,SAAU,CACR,cACA,sBACA,oBACA,qBACA,WAAA,CACF,CACF,EACAX,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,iBAAiB,CAAA,CACtB,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,oBAAoB,CAAA,CACzB,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,iBAAiB,CAAA,CACtB,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,iBAAiB,CAAA,CAAA,CACtB,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,uBAAuB,CAAA,CAC5B,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,2BAA2B,CAAE,CAAA,CACrC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,2BAA2B,CAAE,CAAA,CACrC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,2BAA2B,CAAE,CAAA,CACrC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,2BAA2B,CAAE,CAAA,CACrC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,2BAA2B,CAAE,CAAA,CACrC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,2BAA2B,CAAE,CAAA,CACrC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,gBAAgB,EACrB,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,mBAAmB,EACxB,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,iBAAiB,CAAA,CAAA,CAExB,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCtRMW,EAAyB,CAC7B,WAAY,qBACZ,QAAS,sBACT,KAAM,yCACN,YACE,sJACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,qBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAuB,CACvC,KAAA,CAAE,EAAAL,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,uBAAuB,CAAC,uBACpC,YAAaA,EAAE,uBAAuB,EACtC,UAAU,+CACV,MAAM,uDACN,KAAK,UACL,OAAQI,EACR,SAAU,CACR,oBACA,uBACA,oBACA,wBACA,sBACA,WAAA,CACF,CACF,EACAjB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,uBAAuB,CAAA,CAC5B,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,0BAA0B,CAAA,CAC/B,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,uBAAuB,CAAA,CAC5B,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,uBAAuB,CAAA,CAAA,CAC5B,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,6BAA6B,CAAA,CAClC,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,sBAAsB,EAC3B,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,yBAAyB,EAC9B,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,uBAAuB,CAAA,CAAA,CAE9B,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCvRMa,EAAqB,CACzB,WAAY,qBACZ,QAAS,sBACT,KAAM,qCACN,YACE,0KACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,qBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAmB,CACnC,KAAA,CAAE,EAAAP,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,mBAAmB,CAAC,uBAChC,YAAaA,EAAE,mBAAmB,EAClC,UAAU,2CACV,MAAM,mDACN,KAAK,UACL,OAAQM,EACR,SAAU,CACR,gBACA,qBACA,oBACA,cACA,oBACA,WAAA,CACF,CACF,EACAnB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,mBAAmB,CAAA,CACxB,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,sBAAsB,CAAA,CAC3B,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,mBAAmB,CAAA,CACxB,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,mBAAmB,CAAA,CAAA,CACxB,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,yBAAyB,CAAA,CAC9B,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,uBAAuB,CAC5B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,6BAA6B,CAAE,CAAA,CACvC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,SAAS,CACxB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,uBAAuB,CAC5B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,6BAA6B,CAAE,CAAA,CACvC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,uBAAuB,CAC5B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,6BAA6B,CAAE,CAAA,CACvC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,uBAAuB,CAC5B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,6BAA6B,CAAE,CAAA,CACvC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,uBAAuB,CAC5B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,6BAA6B,CAAE,CAAA,CACvC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,aAAa,CAC5B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,uBAAuB,CAC5B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,6BAA6B,CAAE,CAAA,CACvC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,kBAAkB,EACvB,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,qBAAqB,EAC1B,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,mBAAmB,CAAA,CAAA,CAE1B,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCvRMe,EAAiB,CACrB,WAAY,qBACZ,QAAS,sBACT,KAAM,8CACN,YACE,oKACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,qBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAe,CAC/B,KAAA,CAAE,EAAAT,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,eAAe,CAAC,uBAC5B,YAAaA,EAAE,eAAe,EAC9B,UAAU,uCACV,MAAM,+CACN,KAAK,UACL,OAAQQ,EACR,SAAU,CACR,yBACA,oBACA,oBACA,cACA,uBACA,WAAA,CACF,CACF,EACArB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,eAAe,CAAA,CACpB,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,kBAAkB,CAAA,CACvB,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,eAAe,CAAA,CACpB,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,eAAe,CAAA,CAAA,CACpB,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,qBAAqB,CAAA,CAC1B,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,mBAAmB,CACxB,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,yBAAyB,CAAE,CAAA,CACnC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,mBAAmB,CACxB,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,yBAAyB,CAAE,CAAA,CACnC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,mBAAmB,CACxB,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,yBAAyB,CAAE,CAAA,CACnC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,mBAAmB,CACxB,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,yBAAyB,CAAE,CAAA,CACnC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,mBAAmB,CACxB,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,yBAAyB,CAAE,CAAA,CACnC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,mBAAmB,CACxB,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,yBAAyB,CAAE,CAAA,CACnC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,cAAc,EACnB,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,iBAAiB,EACtB,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,UAAU,+BACV,OAAO,SACP,IAAI,sBAEH,WAAE,eAAe,CAAA,CAAA,CAEtB,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCzRMiB,EAA0B,CAC9B,WAAY,qBACZ,QAAS,sBACT,KAAM,yCACN,YACE,sLACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAwB,CACxC,KAAA,CAAE,EAAAX,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,wBAAwB,CAAC,uBACrC,YAAaA,EAAE,wBAAwB,EACvC,UAAU,gDACV,MAAM,wDACN,KAAK,UACL,OAAQU,EACR,SAAU,CACR,qBACA,qBACA,oBACA,iBACA,kBACA,WAAA,CACF,CACF,EACAvB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,wBAAwB,CAAA,CAC7B,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,2BAA2B,CAAA,CAChC,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,wBAAwB,CAAA,CAC7B,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,wBAAwB,CAAA,CAAA,CAC7B,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,8BAA8B,CAAA,CACnC,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,4BAA4B,CACjC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAE,CAAA,CAC5C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,4BAA4B,CACjC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAE,CAAA,CAC5C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,4BAA4B,CACjC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAE,CAAA,CAC5C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,4BAA4B,CACjC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAE,CAAA,CAC5C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,4BAA4B,CACjC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAE,CAAA,CAC5C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,4BAA4B,CACjC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAE,CAAA,CAC5C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,uBAAuB,EAC5B,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,0BAA0B,EAC/B,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,wBAAwB,CAAA,CAAA,CAE/B,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCvRMmB,EAAyB,CAC7B,WAAY,qBACZ,QAAS,sBACT,KAAM,wCACN,YACE,iMACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAuB,CACvC,KAAA,CAAE,EAAAb,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,uBAAuB,CAAC,uBACpC,YAAaA,EAAE,uBAAuB,EACtC,UAAU,+CACV,MAAM,uDACN,KAAK,UACL,OAAQY,EACR,SAAU,CACR,oBACA,2BACA,oBACA,yBACA,yBACA,WAAA,CACF,CACF,EACAzB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,uBAAuB,CAAA,CAC5B,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,0BAA0B,CAAA,CAC/B,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,uBAAuB,CAAA,CAC5B,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,uBAAuB,CAAA,CAAA,CAC5B,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,6BAA6B,CAAA,CAClC,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,SAAS,CACxB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,iCAAiC,CAAE,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,sBAAsB,EAC3B,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,yBAAyB,EAC9B,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,uBAAuB,CAAA,CAAA,CAE9B,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCvRMqB,EAAoB,CACxB,WAAY,qBACZ,QAAS,sBACT,KAAM,mCACN,YACE,wLACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAkB,CAClC,KAAA,CAAE,EAAAf,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,kBAAkB,CAAC,uBAC/B,YAAaA,EAAE,kBAAkB,EACjC,UAAU,0CACV,MAAM,kDACN,KAAK,UACL,OAAQc,EACR,SAAU,CACR,eACA,qBACA,oBACA,eACA,iBACA,WAAA,CACF,CACF,EACA3B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,kBAAkB,CAAA,CACvB,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,qBAAqB,CAAA,CAC1B,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,kBAAkB,CAAA,CACvB,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,kBAAkB,CAAA,CAAA,CACvB,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,wBAAwB,CAAA,CAC7B,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,sBAAsB,CAC3B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,4BAA4B,CAAE,CAAA,CACtC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,sBAAsB,CAC3B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,4BAA4B,CAAE,CAAA,CACtC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,sBAAsB,CAC3B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,4BAA4B,CAAE,CAAA,CACtC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,sBAAsB,CAC3B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,4BAA4B,CAAE,CAAA,CACtC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,sBAAsB,CAC3B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,4BAA4B,CAAE,CAAA,CACtC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,sBAAsB,CAC3B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,4BAA4B,CAAE,CAAA,CACtC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,iBAAiB,EACtB,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,oBAAoB,EACzB,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,kBAAkB,CAAA,CAAA,CAEzB,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCvRMuB,EAAsB,CAC1B,WAAY,qBACZ,QAAS,sBACT,KAAM,6CACN,YACE,yLACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAA2B,CAC3C,KAAA,CAAE,EAAAjB,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,oBAAoB,CAAC,uBACjC,YAAaA,EAAE,oBAAoB,EACnC,UAAU,4CACV,MAAM,oDACN,KAAK,UACL,OAAQgB,EACR,SAAU,CACR,yBACA,uBACA,oBACA,qBACA,uBACA,WAAA,CACF,CACF,EACA7B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,oBAAoB,CAAA,CACzB,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,uBAAuB,CAAA,CAC5B,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,oBAAoB,CAAA,CACzB,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,oBAAoB,CAAA,CAAA,CACzB,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,0BAA0B,CAAA,CAC/B,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,wBAAwB,CAC7B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,8BAA8B,CAAE,CAAA,CACxC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,wBAAwB,CAC7B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,8BAA8B,CAAE,CAAA,CACxC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,wBAAwB,CAC7B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,8BAA8B,CAAE,CAAA,CACxC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,wBAAwB,CAC7B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,8BAA8B,CAAE,CAAA,CACxC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,SAAS,CACxB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,wBAAwB,CAC7B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,8BAA8B,CAAE,CAAA,CACxC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,aAAa,CAC5B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,wBAAwB,CAC7B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,8BAA8B,CAAE,CAAA,CACxC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,mBAAmB,EACxB,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,sBAAsB,EAC3B,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,oBAAoB,CAAA,CAAA,CAE3B,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCrRMyB,EAA4B,CAChC,WAAY,qBACZ,QAAS,sBACT,KAAM,2CACN,YACE,+LACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAA0B,CAC1C,KAAA,CAAE,EAAAnB,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,0BAA0B,CAAC,uBACvC,YAAaA,EAAE,0BAA0B,EACzC,UAAU,kDACV,MAAM,0DACN,KAAK,UACL,OAAQkB,EACR,SAAU,CACR,uBACA,oBACA,oBACA,mBACA,iBACA,gBACA,WAAA,CACF,CACF,EACA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,0BAA0B,CAAA,CAC/B,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,6BAA6B,CAAA,CAClC,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,0BAA0B,CAAA,CAC/B,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,0BAA0B,CAAA,CAAA,CAC/B,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,gCAAgC,CAAA,CACrC,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,WAAW,CAC1B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,8BAA8B,CACnC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,oCAAoC,CAAE,CAAA,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,8BAA8B,CACnC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,oCAAoC,CAAE,CAAA,CAC9C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,8BAA8B,CACnC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,oCAAoC,CAAE,CAAA,CAC9C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,8BAA8B,CACnC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,oCAAoC,CAAE,CAAA,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,8BAA8B,CACnC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,oCAAoC,CAAE,CAAA,CAC9C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,8BAA8B,CACnC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,oCAAoC,CAAE,CAAA,CAC9C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,yBAAyB,EAC9B,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,4BAA4B,EACjC,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,0BAA0B,CAAA,CAAA,CAEjC,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,8GCxRM2B,EAAwB,CAC5B,WAAY,qBACZ,QAAS,sBACT,KAAM,uCACN,YACE,iMACF,oBAAqB,sBACrB,gBAAiB,MACjB,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,KACjB,EACA,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IAAA,CAEZ,EACA,SAAU,CACR,QAAS,sBACT,KAAM,oBACN,IAAK,mCAAA,CAET,EAEA,SAAwBC,GAAsB,CACtC,KAAA,CAAE,EAAArB,CAAE,EAAIC,EAAe,EAE7B,OAEId,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAO,GAAGF,EAAE,sBAAsB,CAAC,uBACnC,YAAaA,EAAE,sBAAsB,EACrC,UAAU,8CACV,MAAM,sDACN,KAAK,UACL,OAAQoB,EACR,SAAU,CACR,mBACA,0BACA,oBACA,oBACA,yBACA,WAAA,CACF,CACF,EACAjC,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,sBAAsB,CAAA,CAC3B,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,yBAAyB,CAAA,CAC9B,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,sBAAsB,CAAA,CAC3B,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,sBAAsB,CAAA,CAAA,CAC3B,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,kDACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,8CACX,SAAAW,EAAE,4BAA4B,CAAA,CACjC,CACF,CAAA,EACF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,0BAA0B,CAC/B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,gCAAgC,CAAE,CAAA,CAC1C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,0BAA0B,CAC/B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,gCAAgC,CAAE,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,0BAA0B,CAC/B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,gCAAgC,CAAE,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EAEF,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,aAAa,CAC5B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,0BAA0B,CAC/B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,gCAAgC,CAAE,CAAA,CAC1C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,UAAU,CACzB,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,0BAA0B,CAC/B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,gCAAgC,CAAE,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,0BAA0B,CAC/B,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,gCAAgC,CAAE,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,qBAAqB,EAC1B,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,wBAAwB,EAC7B,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,OAAO,SACP,IAAI,sBACJ,UAAU,+BAET,WAAE,sBAAsB,CAAA,CAAA,CAE7B,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ,+GCxRM6B,EAAmB,CACvB,WAAY,qBACZ,QAAS,UACT,KAAM,oBACN,YACE,uGACF,MAAO,CACL,QAAS,QACT,KAAM,WACR,EACA,OAAQ,CACN,QAAS,QACT,MAAO,sBACP,cAAe,MACf,aAAc,4BAChB,EACA,SAAU,8BACZ,EAEA,SAAwBC,GAA+B,CAC/C,KAAA,CAAE,EAAAvB,CAAE,EAAIC,EAAe,EAG7BJ,OAAAA,EAAAA,UAAU,IAAM,CAER,MAAA2B,EAAS,SAAS,cAAc,QAAQ,EAC9C,OAAAA,EAAO,IAAM,4BACbA,EAAO,MAAQ,GACN,SAAA,KAAK,YAAYA,CAAM,EAGhC,QAAQ,IAAI,uBAAuB,EAG5B,IAAM,CACF,SAAA,KAAK,YAAYA,CAAM,CAClC,CACF,EAAG,EAAE,EAIDrC,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACa,EAAA,CACC,MAAM,yEACN,YAAY,iLACZ,UAAU,oDACV,MAAM,yDACN,KAAK,UACL,OAAQoB,EACR,SAAU,CACR,oBACA,6BACA,gBACA,cACA,sBACA,WAAA,CACF,CACF,EACAnC,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QAIC,UAAQ,CAAA,UAAU,uCAAuC,GAAG,OAC3D,SAACH,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,2BAA2B,CAAA,CAChC,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,8CACV,iBAAe,OAEd,WAAE,8BAA8B,CAAA,CACnC,EACAA,EAAA,IAAC,IAAA,CACC,UAAU,iDACV,iBAAe,OAEd,WAAE,2BAA2B,CAAA,CAChC,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uCACL,UAAU,+BAET,WAAE,yBAAyB,CAAA,CAAA,CAC9B,CAAA,CACF,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAACc,GAAY,CAAA,EACf,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,kDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,8CACX,SAAAW,EAAE,+BAA+B,EACpC,QACC,IAAE,CAAA,UAAU,kCACV,SAAAA,EAAE,oCAAoC,CACzC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,YAAY,CAC3B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,oCAAoC,CACzC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,mCAAmC,CAAE,CAAA,CAC7C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,oCAAoC,CACzC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,mCAAmC,CAAE,CAAA,CAC7C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAIAX,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,wBACb,eAAC,IAAE,CAAA,UAAU,eAAe,CAC9B,CAAA,QACC,KAAG,CAAA,UAAU,qBACX,SAAAW,EAAE,oCAAoC,CACzC,CAAA,CAAA,EACF,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,mCAAmC,CAAE,CAAA,CAC7C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAIC,UAAQ,CAAA,UAAU,uCACjB,SAACb,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,kDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,8CACX,SAAAW,EAAE,6BAA6B,EAClC,QACC,IAAE,CAAA,UAAU,kCACV,SAAAA,EAAE,kCAAkC,CACvC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAb,EAAAA,KAAC,MAAI,CAAA,UAAU,wBAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,qDACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CACC,eAAC,KAAG,CAAA,UAAU,qBACX,SAAEW,EAAA,mCAAmC,EACxC,CACF,CAAA,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAA,CAAE,CAC5C,CAAA,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,4BACH,UAAU,qBAET,WAAE,+BAA+B,CAAA,CAAA,CAEtC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGApC,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CACC,eAAC,KAAG,CAAA,UAAU,qBACX,SAAEW,EAAA,mCAAmC,EACxC,CACF,CAAA,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAA,CAAE,CAC5C,CAAA,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,+BACH,UAAU,qBAET,WAAE,+BAA+B,CAAA,CAAA,CAEtC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGApC,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CACC,eAAC,KAAG,CAAA,UAAU,qBACX,SAAEW,EAAA,mCAAmC,EACxC,CACF,CAAA,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAA,CAAE,CAC5C,CAAA,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,2BACH,UAAU,qBAET,WAAE,+BAA+B,CAAA,CAAA,CAEtC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,QAGC,MAAI,CAAA,UAAU,qDACb,SAACtC,EAAA,KAAA,MAAA,CAAI,UAAU,uDACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CACC,eAAC,KAAG,CAAA,UAAU,qBACX,SAAEW,EAAA,mCAAmC,EACxC,CACF,CAAA,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAA,CAAE,CAC5C,CAAA,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,0BACH,UAAU,qBAET,WAAE,+BAA+B,CAAA,CAAA,CAEtC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGApC,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CACC,eAAC,KAAG,CAAA,UAAU,qBACX,SAAEW,EAAA,mCAAmC,EACxC,CACF,CAAA,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAA,CAAE,CAC5C,CAAA,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,gCACH,UAAU,qBAET,WAAE,+BAA+B,CAAA,CAAA,CAEtC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGApC,EAAA,IAAC,MAAA,CACC,UAAU,qDACV,iBAAe,OAEf,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CACC,eAAC,KAAG,CAAA,UAAU,qBACX,SAAEW,EAAA,mCAAmC,EACxC,CACF,CAAA,EACAX,EAAAA,IAAC,OAAI,UAAU,qBACb,eAAC,IAAG,CAAA,SAAAW,EAAE,kCAAkC,CAAA,CAAE,CAC5C,CAAA,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,qCACH,UAAU,qBAET,WAAE,+BAA+B,CAAA,CAAA,CAEtC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACF,QAEC,MAAI,CAAA,UAAU,MACb,SAACpC,MAAA,MAAA,CAAI,UAAU,yCACb,SAAAA,EAAA,IAACoC,EAAA,CACC,GAAG,+BACH,UAAU,+BAET,WAAE,oCAAoC,CAAA,GAE3C,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIAtC,EAAAA,KAAC,UAAQ,CAAA,UAAU,uCACjB,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,MACb,eAAC,MAAI,CAAA,UAAU,wBACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,uDACX,SAAAW,EAAE,kCAAkC,CACvC,CAAA,CAAA,CACF,EACF,CACF,CAAA,EAEAX,EAAA,IAAC,MAAI,CAAA,UAAU,sBAAsB,GAAG,sBACtC,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BAA4B,GAAG,oBAE5C,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,2BACb,SAAAF,EAAA,KAAC,aACC,CAAA,SAAA,CAACE,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECX,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC,aACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECX,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC,aACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECX,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC,aACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAb,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,2BACb,SAAAF,EAAA,KAAC,aACC,CAAA,SAAA,CAACE,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECX,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC,aACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECX,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC,aACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECX,MAAA,MAAA,CAAI,UAAU,2BACb,gBAAC,aACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAG,SAAEW,EAAA,kCAAkC,CAAE,CAAA,EACzCX,EAAAA,IAAA,SAAA,CACE,SAAEW,EAAA,yCAAyC,CAC9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAICX,MAAA,UAAA,CAAQ,UAAU,8CACjB,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,uDACX,SAAAW,EAAE,2BAA2B,EAChC,QACC,IAAE,CAAA,UAAU,iDACV,SAAAA,EAAE,gCAAgC,EACrC,EACAX,EAAAA,IAAC,MAAI,CAAA,UAAU,iCACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAK,uCACL,UAAU,+BAET,WAAE,gCAAgC,CAAA,CAAA,CAEvC,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,QAIC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACI,IAAO,CACV,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,CAEJ"}