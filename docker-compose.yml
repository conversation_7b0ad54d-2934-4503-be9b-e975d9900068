services:
  frontend-blue:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "8082:3000"
    networks:
      - devskills-network
    restart: always
    depends_on:
      - backend

  frontend-green:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "8083:3000"
    networks:
      - devskills-network
    restart: always
    depends_on:
      - backend

  # Backend (API + Blog Management)
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: devskills-backend
    ports:
      - "4005:4004"
    env_file:
      - ./server/.env
    environment:
      - NODE_ENV=production
      - CORS_ORIGIN=https://devskills.ee
    volumes:
      # Map uploads directory for persistent file storage
      - ./uploads:/app/uploads
    restart: always
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:4004/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - devskills-network

networks:
  devskills-network:
    external: true
