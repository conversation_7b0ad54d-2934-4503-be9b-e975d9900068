// client/src/pages/products/bms/core/page.jsx

import React from "react";
import Header from "@/components/headers/Header";
import Footer from "@/components/footers/Footer";
import { menuItems } from "@/data/menu";
import MarqueeDark from "@/components/home/<USER>";
import SEO from "@/components/common/SEO";
import "@/styles/ultimation-styles.css";
import { useTranslation } from "react-i18next";

// JSON-LD structured data for the Core Module page
const coreModuleSchema = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "Ultimation Studio - Core Module",
  description:
    "The Core Module is the command center of your business empire, providing essential framework for managing your entire business ecosystem.",
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web",
  offers: {
    "@type": "Offer",
    price: "Contact for pricing",
    priceCurrency: "EUR",
  },
  publisher: {
    "@type": "Organization",
    name: "DevSkills",
    logo: {
      "@type": "ImageObject",
      url: "https://devskills.ee/logo.png",
      width: "180",
      height: "60",
    },
  },
  isPartOf: {
    "@type": "SoftwareApplication",
    name: "Ultimation Studio",
    url: "https://devskills.ee/products/bms",
  },
};

export default function CoreModulePage() {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={`${t("core.page.title")} - Ultimation Studio`}
        description={t("core.page.intro")}
        canonical="https://devskills.ee/products/bms/core"
        image="https://devskills.ee/og-images/module-core.png"
        type="product"
        schema={coreModuleSchema}
        keywords={[
          "core module",
          "business management",
          "Ultimation Studio",
          "business framework",
          "DEVSKILLS",
        ]}
      />
      <div className="appear-animate">
        {/* Navigation panel */}
        <nav className="main-nav dark transparent stick-fixed wow-menubar">
          <Header links={menuItems} />
        </nav>
        {/* End Navigation panel */}

        {/* Hero Section */}
        <section className="page-section bg-dark-1 light-content" id="home">
          <div className="container relative">
            <div className="row">
              <div className="col-lg-8 offset-lg-2 text-center">
                <h2
                  className="hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort"
                  data-wow-delay="0.1s"
                >
                  {t("core.page.title")}
                </h2>
                <h3
                  className="hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort"
                  data-wow-delay="0.2s"
                >
                  {t("core.page.subtitle")}
                </h3>
                <p
                  className="section-descr mb-50 mb-sm-30 wow fadeInUpShort"
                  data-wow-delay="0.3s"
                >
                  {t("core.page.intro")}
                </p>
                <div
                  className="local-scroll mb-40 mb-xs-20 wow fadeInUpShort"
                  data-wow-delay="0.4s"
                >
                  <a
                    href="https://comanager.ee"
                    className="ultimation-cta-btn btn-large"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {t("core.cta.button")}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End Hero Section */}

        {/* Marquee Section */}
        <div className="page-section overflow-hidden bg-dark-alpha-90">
          <MarqueeDark />
        </div>
        {/* End Marquee Section */}

        {/* Key Features Section */}
        <section className="page-section bg-dark-1 light-content">
          <div className="container relative">
            <div className="row">
              <div className="col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50">
                <h3 className="section-title-small mb-20 wow fadeInUpShort">
                  {t("core.page.description")}
                </h3>
              </div>
            </div>

            <div className="row multi-columns-row">
              {/* Feature 1 */}
              <div className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort">
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-lock"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("core.feature1.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("core.feature1.description")}</p>
                  </div>
                </div>
              </div>
              {/* End Feature 1 */}

              {/* Feature 2 */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.1s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-settings"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("core.feature2.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("core.feature2.description")}</p>
                  </div>
                </div>
              </div>
              {/* End Feature 2 */}

              {/* Feature 3 */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.2s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-link"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("core.feature3.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("core.feature3.description")}</p>
                  </div>
                </div>
              </div>
              {/* End Feature 3 */}
            </div>

            <div className="row multi-columns-row">
              {/* Feature 4 */}
              <div className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort">
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-layers"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("core.feature4.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("core.feature4.description")}</p>
                  </div>
                </div>
              </div>
              {/* End Feature 4 */}

              {/* Feature 5 */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.1s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-search"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("core.feature5.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("core.feature5.description")}</p>
                  </div>
                </div>
              </div>
              {/* End Feature 5 */}

              {/* Feature 6 */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.2s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-bar-chart"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("core.feature6.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("core.feature6.description")}</p>
                  </div>
                </div>
              </div>
              {/* End Feature 6 */}
            </div>
          </div>
        </section>
        {/* End Key Features Section */}

        {/* CTA Section */}
        <section className="page-section bg-dark-alpha-90 light-content">
          <div className="container relative">
            <div className="row">
              <div className="col-md-8 offset-md-2 text-center">
                <h3 className="section-title-small mb-30 mb-sm-20 wow fadeInUpShort">
                  {t("core.cta.title")}
                </h3>
                <p className="section-descr mb-50 mb-sm-30 wow fadeInUpShort">
                  {t("core.cta.subtitle")}
                </p>
                <div className="local-scroll wow fadeInUpShort">
                  <a
                    href="https://comanager.ee"
                    className="ultimation-cta-btn btn-large"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {t("core.cta.button")}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End CTA Section */}

        {/* Footer */}
        <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
          <Footer />
        </footer>
        {/* End Footer */}
      </div>
    </>
  );
}
