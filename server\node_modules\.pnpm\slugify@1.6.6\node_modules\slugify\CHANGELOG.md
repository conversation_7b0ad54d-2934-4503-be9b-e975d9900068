## Change Log

### v1.6.6 (2023-03-26)
- [#174](https://github.com/simov/slugify/pull/174) correctly handle empty strings in charmaps (@iliazeus)
- [#169](https://github.com/simov/slugify/pull/169) Add changelog (@simov)
- [#168](https://github.com/simov/slugify/pull/168) chore: document limitations of the `remove` option (#168) (@Trott)
- [#157](https://github.com/simov/slugify/pull/157) Run CI in Node.js 18 (@stscoundrel)
- [#151](https://github.com/simov/slugify/pull/151) Update README.md (#151) (@lorand-horvath)

### v1.6.5 (2022-01-03)
- [#146](https://github.com/simov/slugify/pull/146) chore: use GitHub Actions for testing (#146) (@Trott)

### v1.6.4 (2021-12-16)
- [#145](https://github.com/simov/slugify/pull/145) fix: consolidate replacement chars from extend() (#145) (@Trott)
- [#142](https://github.com/simov/slugify/pull/142) More Yoruba support (@9jaboy)

### v1.6.3 (2021-11-22)
- [#139](https://github.com/simov/slugify/pull/139) Added few more locales (@daniel1901)
- [#138](https://github.com/simov/slugify/pull/138) Add 'ß' (ss) to german locale (@maxkrickl)

### v1.6.2 (2021-11-07)
- [#136](https://github.com/simov/slugify/pull/136) Add Bulgarian locale. (@haltsir)

### v1.6.1 (2021-10-04)
- [#132](https://github.com/simov/slugify/pull/132) Add arabic and persian characters and numbers to charmap (@kiron)
- [#128](https://github.com/simov/slugify/pull/128) Added danish support to locales.json (@ItzLue)

### v1.6.0 (2021-07-15)
- [#125](https://github.com/simov/slugify/pull/125) Update README.md (@SuecoMarcus)
- [#123](https://github.com/simov/slugify/pull/123) feat: add "trim" option (#123) (@Trott)
- [#119](https://github.com/simov/slugify/pull/119) adding Armenian support (@ashotjanibekyan)

### v1.5.3 (2021-05-11)
- [#116](https://github.com/simov/slugify/pull/116) fix: remove replacement char from start and end in strict mode (#116) (@Trott)

### v1.5.2 (2021-05-08)
- [#115](https://github.com/simov/slugify/pull/115) fix: trim leading and trailing replacement chars (#115) (@Trott)

### v1.5.1 (2021-05-02)
- [#111](https://github.com/simov/slugify/pull/111) Two new special chars for charmap.json (@ovidasas)
- [#113](https://github.com/simov/slugify/pull/113) Added Ukrainian characters (@YegorShtonda)
- [#107](https://github.com/simov/slugify/pull/107) Indicate that ES2015 is required & use Object.assign (@realityking)

### v1.4.7 (2021-02-21)
- [#104](https://github.com/simov/slugify/pull/104) added more translations for german locale (@roydigerhund)
- [#103](https://github.com/simov/slugify/pull/103) Add a French locale to replace symbols like "&", "<", "|" by their French equivalent (#103) (@saadyousfi)

### v1.4.5 (2020-07-26)
- [#87](https://github.com/simov/slugify/pull/87) Add German locale (@schwigri)

### v1.4.2 (2020-06-23)
- [#71](https://github.com/simov/slugify/pull/71) add turkish currency character (@ugurh)
- [#79](https://github.com/simov/slugify/pull/79) Ensure duplicate replacement characters are removed in strict mode (@thompsonsj)

### v1.3.6 (2019-11-03)
- [#63](https://github.com/simov/slugify/pull/63) Fix broken link in README (@roschaefer)

### v1.3.5 (2019-09-08)
- [#47](https://github.com/simov/slugify/pull/47) Update eslint and related packages; fix new linting errors (@realityking)

### v1.3.4 (2018-12-06)
- [#45](https://github.com/simov/slugify/pull/45) Added % handling (#44) (@chucksense)
- [#44](https://github.com/simov/slugify/pull/44) Added % handling (#44) (Chuck Burt)

### v1.3.1 (2018-08-05)
- [#34](https://github.com/simov/slugify/pull/34) docs: align example code (@adrukh)

### v1.3.0 (2018-05-13)
- [#28](https://github.com/simov/slugify/pull/28) Minor typo fix (@chmac)

### v1.2.9 (2017-12-30)
- [#27](https://github.com/simov/slugify/pull/27) Support TypeScript types for `extend` properties (@rohmanhm)

### v1.2.8 (2017-12-23)
- [#25](https://github.com/simov/slugify/pull/25) fix es6 import (@sylvaindumont)

### v1.2.7 (2017-12-17)
- [#23](https://github.com/simov/slugify/pull/23) add georgian alphabet (@xxzefgh)

### v1.2.6 (2017-11-24)
- [#19](https://github.com/simov/slugify/pull/19) fix - ts typings now export slugify correctly (@neonerd)

### v1.2.3 (2017-11-13)
- [#14](https://github.com/simov/slugify/pull/14) add typescript support (@sylvaindumont)

### v1.2.2 (2017-11-09)
- [#7](https://github.com/simov/slugify/pull/7) Added support for Serbian characters (@slavkobabic)
- [#11](https://github.com/simov/slugify/pull/11) Add Russian Ruble (@toptalo)

### v1.1.0 (2017-01-08)
- [#6](https://github.com/simov/slugify/pull/6) Fix then/than homophone grammar mistake. (@PatrickNausha)

### v1.0.1 (2016-08-08)
- [#1](https://github.com/simov/slugify/pull/1) Update README.md (@mrzmyr)
