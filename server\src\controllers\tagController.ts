import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import <PERSON><PERSON> from 'joi';
import slugify from 'slugify';
import { AuthRequest } from '../middleware/auth';

const prisma = new PrismaClient();

// Validation schemas
const createTagSchema = Joi.object({
  name: Joi.string().min(1).max(50).required(),
});

const updateTagSchema = createTagSchema.keys({
  id: Joi.string().required(),
});

// @desc    Get all tags
// @route   GET /api/admin/tags
// @access  Private/Admin
export const getTags = async (req: Request, res: Response) => {
  try {
    const tags = await prisma.tag.findMany({
      include: {
        _count: {
          select: { blogPosts: true },
        },
      },
      orderBy: { name: 'asc' },
    });

    res.json({
      success: true,
      data: tags,
    });
  } catch (error) {
    console.error('Get tags error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create new tag
// @route   POST /api/admin/tags
// @access  Private/Admin
export const createTag = async (req: AuthRequest, res: Response) => {
  try {
    // Validate input
    const { error, value } = createTagSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    const { name } = value;

    // Generate slug
    const slug = slugify(name, { lower: true, strict: true });

    // Check if slug already exists
    const existingTag = await prisma.tag.findUnique({
      where: { slug },
    });

    if (existingTag) {
      return res.status(400).json({
        success: false,
        message: 'A tag with this name already exists',
      });
    }

    // Create tag
    const tag = await prisma.tag.create({
      data: {
        name,
        slug,
      },
    });

    res.status(201).json({
      success: true,
      data: tag,
    });
  } catch (error) {
    console.error('Create tag error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update tag
// @route   PUT /api/admin/tags/:id
// @access  Private/Admin
export const updateTag = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error, value } = updateTagSchema.validate({ ...req.body, id });
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    const { name } = value;

    // Check if tag exists
    const existingTag = await prisma.tag.findUnique({
      where: { id },
    });

    if (!existingTag) {
      return res.status(404).json({
        success: false,
        message: 'Tag not found',
      });
    }

    // Generate new slug if name changed
    let slug = existingTag.slug;
    if (name !== existingTag.name) {
      slug = slugify(name, { lower: true, strict: true });

      // Check if new slug already exists
      const slugExists = await prisma.tag.findUnique({
        where: { slug },
      });

      if (slugExists && slugExists.id !== id) {
        return res.status(400).json({
          success: false,
          message: 'A tag with this name already exists',
        });
      }
    }

    // Update tag
    const updatedTag = await prisma.tag.update({
      where: { id },
      data: {
        name,
        slug,
      },
    });

    res.json({
      success: true,
      data: updatedTag,
    });
  } catch (error) {
    console.error('Update tag error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete tag
// @route   DELETE /api/admin/tags/:id
// @access  Private/Admin
export const deleteTag = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Check if tag exists
    const tag = await prisma.tag.findUnique({
      where: { id },
      include: {
        _count: {
          select: { blogPosts: true },
        },
      },
    });

    if (!tag) {
      return res.status(404).json({
        success: false,
        message: 'Tag not found',
      });
    }

    // Check if tag is being used
    if (tag._count.blogPosts > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete tag. It is being used by ${tag._count.blogPosts} blog post(s).`,
      });
    }

    // Delete tag
    await prisma.tag.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: 'Tag deleted successfully',
    });
  } catch (error) {
    console.error('Delete tag error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
