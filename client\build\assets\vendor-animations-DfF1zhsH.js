var de=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function fe(g){return g&&g.__esModule&&Object.prototype.hasOwnProperty.call(g,"default")?g.default:g}var ve={exports:{}};(function(g){(function(M,m){g.exports?g.exports=m():M.Rellax=m()})(typeof window<"u"?window:de,function(){var M=function(m,w){var t=Object.create(M.prototype),b=0,x=0,S=0,k=0,l=[],E=!0,H=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||function(n){return setTimeout(n,1e3/60)},C=null,y=!1;try{var P=Object.defineProperty({},"passive",{get:function(){y=!0}});window.addEventListener("testPassive",null,P),window.removeEventListener("testPassive",null,P)}catch{}var B=window.cancelAnimationFrame||window.mozCancelAnimationFrame||clearTimeout,_=window.transformProp||function(){var n=document.createElement("div");if(n.style.transform===null){var a=["Webkit","Moz","ms"];for(var f in a)if(n.style[a[f]+"Transform"]!==void 0)return a[f]+"Transform"}return"transform"}();t.options={speed:-2,verticalSpeed:null,horizontalSpeed:null,breakpoints:[576,768,1201],center:!1,wrapper:null,relativeToWrapper:!1,round:!0,vertical:!0,horizontal:!1,verticalScrollAxis:"y",horizontalScrollAxis:"x",callback:function(){}},w&&Object.keys(w).forEach(function(n){t.options[n]=w[n]});function R(){if(t.options.breakpoints.length===3&&Array.isArray(t.options.breakpoints)){var n=!0,a=!0,f;if(t.options.breakpoints.forEach(function(h){typeof h!="number"&&(a=!1),f!==null&&h<f&&(n=!1),f=h}),n&&a)return}t.options.breakpoints=[576,768,1201],console.warn("Rellax: You must pass an array of 3 numbers in ascending order to the breakpoints option. Defaults reverted")}w&&w.breakpoints&&R(),m||(m=".rellax");var W=typeof m=="string"?document.querySelectorAll(m):[m];if(W.length>0)t.elems=W;else{console.warn("Rellax: The elements you're trying to select don't exist.");return}if(t.options.wrapper&&!t.options.wrapper.nodeType){var Y=document.querySelector(t.options.wrapper);if(Y)t.options.wrapper=Y;else{console.warn("Rellax: The wrapper you're trying to use doesn't exist.");return}}var s,i=function(n){var a=t.options.breakpoints;return n<a[0]?"xs":n>=a[0]&&n<a[1]?"sm":n>=a[1]&&n<a[2]?"md":"lg"},e=function(){for(var n=0;n<t.elems.length;n++){var a=o(t.elems[n]);l.push(a)}},r=function(){for(var n=0;n<l.length;n++)t.elems[n].style.cssText=l[n].style;l=[],x=window.innerHeight,k=window.innerWidth,s=i(k),u(),e(),N(),E&&(window.addEventListener("resize",r),E=!1,L())},o=function(n){var a=n.getAttribute("data-rellax-percentage"),f=n.getAttribute("data-rellax-speed"),h=n.getAttribute("data-rellax-xs-speed"),T=n.getAttribute("data-rellax-mobile-speed"),A=n.getAttribute("data-rellax-tablet-speed"),O=n.getAttribute("data-rellax-desktop-speed"),z=n.getAttribute("data-rellax-vertical-speed"),q=n.getAttribute("data-rellax-horizontal-speed"),F=n.getAttribute("data-rellax-vertical-scroll-axis"),v=n.getAttribute("data-rellax-horizontal-scroll-axis"),p=n.getAttribute("data-rellax-zindex")||0,X=n.getAttribute("data-rellax-min"),j=n.getAttribute("data-rellax-max"),pe=n.getAttribute("data-rellax-min-x"),he=n.getAttribute("data-rellax-max-x"),me=n.getAttribute("data-rellax-min-y"),we=n.getAttribute("data-rellax-max-y"),V,Z=!0;!h&&!T&&!A&&!O?Z=!1:V={xs:h,sm:T,md:A,lg:O};var J=t.options.wrapper?t.options.wrapper.scrollTop:window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;if(t.options.relativeToWrapper){var ye=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;J=ye-t.options.wrapper.offsetTop}var K=t.options.vertical&&(a||t.options.center)?J:0,Q=t.options.horizontal&&(a||t.options.center)?t.options.wrapper?t.options.wrapper.scrollLeft:window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft:0,$=K+n.getBoundingClientRect().top,ee=n.clientHeight||n.offsetHeight||n.scrollHeight,te=Q+n.getBoundingClientRect().left,ne=n.clientWidth||n.offsetWidth||n.scrollWidth,re=a||(K-$+x)/(ee+x),ae=a||(Q-te+k)/(ne+k);t.options.center&&(ae=.5,re=.5);var ie=Z&&V[s]!==null?Number(V[s]):f||t.options.speed,oe=z||t.options.verticalSpeed,se=q||t.options.horizontalSpeed,ge=F||t.options.verticalScrollAxis,be=v||t.options.horizontalScrollAxis,le=c(ae,re,ie,oe,se),I=n.style.cssText,D="",ue=/transform\s*:/i.exec(I);if(ue){var xe=ue.index,U=I.slice(xe),ce=U.indexOf(";");ce?D=" "+U.slice(11,ce).replace(/\s/g,""):D=" "+U.slice(11).replace(/\s/g,"")}return{baseX:le.x,baseY:le.y,top:$,left:te,height:ee,width:ne,speed:ie,verticalSpeed:oe,horizontalSpeed:se,verticalScrollAxis:ge,horizontalScrollAxis:be,style:I,transform:D,zindex:p,min:X,max:j,minX:pe,maxX:he,minY:me,maxY:we}},u=function(){var n=b,a=S;if(b=t.options.wrapper?t.options.wrapper.scrollTop:(document.documentElement||document.body.parentNode||document.body).scrollTop||window.pageYOffset,S=t.options.wrapper?t.options.wrapper.scrollLeft:(document.documentElement||document.body.parentNode||document.body).scrollLeft||window.pageXOffset,t.options.relativeToWrapper){var f=(document.documentElement||document.body.parentNode||document.body).scrollTop||window.pageYOffset;b=f-t.options.wrapper.offsetTop}return!!(n!=b&&t.options.vertical||a!=S&&t.options.horizontal)},c=function(n,a,f,h,T){var A={},O=(T||f)*(100*(1-n)),z=(h||f)*(100*(1-a));return A.x=t.options.round?Math.round(O):Math.round(O*100)/100,A.y=t.options.round?Math.round(z):Math.round(z*100)/100,A},d=function(){window.removeEventListener("resize",d),window.removeEventListener("orientationchange",d),(t.options.wrapper?t.options.wrapper:window).removeEventListener("scroll",d),(t.options.wrapper?t.options.wrapper:document).removeEventListener("touchmove",d),C=H(L)},L=function(){u()&&E===!1?(N(),C=H(L)):(C=null,window.addEventListener("resize",d),window.addEventListener("orientationchange",d),(t.options.wrapper?t.options.wrapper:window).addEventListener("scroll",d,y?{passive:!0}:!1),(t.options.wrapper?t.options.wrapper:document).addEventListener("touchmove",d,y?{passive:!0}:!1))},N=function(){for(var n,a=0;a<t.elems.length;a++){var f=l[a].verticalScrollAxis.toLowerCase(),h=l[a].horizontalScrollAxis.toLowerCase(),T=f.indexOf("x")!=-1?b:0,A=f.indexOf("y")!=-1?b:0,O=h.indexOf("x")!=-1?S:0,z=h.indexOf("y")!=-1?S:0,q=(A+z-l[a].top+x)/(l[a].height+x),F=(T+O-l[a].left+k)/(l[a].width+k);n=c(F,q,l[a].speed,l[a].verticalSpeed,l[a].horizontalSpeed);var v=n.y-l[a].baseY,p=n.x-l[a].baseX;l[a].min!==null&&(t.options.vertical&&!t.options.horizontal&&(v=v<=l[a].min?l[a].min:v),t.options.horizontal&&!t.options.vertical&&(p=p<=l[a].min?l[a].min:p)),l[a].minY!=null&&(v=v<=l[a].minY?l[a].minY:v),l[a].minX!=null&&(p=p<=l[a].minX?l[a].minX:p),l[a].max!==null&&(t.options.vertical&&!t.options.horizontal&&(v=v>=l[a].max?l[a].max:v),t.options.horizontal&&!t.options.vertical&&(p=p>=l[a].max?l[a].max:p)),l[a].maxY!=null&&(v=v>=l[a].maxY?l[a].maxY:v),l[a].maxX!=null&&(p=p>=l[a].maxX?l[a].maxX:p);var X=l[a].zindex,j="translate3d("+(t.options.horizontal?p:"0")+"px,"+(t.options.vertical?v:"0")+"px,"+X+"px) "+l[a].transform;t.elems[a].style[_]=j}t.options.callback(n)};return t.destroy=function(){for(var n=0;n<t.elems.length;n++)t.elems[n].style.cssText=l[n].style;E||(window.removeEventListener("resize",r),E=!0),B(C),C=null},r(),t.refresh=r,t};return M})})(ve);var Se=ve.exports;const Ae=fe(Se);var G={exports:{}};(function(g,M){(function(m,w){w(g,M)})(de,function(m,w){Object.defineProperty(w,"__esModule",{value:!0});var t,b;function x(s,i){if(!(s instanceof i))throw new TypeError("Cannot call a class as a function")}var S=function(){function s(i,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(i,o.key,o)}}return function(i,e,r){return e&&s(i.prototype,e),r&&s(i,r),i}}();function k(s,i){return i.indexOf(s)>=0}function l(s,i){for(var e in i)if(s[e]==null){var r=i[e];s[e]=r}return s}function E(s){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(s)}function H(s){var i=arguments.length<=1||arguments[1]===void 0?!1:arguments[1],e=arguments.length<=2||arguments[2]===void 0?!1:arguments[2],r=arguments.length<=3||arguments[3]===void 0?null:arguments[3],o=void 0;return document.createEvent!=null?(o=document.createEvent("CustomEvent"),o.initCustomEvent(s,i,e,r)):document.createEventObject!=null?(o=document.createEventObject(),o.eventType=s):o.eventName=s,o}function C(s,i){s.dispatchEvent!=null?s.dispatchEvent(i):i in(s!=null)?s[i]():"on"+i in(s!=null)&&s["on"+i]()}function y(s,i,e){s.addEventListener!=null?s.addEventListener(i,e,!1):s.attachEvent!=null?s.attachEvent("on"+i,e):s[i]=e}function P(s,i,e){s.removeEventListener!=null?s.removeEventListener(i,e,!1):s.detachEvent!=null?s.detachEvent("on"+i,e):delete s[i]}function B(){return"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight}var _=window.WeakMap||window.MozWeakMap||function(){function s(){x(this,s),this.keys=[],this.values=[]}return S(s,[{key:"get",value:function(e){for(var r=0;r<this.keys.length;r++){var o=this.keys[r];if(o===e)return this.values[r]}}},{key:"set",value:function(e,r){for(var o=0;o<this.keys.length;o++){var u=this.keys[o];if(u===e)return this.values[o]=r,this}return this.keys.push(e),this.values.push(r),this}}]),s}(),R=window.MutationObserver||window.WebkitMutationObserver||window.MozMutationObserver||(b=t=function(){function s(){x(this,s),typeof console<"u"&&console!==null&&(console.warn("MutationObserver is not supported by your browser."),console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content."))}return S(s,[{key:"observe",value:function(){}}]),s}(),t.notSupported=!0,b),W=window.getComputedStyle||function(i){var e=/(\-([a-z]){1})/g;return{getPropertyValue:function(o){o==="float"&&(o="styleFloat"),e.test(o)&&o.replace(e,function(c,d){return d.toUpperCase()});var u=i.currentStyle;return(u!=null?u[o]:void 0)||null}}},Y=function(){function s(){var i=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];x(this,s),this.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null},this.animate=function(){return"requestAnimationFrame"in window?function(r){return window.requestAnimationFrame(r)}:function(r){return r()}}(),this.vendors=["moz","webkit"],this.start=this.start.bind(this),this.resetAnimation=this.resetAnimation.bind(this),this.scrollHandler=this.scrollHandler.bind(this),this.scrollCallback=this.scrollCallback.bind(this),this.scrolled=!0,this.config=l(i,this.defaults),i.scrollContainer!=null&&(this.config.scrollContainer=document.querySelector(i.scrollContainer)),this.animationNameCache=new _,this.wowEvent=H(this.config.boxClass)}return S(s,[{key:"init",value:function(){this.element=window.document.documentElement,k(document.readyState,["interactive","complete"])?this.start():y(document,"DOMContentLoaded",this.start),this.finished=[]}},{key:"start",value:function(){var e=this;if(this.stopped=!1,this.boxes=[].slice.call(this.element.querySelectorAll("."+this.config.boxClass)),this.all=this.boxes.slice(0),this.boxes.length)if(this.disabled())this.resetStyle();else for(var r=0;r<this.boxes.length;r++){var o=this.boxes[r];this.applyStyle(o,!0)}if(this.disabled()||(y(this.config.scrollContainer||window,"scroll",this.scrollHandler),y(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live){var u=new R(function(c){for(var d=0;d<c.length;d++)for(var L=c[d],N=0;N<L.addedNodes.length;N++){var n=L.addedNodes[N];e.doSync(n)}});u.observe(document.body,{childList:!0,subtree:!0})}}},{key:"stop",value:function(){this.stopped=!0,P(this.config.scrollContainer||window,"scroll",this.scrollHandler),P(window,"resize",this.scrollHandler),this.interval!=null&&clearInterval(this.interval)}},{key:"sync",value:function(){R.notSupported&&this.doSync(this.element)}},{key:"doSync",value:function(e){if((typeof e>"u"||e===null)&&(e=this.element),e.nodeType===1){e=e.parentNode||e;for(var r=e.querySelectorAll("."+this.config.boxClass),o=0;o<r.length;o++){var u=r[o];k(u,this.all)||(this.boxes.push(u),this.all.push(u),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(u,!0),this.scrolled=!0)}}}},{key:"show",value:function(e){return this.applyStyle(e),e.className=e.className+" "+this.config.animateClass,this.config.callback!=null&&this.config.callback(e),C(e,this.wowEvent),y(e,"animationend",this.resetAnimation),y(e,"oanimationend",this.resetAnimation),y(e,"webkitAnimationEnd",this.resetAnimation),y(e,"MSAnimationEnd",this.resetAnimation),e}},{key:"applyStyle",value:function(e,r){var o=this,u=e.getAttribute("data-wow-duration"),c=e.getAttribute("data-wow-delay"),d=e.getAttribute("data-wow-iteration");return this.animate(function(){return o.customStyle(e,r,u,c,d)})}},{key:"resetStyle",value:function(){for(var e=0;e<this.boxes.length;e++){var r=this.boxes[e];r.style.visibility="visible"}}},{key:"resetAnimation",value:function(e){if(e.type.toLowerCase().indexOf("animationend")>=0){var r=e.target||e.srcElement;r.className=r.className.replace(this.config.animateClass,"").trim()}}},{key:"customStyle",value:function(e,r,o,u,c){return r&&this.cacheAnimationName(e),e.style.visibility=r?"hidden":"visible",o&&this.vendorSet(e.style,{animationDuration:o}),u&&this.vendorSet(e.style,{animationDelay:u}),c&&this.vendorSet(e.style,{animationIterationCount:c}),this.vendorSet(e.style,{animationName:r?"none":this.cachedAnimationName(e)}),e}},{key:"vendorSet",value:function(e,r){for(var o in r)if(r.hasOwnProperty(o)){var u=r[o];e[""+o]=u;for(var c=0;c<this.vendors.length;c++){var d=this.vendors[c];e[""+d+o.charAt(0).toUpperCase()+o.substr(1)]=u}}}},{key:"vendorCSS",value:function(e,r){for(var o=W(e),u=o.getPropertyCSSValue(r),c=0;c<this.vendors.length;c++){var d=this.vendors[c];u=u||o.getPropertyCSSValue("-"+d+"-"+r)}return u}},{key:"animationName",value:function(e){var r=void 0;try{r=this.vendorCSS(e,"animation-name").cssText}catch{r=W(e).getPropertyValue("animation-name")}return r==="none"?"":r}},{key:"cacheAnimationName",value:function(e){return this.animationNameCache.set(e,this.animationName(e))}},{key:"cachedAnimationName",value:function(e){return this.animationNameCache.get(e)}},{key:"scrollHandler",value:function(){this.scrolled=!0}},{key:"scrollCallback",value:function(){if(this.scrolled){this.scrolled=!1;for(var e=[],r=0;r<this.boxes.length;r++){var o=this.boxes[r];if(o){if(this.isVisible(o)){this.show(o);continue}e.push(o)}}this.boxes=e,!this.boxes.length&&!this.config.live&&this.stop()}}},{key:"offsetTop",value:function(e){for(;e.offsetTop===void 0;)e=e.parentNode;for(var r=e.offsetTop;e.offsetParent;)e=e.offsetParent,r+=e.offsetTop;return r}},{key:"isVisible",value:function(e){var r=e.getAttribute("data-wow-offset")||this.config.offset,o=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset,u=o+Math.min(this.element.clientHeight,B())-r,c=this.offsetTop(e),d=c+e.clientHeight;return c<=u&&d>=o}},{key:"disabled",value:function(){return!this.config.mobile&&E(navigator.userAgent)}}]),s}();w.default=Y,m.exports=w.default})})(G,G.exports);var ke=G.exports;const Ee=fe(ke);export{Ae as R,Ee as W,de as c,fe as g};
//# sourceMappingURL=vendor-animations-DfF1zhsH.js.map
