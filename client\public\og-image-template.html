<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OG Image Template</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        width: 1200px;
        height: 630px;
        display: flex;
        flex-direction: column;
        font-family: "Inter", sans-serif;
        background: linear-gradient(135deg, #06b6d4 0%, #22d3ee 100%);
        color: white;
        overflow: hidden;
      }

      .container {
        display: flex;
        flex-direction: column;
        padding: 60px;
        height: 100%;
        box-sizing: border-box;
      }

      .header {
        display: flex;
        align-items: center;
        margin-bottom: 40px;
      }

      .logo {
        width: 80px;
        height: 80px;
        margin-right: 20px;
      }

      .company-name {
        font-size: 32px;
        font-weight: 700;
      }

      .title {
        font-size: 72px;
        font-weight: 800;
        line-height: 1.2;
        margin-bottom: 30px;
        flex-grow: 1;
      }

      .description {
        font-size: 32px;
        opacity: 0.9;
        max-width: 800px;
        line-height: 1.4;
      }

      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
      }

      .url {
        font-size: 24px;
        opacity: 0.8;
      }

      .decoration {
        position: absolute;
        bottom: -100px;
        right: -100px;
        width: 400px;
        height: 400px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 0;
      }

      .decoration-2 {
        position: absolute;
        top: -50px;
        left: -50px;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 0;
      }

      .content {
        position: relative;
        z-index: 1;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
      }
    </style>
  </head>
  <body>
    <div class="decoration"></div>
    <div class="decoration-2"></div>

    <div class="container">
      <div class="header">
        <svg
          class="logo"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.36 6.64a9 9 0 1 1-12.73 0"
            stroke="white"
            stroke-width="3"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></path>
          <line
            x1="12"
            y1="2"
            x2="12"
            y2="12"
            stroke="white"
            stroke-width="3"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></line>
        </svg>
        <div class="company-name">DevSkills.ee</div>
      </div>

      <div class="content">
        <h1 class="title">Business Management System</h1>
        <p class="description">
          Streamline your operations and boost productivity with our
          comprehensive BMS solution.
        </p>
      </div>

      <div class="footer">
        <div class="url">devskills.ee</div>
      </div>
    </div>
  </body>
</html>
