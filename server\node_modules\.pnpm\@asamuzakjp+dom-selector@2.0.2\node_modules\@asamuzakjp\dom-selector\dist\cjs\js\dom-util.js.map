{"version": 3, "sources": ["../../../src/js/dom-util.js"], "sourcesContent": ["/**\n * dom-util.js\n */\n\n/* import */\nimport bidiFactory from 'bidi-js';\n\n/* constants */\nimport {\n  DOCUMENT_FRAGMENT_NODE, DOCUMENT_NODE, DOCUMENT_POSITION_CONTAINS,\n  DOCUMENT_POSITION_CONTAINED_BY, DOCUMENT_POSITION_PRECEDING, ELEMENT_NODE,\n  REG_SHADOW_MODE, SYNTAX_ERR, TEXT_NODE\n} from './constant.js';\n\n/**\n * is in shadow tree\n * @param {object} node - node\n * @returns {boolean} - result;\n */\nexport const isInShadowTree = (node = {}) => {\n  let bool;\n  if (node.nodeType === ELEMENT_NODE ||\n      node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n    let refNode = node;\n    while (refNode) {\n      const { host, mode, nodeType, parentNode } = refNode;\n      if (host && mode && nodeType === DOCUMENT_FRAGMENT_NODE &&\n          REG_SHADOW_MODE.test(mode)) {\n        bool = true;\n        break;\n      }\n      refNode = parentNode;\n    }\n  }\n  return !!bool;\n};\n\n/**\n * get slotted text content\n * @param {object} node - Element node\n * @returns {?string} - text content\n */\nexport const getSlottedTextContent = (node = {}) => {\n  let res;\n  if (node.localName === 'slot' && isInShadowTree(node)) {\n    const nodes = node.assignedNodes();\n    if (nodes.length) {\n      for (const item of nodes) {\n        res = item.textContent.trim();\n        if (res) {\n          break;\n        }\n      }\n    } else {\n      res = node.textContent.trim();\n    }\n  }\n  return res ?? null;\n};\n\n/**\n * get directionality of node\n * @see https://html.spec.whatwg.org/multipage/dom.html#the-dir-attribute\n * @param {object} node - Element node\n * @returns {?string} - 'ltr' / 'rtl'\n */\nexport const getDirectionality = (node = {}) => {\n  let res;\n  if (node.nodeType === ELEMENT_NODE) {\n    const { dir: nodeDir, localName, parentNode } = node;\n    const { getEmbeddingLevels } = bidiFactory();\n    const regDir = /^(?:ltr|rtl)$/;\n    if (regDir.test(nodeDir)) {\n      res = nodeDir;\n    } else if (nodeDir === 'auto') {\n      let text;\n      switch (localName) {\n        case 'input': {\n          if (!node.type || /^(?:(?:butto|hidde)n|(?:emai|te|ur)l|(?:rese|submi|tex)t|password|search)$/.test(node.type)) {\n            text = node.value;\n          }\n          break;\n        }\n        case 'slot': {\n          text = getSlottedTextContent(node);\n          break;\n        }\n        case 'textarea': {\n          text = node.value;\n          break;\n        }\n        default: {\n          const items = [].slice.call(node.childNodes);\n          for (const item of items) {\n            const {\n              dir: itemDir, localName: itemLocalName, nodeType: itemNodeType,\n              textContent: itemTextContent\n            } = item;\n            if (itemNodeType === TEXT_NODE) {\n              text = itemTextContent.trim();\n            } else if (itemNodeType === ELEMENT_NODE) {\n              if (!/^(?:bdi|s(?:cript|tyle)|textarea)$/.test(itemLocalName) &&\n                  (!itemDir || !regDir.test(itemDir))) {\n                if (itemLocalName === 'slot') {\n                  text = getSlottedTextContent(item);\n                } else {\n                  text = itemTextContent.trim();\n                }\n              }\n            }\n            if (text) {\n              break;\n            }\n          }\n        }\n      }\n      if (text) {\n        const { paragraphs: [{ level }] } = getEmbeddingLevels(text);\n        if (level % 2 === 1) {\n          res = 'rtl';\n        } else {\n          res = 'ltr';\n        }\n      }\n      if (!res) {\n        if (parentNode) {\n          const { nodeType: parentNodeType } = parentNode;\n          if (parentNodeType === ELEMENT_NODE) {\n            res = getDirectionality(parentNode);\n          } else if (parentNodeType === DOCUMENT_NODE ||\n                     parentNodeType === DOCUMENT_FRAGMENT_NODE) {\n            res = 'ltr';\n          }\n        } else {\n          res = 'ltr';\n        }\n      }\n    } else if (localName === 'bdi') {\n      const text = node.textContent.trim();\n      if (text) {\n        const { paragraphs: [{ level }] } = getEmbeddingLevels(text);\n        if (level % 2 === 1) {\n          res = 'rtl';\n        } else {\n          res = 'ltr';\n        }\n      }\n      if (!(res || parentNode)) {\n        res = 'ltr';\n      }\n    } else if (localName === 'input' && node.type === 'tel') {\n      res = 'ltr';\n    } else if (parentNode) {\n      if (localName === 'slot') {\n        const text = getSlottedTextContent(node);\n        if (text) {\n          const { paragraphs: [{ level }] } = getEmbeddingLevels(text);\n          if (level % 2 === 1) {\n            res = 'rtl';\n          } else {\n            res = 'ltr';\n          }\n        }\n      }\n      if (!res) {\n        const { nodeType: parentNodeType } = parentNode;\n        if (parentNodeType === ELEMENT_NODE) {\n          res = getDirectionality(parentNode);\n        } else if (parentNodeType === DOCUMENT_NODE ||\n                   parentNodeType === DOCUMENT_FRAGMENT_NODE) {\n          res = 'ltr';\n        }\n      }\n    } else {\n      res = 'ltr';\n    }\n  }\n  return res ?? null;\n};\n\n/**\n * is content editable\n * NOTE: not implemented in jsdom https://github.com/jsdom/jsdom/issues/1670\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isContentEditable = (node = {}) => {\n  let res;\n  if (node.nodeType === ELEMENT_NODE) {\n    if (typeof node.isContentEditable === 'boolean') {\n      res = node.isContentEditable;\n    } else if (node.ownerDocument.designMode === 'on') {\n      res = true;\n    } else if (node.hasAttribute('contenteditable')) {\n      const attr = node.getAttribute('contenteditable');\n      if (attr === '' || /^(?:plaintext-only|true)$/.test(attr)) {\n        res = true;\n      } else if (attr === 'inherit') {\n        let parent = node.parentNode;\n        while (parent) {\n          if (isContentEditable(parent)) {\n            res = true;\n            break;\n          }\n          parent = parent.parentNode;\n        }\n      }\n    }\n  }\n  return !!res;\n};\n\n/**\n * is namespace declared\n * @param {string} ns - namespace\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isNamespaceDeclared = (ns = '', node = {}) => {\n  let res;\n  if (ns && typeof ns === 'string' && node.nodeType === ELEMENT_NODE) {\n    const attr = `xmlns:${ns}`;\n    const root = node.ownerDocument.documentElement;\n    let parent = node;\n    while (parent) {\n      if (typeof parent.hasAttribute === 'function' &&\n          parent.hasAttribute(attr)) {\n        res = true;\n        break;\n      } else if (parent === root) {\n        break;\n      }\n      parent = parent.parentNode;\n    }\n  }\n  return !!res;\n};\n\n/**\n * is inclusive - nodeA and nodeB are in inclusive relation\n * @param {object} nodeA - Element node\n * @param {object} nodeB - Element node\n * @returns {boolean} - result\n */\nexport const isInclusive = (nodeA = {}, nodeB = {}) => {\n  let res;\n  if (nodeA.nodeType === ELEMENT_NODE && nodeB.nodeType === ELEMENT_NODE) {\n    const posBit = nodeB.compareDocumentPosition(nodeA);\n    res = posBit & DOCUMENT_POSITION_CONTAINS ||\n          posBit & DOCUMENT_POSITION_CONTAINED_BY;\n  }\n  return !!res;\n};\n\n/**\n * is preceding - nodeA precedes and/or contains nodeB\n * @param {object} nodeA - Element node\n * @param {object} nodeB - Element node\n * @returns {boolean} - result\n */\nexport const isPreceding = (nodeA = {}, nodeB = {}) => {\n  let res;\n  if (nodeA.nodeType === ELEMENT_NODE && nodeB.nodeType === ELEMENT_NODE) {\n    const posBit = nodeB.compareDocumentPosition(nodeA);\n    res = posBit & DOCUMENT_POSITION_PRECEDING ||\n          posBit & DOCUMENT_POSITION_CONTAINS;\n  }\n  return !!res;\n};\n\n/**\n * selector to node properties - e.g. ns|E -> { prefix: ns, tagName: E }\n * @param {string} selector - type selector\n * @param {object} [node] - Element node\n * @returns {object} - node properties\n */\nexport const selectorToNodeProps = (selector, node) => {\n  let prefix;\n  let tagName;\n  if (selector && typeof selector === 'string') {\n    if (selector.indexOf('|') > -1) {\n      [prefix, tagName] = selector.split('|');\n    } else {\n      prefix = '*';\n      tagName = selector;\n    }\n  } else {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  return {\n    prefix,\n    tagName\n  };\n};\n"], "mappings": "6iBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,uBAAAE,EAAA,0BAAAC,EAAA,sBAAAC,EAAA,mBAAAC,EAAA,gBAAAC,EAAA,wBAAAC,EAAA,gBAAAC,EAAA,wBAAAC,IAAA,eAAAC,EAAAV,GAKA,IAAAW,EAAwB,wBAGxBC,EAIO,yBAOA,MAAMP,EAAiB,CAACQ,EAAO,CAAC,IAAM,CAC3C,IAAIC,EACJ,GAAID,EAAK,WAAa,gBAClBA,EAAK,WAAa,yBAAwB,CAC5C,IAAIE,EAAUF,EACd,KAAOE,GAAS,CACd,KAAM,CAAE,KAAAC,EAAM,KAAAC,EAAM,SAAAC,EAAU,WAAAC,CAAW,EAAIJ,EAC7C,GAAIC,GAAQC,GAAQC,IAAa,0BAC7B,kBAAgB,KAAKD,CAAI,EAAG,CAC9BH,EAAO,GACP,KACF,CACAC,EAAUI,CACZ,CACF,CACA,MAAO,CAAC,CAACL,CACX,EAOaX,EAAwB,CAACU,EAAO,CAAC,IAAM,CAClD,IAAIO,EACJ,GAAIP,EAAK,YAAc,QAAUR,EAAeQ,CAAI,EAAG,CACrD,MAAMQ,EAAQR,EAAK,cAAc,EACjC,GAAIQ,EAAM,QACR,UAAWC,KAAQD,EAEjB,GADAD,EAAME,EAAK,YAAY,KAAK,EACxBF,EACF,WAIJA,EAAMP,EAAK,YAAY,KAAK,CAEhC,CACA,OAAOO,GAAO,IAChB,EAQalB,EAAoB,CAACW,EAAO,CAAC,IAAM,CAC9C,IAAIO,EACJ,GAAIP,EAAK,WAAa,eAAc,CAClC,KAAM,CAAE,IAAKU,EAAS,UAAAC,EAAW,WAAAL,CAAW,EAAIN,EAC1C,CAAE,mBAAAY,CAAmB,KAAI,EAAAC,SAAY,EACrCC,EAAS,gBACf,GAAIA,EAAO,KAAKJ,CAAO,EACrBH,EAAMG,UACGA,IAAY,OAAQ,CAC7B,IAAIK,EACJ,OAAQJ,EAAW,CACjB,IAAK,QAAS,EACR,CAACX,EAAK,MAAQ,6EAA6E,KAAKA,EAAK,IAAI,KAC3Ge,EAAOf,EAAK,OAEd,KACF,CACA,IAAK,OAAQ,CACXe,EAAOzB,EAAsBU,CAAI,EACjC,KACF,CACA,IAAK,WAAY,CACfe,EAAOf,EAAK,MACZ,KACF,CACA,QAAS,CACP,MAAMgB,EAAQ,CAAC,EAAE,MAAM,KAAKhB,EAAK,UAAU,EAC3C,UAAWS,KAAQO,EAAO,CACxB,KAAM,CACJ,IAAKC,EAAS,UAAWC,EAAe,SAAUC,EAClD,YAAaC,CACf,EAAIX,EAaJ,GAZIU,IAAiB,YACnBJ,EAAOK,EAAgB,KAAK,EACnBD,IAAiB,gBACtB,CAAC,qCAAqC,KAAKD,CAAa,IACvD,CAACD,GAAW,CAACH,EAAO,KAAKG,CAAO,KAC/BC,IAAkB,OACpBH,EAAOzB,EAAsBmB,CAAI,EAEjCM,EAAOK,EAAgB,KAAK,GAI9BL,EACF,KAEJ,CACF,CACF,CACA,GAAIA,EAAM,CACR,KAAM,CAAE,WAAY,CAAC,CAAE,MAAAM,CAAM,CAAC,CAAE,EAAIT,EAAmBG,CAAI,EACvDM,EAAQ,IAAM,EAChBd,EAAM,MAENA,EAAM,KAEV,CACA,GAAI,CAACA,EACH,GAAID,EAAY,CACd,KAAM,CAAE,SAAUgB,CAAe,EAAIhB,EACjCgB,IAAmB,eACrBf,EAAMlB,EAAkBiB,CAAU,GACzBgB,IAAmB,iBACnBA,IAAmB,4BAC5Bf,EAAM,MAEV,MACEA,EAAM,KAGZ,SAAWI,IAAc,MAAO,CAC9B,MAAMI,EAAOf,EAAK,YAAY,KAAK,EACnC,GAAIe,EAAM,CACR,KAAM,CAAE,WAAY,CAAC,CAAE,MAAAM,CAAM,CAAC,CAAE,EAAIT,EAAmBG,CAAI,EACvDM,EAAQ,IAAM,EAChBd,EAAM,MAENA,EAAM,KAEV,CACMA,GAAOD,IACXC,EAAM,MAEV,SAAWI,IAAc,SAAWX,EAAK,OAAS,MAChDO,EAAM,cACGD,EAAY,CACrB,GAAIK,IAAc,OAAQ,CACxB,MAAMI,EAAOzB,EAAsBU,CAAI,EACvC,GAAIe,EAAM,CACR,KAAM,CAAE,WAAY,CAAC,CAAE,MAAAM,CAAM,CAAC,CAAE,EAAIT,EAAmBG,CAAI,EACvDM,EAAQ,IAAM,EAChBd,EAAM,MAENA,EAAM,KAEV,CACF,CACA,GAAI,CAACA,EAAK,CACR,KAAM,CAAE,SAAUe,CAAe,EAAIhB,EACjCgB,IAAmB,eACrBf,EAAMlB,EAAkBiB,CAAU,GACzBgB,IAAmB,iBACnBA,IAAmB,4BAC5Bf,EAAM,MAEV,CACF,MACEA,EAAM,KAEV,CACA,OAAOA,GAAO,IAChB,EAQahB,EAAoB,CAACS,EAAO,CAAC,IAAM,CAC9C,IAAIO,EACJ,GAAIP,EAAK,WAAa,gBACpB,GAAI,OAAOA,EAAK,mBAAsB,UACpCO,EAAMP,EAAK,0BACFA,EAAK,cAAc,aAAe,KAC3CO,EAAM,WACGP,EAAK,aAAa,iBAAiB,EAAG,CAC/C,MAAMuB,EAAOvB,EAAK,aAAa,iBAAiB,EAChD,GAAIuB,IAAS,IAAM,4BAA4B,KAAKA,CAAI,EACtDhB,EAAM,WACGgB,IAAS,UAAW,CAC7B,IAAIC,EAASxB,EAAK,WAClB,KAAOwB,GAAQ,CACb,GAAIjC,EAAkBiC,CAAM,EAAG,CAC7BjB,EAAM,GACN,KACF,CACAiB,EAASA,EAAO,UAClB,CACF,CACF,EAEF,MAAO,CAAC,CAACjB,CACX,EAQab,EAAsB,CAAC+B,EAAK,GAAIzB,EAAO,CAAC,IAAM,CACzD,IAAIO,EACJ,GAAIkB,GAAM,OAAOA,GAAO,UAAYzB,EAAK,WAAa,eAAc,CAClE,MAAMuB,EAAO,SAASE,CAAE,GAClBC,EAAO1B,EAAK,cAAc,gBAChC,IAAIwB,EAASxB,EACb,KAAOwB,GAAQ,CACb,GAAI,OAAOA,EAAO,cAAiB,YAC/BA,EAAO,aAAaD,CAAI,EAAG,CAC7BhB,EAAM,GACN,KACF,SAAWiB,IAAWE,EACpB,MAEFF,EAASA,EAAO,UAClB,CACF,CACA,MAAO,CAAC,CAACjB,CACX,EAQad,EAAc,CAACkC,EAAQ,CAAC,EAAGC,EAAQ,CAAC,IAAM,CACrD,IAAIrB,EACJ,GAAIoB,EAAM,WAAa,gBAAgBC,EAAM,WAAa,eAAc,CACtE,MAAMC,EAASD,EAAM,wBAAwBD,CAAK,EAClDpB,EAAMsB,EAAS,8BACTA,EAAS,gCACjB,CACA,MAAO,CAAC,CAACtB,CACX,EAQaZ,EAAc,CAACgC,EAAQ,CAAC,EAAGC,EAAQ,CAAC,IAAM,CACrD,IAAIrB,EACJ,GAAIoB,EAAM,WAAa,gBAAgBC,EAAM,WAAa,eAAc,CACtE,MAAMC,EAASD,EAAM,wBAAwBD,CAAK,EAClDpB,EAAMsB,EAAS,+BACTA,EAAS,4BACjB,CACA,MAAO,CAAC,CAACtB,CACX,EAQaX,EAAsB,CAACkC,EAAU9B,IAAS,CACrD,IAAI+B,EACAC,EACJ,GAAIF,GAAY,OAAOA,GAAa,SAC9BA,EAAS,QAAQ,GAAG,EAAI,GAC1B,CAACC,EAAQC,CAAO,EAAIF,EAAS,MAAM,GAAG,GAEtCC,EAAS,IACTC,EAAUF,OAGZ,OAAM,IAAI,aAAa,oBAAoBA,CAAQ,GAAI,YAAU,EAEnE,MAAO,CACL,OAAAC,EACA,QAAAC,CACF,CACF", "names": ["dom_util_exports", "__export", "getDirectionality", "getSlottedTextContent", "isContentEditable", "isInShadowTree", "isInclusive", "isNamespaceDeclared", "isPreceding", "selectorToNodeProps", "__toCommonJS", "import_bidi_js", "import_constant", "node", "bool", "refNode", "host", "mode", "nodeType", "parentNode", "res", "nodes", "item", "nodeDir", "localName", "getEmbeddingLevels", "bidiFactory", "regDir", "text", "items", "itemDir", "itemLocalName", "itemNodeType", "itemTextContent", "level", "parentNodeType", "attr", "parent", "ns", "root", "nodeA", "nodeB", "posBit", "selector", "prefix", "tagName"]}