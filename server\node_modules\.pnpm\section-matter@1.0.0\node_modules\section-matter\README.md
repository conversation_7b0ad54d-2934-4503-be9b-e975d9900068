# section-matter [![NPM version](https://img.shields.io/npm/v/section-matter.svg?style=flat)](https://www.npmjs.com/package/section-matter) [![NPM monthly downloads](https://img.shields.io/npm/dm/section-matter.svg?style=flat)](https://npmjs.org/package/section-matter) [![NPM total downloads](https://img.shields.io/npm/dt/section-matter.svg?style=flat)](https://npmjs.org/package/section-matter) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/section-matter.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/section-matter) 

> Like front-matter, but supports multiple sections in a document.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install
Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save section-matter
```

## Usage

**Params**

* `input` **{String|Buffer|Object}**: If input is an object, it's `content` property must be a string or buffer.    
* **{Object}**: options    
* `returns` **{Object}**: Returns an object with a `content` string and an array of `sections` objects.  

**Example**

```js
var sections = require('{%= name %}');
var result = sections(input, options);
// { content: 'Content before sections', sections: [] }
```

See available [options](#options).

## Example

_With the exception of front-matter, **which must be the very first thing in the string**, the opening delimiter of all other sections must be followed by a string to be used as the `key` for the section._

Given the following string:

```
Content before the sections.

---

More content.

---one
title: One
---

This is the first section.
```

The following code:

```js
console.log(sections(input));
```

Results in:

```js
{ 
  content: 'Content before the sections.\n\n---\n\nMore content.\n',
  sections: [
    { 
      key: 'one',
      data: 'title: One',
      content: '\nThis is the first section.' 
    } 
  ] 
}
```

## Options

### options.section_parse

**Type**: `function`

**Default**: `undefined`

Function to be called on each section after it's parsed from the string.

**Example**

Given the following string (`foo.md`):

```
This is content before the sections.

---one
title: First section
---

This is section one.

---two
title: Second section
---

This is section two.
```

Using the following custom `section_parse` function:

```js
var fs = require('fs');
var path = require('path');
var yaml = require('js-yaml');
var sections = require('section-matter');

var str = fs.readFileSync('foo.md');
var options = {
  section_parse: function(section) {
    console.log(section)
    section.key = 'section-' + section.key;
    section.data = yaml.safeLoad(section.data);
  }
};

var result = sections(str, options);
console.log(result);
```

Results in:

```js
{
  content: 'This is content before the sections.\n',
  sections: [
    {
      key: 'section-one',
      data: { title: 'First section' },
      content: '\nThis is section one.\n'
    },
    {
      key: 'section-two',
      data: { title: 'Second section' },
      content: '\nThis is section two.\n'
    }
  ]
}
```

### options.section_delimiter

**Type**: `string`

**Default**: `---`

Delimiter to use as the separator for sections. _With the exception of front-matter, which must be the very first thing in the string, the opening delimiter of all other sections must be followed by a string to be used as the `key` for the section._

**Example**

```js
var input = '~~~\ntitle: bar\n~~~\n\nfoo\n~~~one\ntitle: One\n~~~\nThis is one';
console.log(sections(input, {section_delimiter: '~~~'}));
```

Results in:

```js
{
  content: '',
  sections: [
    {
      key: '',
      data: 'title: bar',
      content: '\nfoo'
    },
    {
      key: 'one',
      data: 'title: One',
      content: 'This is one'
    }
  ]
}
```

## About
<details>
  <summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

Please read the [contributing guide](.github/contributing.md) for advice on opening issues, pull requests, and coding standards.

</details>

<details>
  <summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
  <summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects: 

- [assemble](https://www.npmjs.com/package/assemble): Get the rocks out of your socks! Assemble makes you fast at creating web projects… [more](https://github.com/assemble/assemble) | [homepage](https://github.com/assemble/assemble "Get the rocks out of your socks! Assemble makes you fast at creating web projects. Assemble is used by thousands of projects for rapid prototyping, creating themes, scaffolds, boilerplates, e-books, UI components, API documentation, blogs, building websit")
- [gray-matter](https://www.npmjs.com/package/gray-matter): Parse front-matter from a string or file. Fast, reliable and easy to use. Parses YAML… [more](https://github.com/jonschlinkert/gray-matter) | [homepage](https://github.com/jonschlinkert/gray-matter "Parse front-matter from a string or file. Fast, reliable and easy to use. Parses YAML front matter by default, but also has support for YAML, JSON, TOML or Coffee Front-Matter, with options to set custom delimiters. Used by metalsmith, assemble, verb and ")
- [verb](https://www.npmjs.com/package/verb): Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used… [more](https://github.com/verbose/verb) | [homepage](https://github.com/verbose/verb "Documentation generator for GitHub projects. Verb is extremely powerful, easy to use, and is used on hundreds of projects of all sizes to generate everything from API docs to readmes.")  

### Contributors

### Author
**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License
Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on October 23, 2017._

