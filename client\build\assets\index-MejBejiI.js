const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/pages-products-Bw2sLK3T.js","assets/vendor-react-Cy9l5Slk.js","assets/vendor-misc-C-eNx6BD.js","assets/vendor-animations-DfF1zhsH.js","assets/vendor-ui-FmAuOtO-.js","assets/components-layout-bSmHkFSl.js","assets/components-common-BBpqRGpk.js","assets/components-layout-LOVgDiEq.css","assets/components-home-CLoPu5ar.js","assets/vendor-utils-t--hEgTQ.js","assets/pages-products-DEbkkO14.css","assets/pages-static-ByckuOLc.js","assets/pages-other-DqDbC0ab.js"])))=>i.map(i=>d[i]);
import{r as i,a as v,j as t,e as j,f as a,R as L,b as w,B as P,g as p}from"./vendor-react-Cy9l5Slk.js";import{_ as n}from"./vendor-misc-C-eNx6BD.js";import"./components-layout-bSmHkFSl.js";import{t as A,n as b,G as R,E as D}from"./components-common-BBpqRGpk.js";import{R as f,W as x}from"./vendor-animations-DfF1zhsH.js";import{H as O}from"./pages-other-DqDbC0ab.js";import"./vendor-ui-FmAuOtO-.js";import"./components-home-CLoPu5ar.js";import"./vendor-utils-t--hEgTQ.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))c(r);new MutationObserver(r=>{for(const l of r)if(l.type==="childList")for(const d of l.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&c(d)}).observe(document,{childList:!0,subtree:!0});function o(r){const l={};return r.integrity&&(l.integrity=r.integrity),r.referrerPolicy&&(l.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?l.credentials="include":r.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function c(r){if(r.ep)return;r.ep=!0;const l=o(r);fetch(r.href,l)}})();const T=()=>{document.querySelectorAll(".parallax-mousemove-scene").forEach(e=>{e.addEventListener("mousemove",function(s){const o=window.innerWidth,c=window.innerHeight,r=.5-(s.pageX-this.offsetLeft)/o,l=.5-(s.pageY-this.offsetTop)/c;this.querySelectorAll(".parallax-mousemove").forEach(m=>{const _=parseInt(m.getAttribute("data-offset")),E=`translate3d(${Math.round(r*_)}px, ${Math.round(l*_)}px, 0px)`;m.style.transform=E});let d=s.pageX-this.offsetLeft;s.pageY-this.offsetTop,this.querySelectorAll(".parallax-mousemove-follow").forEach(m=>{m.style.left=`${d}px`,m.style.top="31px"})}),e.addEventListener("mouseenter",function(s){this.querySelectorAll(".parallax-mousemove-follow").forEach(o=>{setTimeout(()=>{o.style.transition="all .27s var(--ease-out-short)",o.style.willChange="transform"},27)})}),e.addEventListener("mouseout",function(s){this.querySelectorAll(".parallax-mousemove-follow").forEach(o=>{o.style.transition="none"})})})};function S(){if(document.querySelectorAll("[data-rellax-y]").length&&window.innerWidth>=1280){let s=function(){document.querySelectorAll("[data-rellax-y]").forEach(o=>{o.getBoundingClientRect().top<window.innerHeight&&o.getBoundingClientRect().bottom>0?o.classList.contains("js-in-viewport")||(o.classList.add("js-in-viewport"),e.refresh()):o.classList.contains("js-in-viewport")&&o.classList.remove("js-in-viewport")})};const e=new f("[data-rellax-y]",{vertical:!0,horizontal:!1});window.addEventListener("scroll",s)}if(document.querySelectorAll("[data-rellax-x]").length&&window.innerWidth>=1280){let s=function(){document.querySelectorAll("[data-rellax-x]").forEach(o=>{o.getBoundingClientRect().top<window.innerHeight&&o.getBoundingClientRect().bottom>0?o.classList.contains("js-in-viewport")||(o.classList.add("js-in-viewport"),e.refresh()):o.classList.contains("js-in-viewport")&&o.classList.remove("js-in-viewport")})};const e=new f("[data-rellax-x]",{horizontal:!0});window.addEventListener("scroll",s)}}function I(){setTimeout(()=>{try{document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow").forEach(o=>o.classList.add("no-animate"));var e=new x({boxClass:"wow",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(o){o.classList.add("animated"),o.style.opacity="1",o.style.visibility="visible"}});document.body.classList.contains("appear-animate")||document.body.classList.add("appear-animate"),e.init(),setTimeout(()=>{document.querySelectorAll(".wow").forEach(o=>{o.classList.contains("animated")||(o.style.opacity="1",o.style.visibility="visible",o.classList.add("animated"))})},2e3),document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow-p").forEach(o=>o.classList.add("no-animate"));var s=new x({boxClass:"wow-p",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(o){o.classList.add("animated"),o.style.opacity="1",o.style.visibility="visible"}});document.body.classList.contains("appear-animate")?s.init():document.querySelectorAll(".wow-p").forEach(o=>o.style.opacity="1"),document.body.classList.contains("appear-animate")&&window.innerWidth>=1024&&document.documentElement.classList.contains("no-mobile")?document.querySelectorAll(".wow-menubar").forEach(o=>{o.classList.add("no-animate","fadeInDown","animated"),setInterval(()=>{o.classList.remove("no-animate")},1500)}):document.querySelectorAll(".wow-menubar").forEach(o=>o.style.opacity="1")}catch(o){console.error("Error initializing WOW.js:",o),document.querySelectorAll(".wow, .wow-p, .wow-menubar").forEach(c=>{c.style.opacity="1",c.classList.add("animated")})}},100)}const g=()=>{var e=document.querySelector(".main-nav"),s=document.querySelector(".nav-logo-wrap .logo"),o=document.querySelector(".light-after-scroll");e&&(window.scrollY>0?(e.classList.remove("transparent"),e.classList.add("small-height","body-scrolled"),s&&s.classList.add("small-height"),o&&o.classList.remove("dark")):window.scrollY===0&&(e.classList.add("transparent"),e.classList.remove("small-height","body-scrolled"),s&&s.classList.remove("small-height"),o&&o.classList.add("dark")))},z=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.p),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),V=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.a),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),q=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.b),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),M=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.c),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),C=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.d),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),B=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.e),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),k=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.f),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),W=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.g),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),H=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.h),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),X=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.i),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),Y=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.j),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),$=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.k),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),F=i.lazy(()=>n(()=>import("./pages-products-Bw2sLK3T.js").then(e=>e.l),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),N=i.lazy(()=>n(()=>import("./pages-static-ByckuOLc.js").then(e=>e.p),__vite__mapDeps([11,1,2,3,4,5,6,7,8,9]))),G=i.lazy(()=>n(()=>import("./pages-static-ByckuOLc.js").then(e=>e.a),__vite__mapDeps([11,1,2,3,4,5,6,7,8,9]))),K=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.p),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),Q=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.a),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),U=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.b),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),J=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.c),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),Z=i.lazy(()=>n(()=>import("./pages-static-ByckuOLc.js").then(e=>e.b),__vite__mapDeps([11,1,2,3,4,5,6,7,8,9]))),ee=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.d),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),te=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.A),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),oe=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.e),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),se=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.f),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),y=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.g),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),ae=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.h),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),ie=i.lazy(()=>n(()=>import("./pages-other-DqDbC0ab.js").then(e=>e.i),__vite__mapDeps([12,1,2,3,4,5,6,7,8,9]))),ne=()=>t.jsxs("div",{className:"page-loader",style:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:"#1a1a1a",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:[t.jsx("div",{className:"loader",style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #fff",borderRadius:"50%",animation:"spin 1s linear infinite"}}),t.jsx("style",{children:`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `})]});function re(){const{pathname:e}=v();return i.useEffect(()=>{var r;I(),T();var s=document.querySelector(".main-nav");s!=null&&s.classList.contains("transparent")?s.classList.add("js-transparent"):(r=s==null?void 0:s.classList)!=null&&r.contains("dark")||s==null||s.classList.add("js-no-transparent-white"),window.addEventListener("scroll",g),S();const o=document.title||"DevSkills",c=window.location.href;return A(o,c),()=>{window.removeEventListener("scroll",g)}},[e]),i.useEffect(()=>{typeof window<"u"&&n(()=>import("./vendor-ui-FmAuOtO-.js").then(s=>s.b),__vite__mapDeps([4,2,3])).then(()=>{console.log("Bootstrap loaded")})},[]),t.jsxs(t.Fragment,{children:[t.jsx(i.Suspense,{fallback:t.jsx(ne,{}),children:t.jsx(j,{children:t.jsxs(a,{path:"/",children:[t.jsx(a,{index:!0,element:t.jsx(O,{})}),t.jsx(a,{path:"about",element:t.jsx(N,{})}),t.jsxs(a,{path:"products",children:[t.jsx(a,{path:"bms",element:t.jsx(z,{})}),t.jsx(a,{path:"bms/core",element:t.jsx(q,{})}),t.jsx(a,{path:"bms/accounting",element:t.jsx(M,{})}),t.jsx(a,{path:"bms/budget",element:t.jsx(C,{})}),t.jsx(a,{path:"bms/hr",element:t.jsx(B,{})}),t.jsx(a,{path:"bms/recruitment",element:t.jsx(k,{})}),t.jsx(a,{path:"bms/production",element:t.jsx(W,{})}),t.jsx(a,{path:"bms/sales",element:t.jsx(H,{})}),t.jsx(a,{path:"bms/quality",element:t.jsx(X,{})}),t.jsx(a,{path:"bms/communication",element:t.jsx(Y,{})}),t.jsx(a,{path:"bms/companies",element:t.jsx($,{})}),t.jsx(a,{path:"bms/:moduleId",element:t.jsx(V,{})}),t.jsx(a,{path:"ultimation/overview",element:t.jsx(F,{})})]}),t.jsx(a,{path:"services",element:t.jsx(G,{})}),t.jsx(a,{path:"portfolio",element:t.jsx(K,{})}),t.jsx(a,{path:"portfolio-single/:id",element:t.jsx(U,{})}),t.jsx(a,{path:"blog",element:t.jsx(Q,{})}),t.jsx(a,{path:"blog-single/:id",element:t.jsx(J,{})}),t.jsx(a,{path:"contact",element:t.jsx(Z,{})}),t.jsx(a,{path:"admin",element:t.jsx(te,{})}),t.jsx(a,{path:"admin/dashboard",element:t.jsx(oe,{})}),t.jsx(a,{path:"admin/posts",element:t.jsx(se,{})}),t.jsx(a,{path:"admin/blog/new",element:t.jsx(y,{})}),t.jsx(a,{path:"admin/blog/edit/:id",element:t.jsx(y,{})}),t.jsx(a,{path:"admin/categories",element:t.jsx(ae,{})}),t.jsx(a,{path:"admin/tags",element:t.jsx(ie,{})}),t.jsx(a,{path:"*",element:t.jsx(ee,{})})]})})}),t.jsx(b,{}),t.jsx(R,{})]})}const u=document.getElementById("root"),h=t.jsx(L.StrictMode,{children:t.jsx(D,{children:t.jsx(w,{children:t.jsx(P,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:t.jsx(re,{})})})})}),le=u&&u.hasChildNodes();try{le?p.hydrateRoot(u,h):p.createRoot(u).render(h)}catch(e){console.error("Error rendering app:",e),p.createRoot(u).render(h)}
//# sourceMappingURL=index-MejBejiI.js.map
