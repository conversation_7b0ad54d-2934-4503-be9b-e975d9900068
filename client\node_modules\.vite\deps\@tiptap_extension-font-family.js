import "./chunk-ERN7F2PZ.js";
import {
  Extension
} from "./chunk-W6QQZAON.js";
import "./chunk-DC5AMYBS.js";

// node_modules/.pnpm/@tiptap+extension-font-family@2.22.3_@tiptap+core@2.22.3_@tiptap+pm@2.22.3__@tiptap+extension_rbhprqendhpaogg6bg6hnvyksq/node_modules/@tiptap/extension-font-family/dist/index.js
var FontFamily = Extension.create({
  name: "fontFamily",
  addOptions() {
    return {
      types: ["textStyle"]
    };
  },
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontFamily: {
            default: null,
            parseHTML: (element) => element.style.fontFamily,
            renderHTML: (attributes) => {
              if (!attributes.fontFamily) {
                return {};
              }
              return {
                style: `font-family: ${attributes.fontFamily}`
              };
            }
          }
        }
      }
    ];
  },
  addCommands() {
    return {
      setFontFamily: (fontFamily) => ({ chain }) => {
        return chain().setMark("textStyle", { fontFamily }).run();
      },
      unsetFontFamily: () => ({ chain }) => {
        return chain().setMark("textStyle", { fontFamily: null }).removeEmptyTextStyle().run();
      }
    };
  }
});
export {
  FontFamily,
  FontFamily as default
};
//# sourceMappingURL=@tiptap_extension-font-family.js.map
