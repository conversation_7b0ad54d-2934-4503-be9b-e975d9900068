<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OG Image Template</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");

      body {
        margin: 0;
        padding: 0;
        width: 1200px;
        height: 630px;
        display: flex;
        flex-direction: column;
        font-family: "DM Sans", sans-serif;
        background-color: #000000;
        color: white;
        overflow: hidden;
      }

      .container {
        display: flex;
        flex-direction: column;
        padding: 60px;
        height: 100%;
        box-sizing: border-box;
        position: relative;
      }

      .header {
        display: flex;
        align-items: center;
        margin-bottom: 60px;
        position: relative;
        z-index: 2;
      }

      .logo-container {
        display: flex;
        align-items: center;
      }

      .logo {
        width: 60px;
        height: 60px;
        margin-right: 20px;
      }

      .company-name {
        font-size: 32px;
        font-weight: 700;
        letter-spacing: 1px;
        text-transform: uppercase;
      }

      .title {
        font-size: 80px;
        font-weight: 700;
        line-height: 1.1;
        margin-bottom: 30px;
        max-width: 900px;
        position: relative;
        z-index: 2;
        background: linear-gradient(90deg, #ffffff 0%, #cccccc 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .description {
        font-size: 32px;
        opacity: 0.8;
        max-width: 800px;
        line-height: 1.4;
        position: relative;
        z-index: 2;
      }

      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        position: relative;
        z-index: 2;
      }

      .url {
        font-size: 24px;
        opacity: 0.6;
        font-weight: 500;
      }

      .grid-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: linear-gradient(
            rgba(255, 255, 255, 0.05) 1px,
            transparent 1px
          ),
          linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 40px 40px;
        z-index: 1;
      }

      .accent-circle {
        position: absolute;
        width: 500px;
        height: 500px;
        border-radius: 50%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(0, 0, 0, 0) 70%
        );
        top: -100px;
        right: -100px;
        z-index: 1;
      }

      .accent-line {
        position: absolute;
        width: 3px;
        height: 150px;
        background-color: #ffffff;
        bottom: 160px;
        left: 30px;
        z-index: 1;
      }

      .tag {
        display: inline-block;
        padding: 8px 16px;
        background-color: #ffffff;
        color: #000;
        font-weight: 700;
        font-size: 18px;
        border-radius: 4px;
        margin-bottom: 30px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      /* Animation effect for static image */
      .animated-element {
        position: absolute;
        width: 200px;
        height: 200px;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        right: 100px;
        bottom: 100px;
        z-index: 1;
      }

      .animated-element::before {
        content: "";
        position: absolute;
        width: 100px;
        height: 100px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .animated-element::after {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        background-color: #ffffff;
        border-radius: 50%;
        top: 30px;
        left: 30px;
      }
    </style>
  </head>
  <body>
    <div class="grid-pattern"></div>
    <div class="accent-circle"></div>
    <div class="accent-line"></div>
    <div class="animated-element"></div>

    <div class="container">
      <div class="header">
        <div class="logo-container">
          <svg
            class="logo"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18.36 6.64a9 9 0 1 1-12.73 0"
              stroke="white"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <line
              x1="12"
              y1="2"
              x2="12"
              y2="12"
              stroke="white"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></line>
          </svg>
          <div class="company-name">DEVSKILLS</div>
        </div>
      </div>

      <div class="content">
        <div class="tag">Business Management System</div>
        <h1 class="title">Streamline Your Business Operations</h1>
        <p class="description">
          Boost productivity with our comprehensive solution designed for modern
          enterprises.
        </p>
      </div>

      <div class="footer">
        <div class="url">devskills.ee</div>
      </div>
    </div>
  </body>
</html>
