import React, { useEffect, useRef } from "react";
import hljs from "highlight.js";

export default function HighlightTestPage() {
  const containerRef = useRef(null);

  useEffect(() => {
    if (containerRef.current) {
      console.log('🔍 Test page loaded');
      
      const codeBlocks = containerRef.current.querySelectorAll('pre code');
      console.log(`🔍 Found ${codeBlocks.length} code blocks`);
      
      // Apply highlighting
      let highlightedCount = 0;
      codeBlocks.forEach((block, index) => {
        console.log(`🔍 Processing block ${index}:`, block);
        console.log(`🔍 Block classes before:`, block.className);
        
        try {
          hljs.highlightElement(block);
          highlightedCount++;
          console.log(`✅ Block ${index} highlighted successfully`);
          console.log(`🔍 Block classes after:`, block.className);
          
          // Apply inline styles with !important
          applyInlineStyles(block);
          
          // Check for highlight classes
          const highlightSpans = block.querySelectorAll('[class*="hljs-"]');
          console.log(`🔍 Found ${highlightSpans.length} highlighted elements in block ${index}`);
          
        } catch (error) {
          console.error(`❌ Error highlighting block ${index}:`, error);
        }
      });
      
      console.log(`🔍 Highlighting complete: ${highlightedCount}/${codeBlocks.length} blocks`);
    }
  }, []);

  const applyInlineStyles = (codeBlock) => {
    console.log("🎨 Applying inline styles to test page...");
    
    const highlightColors = {
      'hljs-keyword': '#569cd6',
      'hljs-string': '#ce9178',
      'hljs-number': '#b5cea8',
      'hljs-comment': '#6a9955',
      'hljs-tag': '#569cd6',
      'hljs-name': '#4fc1ff',
      'hljs-attr': '#9cdcfe',
      'hljs-function': '#dcdcaa',
      'hljs-title': '#dcdcaa',
      'hljs-variable': '#9cdcfe',
      'hljs-property': '#9cdcfe',
    };

    // Apply styles to highlighted spans
    Object.keys(highlightColors).forEach(className => {
      const elements = codeBlock.querySelectorAll(`.${className}`);
      elements.forEach(element => {
        element.style.setProperty('color', highlightColors[className], 'important');
        element.style.setProperty('background', 'none', 'important');
        element.style.setProperty('border', '1px solid red', 'important'); // Debug border
        console.log(`🎨 Applied color ${highlightColors[className]} to .${className}`);
      });
    });

    // Style the pre element
    const preElement = codeBlock.closest('pre');
    if (preElement) {
      preElement.style.setProperty('background', '#1e1e1e', 'important');
      preElement.style.setProperty('color', '#d4d4d4', 'important');
      preElement.style.setProperty('padding', '16px', 'important');
      preElement.style.setProperty('border-radius', '8px', 'important');
      preElement.style.setProperty('border', '3px solid yellow', 'important'); // Debug border
      preElement.style.setProperty('font-family', 'Consolas, "Courier New", monospace', 'important');
      preElement.style.setProperty('font-size', '14px', 'important');
      preElement.style.setProperty('line-height', '1.5', 'important');
      console.log("🎨 Applied styles to pre element");
    }
  };

  return (
    <div 
      ref={containerRef}
      style={{
        // Completely isolated styles
        all: 'initial',
        fontFamily: 'Arial, sans-serif',
        maxWidth: '800px',
        margin: '0 auto',
        padding: '20px',
        background: '#2d2d2d',
        color: '#ffffff',
        minHeight: '100vh'
      }}
    >
      <h1 style={{ color: '#569cd6', fontSize: '24px', marginBottom: '20px' }}>
        Highlight.js Test Page (Isolated)
      </h1>
      
      <div style={{ margin: '30px 0', padding: '20px', border: '2px solid #555', borderRadius: '8px' }}>
        <h2 style={{ color: '#4fc1ff', fontSize: '18px', marginBottom: '15px' }}>
          Test 1: HTML Code (should have yellow border)
        </h2>
        <pre><code className="language-html">{`<!DOCTYPE html>
<html>
<head>
  <title>Age Checker</title>
</head>
<body>
  <h1>Club Entry Checker</h1>
  <input type="number" id="ageInput" placeholder="Enter your age">
  <button onclick="checkAge()">Check Age</button>
  <p id="result"></p>
  <script>
    function checkAge() {
      let age = document.getElementById("ageInput").value;
      let message = age >= 18 ? "Welcome to the club!" : "Sorry, you're too young!";
      document.getElementById("result").innerText = message;
    }
  </script>
</body>
</html>`}</code></pre>
      </div>

      <div style={{ margin: '30px 0', padding: '20px', border: '2px solid #555', borderRadius: '8px' }}>
        <h2 style={{ color: '#4fc1ff', fontSize: '18px', marginBottom: '15px' }}>
          Test 2: JavaScript Code
        </h2>
        <pre><code className="language-javascript">{`function calculateAge(birthYear) {
    const currentYear = new Date().getFullYear();
    const age = currentYear - birthYear;
    
    if (age >= 18) {
        console.log("You are an adult!");
        return true;
    } else {
        console.log("You are a minor.");
        return false;
    }
}

// Test the function
const isAdult = calculateAge(1990);
console.log("Is adult:", isAdult);`}</code></pre>
      </div>

      <div style={{ margin: '30px 0', padding: '20px', border: '2px solid #555', borderRadius: '8px' }}>
        <h2 style={{ color: '#4fc1ff', fontSize: '18px', marginBottom: '15px' }}>
          Expected Results:
        </h2>
        <ul style={{ color: '#d4d4d4', lineHeight: '1.6' }}>
          <li><strong style={{ color: '#569cd6' }}>Yellow borders</strong> around code blocks</li>
          <li><strong style={{ color: '#ce9178' }}>Red borders</strong> around individual syntax elements</li>
          <li><strong style={{ color: '#569cd6' }}>Blue keywords</strong> (function, let, const, if)</li>
          <li><strong style={{ color: '#ce9178' }}>Orange strings</strong> ("Age Checker", "ageInput")</li>
          <li><strong style={{ color: '#4fc1ff' }}>Light blue HTML tags</strong> (html, head, body)</li>
          <li><strong style={{ color: '#b5cea8' }}>Green numbers</strong> (18, 1990)</li>
        </ul>
      </div>
    </div>
  );
}
