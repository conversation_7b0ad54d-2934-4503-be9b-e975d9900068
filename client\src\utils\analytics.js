// Google Analytics 4 utility functions

// Track page views
export const trackPageView = (page_title, page_location) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'page_view', {
      page_title: page_title,
      page_location: page_location,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track button clicks
export const trackButtonClick = (button_name, button_location, additional_params = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'click', {
      event_category: 'Button',
      event_label: button_name,
      button_location: button_location,
      ...additional_params,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track form submissions
export const trackFormSubmission = (form_name, form_location, success = true) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', success ? 'form_submit' : 'form_error', {
      event_category: 'Form',
      event_label: form_name,
      form_location: form_location,
      success: success,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track contact form submissions (conversion)
export const trackContactFormSubmission = (email, message_length) => {
  if (typeof window !== 'undefined' && window.gtag) {
    // Track as conversion
    window.gtag('event', 'conversion', {
      send_to: 'G-8NEGL4LL8Q',
      event_category: 'Contact',
      event_label: 'Contact Form Submission',
      value: 1,
      currency: 'EUR',
      user_email: email,
      message_length: message_length
    });

    // Also track as generate_lead
    window.gtag('event', 'generate_lead', {
      send_to: 'G-8NEGL4LL8Q',
      event_category: 'Lead Generation',
      event_label: 'Contact Form',
      value: 1,
      currency: 'EUR'
    });
  }
};

// Track language changes
export const trackLanguageChange = (from_language, to_language) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'language_change', {
      event_category: 'User Interaction',
      event_label: `${from_language} to ${to_language}`,
      from_language: from_language,
      to_language: to_language,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track navigation menu interactions
export const trackNavigation = (menu_item, menu_location) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'navigation_click', {
      event_category: 'Navigation',
      event_label: menu_item,
      menu_location: menu_location,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track scroll depth
export const trackScrollDepth = (scroll_percentage, page_location) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'scroll', {
      event_category: 'User Engagement',
      event_label: `${scroll_percentage}% scrolled`,
      scroll_percentage: scroll_percentage,
      page_location: page_location,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track time on page
export const trackTimeOnPage = (time_seconds, page_location) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'timing_complete', {
      name: 'page_view_time',
      value: time_seconds,
      event_category: 'User Engagement',
      event_label: page_location,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track file downloads
export const trackFileDownload = (file_name, file_type, download_location) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'file_download', {
      event_category: 'Downloads',
      event_label: file_name,
      file_type: file_type,
      download_location: download_location,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track external link clicks
export const trackExternalLink = (link_url, link_text, link_location) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'click', {
      event_category: 'External Link',
      event_label: link_text,
      link_url: link_url,
      link_location: link_location,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track search queries (if you have search functionality)
export const trackSearch = (search_term, search_results_count) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'search', {
      search_term: search_term,
      event_category: 'Search',
      event_label: search_term,
      search_results: search_results_count,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Track video interactions
export const trackVideoInteraction = (video_title, action, video_progress = null) => {
  if (typeof window !== 'undefined' && window.gtag) {
    const eventData = {
      event_category: 'Video',
      event_label: video_title,
      video_action: action,
      send_to: 'G-8NEGL4LL8Q'
    };

    if (video_progress !== null) {
      eventData.video_progress = video_progress;
    }

    window.gtag('event', `video_${action}`, eventData);
  }
};

// Track custom events
export const trackCustomEvent = (event_name, event_category, event_label, value = null, additional_params = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    const eventData = {
      event_category: event_category,
      event_label: event_label,
      send_to: 'G-8NEGL4LL8Q',
      ...additional_params
    };

    if (value !== null) {
      eventData.value = value;
    }

    window.gtag('event', event_name, eventData);
  }
};

// Track user engagement milestones
export const trackEngagementMilestone = (milestone_name, page_location, additional_data = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'engagement_milestone', {
      event_category: 'User Engagement',
      event_label: milestone_name,
      page_location: page_location,
      ...additional_data,
      send_to: 'G-8NEGL4LL8Q'
    });
  }
};

// Debug function to check if analytics is working
export const debugAnalytics = () => {
  if (typeof window !== 'undefined') {
    console.log('Google Analytics Debug Info:');
    console.log('gtag available:', typeof window.gtag !== 'undefined');
    console.log('dataLayer:', window.dataLayer);
    
    if (window.gtag) {
      // Test event
      window.gtag('event', 'debug_test', {
        event_category: 'Debug',
        event_label: 'Analytics Test',
        send_to: 'G-8NEGL4LL8Q'
      });
      console.log('Test event sent');
    }
  }
};
