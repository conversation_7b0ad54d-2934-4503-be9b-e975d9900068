/* Ultimation Studio specific styles */

/* Purple-blue gradient border for feature boxes */
.ultimation-feature-box {
  position: relative;
  padding: 30px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  overflow: hidden;
  z-index: 1;
  height: 100%; /* Make all boxes the same height */
  display: flex;
  flex-direction: column;
}

.ultimation-feature-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  padding: 1px; /* Thinner border */
  background: linear-gradient(
    135deg,
    rgba(63, 63, 191, 0.8),
    rgba(128, 63, 191, 0.8),
    rgba(43, 96, 191, 0.8)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.ultimation-feature-box:hover::before {
  background: linear-gradient(
    135deg,
    rgba(99, 63, 191, 0.8),
    rgba(153, 43, 191, 0.8),
    rgba(43, 96, 191, 0.8)
  );
}

/* Make content inside feature boxes flex to maintain equal heights */
.ultimation-feature-box .alt-features-title {
  margin-bottom: 15px;
}

.ultimation-feature-box .alt-features-descr {
  flex-grow: 1; /* Allow description to take up remaining space */
  display: flex;
  align-items: center;
}

/* Module buttons styling */
.ultimation-module-btn {
  display: inline-block;
  margin: 0;
  padding: 8px 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 30px;
  color: #fff;
  cursor: pointer;
  transition: all 0.27s cubic-bezier(0, 0, 0.58, 1);
}

.ultimation-module-btn:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.1);
}

.ultimation-module-btn.active {
  border-color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

/* Testimonial marquee */
.testimonial-marquee {
  overflow: hidden;
  position: relative;
  background-color: #1a1a1a;
  padding: 40px 0;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  touch-action: pan-y;
}

.testimonial-marquee:active {
  cursor: grabbing !important;
}

.testimonial-marquee-track {
  display: flex;
  position: relative;
  will-change: transform;
  transition: transform 0.1s ease-out;
  animation: testimonial-marquee 40s linear infinite;
  animation-play-state: running;
}

.testimonial-marquee:hover .testimonial-marquee-track {
  animation-play-state: paused;
}

.testimonial-marquee.active {
  cursor: grabbing !important;
}

.testimonial-marquee.active .testimonial-marquee-track {
  animation-play-state: paused;
  transition: transform 0.1s ease-out;
}

.testimonial-marquee-item {
  flex-shrink: 0;
  width: 500px; /* Wider cards */
  padding: 40px; /* More padding */
  margin-right: 30px; /* Space between cards */
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  position: relative;
}

.testimonial-marquee-item blockquote {
  margin: 0;
}

.testimonial-marquee-item p {
  font-size: 16px;
  line-height: 1.8; /* Increased line height */
  margin-bottom: 20px; /* More space below text */
}

.testimonial-marquee-item footer {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  padding-top: 10px; /* Space above footer */
  border-top: 1px solid rgba(255, 255, 255, 0.1); /* Subtle separator */
}

/* Clone testimonials for continuous loop */
.testimonial-marquee-track {
  display: flex;
}

.testimonial-marquee-track .testimonial-clone {
  display: flex;
}

@keyframes testimonial-marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* CTA button styling - exactly matching the module buttons */
.ultimation-cta-btn {
  display: inline-block;
  margin: 0;
  padding: 5px 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: #fff;
  cursor: pointer;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  font-size: 11px;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  background-color: transparent;
  transition: all 0.27s cubic-bezier(0, 0, 0.58, 1);
}

.ultimation-cta-btn:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  text-decoration: none;
}

/* For larger CTA buttons */
.ultimation-cta-btn.btn-large {
  padding: 12px 35px;
  font-size: 13px;
}

/* Icon styling */
.ultimation-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(63, 63, 191, 0.8),
    rgba(128, 63, 191, 0.8)
  );
  color: #fff;
  font-size: 32px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
