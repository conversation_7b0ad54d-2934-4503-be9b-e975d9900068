import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO from '../components/common/SEO';
import AdminLayout from '../components/admin/AdminLayout';
import { adminAPI } from '../utils/api';

const AdminTags = () => {
  const navigate = useNavigate();
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [formData, setFormData] = useState({
    name: ''
  });

  // Load tags on component mount
  useEffect(() => {
    loadTags();
  }, []);

  const loadTags = async () => {
    try {
      setLoading(true);
      const { response, data } = await adminAPI.getTags();

      if (data.success) {
        setTags(data.data || []);
      } else {
        setError(data.message || 'Failed to load tags');
      }
    } catch (error) {
      console.error('Load tags error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      let response, data;

      if (editingTag) {
        ({ response, data } = await adminAPI.updateTag(editingTag.id, formData));
      } else {
        ({ response, data } = await adminAPI.createTag(formData));
      }

      if (data.success) {
        setSuccess(editingTag ? 'Tag updated successfully!' : 'Tag created successfully!');
        setShowModal(false);
        setEditingTag(null);
        setFormData({ name: '' });
        loadTags();
      } else {
        setError(data.message || 'Failed to save tag');
      }
    } catch (error) {
      console.error('Save tag error:', error);
      setError('Network error. Please try again.');
    }
  };

  const handleEdit = (tag) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name
    });
    setShowModal(true);
  };

  const handleDelete = async (tagId) => {
    if (!window.confirm('Are you sure you want to delete this tag? This action cannot be undone.')) {
      return;
    }

    try {
      const { response, data } = await adminAPI.deleteTag(tagId);

      if (data.success) {
        setSuccess('Tag deleted successfully!');
        loadTags();
      } else {
        setError(data.message || 'Failed to delete tag');
      }
    } catch (error) {
      console.error('Delete tag error:', error);
      setError('Network error. Please try again.');
    }
  };

  const openCreateModal = () => {
    setEditingTag(null);
    setFormData({ name: '' });
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingTag(null);
    setFormData({ name: '' });
    setError('');
  };

  return (
    <>
      <SEO 
        title="Manage Tags - Admin"
        description="Manage blog tags in the admin panel"
        noIndex={true}
      />
      
      <AdminLayout title="Tags">
        
        {/* Action Bar */}
        <div className="mb-30">
          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="section-descr mb-0">
                Tag your blog posts for better organization and discoverability. Create and manage content tags.
              </p>
            </div>
            <div className="col-md-6 text-md-end">
              <button
                onClick={openCreateModal}
                className="btn btn-mod btn-color btn-round"
              >
                <i className="mi-plus me-2"></i>
                New Tag
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="alert alert-danger mb-30" role="alert">
            <i className="mi-warning me-2"></i>
            {error}
          </div>
        )}
        
        {success && (
          <div className="alert alert-success mb-30" role="alert">
            <i className="mi-check me-2"></i>
            {success}
          </div>
        )}

        {/* Tags Table */}
        <div className="admin-table">
          {loading ? (
            <div className="text-center py-60">
              <i className="fa fa-spinner fa-spin fa-2x color-primary-1 mb-20"></i>
              <div className="hs-line-4 font-alt black">Loading tags...</div>
            </div>
          ) : tags.length === 0 ? (
            <div className="text-center py-60">
              <i className="mi-tag fa-3x color-gray-light-1 mb-20"></i>
              <div className="hs-line-4 font-alt black mb-10">No tags found</div>
              <p className="section-descr mb-30">
                Create your first tag to start organizing your blog posts.
              </p>
              <button
                onClick={openCreateModal}
                className="btn btn-mod btn-color btn-round"
              >
                <i className="mi-plus me-2"></i>
                Create First Tag
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Tag Name</th>
                    <th>Posts</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {tags.map((tag) => (
                    <tr key={tag.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <i className="mi-tag me-3 color-primary-1"></i>
                          <div>
                            <div className="fw-bold">{tag.name}</div>
                            <small className="text-muted">/{tag.slug}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-secondary">
                          {tag._count?.posts || 0} posts
                        </span>
                      </td>
                      <td>
                        {new Date(tag.createdAt).toLocaleDateString()}
                      </td>
                      <td>
                        <div className="btn-group" role="group">
                          <button
                            onClick={() => handleEdit(tag)}
                            className="btn btn-sm btn-outline-primary"
                            title="Edit"
                          >
                            <i className="mi-edit"></i>
                          </button>
                          <button
                            onClick={() => handleDelete(tag.id)}
                            className="btn btn-sm btn-outline-danger"
                            title="Delete"
                          >
                            <i className="mi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="modal-overlay" onClick={closeModal}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h4 className="modal-title">
                  <i className="mi-tag me-2"></i>
                  {editingTag ? 'Edit Tag' : 'Create New Tag'}
                </h4>
                <button 
                  type="button" 
                  className="modal-close"
                  onClick={closeModal}
                >
                  <i className="mi-close"></i>
                </button>
              </div>
              
              <form onSubmit={handleSubmit}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-12 mb-20">
                      <label className="form-label">
                        <i className="mi-edit me-2"></i>
                        Tag Name *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="form-control"
                        placeholder="Enter tag name"
                        required
                      />
                      <small className="form-text text-muted">
                        Keep it short and descriptive (e.g., "JavaScript", "Tutorial", "News")
                      </small>
                    </div>
                  </div>
                </div>
                
                <div className="modal-footer">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="btn btn-mod btn-gray btn-round me-3"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-mod btn-color btn-round"
                  >
                    <i className="mi-check me-2"></i>
                    {editingTag ? 'Update Tag' : 'Create Tag'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
        
      </AdminLayout>
    </>
  );
};

export default AdminTags;
