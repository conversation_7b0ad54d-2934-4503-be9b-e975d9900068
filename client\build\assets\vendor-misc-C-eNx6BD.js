import{g as Xe,c as Bt}from"./vendor-animations-DfF1zhsH.js";function xr(n,e){for(var t=0;t<e.length;t++){const r=e[t];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in n)){const o=Object.getOwnPropertyDescriptor(r,i);o&&Object.defineProperty(n,i,o.get?o:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}var On={exports:{}},Sn={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(n){function e(d,v){var w=d.length;d.push(v);e:for(;0<w;){var S=w-1>>>1,P=d[S];if(0<i(P,v))d[S]=v,d[w]=P,w=S;else break e}}function t(d){return d.length===0?null:d[0]}function r(d){if(d.length===0)return null;var v=d[0],w=d.pop();if(w!==v){d[0]=w;e:for(var S=0,P=d.length,N=P>>>1;S<N;){var H=2*(S+1)-1,F=d[H],_=H+1,V=d[_];if(0>i(F,w))_<P&&0>i(V,F)?(d[S]=V,d[_]=w,S=_):(d[S]=F,d[H]=w,S=H);else if(_<P&&0>i(V,w))d[S]=V,d[_]=w,S=_;else break e}}return v}function i(d,v){var w=d.sortIndex-v.sortIndex;return w!==0?w:d.id-v.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var o=performance;n.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();n.unstable_now=function(){return s.now()-a}}var l=[],f=[],u=1,c=null,h=3,g=!1,m=!1,y=!1,b=typeof setTimeout=="function"?setTimeout:null,O=typeof clearTimeout=="function"?clearTimeout:null,E=typeof setImmediate<"u"?setImmediate:null;function C(d){for(var v=t(f);v!==null;){if(v.callback===null)r(f);else if(v.startTime<=d)r(f),v.sortIndex=v.expirationTime,e(l,v);else break;v=t(f)}}function x(d){if(y=!1,C(d),!m)if(t(l)!==null)m=!0,B();else{var v=t(f);v!==null&&p(x,v.startTime-d)}}var $=!1,A=-1,j=5,D=-1;function R(){return!(n.unstable_now()-D<j)}function k(){if($){var d=n.unstable_now();D=d;var v=!0;try{e:{m=!1,y&&(y=!1,O(A),A=-1),g=!0;var w=h;try{t:{for(C(d),c=t(l);c!==null&&!(c.expirationTime>d&&R());){var S=c.callback;if(typeof S=="function"){c.callback=null,h=c.priorityLevel;var P=S(c.expirationTime<=d);if(d=n.unstable_now(),typeof P=="function"){c.callback=P,C(d),v=!0;break t}c===t(l)&&r(l),C(d)}else r(l);c=t(l)}if(c!==null)v=!0;else{var N=t(f);N!==null&&p(x,N.startTime-d),v=!1}}break e}finally{c=null,h=w,g=!1}v=void 0}}finally{v?M():$=!1}}}var M;if(typeof E=="function")M=function(){E(k)};else if(typeof MessageChannel<"u"){var I=new MessageChannel,T=I.port2;I.port1.onmessage=k,M=function(){T.postMessage(null)}}else M=function(){b(k,0)};function B(){$||($=!0,M())}function p(d,v){A=b(function(){d(n.unstable_now())},v)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(d){d.callback=null},n.unstable_continueExecution=function(){m||g||(m=!0,B())},n.unstable_forceFrameRate=function(d){0>d||125<d?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<d?Math.floor(1e3/d):5},n.unstable_getCurrentPriorityLevel=function(){return h},n.unstable_getFirstCallbackNode=function(){return t(l)},n.unstable_next=function(d){switch(h){case 1:case 2:case 3:var v=3;break;default:v=h}var w=h;h=v;try{return d()}finally{h=w}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(d,v){switch(d){case 1:case 2:case 3:case 4:case 5:break;default:d=3}var w=h;h=d;try{return v()}finally{h=w}},n.unstable_scheduleCallback=function(d,v,w){var S=n.unstable_now();switch(typeof w=="object"&&w!==null?(w=w.delay,w=typeof w=="number"&&0<w?S+w:S):w=S,d){case 1:var P=-1;break;case 2:P=250;break;case 5:P=1073741823;break;case 4:P=1e4;break;default:P=5e3}return P=w+P,d={id:u++,callback:v,priorityLevel:d,startTime:w,expirationTime:P,sortIndex:-1},w>S?(d.sortIndex=w,e(f,d),t(l)===null&&d===t(f)&&(y?(O(A),A=-1):y=!0,p(x,w-S))):(d.sortIndex=P,e(l,d),m||g||(m=!0,B())),d},n.unstable_shouldYield=R,n.unstable_wrapCallback=function(d){var v=h;return function(){var w=h;h=v;try{return d.apply(this,arguments)}finally{h=w}}}})(Sn);On.exports=Sn;var ea=On.exports;const Or="modulepreload",Sr=function(n){return"/"+n},Nt={},Pr=function(e,t,r){let i=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));i=Promise.allSettled(t.map(l=>{if(l=Sr(l),l in Nt)return;Nt[l]=!0;const f=l.endsWith(".css"),u=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${u}`))return;const c=document.createElement("link");if(c.rel=f?"stylesheet":Or,f||(c.as="script"),c.crossOrigin="",c.href=l,a&&c.setAttribute("nonce",a),document.head.appendChild(c),f)return new Promise((h,g)=>{c.addEventListener("load",h),c.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${l}`)))})}))}function o(s){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=s,window.dispatchEvent(a),!a.defaultPrevented)throw s}return i.then(s=>{for(const a of s||[])a.status==="rejected"&&o(a.reason);return e().catch(o)})},L=n=>typeof n=="string",Ee=()=>{let n,e;const t=new Promise((r,i)=>{n=r,e=i});return t.resolve=n,t.reject=e,t},Ft=n=>n==null?"":""+n,Er=(n,e,t)=>{n.forEach(r=>{e[r]&&(t[r]=e[r])})},$r=/###/g,Mt=n=>n&&n.indexOf("###")>-1?n.replace($r,"."):n,_t=n=>!n||L(n),Le=(n,e,t)=>{const r=L(e)?e.split("."):e;let i=0;for(;i<r.length-1;){if(_t(n))return{};const o=Mt(r[i]);!n[o]&&t&&(n[o]=new t),Object.prototype.hasOwnProperty.call(n,o)?n=n[o]:n={},++i}return _t(n)?{}:{obj:n,k:Mt(r[i])}},Ht=(n,e,t)=>{const{obj:r,k:i}=Le(n,e,Object);if(r!==void 0||e.length===1){r[i]=t;return}let o=e[e.length-1],s=e.slice(0,e.length-1),a=Le(n,s,Object);for(;a.obj===void 0&&s.length;)o=`${s[s.length-1]}.${o}`,s=s.slice(0,s.length-1),a=Le(n,s,Object),a!=null&&a.obj&&typeof a.obj[`${a.k}.${o}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${o}`]=t},Lr=(n,e,t,r)=>{const{obj:i,k:o}=Le(n,e,Object);i[o]=i[o]||[],i[o].push(t)},Ue=(n,e)=>{const{obj:t,k:r}=Le(n,e);if(t&&Object.prototype.hasOwnProperty.call(t,r))return t[r]},Rr=(n,e,t)=>{const r=Ue(n,t);return r!==void 0?r:Ue(e,t)},Pn=(n,e,t)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in n?L(n[r])||n[r]instanceof String||L(e[r])||e[r]instanceof String?t&&(n[r]=e[r]):Pn(n[r],e[r],t):n[r]=e[r]);return n},ue=n=>n.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Ar={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Cr=n=>L(n)?n.replace(/[&<>"'\/]/g,e=>Ar[e]):n;class kr{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const jr=[" ",",","?","!",";"],Tr=new kr(20),Dr=(n,e,t)=>{e=e||"",t=t||"";const r=jr.filter(s=>e.indexOf(s)<0&&t.indexOf(s)<0);if(r.length===0)return!0;const i=Tr.getRegExp(`(${r.map(s=>s==="?"?"\\?":s).join("|")})`);let o=!i.test(n);if(!o){const s=n.indexOf(t);s>0&&!i.test(n.substring(0,s))&&(o=!0)}return o},at=(n,e,t=".")=>{if(!n)return;if(n[e])return Object.prototype.hasOwnProperty.call(n,e)?n[e]:void 0;const r=e.split(t);let i=n;for(let o=0;o<r.length;){if(!i||typeof i!="object")return;let s,a="";for(let l=o;l<r.length;++l)if(l!==o&&(a+=t),a+=r[l],s=i[a],s!==void 0){if(["string","number","boolean"].indexOf(typeof s)>-1&&l<r.length-1)continue;o+=l-o+1;break}i=s}return i},Ce=n=>n==null?void 0:n.replace("_","-"),Ir={type:"logger",log(n){this.output("log",n)},warn(n){this.output("warn",n)},error(n){this.output("error",n)},output(n,e){var t,r;(r=(t=console==null?void 0:console[n])==null?void 0:t.apply)==null||r.call(t,console,e)}};class qe{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||Ir,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,i){return i&&!this.debug?null:(L(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new qe(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new qe(this.logger,e)}}var G=new qe;class Je{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const i=this.observers[r].get(t)||0;this.observers[r].set(t,i+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([i,o])=>{for(let s=0;s<o;s++)i(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([i,o])=>{for(let s=0;s<o;s++)i.apply(i,[e,...t])})}}class Vt extends Je{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r,i={}){var f,u;const o=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,s=i.ignoreJSONStructure!==void 0?i.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],r&&(Array.isArray(r)?a.push(...r):L(r)&&o?a.push(...r.split(o)):a.push(r)));const l=Ue(this.data,a);return!l&&!t&&!r&&e.indexOf(".")>-1&&(e=a[0],t=a[1],r=a.slice(2).join(".")),l||!s||!L(r)?l:at((u=(f=this.data)==null?void 0:f[e])==null?void 0:u[t],r,o)}addResource(e,t,r,i,o={silent:!1}){const s=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let a=[e,t];r&&(a=a.concat(s?r.split(s):r)),e.indexOf(".")>-1&&(a=e.split("."),i=t,t=a[1]),this.addNamespaces(t),Ht(this.data,a,i),o.silent||this.emit("added",e,t,r,i)}addResources(e,t,r,i={silent:!1}){for(const o in r)(L(r[o])||Array.isArray(r[o]))&&this.addResource(e,t,o,r[o],{silent:!0});i.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,i,o,s={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),i=r,r=t,t=a[1]),this.addNamespaces(t);let l=Ue(this.data,a)||{};s.skipCopy||(r=JSON.parse(JSON.stringify(r))),i?Pn(l,r,o):l={...l,...r},Ht(this.data,a,l),s.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(i=>t[i]&&Object.keys(t[i]).length>0)}toJSON(){return this.data}}var En={processors:{},addPostProcessor(n){this.processors[n.name]=n},handle(n,e,t,r,i){return n.forEach(o=>{var s;e=((s=this.processors[o])==null?void 0:s.process(e,t,r,i))??e}),e}};const Ut={},qt=n=>!L(n)&&typeof n!="boolean"&&typeof n!="number";class We extends Je{constructor(e,t={}){super(),Er(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=G.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const r={...t};if(e==null)return!1;const i=this.resolve(e,r);return(i==null?void 0:i.res)!==void 0}extractFromKey(e,t){let r=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const i=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let o=t.ns||this.options.defaultNS||[];const s=r&&e.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!Dr(e,r,i);if(s&&!a){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:L(o)?[o]:o};const f=e.split(r);(r!==i||r===i&&this.options.ns.indexOf(f[0])>-1)&&(o=f.shift()),e=f.join(i)}return{key:e,namespaces:L(o)?[o]:o}}translate(e,t,r){let i=typeof t=="object"?{...t}:t;if(typeof i!="object"&&this.options.overloadTranslationOptionHandler&&(i=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(i={...i}),i||(i={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const o=i.returnDetails!==void 0?i.returnDetails:this.options.returnDetails,s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(e[e.length-1],i),f=l[l.length-1];let u=i.nsSeparator!==void 0?i.nsSeparator:this.options.nsSeparator;u===void 0&&(u=":");const c=i.lng||this.language,h=i.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((c==null?void 0:c.toLowerCase())==="cimode")return h?o?{res:`${f}${u}${a}`,usedKey:a,exactUsedKey:a,usedLng:c,usedNS:f,usedParams:this.getUsedParamsDetails(i)}:`${f}${u}${a}`:o?{res:a,usedKey:a,exactUsedKey:a,usedLng:c,usedNS:f,usedParams:this.getUsedParamsDetails(i)}:a;const g=this.resolve(e,i);let m=g==null?void 0:g.res;const y=(g==null?void 0:g.usedKey)||a,b=(g==null?void 0:g.exactUsedKey)||a,O=["[object Number]","[object Function]","[object RegExp]"],E=i.joinArrays!==void 0?i.joinArrays:this.options.joinArrays,C=!this.i18nFormat||this.i18nFormat.handleAsObject,x=i.count!==void 0&&!L(i.count),$=We.hasDefaultValue(i),A=x?this.pluralResolver.getSuffix(c,i.count,i):"",j=i.ordinal&&x?this.pluralResolver.getSuffix(c,i.count,{ordinal:!1}):"",D=x&&!i.ordinal&&i.count===0,R=D&&i[`defaultValue${this.options.pluralSeparator}zero`]||i[`defaultValue${A}`]||i[`defaultValue${j}`]||i.defaultValue;let k=m;C&&!m&&$&&(k=R);const M=qt(k),I=Object.prototype.toString.apply(k);if(C&&k&&M&&O.indexOf(I)<0&&!(L(E)&&Array.isArray(k))){if(!i.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const T=this.options.returnedObjectHandler?this.options.returnedObjectHandler(y,k,{...i,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return o?(g.res=T,g.usedParams=this.getUsedParamsDetails(i),g):T}if(s){const T=Array.isArray(k),B=T?[]:{},p=T?b:y;for(const d in k)if(Object.prototype.hasOwnProperty.call(k,d)){const v=`${p}${s}${d}`;$&&!m?B[d]=this.translate(v,{...i,defaultValue:qt(R)?R[d]:void 0,joinArrays:!1,ns:l}):B[d]=this.translate(v,{...i,joinArrays:!1,ns:l}),B[d]===v&&(B[d]=k[d])}m=B}}else if(C&&L(E)&&Array.isArray(m))m=m.join(E),m&&(m=this.extendTranslation(m,e,i,r));else{let T=!1,B=!1;!this.isValidLookup(m)&&$&&(T=!0,m=R),this.isValidLookup(m)||(B=!0,m=a);const d=(i.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&B?void 0:m,v=$&&R!==m&&this.options.updateMissing;if(B||T||v){if(this.logger.log(v?"updateKey":"missingKey",c,f,a,v?R:m),s){const N=this.resolve(a,{...i,keySeparator:!1});N&&N.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let w=[];const S=this.languageUtils.getFallbackCodes(this.options.fallbackLng,i.lng||this.language);if(this.options.saveMissingTo==="fallback"&&S&&S[0])for(let N=0;N<S.length;N++)w.push(S[N]);else this.options.saveMissingTo==="all"?w=this.languageUtils.toResolveHierarchy(i.lng||this.language):w.push(i.lng||this.language);const P=(N,H,F)=>{var V;const _=$&&F!==m?F:d;this.options.missingKeyHandler?this.options.missingKeyHandler(N,f,H,_,v,i):(V=this.backendConnector)!=null&&V.saveMissing&&this.backendConnector.saveMissing(N,f,H,_,v,i),this.emit("missingKey",N,f,H,m)};this.options.saveMissing&&(this.options.saveMissingPlurals&&x?w.forEach(N=>{const H=this.pluralResolver.getSuffixes(N,i);D&&i[`defaultValue${this.options.pluralSeparator}zero`]&&H.indexOf(`${this.options.pluralSeparator}zero`)<0&&H.push(`${this.options.pluralSeparator}zero`),H.forEach(F=>{P([N],a+F,i[`defaultValue${F}`]||R)})}):P(w,a,R))}m=this.extendTranslation(m,e,i,g,r),B&&m===a&&this.options.appendNamespaceToMissingKey&&(m=`${f}${u}${a}`),(B||T)&&this.options.parseMissingKeyHandler&&(m=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${f}${u}${a}`:a,T?m:void 0,i))}return o?(g.res=m,g.usedParams=this.getUsedParamsDetails(i),g):m}extendTranslation(e,t,r,i,o){var l,f;if((l=this.i18nFormat)!=null&&l.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=L(e)&&(((f=r==null?void 0:r.interpolation)==null?void 0:f.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(u){const g=e.match(this.interpolator.nestingRegexp);c=g&&g.length}let h=r.replace&&!L(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,r.lng||this.language||i.usedLng,r),u){const g=e.match(this.interpolator.nestingRegexp),m=g&&g.length;c<m&&(r.nest=!1)}!r.lng&&i&&i.res&&(r.lng=this.language||i.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,(...g)=>(o==null?void 0:o[0])===g[0]&&!r.context?(this.logger.warn(`It seems you are nesting recursively key: ${g[0]} in key: ${t[0]}`),null):this.translate(...g,t),r)),r.interpolation&&this.interpolator.reset()}const s=r.postProcess||this.options.postProcess,a=L(s)?[s]:s;return e!=null&&(a!=null&&a.length)&&r.applyPostProcessor!==!1&&(e=En.handle(a,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e,t={}){let r,i,o,s,a;return L(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(r))return;const f=this.extractFromKey(l,t),u=f.key;i=u;let c=f.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));const h=t.count!==void 0&&!L(t.count),g=h&&!t.ordinal&&t.count===0,m=t.context!==void 0&&(L(t.context)||typeof t.context=="number")&&t.context!=="",y=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);c.forEach(b=>{var O,E;this.isValidLookup(r)||(a=b,!Ut[`${y[0]}-${b}`]&&((O=this.utils)!=null&&O.hasLoadedNamespace)&&!((E=this.utils)!=null&&E.hasLoadedNamespace(a))&&(Ut[`${y[0]}-${b}`]=!0,this.logger.warn(`key "${i}" for languages "${y.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),y.forEach(C=>{var A;if(this.isValidLookup(r))return;s=C;const x=[u];if((A=this.i18nFormat)!=null&&A.addLookupKeys)this.i18nFormat.addLookupKeys(x,u,C,b,t);else{let j;h&&(j=this.pluralResolver.getSuffix(C,t.count,t));const D=`${this.options.pluralSeparator}zero`,R=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(h&&(x.push(u+j),t.ordinal&&j.indexOf(R)===0&&x.push(u+j.replace(R,this.options.pluralSeparator)),g&&x.push(u+D)),m){const k=`${u}${this.options.contextSeparator}${t.context}`;x.push(k),h&&(x.push(k+j),t.ordinal&&j.indexOf(R)===0&&x.push(k+j.replace(R,this.options.pluralSeparator)),g&&x.push(k+D))}}let $;for(;$=x.pop();)this.isValidLookup(r)||(o=$,r=this.getResource(C,b,$,t))}))})}),{res:r,usedKey:i,exactUsedKey:o,usedLng:s,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,r,i={}){var o;return(o=this.i18nFormat)!=null&&o.getResource?this.i18nFormat.getResource(e,t,r,i):this.resourceStore.getResource(e,t,r,i)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!L(e.replace);let i=r?e.replace:e;if(r&&typeof e.count<"u"&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!r){i={...i};for(const o of t)delete i[o]}return i}static hasDefaultValue(e){const t="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&e[r]!==void 0)return!0;return!1}}class Wt{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=G.create("languageUtils")}getScriptPartFromCode(e){if(e=Ce(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=Ce(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(L(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(r=>{if(t)return;const i=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(i))&&(t=i)}),!t&&this.options.supportedLngs&&e.forEach(r=>{if(t)return;const i=this.getScriptPartFromCode(r);if(this.isSupportedCode(i))return t=i;const o=this.getLanguagePartFromCode(r);if(this.isSupportedCode(o))return t=o;t=this.options.supportedLngs.find(s=>{if(s===o)return s;if(!(s.indexOf("-")<0&&o.indexOf("-")<0)&&(s.indexOf("-")>0&&o.indexOf("-")<0&&s.substring(0,s.indexOf("-"))===o||s.indexOf(o)===0&&o.length>1))return s})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),L(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){const r=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),i=[],o=s=>{s&&(this.isSupportedCode(s)?i.push(s):this.logger.warn(`rejecting language code not found in supportedLngs: ${s}`))};return L(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&o(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&o(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&o(this.getLanguagePartFromCode(e))):L(e)&&o(this.formatLanguageCode(e)),r.forEach(s=>{i.indexOf(s)<0&&o(this.formatLanguageCode(s))}),i}}const zt={zero:0,one:1,two:2,few:3,many:4,other:5},Kt={select:n=>n===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Br{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=G.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const r=Ce(e==="dev"?"en":e),i=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:r,type:i});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let s;try{s=new Intl.PluralRules(r,{type:i})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Kt;if(!e.match(/-|_/))return Kt;const l=this.languageUtils.getLanguagePartFromCode(e);s=this.getRule(l,t)}return this.pluralRulesCache[o]=s,s}needsPlural(e,t={}){let r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),(r==null?void 0:r.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t,r={}){return this.getSuffixes(e,r).map(i=>`${t}${i}`)}getSuffixes(e,t={}){let r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),r?r.resolvedOptions().pluralCategories.sort((i,o)=>zt[i]-zt[o]).map(i=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${i}`):[]}getSuffix(e,t,r={}){const i=this.getRule(e,r);return i?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,r))}}const Xt=(n,e,t,r=".",i=!0)=>{let o=Rr(n,e,t);return!o&&i&&L(t)&&(o=at(n,t,r),o===void 0&&(o=at(e,t,r))),o},rt=n=>n.replace(/\$/g,"$$$$");class Nr{constructor(e={}){var t;this.logger=G.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(r=>r),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:r,useRawValueToEscape:i,prefix:o,prefixEscaped:s,suffix:a,suffixEscaped:l,formatSeparator:f,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:h,nestingPrefixEscaped:g,nestingSuffix:m,nestingSuffixEscaped:y,nestingOptionsSeparator:b,maxReplaces:O,alwaysFormat:E}=e.interpolation;this.escape=t!==void 0?t:Cr,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=i!==void 0?i:!1,this.prefix=o?ue(o):s||"{{",this.suffix=a?ue(a):l||"}}",this.formatSeparator=f||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=h?ue(h):g||ue("$t("),this.nestingSuffix=m?ue(m):y||ue(")"),this.nestingOptionsSeparator=b||",",this.maxReplaces=O||1e3,this.alwaysFormat=E!==void 0?E:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,r)=>(t==null?void 0:t.source)===r?(t.lastIndex=0,t):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,i){var g;let o,s,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},f=m=>{if(m.indexOf(this.formatSeparator)<0){const E=Xt(t,l,m,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(E,void 0,r,{...i,...t,interpolationkey:m}):E}const y=m.split(this.formatSeparator),b=y.shift().trim(),O=y.join(this.formatSeparator).trim();return this.format(Xt(t,l,b,this.options.keySeparator,this.options.ignoreJSONStructure),O,r,{...i,...t,interpolationkey:b})};this.resetRegExp();const u=(i==null?void 0:i.missingInterpolationHandler)||this.options.missingInterpolationHandler,c=((g=i==null?void 0:i.interpolation)==null?void 0:g.skipOnVariables)!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:m=>rt(m)},{regex:this.regexp,safeValue:m=>this.escapeValue?rt(this.escape(m)):rt(m)}].forEach(m=>{for(a=0;o=m.regex.exec(e);){const y=o[1].trim();if(s=f(y),s===void 0)if(typeof u=="function"){const O=u(e,o,i);s=L(O)?O:""}else if(i&&Object.prototype.hasOwnProperty.call(i,y))s="";else if(c){s=o[0];continue}else this.logger.warn(`missed to pass in variable ${y} for interpolating ${e}`),s="";else!L(s)&&!this.useRawValueToEscape&&(s=Ft(s));const b=m.safeValue(s);if(e=e.replace(o[0],b),c?(m.regex.lastIndex+=s.length,m.regex.lastIndex-=o[0].length):m.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t,r={}){let i,o,s;const a=(l,f)=>{const u=this.nestingOptionsSeparator;if(l.indexOf(u)<0)return l;const c=l.split(new RegExp(`${u}[ ]*{`));let h=`{${c[1]}`;l=c[0],h=this.interpolate(h,s);const g=h.match(/'/g),m=h.match(/"/g);(((g==null?void 0:g.length)??0)%2===0&&!m||m.length%2!==0)&&(h=h.replace(/'/g,'"'));try{s=JSON.parse(h),f&&(s={...f,...s})}catch(y){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,y),`${l}${u}${h}`}return s.defaultValue&&s.defaultValue.indexOf(this.prefix)>-1&&delete s.defaultValue,l};for(;i=this.nestingRegexp.exec(e);){let l=[];s={...r},s=s.replace&&!L(s.replace)?s.replace:s,s.applyPostProcessor=!1,delete s.defaultValue;let f=!1;if(i[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(i[1])){const u=i[1].split(this.formatSeparator).map(c=>c.trim());i[1]=u.shift(),l=u,f=!0}if(o=t(a.call(this,i[1].trim(),s),s),o&&i[0]===e&&!L(o))return o;L(o)||(o=Ft(o)),o||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),o=""),f&&(o=l.reduce((u,c)=>this.format(u,c,r.lng,{...r,interpolationkey:i[1].trim()}),o.trim())),e=e.replace(i[0],o),this.regexp.lastIndex=0}return e}}const Fr=n=>{let e=n.toLowerCase().trim();const t={};if(n.indexOf("(")>-1){const r=n.split("(");e=r[0].toLowerCase().trim();const i=r[1].substring(0,r[1].length-1);e==="currency"&&i.indexOf(":")<0?t.currency||(t.currency=i.trim()):e==="relativetime"&&i.indexOf(":")<0?t.range||(t.range=i.trim()):i.split(";").forEach(s=>{if(s){const[a,...l]=s.split(":"),f=l.join(":").trim().replace(/^'+|'+$/g,""),u=a.trim();t[u]||(t[u]=f),f==="false"&&(t[u]=!1),f==="true"&&(t[u]=!0),isNaN(f)||(t[u]=parseInt(f,10))}})}return{formatName:e,formatOptions:t}},Jt=n=>{const e={};return(t,r,i)=>{let o=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(o={...o,[i.interpolationkey]:void 0});const s=r+JSON.stringify(o);let a=e[s];return a||(a=n(Ce(r),i),e[s]=a),a(t)}},Mr=n=>(e,t,r)=>n(Ce(t),r)(e);class _r{constructor(e={}){this.logger=G.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const r=t.cacheInBuiltFormats?Jt:Mr;this.formats={number:r((i,o)=>{const s=new Intl.NumberFormat(i,{...o});return a=>s.format(a)}),currency:r((i,o)=>{const s=new Intl.NumberFormat(i,{...o,style:"currency"});return a=>s.format(a)}),datetime:r((i,o)=>{const s=new Intl.DateTimeFormat(i,{...o});return a=>s.format(a)}),relativetime:r((i,o)=>{const s=new Intl.RelativeTimeFormat(i,{...o});return a=>s.format(a,o.range||"day")}),list:r((i,o)=>{const s=new Intl.ListFormat(i,{...o});return a=>s.format(a)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=Jt(t)}format(e,t,r,i={}){const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find(a=>a.indexOf(")")>-1)){const a=o.findIndex(l=>l.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,a)].join(this.formatSeparator)}return o.reduce((a,l)=>{var c;const{formatName:f,formatOptions:u}=Fr(l);if(this.formats[f]){let h=a;try{const g=((c=i==null?void 0:i.formatParams)==null?void 0:c[i.interpolationkey])||{},m=g.locale||g.lng||i.locale||i.lng||r;h=this.formats[f](a,m,{...u,...i,...g})}catch(g){this.logger.warn(g)}return h}else this.logger.warn(`there was no format function for ${f}`);return a},e)}}const Hr=(n,e)=>{n.pending[e]!==void 0&&(delete n.pending[e],n.pendingCount--)};class Vr extends Je{constructor(e,t,r,i={}){var o,s;super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=i,this.logger=G.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],(s=(o=this.backend)==null?void 0:o.init)==null||s.call(o,r,i.backend,i)}queueLoad(e,t,r,i){const o={},s={},a={},l={};return e.forEach(f=>{let u=!0;t.forEach(c=>{const h=`${f}|${c}`;!r.reload&&this.store.hasResourceBundle(f,c)?this.state[h]=2:this.state[h]<0||(this.state[h]===1?s[h]===void 0&&(s[h]=!0):(this.state[h]=1,u=!1,s[h]===void 0&&(s[h]=!0),o[h]===void 0&&(o[h]=!0),l[c]===void 0&&(l[c]=!0)))}),u||(a[f]=!0)}),(Object.keys(o).length||Object.keys(s).length)&&this.queue.push({pending:s,pendingCount:Object.keys(s).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(o),pending:Object.keys(s),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(e,t,r){const i=e.split("|"),o=i[0],s=i[1];t&&this.emit("failedLoading",o,s,t),!t&&r&&this.store.addResourceBundle(o,s,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);const a={};this.queue.forEach(l=>{Lr(l.loaded,[o],s),Hr(l,e),t&&l.errors.push(t),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(f=>{a[f]||(a[f]={});const u=l.loaded[f];u.length&&u.forEach(c=>{a[f][c]===void 0&&(a[f][c]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(e,t,r,i=0,o=this.retryTimeout,s){if(!e.length)return s(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:i,wait:o,callback:s});return}this.readingCalls++;const a=(f,u)=>{if(this.readingCalls--,this.waitingReads.length>0){const c=this.waitingReads.shift();this.read(c.lng,c.ns,c.fcName,c.tried,c.wait,c.callback)}if(f&&u&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,i+1,o*2,s)},o);return}s(f,u)},l=this.backend[r].bind(this.backend);if(l.length===2){try{const f=l(e,t);f&&typeof f.then=="function"?f.then(u=>a(null,u)).catch(a):a(null,f)}catch(f){a(f)}return}return l(e,t,a)}prepareLoading(e,t,r={},i){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();L(e)&&(e=this.languageUtils.toResolveHierarchy(e)),L(t)&&(t=[t]);const o=this.queueLoad(e,t,r,i);if(!o.toLoad.length)return o.pending.length||i(),null;o.toLoad.forEach(s=>{this.loadOne(s)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e,t=""){const r=e.split("|"),i=r[0],o=r[1];this.read(i,o,"read",void 0,void 0,(s,a)=>{s&&this.logger.warn(`${t}loading namespace ${o} for language ${i} failed`,s),!s&&a&&this.logger.log(`${t}loaded namespace ${o} for language ${i}`,a),this.loaded(e,s,a)})}saveMissing(e,t,r,i,o,s={},a=()=>{}){var l,f,u,c,h;if((f=(l=this.services)==null?void 0:l.utils)!=null&&f.hasLoadedNamespace&&!((c=(u=this.services)==null?void 0:u.utils)!=null&&c.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if((h=this.backend)!=null&&h.create){const g={...s,isUpdate:o},m=this.backend.create.bind(this.backend);if(m.length<6)try{let y;m.length===5?y=m(e,t,r,i,g):y=m(e,t,r,i),y&&typeof y.then=="function"?y.then(b=>a(null,b)).catch(a):a(null,y)}catch(y){a(y)}else m(e,t,r,i,a,g)}!e||!e[0]||this.store.addResource(e[0],t,r,i)}}}const Yt=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:n=>{let e={};if(typeof n[1]=="object"&&(e=n[1]),L(n[1])&&(e.defaultValue=n[1]),L(n[2])&&(e.tDescription=n[2]),typeof n[2]=="object"||typeof n[3]=="object"){const t=n[3]||n[2];Object.keys(t).forEach(r=>{e[r]=t[r]})}return e},interpolation:{escapeValue:!0,format:n=>n,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Gt=n=>{var e,t;return L(n.ns)&&(n.ns=[n.ns]),L(n.fallbackLng)&&(n.fallbackLng=[n.fallbackLng]),L(n.fallbackNS)&&(n.fallbackNS=[n.fallbackNS]),((t=(e=n.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(n.supportedLngs=n.supportedLngs.concat(["cimode"])),typeof n.initImmediate=="boolean"&&(n.initAsync=n.initImmediate),n},Fe=()=>{},Ur=n=>{Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach(t=>{typeof n[t]=="function"&&(n[t]=n[t].bind(n))})};class ke extends Je{constructor(e={},t){if(super(),this.options=Gt(e),this.services={},this.logger=G,this.modules={external:[]},Ur(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(L(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const r=Yt();this.options={...r,...this.options,...Gt(e)},this.options.interpolation={...r.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const i=f=>f?typeof f=="function"?new f:f:null;if(!this.options.isClone){this.modules.logger?G.init(i(this.modules.logger),this.options):G.init(null,this.options);let f;this.modules.formatter?f=this.modules.formatter:f=_r;const u=new Wt(this.options);this.store=new Vt(this.options.resources,this.options);const c=this.services;c.logger=G,c.resourceStore=this.store,c.languageUtils=u,c.pluralResolver=new Br(u,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),f&&(!this.options.interpolation.format||this.options.interpolation.format===r.interpolation.format)&&(c.formatter=i(f),c.formatter.init(c,this.options),this.options.interpolation.format=c.formatter.format.bind(c.formatter)),c.interpolator=new Nr(this.options),c.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},c.backendConnector=new Vr(i(this.modules.backend),c.resourceStore,c,this.options),c.backendConnector.on("*",(h,...g)=>{this.emit(h,...g)}),this.modules.languageDetector&&(c.languageDetector=i(this.modules.languageDetector),c.languageDetector.init&&c.languageDetector.init(c,this.options.detection,this.options)),this.modules.i18nFormat&&(c.i18nFormat=i(this.modules.i18nFormat),c.i18nFormat.init&&c.i18nFormat.init(this)),this.translator=new We(this.services,this.options),this.translator.on("*",(h,...g)=>{this.emit(h,...g)}),this.modules.external.forEach(h=>{h.init&&h.init(this)})}if(this.format=this.options.interpolation.format,t||(t=Fe),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const f=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);f.length>0&&f[0]!=="dev"&&(this.options.lng=f[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(f=>{this[f]=(...u)=>this.store[f](...u)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(f=>{this[f]=(...u)=>(this.store[f](...u),this)});const a=Ee(),l=()=>{const f=(u,c)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(c),t(u,c)};if(this.languages&&!this.isInitialized)return f(null,this.t.bind(this));this.changeLanguage(this.options.lng,f)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),a}loadResources(e,t=Fe){var o,s;let r=t;const i=L(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if((i==null?void 0:i.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const a=[],l=f=>{if(!f||f==="cimode")return;this.services.languageUtils.toResolveHierarchy(f).forEach(c=>{c!=="cimode"&&a.indexOf(c)<0&&a.push(c)})};i?l(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>l(u)),(s=(o=this.options.preload)==null?void 0:o.forEach)==null||s.call(o,f=>l(f)),this.services.backendConnector.load(a,this.options.ns,f=>{!f&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(f)})}else r(null)}reloadResources(e,t,r){const i=Ee();return typeof e=="function"&&(r=e,e=void 0),typeof t=="function"&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=Fe),this.services.backendConnector.reload(e,t,o=>{i.resolve(),r(o)}),i}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&En.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const r=this.languages[t];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const r=Ee();this.emit("languageChanging",e);const i=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},o=(a,l)=>{l?this.isLanguageChangingTo===e&&(i(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,r.resolve((...f)=>this.t(...f)),t&&t(a,(...f)=>this.t(...f))},s=a=>{var u,c;!e&&!a&&this.services.languageDetector&&(a=[]);const l=L(a)?a:a&&a[0],f=this.store.hasLanguageSomeTranslations(l)?l:this.services.languageUtils.getBestMatchFromCodes(L(a)?[a]:a);f&&(this.language||i(f),this.translator.language||this.translator.changeLanguage(f),(c=(u=this.services.languageDetector)==null?void 0:u.cacheUserLanguage)==null||c.call(u,f)),this.loadResources(f,h=>{o(h,f)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?s(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(s):this.services.languageDetector.detect(s):s(e),r}getFixedT(e,t,r){const i=(o,s,...a)=>{let l;typeof s!="object"?l=this.options.overloadTranslationOptionHandler([o,s].concat(a)):l={...s},l.lng=l.lng||i.lng,l.lngs=l.lngs||i.lngs,l.ns=l.ns||i.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||r||i.keyPrefix);const f=this.options.keySeparator||".";let u;return l.keyPrefix&&Array.isArray(o)?u=o.map(c=>`${l.keyPrefix}${f}${c}`):u=l.keyPrefix?`${l.keyPrefix}${f}${o}`:o,this.t(u,l)};return L(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=r,i}t(...e){var t;return(t=this.translator)==null?void 0:t.translate(...e)}exists(...e){var t;return(t=this.translator)==null?void 0:t.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=t.lng||this.resolvedLanguage||this.languages[0],i=this.options?this.options.fallbackLng:!1,o=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const s=(a,l)=>{const f=this.services.backendConnector.state[`${a}|${l}`];return f===-1||f===0||f===2};if(t.precheck){const a=t.precheck(this,s);if(a!==void 0)return a}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||s(r,e)&&(!i||s(o,e)))}loadNamespaces(e,t){const r=Ee();return this.options.ns?(L(e)&&(e=[e]),e.forEach(i=>{this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}),this.loadResources(i=>{r.resolve(),t&&t(i)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){const r=Ee();L(e)&&(e=[e]);const i=this.options.preload||[],o=e.filter(s=>i.indexOf(s)<0&&this.services.languageUtils.isSupportedCode(s));return o.length?(this.options.preload=i.concat(o),this.loadResources(s=>{r.resolve(),t&&t(s)}),r):(t&&t(),Promise.resolve())}dir(e){var i,o;if(e||(e=this.resolvedLanguage||(((i=this.languages)==null?void 0:i.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=((o=this.services)==null?void 0:o.languageUtils)||new Wt(Yt());return t.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new ke(e,t)}cloneInstance(e={},t=Fe){const r=e.forkResourceStore;r&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},o=new ke(i);if((e.debug!==void 0||e.prefix!==void 0)&&(o.logger=o.logger.clone(e)),["store","services","language"].forEach(a=>{o[a]=this[a]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},r){const a=Object.keys(this.store.data).reduce((l,f)=>(l[f]={...this.store.data[f]},l[f]=Object.keys(l[f]).reduce((u,c)=>(u[c]={...l[f][c]},u),l[f]),l),{});o.store=new Vt(a,i),o.services.resourceStore=o.store}return o.translator=new We(o.services,i),o.translator.on("*",(a,...l)=>{o.emit(a,...l)}),o.init(i,t),o.translator.options=i,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const W=ke.createInstance();W.createInstance=ke.createInstance;W.createInstance;W.dir;W.init;W.loadResources;W.reloadResources;W.use;W.changeLanguage;W.getFixedT;W.t;W.exists;W.setDefaultNamespace;W.hasLoadedNamespace;W.loadNamespaces;W.loadLanguages;const{slice:qr,forEach:Wr}=[];function zr(n){return Wr.call(qr.call(arguments,1),e=>{if(e)for(const t in e)n[t]===void 0&&(n[t]=e[t])}),n}function Kr(n){return typeof n!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(t=>t.test(n))}const Qt=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,Xr=function(n,e){const r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},i=encodeURIComponent(e);let o=`${n}=${i}`;if(r.maxAge>0){const s=r.maxAge-0;if(Number.isNaN(s))throw new Error("maxAge should be a Number");o+=`; Max-Age=${Math.floor(s)}`}if(r.domain){if(!Qt.test(r.domain))throw new TypeError("option domain is invalid");o+=`; Domain=${r.domain}`}if(r.path){if(!Qt.test(r.path))throw new TypeError("option path is invalid");o+=`; Path=${r.path}`}if(r.expires){if(typeof r.expires.toUTCString!="function")throw new TypeError("option expires is invalid");o+=`; Expires=${r.expires.toUTCString()}`}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"strict":o+="; SameSite=Strict";break;case"none":o+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r.partitioned&&(o+="; Partitioned"),o},Zt={create(n,e,t,r){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};t&&(i.expires=new Date,i.expires.setTime(i.expires.getTime()+t*60*1e3)),r&&(i.domain=r),document.cookie=Xr(n,e,i)},read(n){const e=`${n}=`,t=document.cookie.split(";");for(let r=0;r<t.length;r++){let i=t[r];for(;i.charAt(0)===" ";)i=i.substring(1,i.length);if(i.indexOf(e)===0)return i.substring(e.length,i.length)}return null},remove(n,e){this.create(n,"",-1,e)}};var Jr={name:"cookie",lookup(n){let{lookupCookie:e}=n;if(e&&typeof document<"u")return Zt.read(e)||void 0},cacheUserLanguage(n,e){let{lookupCookie:t,cookieMinutes:r,cookieDomain:i,cookieOptions:o}=e;t&&typeof document<"u"&&Zt.create(t,n,r,i,o)}},Yr={name:"querystring",lookup(n){var r;let{lookupQuerystring:e}=n,t;if(typeof window<"u"){let{search:i}=window.location;!window.location.search&&((r=window.location.hash)==null?void 0:r.indexOf("?"))>-1&&(i=window.location.hash.substring(window.location.hash.indexOf("?")));const s=i.substring(1).split("&");for(let a=0;a<s.length;a++){const l=s[a].indexOf("=");l>0&&s[a].substring(0,l)===e&&(t=s[a].substring(l+1))}}return t}},Gr={name:"hash",lookup(n){var i;let{lookupHash:e,lookupFromHashIndex:t}=n,r;if(typeof window<"u"){const{hash:o}=window.location;if(o&&o.length>2){const s=o.substring(1);if(e){const a=s.split("&");for(let l=0;l<a.length;l++){const f=a[l].indexOf("=");f>0&&a[l].substring(0,f)===e&&(r=a[l].substring(f+1))}}if(r)return r;if(!r&&t>-1){const a=o.match(/\/([a-zA-Z-]*)/g);return Array.isArray(a)?(i=a[typeof t=="number"?t:0])==null?void 0:i.replace("/",""):void 0}}}return r}};let ce=null;const en=()=>{if(ce!==null)return ce;try{if(ce=typeof window<"u"&&window.localStorage!==null,!ce)return!1;const n="i18next.translate.boo";window.localStorage.setItem(n,"foo"),window.localStorage.removeItem(n)}catch{ce=!1}return ce};var Qr={name:"localStorage",lookup(n){let{lookupLocalStorage:e}=n;if(e&&en())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(n,e){let{lookupLocalStorage:t}=e;t&&en()&&window.localStorage.setItem(t,n)}};let de=null;const tn=()=>{if(de!==null)return de;try{if(de=typeof window<"u"&&window.sessionStorage!==null,!de)return!1;const n="i18next.translate.boo";window.sessionStorage.setItem(n,"foo"),window.sessionStorage.removeItem(n)}catch{de=!1}return de};var Zr={name:"sessionStorage",lookup(n){let{lookupSessionStorage:e}=n;if(e&&tn())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(n,e){let{lookupSessionStorage:t}=e;t&&tn()&&window.sessionStorage.setItem(t,n)}},ei={name:"navigator",lookup(n){const e=[];if(typeof navigator<"u"){const{languages:t,userLanguage:r,language:i}=navigator;if(t)for(let o=0;o<t.length;o++)e.push(t[o]);r&&e.push(r),i&&e.push(i)}return e.length>0?e:void 0}},ti={name:"htmlTag",lookup(n){let{htmlTag:e}=n,t;const r=e||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(t=r.getAttribute("lang")),t}},ni={name:"path",lookup(n){var i;let{lookupFromPathIndex:e}=n;if(typeof window>"u")return;const t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(t)?(i=t[typeof e=="number"?e:0])==null?void 0:i.replace("/",""):void 0}},ri={name:"subdomain",lookup(n){var i,o;let{lookupFromSubdomainIndex:e}=n;const t=typeof e=="number"?e+1:1,r=typeof window<"u"&&((o=(i=window.location)==null?void 0:i.hostname)==null?void 0:o.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(r)return r[t]}};let $n=!1;try{document.cookie,$n=!0}catch{}const Ln=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];$n||Ln.splice(1,1);const ii=()=>({order:Ln,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:n=>n});class oi{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=zr(t,this.options||{},ii()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=i=>i.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=r,this.addDetector(Jr),this.addDetector(Yr),this.addDetector(Qr),this.addDetector(Zr),this.addDetector(ei),this.addDetector(ti),this.addDetector(ni),this.addDetector(ri),this.addDetector(Gr)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,t=[];return e.forEach(r=>{if(this.detectors[r]){let i=this.detectors[r].lookup(this.options);i&&typeof i=="string"&&(i=[i]),i&&(t=t.concat(i))}}),t=t.filter(r=>r!=null&&!Kr(r)).map(r=>this.options.convertDetectedLanguage(r)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}cacheUserLanguage(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach(r=>{this.detectors[r]&&this.detectors[r].cacheUserLanguage(e,this.options)}))}}oi.type="languageDetector";function st(n){"@babel/helpers - typeof";return st=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},st(n)}function Rn(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":st(XMLHttpRequest))==="object"}function ai(n){return!!n&&typeof n.then=="function"}function si(n){return ai(n)?n:Promise.resolve(n)}function nn(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,r)}return t}function rn(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?nn(Object(t),!0).forEach(function(r){li(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):nn(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function li(n,e,t){return(e=fi(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function fi(n){var e=ui(n,"string");return se(e)=="symbol"?e:e+""}function ui(n,e){if(se(n)!="object"||!n)return n;var t=n[Symbol.toPrimitive];if(t!==void 0){var r=t.call(n,e);if(se(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(n)}function se(n){"@babel/helpers - typeof";return se=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},se(n)}var te=typeof fetch=="function"?fetch:void 0;typeof global<"u"&&global.fetch?te=global.fetch:typeof window<"u"&&window.fetch&&(te=window.fetch);var je;Rn()&&(typeof global<"u"&&global.XMLHttpRequest?je=global.XMLHttpRequest:typeof window<"u"&&window.XMLHttpRequest&&(je=window.XMLHttpRequest));var ze;typeof ActiveXObject=="function"&&(typeof global<"u"&&global.ActiveXObject?ze=global.ActiveXObject:typeof window<"u"&&window.ActiveXObject&&(ze=window.ActiveXObject));typeof te!="function"&&(te=void 0);if(!te&&!je&&!ze)try{Pr(()=>Promise.resolve().then(()=>uo),void 0).then(function(n){te=n.default}).catch(function(){})}catch{}var lt=function(e,t){if(t&&se(t)==="object"){var r="";for(var i in t)r+="&"+encodeURIComponent(i)+"="+encodeURIComponent(t[i]);if(!r)return e;e=e+(e.indexOf("?")!==-1?"&":"?")+r.slice(1)}return e},on=function(e,t,r,i){var o=function(l){if(!l.ok)return r(l.statusText||"Error",{status:l.status});l.text().then(function(f){r(null,{status:l.status,data:f})}).catch(r)};if(i){var s=i(e,t);if(s instanceof Promise){s.then(o).catch(r);return}}typeof fetch=="function"?fetch(e,t).then(o).catch(r):te(e,t).then(o).catch(r)},an=!1,ci=function(e,t,r,i){e.queryStringParams&&(t=lt(t,e.queryStringParams));var o=rn({},typeof e.customHeaders=="function"?e.customHeaders():e.customHeaders);typeof window>"u"&&typeof global<"u"&&typeof global.process<"u"&&global.process.versions&&global.process.versions.node&&(o["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),r&&(o["Content-Type"]="application/json");var s=typeof e.requestOptions=="function"?e.requestOptions(r):e.requestOptions,a=rn({method:r?"POST":"GET",body:r?e.stringify(r):void 0,headers:o},an?{}:s),l=typeof e.alternateFetch=="function"&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{on(t,a,i,l)}catch(f){if(!s||Object.keys(s).length===0||!f.message||f.message.indexOf("not implemented")<0)return i(f);try{Object.keys(s).forEach(function(u){delete a[u]}),on(t,a,i,l),an=!0}catch(u){i(u)}}},di=function(e,t,r,i){r&&se(r)==="object"&&(r=lt("",r).slice(1)),e.queryStringParams&&(t=lt(t,e.queryStringParams));try{var o=je?new je:new ze("MSXML2.XMLHTTP.3.0");o.open(r?"POST":"GET",t,1),e.crossDomain||o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.withCredentials=!!e.withCredentials,r&&o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.overrideMimeType&&o.overrideMimeType("application/json");var s=e.customHeaders;if(s=typeof s=="function"?s():s,s)for(var a in s)o.setRequestHeader(a,s[a]);o.onreadystatechange=function(){o.readyState>3&&i(o.status>=400?o.statusText:null,{status:o.status,data:o.responseText})},o.send(r)}catch(l){console&&console.log(l)}},pi=function(e,t,r,i){if(typeof r=="function"&&(i=r,r=void 0),i=i||function(){},te&&t.indexOf("file:")!==0)return ci(e,t,r,i);if(Rn()||typeof ActiveXObject=="function")return di(e,t,r,i);i(new Error("No fetch and no xhr implementation found!"))};function ye(n){"@babel/helpers - typeof";return ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ye(n)}function sn(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,r)}return t}function it(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?sn(Object(t),!0).forEach(function(r){An(n,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):sn(Object(t)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(t,r))})}return n}function hi(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function gi(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,Cn(r.key),r)}}function mi(n,e,t){return e&&gi(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}function An(n,e,t){return(e=Cn(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function Cn(n){var e=yi(n,"string");return ye(e)=="symbol"?e:e+""}function yi(n,e){if(ye(n)!="object"||!n)return n;var t=n[Symbol.toPrimitive];if(t!==void 0){var r=t.call(n,e);if(ye(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(n)}var vi=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(t){return JSON.parse(t)},stringify:JSON.stringify,parsePayload:function(t,r,i){return An({},r,i||"")},parseLoadPayload:function(t,r){},request:pi,reloadInterval:typeof window<"u"?!1:60*60*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},bi=function(){function n(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};hi(this,n),this.services=e,this.options=t,this.allOptions=r,this.type="backend",this.init(e,t,r)}return mi(n,[{key:"init",value:function(t){var r=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.services=t,this.options=it(it(it({},vi()),this.options||{}),i),this.allOptions=o,this.services&&this.options.reloadInterval){var s=setInterval(function(){return r.reload()},this.options.reloadInterval);ye(s)==="object"&&typeof s.unref=="function"&&s.unref()}}},{key:"readMulti",value:function(t,r,i){this._readAny(t,t,r,r,i)}},{key:"read",value:function(t,r,i){this._readAny([t],t,[r],r,i)}},{key:"_readAny",value:function(t,r,i,o,s){var a=this,l=this.options.loadPath;typeof this.options.loadPath=="function"&&(l=this.options.loadPath(t,i)),l=si(l),l.then(function(f){if(!f)return s(null,{});var u=a.services.interpolator.interpolate(f,{lng:t.join("+"),ns:i.join("+")});a.loadUrl(u,s,r,o)})}},{key:"loadUrl",value:function(t,r,i,o){var s=this,a=typeof i=="string"?[i]:i,l=typeof o=="string"?[o]:o,f=this.options.parseLoadPayload(a,l);this.options.request(this.options,t,f,function(u,c){if(c&&(c.status>=500&&c.status<600||!c.status))return r("failed loading "+t+"; status code: "+c.status,!0);if(c&&c.status>=400&&c.status<500)return r("failed loading "+t+"; status code: "+c.status,!1);if(!c&&u&&u.message){var h=u.message.toLowerCase(),g=["failed","fetch","network","load"].find(function(b){return h.indexOf(b)>-1});if(g)return r("failed loading "+t+": "+u.message,!0)}if(u)return r(u,!1);var m,y;try{typeof c.data=="string"?m=s.options.parse(c.data,i,o):m=c.data}catch{y="failed parsing "+t+" to json"}if(y)return r(y,!1);r(null,m)})}},{key:"create",value:function(t,r,i,o,s){var a=this;if(this.options.addPath){typeof t=="string"&&(t=[t]);var l=this.options.parsePayload(r,i,o),f=0,u=[],c=[];t.forEach(function(h){var g=a.options.addPath;typeof a.options.addPath=="function"&&(g=a.options.addPath(h,r));var m=a.services.interpolator.interpolate(g,{lng:h,ns:r});a.options.request(a.options,m,l,function(y,b){f+=1,u.push(y),c.push(b),f===t.length&&typeof s=="function"&&s(u,c)})})}}},{key:"reload",value:function(){var t=this,r=this.services,i=r.backendConnector,o=r.languageUtils,s=r.logger,a=i.language;if(!(a&&a.toLowerCase()==="cimode")){var l=[],f=function(c){var h=o.toResolveHierarchy(c);h.forEach(function(g){l.indexOf(g)<0&&l.push(g)})};f(a),this.allOptions.preload&&this.allOptions.preload.forEach(function(u){return f(u)}),l.forEach(function(u){t.allOptions.ns.forEach(function(c){i.read(u,c,"read",null,null,function(h,g){h&&s.warn("loading namespace ".concat(c," for language ").concat(u," failed"),h),!h&&g&&s.log("loaded namespace ".concat(c," for language ").concat(u),g),i.loaded("".concat(u,"|").concat(c),h,g)})})})}}}])}();bi.type="backend";var kn={exports:{}},wi="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",xi=wi,Oi=xi;function jn(){}function Tn(){}Tn.resetWarningCache=jn;var Si=function(){function n(r,i,o,s,a,l){if(l!==Oi){var f=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw f.name="Invariant Violation",f}}n.isRequired=n;function e(){return n}var t={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:e,element:n,elementType:n,instanceOf:e,node:n,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:Tn,resetWarningCache:jn};return t.PropTypes=t,t};kn.exports=Si();var Pi=kn.exports;const ta=Xe(Pi);/**
 * @remix-run/router v1.22.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Te(){return Te=Object.assign?Object.assign.bind():function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Te.apply(this,arguments)}var ge;(function(n){n.Pop="POP",n.Push="PUSH",n.Replace="REPLACE"})(ge||(ge={}));const ln="popstate";function na(n){n===void 0&&(n={});function e(r,i){let{pathname:o,search:s,hash:a}=r.location;return ft("",{pathname:o,search:s,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function t(r,i){return typeof i=="string"?i:In(i)}return $i(e,t,null,n)}function me(n,e){if(n===!1||n===null||typeof n>"u")throw new Error(e)}function Dn(n,e){if(!n){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function Ei(){return Math.random().toString(36).substr(2,8)}function fn(n,e){return{usr:n.state,key:n.key,idx:e}}function ft(n,e,t,r){return t===void 0&&(t=null),Te({pathname:typeof n=="string"?n:n.pathname,search:"",hash:""},typeof e=="string"?Ye(e):e,{state:t,key:e&&e.key||r||Ei()})}function In(n){let{pathname:e="/",search:t="",hash:r=""}=n;return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ye(n){let e={};if(n){let t=n.indexOf("#");t>=0&&(e.hash=n.substr(t),n=n.substr(0,t));let r=n.indexOf("?");r>=0&&(e.search=n.substr(r),n=n.substr(0,r)),n&&(e.pathname=n)}return e}function $i(n,e,t,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,s=i.history,a=ge.Pop,l=null,f=u();f==null&&(f=0,s.replaceState(Te({},s.state,{idx:f}),""));function u(){return(s.state||{idx:null}).idx}function c(){a=ge.Pop;let b=u(),O=b==null?null:b-f;f=b,l&&l({action:a,location:y.location,delta:O})}function h(b,O){a=ge.Push;let E=ft(y.location,b,O);f=u()+1;let C=fn(E,f),x=y.createHref(E);try{s.pushState(C,"",x)}catch($){if($ instanceof DOMException&&$.name==="DataCloneError")throw $;i.location.assign(x)}o&&l&&l({action:a,location:y.location,delta:1})}function g(b,O){a=ge.Replace;let E=ft(y.location,b,O);f=u();let C=fn(E,f),x=y.createHref(E);s.replaceState(C,"",x),o&&l&&l({action:a,location:y.location,delta:0})}function m(b){let O=i.location.origin!=="null"?i.location.origin:i.location.href,E=typeof b=="string"?b:In(b);return E=E.replace(/ $/,"%20"),me(O,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,O)}let y={get action(){return a},get location(){return n(i,s)},listen(b){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(ln,c),l=b,()=>{i.removeEventListener(ln,c),l=null}},createHref(b){return e(i,b)},createURL:m,encodeLocation(b){let O=m(b);return{pathname:O.pathname,search:O.search,hash:O.hash}},push:h,replace:g,go(b){return s.go(b)}};return y}var un;(function(n){n.data="data",n.deferred="deferred",n.redirect="redirect",n.error="error"})(un||(un={}));function ra(n,e,t){return t===void 0&&(t="/"),Li(n,e,t)}function Li(n,e,t,r){let i=typeof e=="string"?Ye(e):e,o=Hi(i.pathname||"/",t);if(o==null)return null;let s=Bn(n);Ri(s);let a=null;for(let l=0;a==null&&l<s.length;++l){let f=_i(o);a=Ni(s[l],f)}return a}function Bn(n,e,t,r){e===void 0&&(e=[]),t===void 0&&(t=[]),r===void 0&&(r="");let i=(o,s,a)=>{let l={relativePath:a===void 0?o.path||"":a,caseSensitive:o.caseSensitive===!0,childrenIndex:s,route:o};l.relativePath.startsWith("/")&&(me(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let f=_e([r,l.relativePath]),u=t.concat(l);o.children&&o.children.length>0&&(me(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Bn(o.children,e,u,f)),!(o.path==null&&!o.index)&&e.push({path:f,score:Ii(f,o.index),routesMeta:u})};return n.forEach((o,s)=>{var a;if(o.path===""||!((a=o.path)!=null&&a.includes("?")))i(o,s);else for(let l of Nn(o.path))i(o,s,l)}),e}function Nn(n){let e=n.split("/");if(e.length===0)return[];let[t,...r]=e,i=t.endsWith("?"),o=t.replace(/\?$/,"");if(r.length===0)return i?[o,""]:[o];let s=Nn(r.join("/")),a=[];return a.push(...s.map(l=>l===""?o:[o,l].join("/"))),i&&a.push(...s),a.map(l=>n.startsWith("/")&&l===""?"/":l)}function Ri(n){n.sort((e,t)=>e.score!==t.score?t.score-e.score:Bi(e.routesMeta.map(r=>r.childrenIndex),t.routesMeta.map(r=>r.childrenIndex)))}const Ai=/^:[\w-]+$/,Ci=3,ki=2,ji=1,Ti=10,Di=-2,cn=n=>n==="*";function Ii(n,e){let t=n.split("/"),r=t.length;return t.some(cn)&&(r+=Di),e&&(r+=ki),t.filter(i=>!cn(i)).reduce((i,o)=>i+(Ai.test(o)?Ci:o===""?ji:Ti),r)}function Bi(n,e){return n.length===e.length&&n.slice(0,-1).every((r,i)=>r===e[i])?n[n.length-1]-e[e.length-1]:0}function Ni(n,e,t){let{routesMeta:r}=n,i={},o="/",s=[];for(let a=0;a<r.length;++a){let l=r[a],f=a===r.length-1,u=o==="/"?e:e.slice(o.length)||"/",c=Fi({path:l.relativePath,caseSensitive:l.caseSensitive,end:f},u),h=l.route;if(!c)return null;Object.assign(i,c.params),s.push({params:i,pathname:_e([o,c.pathname]),pathnameBase:Wi(_e([o,c.pathnameBase])),route:h}),c.pathnameBase!=="/"&&(o=_e([o,c.pathnameBase]))}return s}function Fi(n,e){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[t,r]=Mi(n.path,n.caseSensitive,n.end),i=e.match(t);if(!i)return null;let o=i[0],s=o.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((f,u,c)=>{let{paramName:h,isOptional:g}=u;if(h==="*"){let y=a[c]||"";s=o.slice(0,o.length-y.length).replace(/(.)\/+$/,"$1")}const m=a[c];return g&&!m?f[h]=void 0:f[h]=(m||"").replace(/%2F/g,"/"),f},{}),pathname:o,pathnameBase:s,pattern:n}}function Mi(n,e,t){e===void 0&&(e=!1),t===void 0&&(t=!0),Dn(n==="*"||!n.endsWith("*")||n.endsWith("/*"),'Route path "'+n+'" will be treated as if it were '+('"'+n.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+n.replace(/\*$/,"/*")+'".'));let r=[],i="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(r.push({paramName:"*"}),i+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):t?i+="\\/*$":n!==""&&n!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function _i(n){try{return n.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return Dn(!1,'The URL path "'+n+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),n}}function Hi(n,e){if(e==="/")return n;if(!n.toLowerCase().startsWith(e.toLowerCase()))return null;let t=e.endsWith("/")?e.length-1:e.length,r=n.charAt(t);return r&&r!=="/"?null:n.slice(t)||"/"}function Vi(n,e){e===void 0&&(e="/");let{pathname:t,search:r="",hash:i=""}=typeof n=="string"?Ye(n):n;return{pathname:t?t.startsWith("/")?t:Ui(t,e):e,search:zi(r),hash:Ki(i)}}function Ui(n,e){let t=e.replace(/\/+$/,"").split("/");return n.split("/").forEach(i=>{i===".."?t.length>1&&t.pop():i!=="."&&t.push(i)}),t.length>1?t.join("/"):"/"}function ot(n,e,t,r){return"Cannot include a '"+n+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+t+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function qi(n){return n.filter((e,t)=>t===0||e.route.path&&e.route.path.length>0)}function ia(n,e){let t=qi(n);return e?t.map((r,i)=>i===t.length-1?r.pathname:r.pathnameBase):t.map(r=>r.pathnameBase)}function oa(n,e,t,r){r===void 0&&(r=!1);let i;typeof n=="string"?i=Ye(n):(i=Te({},n),me(!i.pathname||!i.pathname.includes("?"),ot("?","pathname","search",i)),me(!i.pathname||!i.pathname.includes("#"),ot("#","pathname","hash",i)),me(!i.search||!i.search.includes("#"),ot("#","search","hash",i)));let o=n===""||i.pathname==="",s=o?"/":i.pathname,a;if(s==null)a=t;else{let c=e.length-1;if(!r&&s.startsWith("..")){let h=s.split("/");for(;h[0]==="..";)h.shift(),c-=1;i.pathname=h.join("/")}a=c>=0?e[c]:"/"}let l=Vi(i,a),f=s&&s!=="/"&&s.endsWith("/"),u=(o||s===".")&&t.endsWith("/");return!l.pathname.endsWith("/")&&(f||u)&&(l.pathname+="/"),l}const _e=n=>n.join("/").replace(/\/\/+/g,"/"),Wi=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),zi=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,Ki=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function aa(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}const Fn=["post","put","patch","delete"];new Set(Fn);const Xi=["get",...Fn];new Set(Xi);/*!
 * Jarallax v2.2.1 (https://github.com/nk-o/jarallax)
 * Copyright 2024 nK <https://nkdev.info>
 * Licensed under MIT (https://github.com/nk-o/jarallax/blob/master/LICENSE)
 */var Ji={type:"scroll",speed:.5,containerClass:"jarallax-container",imgSrc:null,imgElement:".jarallax-img",imgSize:"cover",imgPosition:"50% 50%",imgRepeat:"no-repeat",keepImg:!1,elementInViewport:null,zIndex:-100,disableParallax:!1,onScroll:null,onInit:null,onDestroy:null,onCoverImage:null,videoClass:"jarallax-video",videoSrc:null,videoStartTime:0,videoEndTime:0,videoVolume:0,videoLoop:!0,videoPlayOnlyVisible:!0,videoLazyLoading:!0,disableVideo:!1,onVideoInsert:null,onVideoWorkerInit:null};let $e;typeof window<"u"?$e=window:typeof global<"u"?$e=global:typeof self<"u"?$e=self:$e={};var z=$e;function Yi(n,e){return typeof e=="string"?z.getComputedStyle(n).getPropertyValue(e):(Object.keys(e).forEach(t=>{n.style[t]=e[t]}),n)}function Gi(n,...e){return n=n||{},Object.keys(e).forEach(t=>{e[t]&&Object.keys(e[t]).forEach(r=>{n[r]=e[t][r]})}),n}function Qi(n){const e=[];for(;n.parentElement!==null;)n=n.parentElement,n.nodeType===1&&e.push(n);return e}function Zi(n){document.readyState==="complete"||document.readyState==="interactive"?n():document.addEventListener("DOMContentLoaded",n,{capture:!0,once:!0,passive:!0})}const{navigator:eo}=z,to=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(eo.userAgent);function no(){return to}let Mn,ut,pe;function ro(){return!pe&&document.body&&(pe=document.createElement("div"),pe.style.cssText="position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;",document.body.appendChild(pe)),(pe?pe.clientHeight:0)||z.innerHeight||document.documentElement.clientHeight}function De(){Mn=z.innerWidth||document.documentElement.clientWidth,no()?ut=ro():ut=z.innerHeight||document.documentElement.clientHeight}De();z.addEventListener("resize",De);z.addEventListener("orientationchange",De);z.addEventListener("load",De);Zi(()=>{De()});function He(){return{width:Mn,height:ut}}const oe=[];function _n(){if(!oe.length)return;const{width:n,height:e}=He();oe.forEach((t,r)=>{const{instance:i,oldData:o}=t;if(!i.isVisible())return;const s=i.$item.getBoundingClientRect(),a={width:s.width,height:s.height,top:s.top,bottom:s.bottom,wndW:n,wndH:e},l=!o||o.wndW!==a.wndW||o.wndH!==a.wndH||o.width!==a.width||o.height!==a.height,f=l||!o||o.top!==a.top||o.bottom!==a.bottom;oe[r].oldData=a,l&&i.onResize(),f&&i.onScroll()}),z.requestAnimationFrame(_n)}const Hn=new z.IntersectionObserver(n=>{n.forEach(e=>{e.target.jarallax.isElementInViewport=e.isIntersecting})},{rootMargin:"50px"});function io(n){oe.push({instance:n}),oe.length===1&&z.requestAnimationFrame(_n),Hn.observe(n.options.elementInViewport||n.$item)}function oo(n){oe.forEach((e,t)=>{e.instance.instanceID===n.instanceID&&oe.splice(t,1)}),Hn.unobserve(n.options.elementInViewport||n.$item)}const{navigator:dn}=z;let pn=0;class Vn{constructor(e,t){const r=this;r.instanceID=pn,pn+=1,r.$item=e,r.defaults={...Ji};const i=r.$item.dataset||{},o={};if(Object.keys(i).forEach(a=>{const l=a.substr(0,1).toLowerCase()+a.substr(1);l&&typeof r.defaults[l]<"u"&&(o[l]=i[a])}),r.options=r.extend({},r.defaults,o,t),r.pureOptions=r.extend({},r.options),Object.keys(r.options).forEach(a=>{r.options[a]==="true"?r.options[a]=!0:r.options[a]==="false"&&(r.options[a]=!1)}),r.options.speed=Math.min(2,Math.max(-1,parseFloat(r.options.speed))),typeof r.options.disableParallax=="string"&&(r.options.disableParallax=new RegExp(r.options.disableParallax)),r.options.disableParallax instanceof RegExp){const a=r.options.disableParallax;r.options.disableParallax=()=>a.test(dn.userAgent)}if(typeof r.options.disableParallax!="function"){const a=r.options.disableParallax;r.options.disableParallax=()=>a===!0}if(typeof r.options.disableVideo=="string"&&(r.options.disableVideo=new RegExp(r.options.disableVideo)),r.options.disableVideo instanceof RegExp){const a=r.options.disableVideo;r.options.disableVideo=()=>a.test(dn.userAgent)}if(typeof r.options.disableVideo!="function"){const a=r.options.disableVideo;r.options.disableVideo=()=>a===!0}let s=r.options.elementInViewport;s&&typeof s=="object"&&typeof s.length<"u"&&([s]=s),s instanceof Element||(s=null),r.options.elementInViewport=s,r.image={src:r.options.imgSrc||null,$container:null,useImgTag:!1,position:"fixed"},r.initImg()&&r.canInitParallax()&&r.init()}css(e,t){return Yi(e,t)}extend(e,...t){return Gi(e,...t)}getWindowData(){const{width:e,height:t}=He();return{width:e,height:t,y:document.documentElement.scrollTop}}initImg(){const e=this;let t=e.options.imgElement;return t&&typeof t=="string"&&(t=e.$item.querySelector(t)),t instanceof Element||(e.options.imgSrc?(t=new Image,t.src=e.options.imgSrc):t=null),t&&(e.options.keepImg?e.image.$item=t.cloneNode(!0):(e.image.$item=t,e.image.$itemParent=t.parentNode),e.image.useImgTag=!0),e.image.$item?!0:(e.image.src===null&&(e.image.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",e.image.bgImage=e.css(e.$item,"background-image")),!(!e.image.bgImage||e.image.bgImage==="none"))}canInitParallax(){return!this.options.disableParallax()}init(){const e=this,t={position:"absolute",top:0,left:0,width:"100%",height:"100%",overflow:"hidden"};let r={pointerEvents:"none",transformStyle:"preserve-3d",backfaceVisibility:"hidden"};if(!e.options.keepImg){const i=e.$item.getAttribute("style");if(i&&e.$item.setAttribute("data-jarallax-original-styles",i),e.image.useImgTag){const o=e.image.$item.getAttribute("style");o&&e.image.$item.setAttribute("data-jarallax-original-styles",o)}}if(e.css(e.$item,"position")==="static"&&e.css(e.$item,{position:"relative"}),e.css(e.$item,"z-index")==="auto"&&e.css(e.$item,{zIndex:0}),e.image.$container=document.createElement("div"),e.css(e.image.$container,t),e.css(e.image.$container,{"z-index":e.options.zIndex}),this.image.position==="fixed"&&e.css(e.image.$container,{"-webkit-clip-path":"polygon(0 0, 100% 0, 100% 100%, 0 100%)","clip-path":"polygon(0 0, 100% 0, 100% 100%, 0 100%)"}),e.image.$container.setAttribute("id",`jarallax-container-${e.instanceID}`),e.options.containerClass&&e.image.$container.setAttribute("class",e.options.containerClass),e.$item.appendChild(e.image.$container),e.image.useImgTag?r=e.extend({"object-fit":e.options.imgSize,"object-position":e.options.imgPosition,"max-width":"none"},t,r):(e.image.$item=document.createElement("div"),e.image.src&&(r=e.extend({"background-position":e.options.imgPosition,"background-size":e.options.imgSize,"background-repeat":e.options.imgRepeat,"background-image":e.image.bgImage||`url("${e.image.src}")`},t,r))),(e.options.type==="opacity"||e.options.type==="scale"||e.options.type==="scale-opacity"||e.options.speed===1)&&(e.image.position="absolute"),e.image.position==="fixed"){const i=Qi(e.$item).filter(o=>{const s=z.getComputedStyle(o),a=s["-webkit-transform"]||s["-moz-transform"]||s.transform;return a&&a!=="none"||/(auto|scroll)/.test(s.overflow+s["overflow-y"]+s["overflow-x"])});e.image.position=i.length?"absolute":"fixed"}r.position=e.image.position,e.css(e.image.$item,r),e.image.$container.appendChild(e.image.$item),e.onResize(),e.onScroll(!0),e.options.onInit&&e.options.onInit.call(e),e.css(e.$item,"background-image")!=="none"&&e.css(e.$item,{"background-image":"none"}),io(e)}destroy(){const e=this;oo(e);const t=e.$item.getAttribute("data-jarallax-original-styles");if(e.$item.removeAttribute("data-jarallax-original-styles"),t?e.$item.setAttribute("style",t):e.$item.removeAttribute("style"),e.image.useImgTag){const r=e.image.$item.getAttribute("data-jarallax-original-styles");e.image.$item.removeAttribute("data-jarallax-original-styles"),r?e.image.$item.setAttribute("style",t):e.image.$item.removeAttribute("style"),e.image.$itemParent&&e.image.$itemParent.appendChild(e.image.$item)}e.image.$container&&e.image.$container.parentNode.removeChild(e.image.$container),e.options.onDestroy&&e.options.onDestroy.call(e),delete e.$item.jarallax}coverImage(){const e=this,{height:t}=He(),r=e.image.$container.getBoundingClientRect(),i=r.height,{speed:o}=e.options,s=e.options.type==="scroll"||e.options.type==="scroll-opacity";let a=0,l=i,f=0;return s&&(o<0?(a=o*Math.max(i,t),t<i&&(a-=o*(i-t))):a=o*(i+t),o>1?l=Math.abs(a-t):o<0?l=a/o+Math.abs(a):l+=(t-i)*(1-o),a/=2),e.parallaxScrollDistance=a,s?f=(t-l)/2:f=(i-l)/2,e.css(e.image.$item,{height:`${l}px`,marginTop:`${f}px`,left:e.image.position==="fixed"?`${r.left}px`:"0",width:`${r.width}px`}),e.options.onCoverImage&&e.options.onCoverImage.call(e),{image:{height:l,marginTop:f},container:r}}isVisible(){return this.isElementInViewport||!1}onScroll(e){const t=this;if(!e&&!t.isVisible())return;const{height:r}=He(),i=t.$item.getBoundingClientRect(),o=i.top,s=i.height,a={},l=Math.max(0,o),f=Math.max(0,s+o),u=Math.max(0,-o),c=Math.max(0,o+s-r),h=Math.max(0,s-(o+s-r)),g=Math.max(0,-o+r-s),m=1-2*((r-o)/(r+s));let y=1;if(s<r?y=1-(u||c)/s:f<=r?y=f/r:h<=r&&(y=h/r),(t.options.type==="opacity"||t.options.type==="scale-opacity"||t.options.type==="scroll-opacity")&&(a.transform="translate3d(0,0,0)",a.opacity=y),t.options.type==="scale"||t.options.type==="scale-opacity"){let b=1;t.options.speed<0?b-=t.options.speed*y:b+=t.options.speed*(1-y),a.transform=`scale(${b}) translate3d(0,0,0)`}if(t.options.type==="scroll"||t.options.type==="scroll-opacity"){let b=t.parallaxScrollDistance*m;t.image.position==="absolute"&&(b-=o),a.transform=`translate3d(0,${b}px,0)`}t.css(t.image.$item,a),t.options.onScroll&&t.options.onScroll.call(t,{section:i,beforeTop:l,beforeTopEnd:f,afterTop:u,beforeBottom:c,beforeBottomEnd:h,afterBottom:g,visiblePercent:y,fromViewportCenter:m})}onResize(){this.coverImage()}}const Un=function(n,e,...t){(typeof HTMLElement=="object"?n instanceof HTMLElement:n&&typeof n=="object"&&n!==null&&n.nodeType===1&&typeof n.nodeName=="string")&&(n=[n]);const r=n.length;let i=0,o;for(i;i<r;i+=1)if(typeof e=="object"||typeof e>"u"?n[i].jarallax||(n[i].jarallax=new Vn(n[i],e)):n[i].jarallax&&(o=n[i].jarallax[e].apply(n[i].jarallax,t)),typeof o<"u")return o;return n};Un.constructor=Vn;const sa=Un;var ao=function(n,e,t,r,i,o,s,a){if(!n){var l;if(e===void 0)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var f=[t,r,i,o,s,a],u=0;l=new Error(e.replace(/%s/g,function(){return f[u++]})),l.name="Invariant Violation"}throw l.framesToPop=1,l}},so=ao;const la=Xe(so);var lo=function(e,t,r,i){var o=r?r.call(i,e,t):void 0;if(o!==void 0)return!!o;if(e===t)return!0;if(typeof e!="object"||!e||typeof t!="object"||!t)return!1;var s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(var l=Object.prototype.hasOwnProperty.bind(t),f=0;f<s.length;f++){var u=s[f];if(!l(u))return!1;var c=e[u],h=t[u];if(o=r?r.call(i,c,h,u):void 0,o===!1||o===void 0&&c!==h)return!1}return!0};const fa=Xe(lo);var ct={exports:{}};(function(n,e){var t=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof Bt<"u"&&Bt,r=function(){function o(){this.fetch=!1,this.DOMException=t.DOMException}return o.prototype=t,new o}();(function(o){(function(s){var a=typeof o<"u"&&o||typeof self<"u"&&self||typeof a<"u"&&a,l={searchParams:"URLSearchParams"in a,iterable:"Symbol"in a&&"iterator"in Symbol,blob:"FileReader"in a&&"Blob"in a&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in a,arrayBuffer:"ArrayBuffer"in a};function f(p){return p&&DataView.prototype.isPrototypeOf(p)}if(l.arrayBuffer)var u=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],c=ArrayBuffer.isView||function(p){return p&&u.indexOf(Object.prototype.toString.call(p))>-1};function h(p){if(typeof p!="string"&&(p=String(p)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(p)||p==="")throw new TypeError('Invalid character in header field name: "'+p+'"');return p.toLowerCase()}function g(p){return typeof p!="string"&&(p=String(p)),p}function m(p){var d={next:function(){var v=p.shift();return{done:v===void 0,value:v}}};return l.iterable&&(d[Symbol.iterator]=function(){return d}),d}function y(p){this.map={},p instanceof y?p.forEach(function(d,v){this.append(v,d)},this):Array.isArray(p)?p.forEach(function(d){this.append(d[0],d[1])},this):p&&Object.getOwnPropertyNames(p).forEach(function(d){this.append(d,p[d])},this)}y.prototype.append=function(p,d){p=h(p),d=g(d);var v=this.map[p];this.map[p]=v?v+", "+d:d},y.prototype.delete=function(p){delete this.map[h(p)]},y.prototype.get=function(p){return p=h(p),this.has(p)?this.map[p]:null},y.prototype.has=function(p){return this.map.hasOwnProperty(h(p))},y.prototype.set=function(p,d){this.map[h(p)]=g(d)},y.prototype.forEach=function(p,d){for(var v in this.map)this.map.hasOwnProperty(v)&&p.call(d,this.map[v],v,this)},y.prototype.keys=function(){var p=[];return this.forEach(function(d,v){p.push(v)}),m(p)},y.prototype.values=function(){var p=[];return this.forEach(function(d){p.push(d)}),m(p)},y.prototype.entries=function(){var p=[];return this.forEach(function(d,v){p.push([v,d])}),m(p)},l.iterable&&(y.prototype[Symbol.iterator]=y.prototype.entries);function b(p){if(p.bodyUsed)return Promise.reject(new TypeError("Already read"));p.bodyUsed=!0}function O(p){return new Promise(function(d,v){p.onload=function(){d(p.result)},p.onerror=function(){v(p.error)}})}function E(p){var d=new FileReader,v=O(d);return d.readAsArrayBuffer(p),v}function C(p){var d=new FileReader,v=O(d);return d.readAsText(p),v}function x(p){for(var d=new Uint8Array(p),v=new Array(d.length),w=0;w<d.length;w++)v[w]=String.fromCharCode(d[w]);return v.join("")}function $(p){if(p.slice)return p.slice(0);var d=new Uint8Array(p.byteLength);return d.set(new Uint8Array(p)),d.buffer}function A(){return this.bodyUsed=!1,this._initBody=function(p){this.bodyUsed=this.bodyUsed,this._bodyInit=p,p?typeof p=="string"?this._bodyText=p:l.blob&&Blob.prototype.isPrototypeOf(p)?this._bodyBlob=p:l.formData&&FormData.prototype.isPrototypeOf(p)?this._bodyFormData=p:l.searchParams&&URLSearchParams.prototype.isPrototypeOf(p)?this._bodyText=p.toString():l.arrayBuffer&&l.blob&&f(p)?(this._bodyArrayBuffer=$(p.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):l.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(p)||c(p))?this._bodyArrayBuffer=$(p):this._bodyText=p=Object.prototype.toString.call(p):this._bodyText="",this.headers.get("content-type")||(typeof p=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):l.searchParams&&URLSearchParams.prototype.isPrototypeOf(p)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},l.blob&&(this.blob=function(){var p=b(this);if(p)return p;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var p=b(this);return p||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else return this.blob().then(E)}),this.text=function(){var p=b(this);if(p)return p;if(this._bodyBlob)return C(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(x(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},l.formData&&(this.formData=function(){return this.text().then(k)}),this.json=function(){return this.text().then(JSON.parse)},this}var j=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function D(p){var d=p.toUpperCase();return j.indexOf(d)>-1?d:p}function R(p,d){if(!(this instanceof R))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');d=d||{};var v=d.body;if(p instanceof R){if(p.bodyUsed)throw new TypeError("Already read");this.url=p.url,this.credentials=p.credentials,d.headers||(this.headers=new y(p.headers)),this.method=p.method,this.mode=p.mode,this.signal=p.signal,!v&&p._bodyInit!=null&&(v=p._bodyInit,p.bodyUsed=!0)}else this.url=String(p);if(this.credentials=d.credentials||this.credentials||"same-origin",(d.headers||!this.headers)&&(this.headers=new y(d.headers)),this.method=D(d.method||this.method||"GET"),this.mode=d.mode||this.mode||null,this.signal=d.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&v)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(v),(this.method==="GET"||this.method==="HEAD")&&(d.cache==="no-store"||d.cache==="no-cache")){var w=/([?&])_=[^&]*/;if(w.test(this.url))this.url=this.url.replace(w,"$1_="+new Date().getTime());else{var S=/\?/;this.url+=(S.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}R.prototype.clone=function(){return new R(this,{body:this._bodyInit})};function k(p){var d=new FormData;return p.trim().split("&").forEach(function(v){if(v){var w=v.split("="),S=w.shift().replace(/\+/g," "),P=w.join("=").replace(/\+/g," ");d.append(decodeURIComponent(S),decodeURIComponent(P))}}),d}function M(p){var d=new y,v=p.replace(/\r?\n[\t ]+/g," ");return v.split("\r").map(function(w){return w.indexOf(`
`)===0?w.substr(1,w.length):w}).forEach(function(w){var S=w.split(":"),P=S.shift().trim();if(P){var N=S.join(":").trim();d.append(P,N)}}),d}A.call(R.prototype);function I(p,d){if(!(this instanceof I))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');d||(d={}),this.type="default",this.status=d.status===void 0?200:d.status,this.ok=this.status>=200&&this.status<300,this.statusText=d.statusText===void 0?"":""+d.statusText,this.headers=new y(d.headers),this.url=d.url||"",this._initBody(p)}A.call(I.prototype),I.prototype.clone=function(){return new I(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new y(this.headers),url:this.url})},I.error=function(){var p=new I(null,{status:0,statusText:""});return p.type="error",p};var T=[301,302,303,307,308];I.redirect=function(p,d){if(T.indexOf(d)===-1)throw new RangeError("Invalid status code");return new I(null,{status:d,headers:{location:p}})},s.DOMException=a.DOMException;try{new s.DOMException}catch{s.DOMException=function(d,v){this.message=d,this.name=v;var w=Error(d);this.stack=w.stack},s.DOMException.prototype=Object.create(Error.prototype),s.DOMException.prototype.constructor=s.DOMException}function B(p,d){return new Promise(function(v,w){var S=new R(p,d);if(S.signal&&S.signal.aborted)return w(new s.DOMException("Aborted","AbortError"));var P=new XMLHttpRequest;function N(){P.abort()}P.onload=function(){var F={status:P.status,statusText:P.statusText,headers:M(P.getAllResponseHeaders()||"")};F.url="responseURL"in P?P.responseURL:F.headers.get("X-Request-URL");var _="response"in P?P.response:P.responseText;setTimeout(function(){v(new I(_,F))},0)},P.onerror=function(){setTimeout(function(){w(new TypeError("Network request failed"))},0)},P.ontimeout=function(){setTimeout(function(){w(new TypeError("Network request failed"))},0)},P.onabort=function(){setTimeout(function(){w(new s.DOMException("Aborted","AbortError"))},0)};function H(F){try{return F===""&&a.location.href?a.location.href:F}catch{return F}}P.open(S.method,H(S.url),!0),S.credentials==="include"?P.withCredentials=!0:S.credentials==="omit"&&(P.withCredentials=!1),"responseType"in P&&(l.blob?P.responseType="blob":l.arrayBuffer&&S.headers.get("Content-Type")&&S.headers.get("Content-Type").indexOf("application/octet-stream")!==-1&&(P.responseType="arraybuffer")),d&&typeof d.headers=="object"&&!(d.headers instanceof y)?Object.getOwnPropertyNames(d.headers).forEach(function(F){P.setRequestHeader(F,g(d.headers[F]))}):S.headers.forEach(function(F,_){P.setRequestHeader(_,F)}),S.signal&&(S.signal.addEventListener("abort",N),P.onreadystatechange=function(){P.readyState===4&&S.signal.removeEventListener("abort",N)}),P.send(typeof S._bodyInit>"u"?null:S._bodyInit)})}return B.polyfill=!0,a.fetch||(a.fetch=B,a.Headers=y,a.Request=R,a.Response=I),s.Headers=y,s.Request=R,s.Response=I,s.fetch=B,s})({})})(r),r.fetch.ponyfill=!0,delete r.fetch.polyfill;var i=t.fetch?t:r;e=i.fetch,e.default=i.fetch,e.fetch=i.fetch,e.Headers=i.Headers,e.Request=i.Request,e.Response=i.Response,n.exports=e})(ct,ct.exports);var qn=ct.exports;const fo=Xe(qn),uo=xr({__proto__:null,default:fo},[qn]);var U="top",K="bottom",X="right",q="left",Ge="auto",Se=[U,K,X,q],le="start",ve="end",Wn="clippingParents",gt="viewport",he="popper",zn="reference",dt=Se.reduce(function(n,e){return n.concat([e+"-"+le,e+"-"+ve])},[]),mt=[].concat(Se,[Ge]).reduce(function(n,e){return n.concat([e,e+"-"+le,e+"-"+ve])},[]),Kn="beforeRead",Xn="read",Jn="afterRead",Yn="beforeMain",Gn="main",Qn="afterMain",Zn="beforeWrite",er="write",tr="afterWrite",nr=[Kn,Xn,Jn,Yn,Gn,Qn,Zn,er,tr];function Z(n){return n?(n.nodeName||"").toLowerCase():null}function J(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var e=n.ownerDocument;return e&&e.defaultView||window}return n}function fe(n){var e=J(n).Element;return n instanceof e||n instanceof Element}function Y(n){var e=J(n).HTMLElement;return n instanceof e||n instanceof HTMLElement}function yt(n){if(typeof ShadowRoot>"u")return!1;var e=J(n).ShadowRoot;return n instanceof e||n instanceof ShadowRoot}function co(n){var e=n.state;Object.keys(e.elements).forEach(function(t){var r=e.styles[t]||{},i=e.attributes[t]||{},o=e.elements[t];!Y(o)||!Z(o)||(Object.assign(o.style,r),Object.keys(i).forEach(function(s){var a=i[s];a===!1?o.removeAttribute(s):o.setAttribute(s,a===!0?"":a)}))})}function po(n){var e=n.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach(function(r){var i=e.elements[r],o=e.attributes[r]||{},s=Object.keys(e.styles.hasOwnProperty(r)?e.styles[r]:t[r]),a=s.reduce(function(l,f){return l[f]="",l},{});!Y(i)||!Z(i)||(Object.assign(i.style,a),Object.keys(o).forEach(function(l){i.removeAttribute(l)}))})}}const vt={name:"applyStyles",enabled:!0,phase:"write",fn:co,effect:po,requires:["computeStyles"]};function Q(n){return n.split("-")[0]}var ae=Math.max,Ke=Math.min,be=Math.round;function pt(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function rr(){return!/^((?!chrome|android).)*safari/i.test(pt())}function we(n,e,t){e===void 0&&(e=!1),t===void 0&&(t=!1);var r=n.getBoundingClientRect(),i=1,o=1;e&&Y(n)&&(i=n.offsetWidth>0&&be(r.width)/n.offsetWidth||1,o=n.offsetHeight>0&&be(r.height)/n.offsetHeight||1);var s=fe(n)?J(n):window,a=s.visualViewport,l=!rr()&&t,f=(r.left+(l&&a?a.offsetLeft:0))/i,u=(r.top+(l&&a?a.offsetTop:0))/o,c=r.width/i,h=r.height/o;return{width:c,height:h,top:u,right:f+c,bottom:u+h,left:f,x:f,y:u}}function bt(n){var e=we(n),t=n.offsetWidth,r=n.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:n.offsetLeft,y:n.offsetTop,width:t,height:r}}function ir(n,e){var t=e.getRootNode&&e.getRootNode();if(n.contains(e))return!0;if(t&&yt(t)){var r=e;do{if(r&&n.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ee(n){return J(n).getComputedStyle(n)}function ho(n){return["table","td","th"].indexOf(Z(n))>=0}function ne(n){return((fe(n)?n.ownerDocument:n.document)||window.document).documentElement}function Qe(n){return Z(n)==="html"?n:n.assignedSlot||n.parentNode||(yt(n)?n.host:null)||ne(n)}function hn(n){return!Y(n)||ee(n).position==="fixed"?null:n.offsetParent}function go(n){var e=/firefox/i.test(pt()),t=/Trident/i.test(pt());if(t&&Y(n)){var r=ee(n);if(r.position==="fixed")return null}var i=Qe(n);for(yt(i)&&(i=i.host);Y(i)&&["html","body"].indexOf(Z(i))<0;){var o=ee(i);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||e&&o.willChange==="filter"||e&&o.filter&&o.filter!=="none")return i;i=i.parentNode}return null}function Ie(n){for(var e=J(n),t=hn(n);t&&ho(t)&&ee(t).position==="static";)t=hn(t);return t&&(Z(t)==="html"||Z(t)==="body"&&ee(t).position==="static")?e:t||go(n)||e}function wt(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function Re(n,e,t){return ae(n,Ke(e,t))}function mo(n,e,t){var r=Re(n,e,t);return r>t?t:r}function or(){return{top:0,right:0,bottom:0,left:0}}function ar(n){return Object.assign({},or(),n)}function sr(n,e){return e.reduce(function(t,r){return t[r]=n,t},{})}var yo=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,ar(typeof e!="number"?e:sr(e,Se))};function vo(n){var e,t=n.state,r=n.name,i=n.options,o=t.elements.arrow,s=t.modifiersData.popperOffsets,a=Q(t.placement),l=wt(a),f=[q,X].indexOf(a)>=0,u=f?"height":"width";if(!(!o||!s)){var c=yo(i.padding,t),h=bt(o),g=l==="y"?U:q,m=l==="y"?K:X,y=t.rects.reference[u]+t.rects.reference[l]-s[l]-t.rects.popper[u],b=s[l]-t.rects.reference[l],O=Ie(o),E=O?l==="y"?O.clientHeight||0:O.clientWidth||0:0,C=y/2-b/2,x=c[g],$=E-h[u]-c[m],A=E/2-h[u]/2+C,j=Re(x,A,$),D=l;t.modifiersData[r]=(e={},e[D]=j,e.centerOffset=j-A,e)}}function bo(n){var e=n.state,t=n.options,r=t.element,i=r===void 0?"[data-popper-arrow]":r;i!=null&&(typeof i=="string"&&(i=e.elements.popper.querySelector(i),!i)||ir(e.elements.popper,i)&&(e.elements.arrow=i))}const lr={name:"arrow",enabled:!0,phase:"main",fn:vo,effect:bo,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function xe(n){return n.split("-")[1]}var wo={top:"auto",right:"auto",bottom:"auto",left:"auto"};function xo(n,e){var t=n.x,r=n.y,i=e.devicePixelRatio||1;return{x:be(t*i)/i||0,y:be(r*i)/i||0}}function gn(n){var e,t=n.popper,r=n.popperRect,i=n.placement,o=n.variation,s=n.offsets,a=n.position,l=n.gpuAcceleration,f=n.adaptive,u=n.roundOffsets,c=n.isFixed,h=s.x,g=h===void 0?0:h,m=s.y,y=m===void 0?0:m,b=typeof u=="function"?u({x:g,y}):{x:g,y};g=b.x,y=b.y;var O=s.hasOwnProperty("x"),E=s.hasOwnProperty("y"),C=q,x=U,$=window;if(f){var A=Ie(t),j="clientHeight",D="clientWidth";if(A===J(t)&&(A=ne(t),ee(A).position!=="static"&&a==="absolute"&&(j="scrollHeight",D="scrollWidth")),A=A,i===U||(i===q||i===X)&&o===ve){x=K;var R=c&&A===$&&$.visualViewport?$.visualViewport.height:A[j];y-=R-r.height,y*=l?1:-1}if(i===q||(i===U||i===K)&&o===ve){C=X;var k=c&&A===$&&$.visualViewport?$.visualViewport.width:A[D];g-=k-r.width,g*=l?1:-1}}var M=Object.assign({position:a},f&&wo),I=u===!0?xo({x:g,y},J(t)):{x:g,y};if(g=I.x,y=I.y,l){var T;return Object.assign({},M,(T={},T[x]=E?"0":"",T[C]=O?"0":"",T.transform=($.devicePixelRatio||1)<=1?"translate("+g+"px, "+y+"px)":"translate3d("+g+"px, "+y+"px, 0)",T))}return Object.assign({},M,(e={},e[x]=E?y+"px":"",e[C]=O?g+"px":"",e.transform="",e))}function Oo(n){var e=n.state,t=n.options,r=t.gpuAcceleration,i=r===void 0?!0:r,o=t.adaptive,s=o===void 0?!0:o,a=t.roundOffsets,l=a===void 0?!0:a,f={placement:Q(e.placement),variation:xe(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,gn(Object.assign({},f,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:s,roundOffsets:l})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,gn(Object.assign({},f,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}const xt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Oo,data:{}};var Me={passive:!0};function So(n){var e=n.state,t=n.instance,r=n.options,i=r.scroll,o=i===void 0?!0:i,s=r.resize,a=s===void 0?!0:s,l=J(e.elements.popper),f=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&f.forEach(function(u){u.addEventListener("scroll",t.update,Me)}),a&&l.addEventListener("resize",t.update,Me),function(){o&&f.forEach(function(u){u.removeEventListener("scroll",t.update,Me)}),a&&l.removeEventListener("resize",t.update,Me)}}const Ot={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:So,data:{}};var Po={left:"right",right:"left",bottom:"top",top:"bottom"};function Ve(n){return n.replace(/left|right|bottom|top/g,function(e){return Po[e]})}var Eo={start:"end",end:"start"};function mn(n){return n.replace(/start|end/g,function(e){return Eo[e]})}function St(n){var e=J(n),t=e.pageXOffset,r=e.pageYOffset;return{scrollLeft:t,scrollTop:r}}function Pt(n){return we(ne(n)).left+St(n).scrollLeft}function $o(n,e){var t=J(n),r=ne(n),i=t.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;var f=rr();(f||!f&&e==="fixed")&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a+Pt(n),y:l}}function Lo(n){var e,t=ne(n),r=St(n),i=(e=n.ownerDocument)==null?void 0:e.body,o=ae(t.scrollWidth,t.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=ae(t.scrollHeight,t.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+Pt(n),l=-r.scrollTop;return ee(i||t).direction==="rtl"&&(a+=ae(t.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}function Et(n){var e=ee(n),t=e.overflow,r=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+i+r)}function fr(n){return["html","body","#document"].indexOf(Z(n))>=0?n.ownerDocument.body:Y(n)&&Et(n)?n:fr(Qe(n))}function Ae(n,e){var t;e===void 0&&(e=[]);var r=fr(n),i=r===((t=n.ownerDocument)==null?void 0:t.body),o=J(r),s=i?[o].concat(o.visualViewport||[],Et(r)?r:[]):r,a=e.concat(s);return i?a:a.concat(Ae(Qe(s)))}function ht(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function Ro(n,e){var t=we(n,!1,e==="fixed");return t.top=t.top+n.clientTop,t.left=t.left+n.clientLeft,t.bottom=t.top+n.clientHeight,t.right=t.left+n.clientWidth,t.width=n.clientWidth,t.height=n.clientHeight,t.x=t.left,t.y=t.top,t}function yn(n,e,t){return e===gt?ht($o(n,t)):fe(e)?Ro(e,t):ht(Lo(ne(n)))}function Ao(n){var e=Ae(Qe(n)),t=["absolute","fixed"].indexOf(ee(n).position)>=0,r=t&&Y(n)?Ie(n):n;return fe(r)?e.filter(function(i){return fe(i)&&ir(i,r)&&Z(i)!=="body"}):[]}function Co(n,e,t,r){var i=e==="clippingParents"?Ao(n):[].concat(e),o=[].concat(i,[t]),s=o[0],a=o.reduce(function(l,f){var u=yn(n,f,r);return l.top=ae(u.top,l.top),l.right=Ke(u.right,l.right),l.bottom=Ke(u.bottom,l.bottom),l.left=ae(u.left,l.left),l},yn(n,s,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function ur(n){var e=n.reference,t=n.element,r=n.placement,i=r?Q(r):null,o=r?xe(r):null,s=e.x+e.width/2-t.width/2,a=e.y+e.height/2-t.height/2,l;switch(i){case U:l={x:s,y:e.y-t.height};break;case K:l={x:s,y:e.y+e.height};break;case X:l={x:e.x+e.width,y:a};break;case q:l={x:e.x-t.width,y:a};break;default:l={x:e.x,y:e.y}}var f=i?wt(i):null;if(f!=null){var u=f==="y"?"height":"width";switch(o){case le:l[f]=l[f]-(e[u]/2-t[u]/2);break;case ve:l[f]=l[f]+(e[u]/2-t[u]/2);break}}return l}function Oe(n,e){e===void 0&&(e={});var t=e,r=t.placement,i=r===void 0?n.placement:r,o=t.strategy,s=o===void 0?n.strategy:o,a=t.boundary,l=a===void 0?Wn:a,f=t.rootBoundary,u=f===void 0?gt:f,c=t.elementContext,h=c===void 0?he:c,g=t.altBoundary,m=g===void 0?!1:g,y=t.padding,b=y===void 0?0:y,O=ar(typeof b!="number"?b:sr(b,Se)),E=h===he?zn:he,C=n.rects.popper,x=n.elements[m?E:h],$=Co(fe(x)?x:x.contextElement||ne(n.elements.popper),l,u,s),A=we(n.elements.reference),j=ur({reference:A,element:C,placement:i}),D=ht(Object.assign({},C,j)),R=h===he?D:A,k={top:$.top-R.top+O.top,bottom:R.bottom-$.bottom+O.bottom,left:$.left-R.left+O.left,right:R.right-$.right+O.right},M=n.modifiersData.offset;if(h===he&&M){var I=M[i];Object.keys(k).forEach(function(T){var B=[X,K].indexOf(T)>=0?1:-1,p=[U,K].indexOf(T)>=0?"y":"x";k[T]+=I[p]*B})}return k}function ko(n,e){e===void 0&&(e={});var t=e,r=t.placement,i=t.boundary,o=t.rootBoundary,s=t.padding,a=t.flipVariations,l=t.allowedAutoPlacements,f=l===void 0?mt:l,u=xe(r),c=u?a?dt:dt.filter(function(m){return xe(m)===u}):Se,h=c.filter(function(m){return f.indexOf(m)>=0});h.length===0&&(h=c);var g=h.reduce(function(m,y){return m[y]=Oe(n,{placement:y,boundary:i,rootBoundary:o,padding:s})[Q(y)],m},{});return Object.keys(g).sort(function(m,y){return g[m]-g[y]})}function jo(n){if(Q(n)===Ge)return[];var e=Ve(n);return[mn(n),e,mn(e)]}function To(n){var e=n.state,t=n.options,r=n.name;if(!e.modifiersData[r]._skip){for(var i=t.mainAxis,o=i===void 0?!0:i,s=t.altAxis,a=s===void 0?!0:s,l=t.fallbackPlacements,f=t.padding,u=t.boundary,c=t.rootBoundary,h=t.altBoundary,g=t.flipVariations,m=g===void 0?!0:g,y=t.allowedAutoPlacements,b=e.options.placement,O=Q(b),E=O===b,C=l||(E||!m?[Ve(b)]:jo(b)),x=[b].concat(C).reduce(function(_,V){return _.concat(Q(V)===Ge?ko(e,{placement:V,boundary:u,rootBoundary:c,padding:f,flipVariations:m,allowedAutoPlacements:y}):V)},[]),$=e.rects.reference,A=e.rects.popper,j=new Map,D=!0,R=x[0],k=0;k<x.length;k++){var M=x[k],I=Q(M),T=xe(M)===le,B=[U,K].indexOf(I)>=0,p=B?"width":"height",d=Oe(e,{placement:M,boundary:u,rootBoundary:c,altBoundary:h,padding:f}),v=B?T?X:q:T?K:U;$[p]>A[p]&&(v=Ve(v));var w=Ve(v),S=[];if(o&&S.push(d[I]<=0),a&&S.push(d[v]<=0,d[w]<=0),S.every(function(_){return _})){R=M,D=!1;break}j.set(M,S)}if(D)for(var P=m?3:1,N=function(V){var Pe=x.find(function(Be){var re=j.get(Be);if(re)return re.slice(0,V).every(function(et){return et})});if(Pe)return R=Pe,"break"},H=P;H>0;H--){var F=N(H);if(F==="break")break}e.placement!==R&&(e.modifiersData[r]._skip=!0,e.placement=R,e.reset=!0)}}const cr={name:"flip",enabled:!0,phase:"main",fn:To,requiresIfExists:["offset"],data:{_skip:!1}};function vn(n,e,t){return t===void 0&&(t={x:0,y:0}),{top:n.top-e.height-t.y,right:n.right-e.width+t.x,bottom:n.bottom-e.height+t.y,left:n.left-e.width-t.x}}function bn(n){return[U,X,K,q].some(function(e){return n[e]>=0})}function Do(n){var e=n.state,t=n.name,r=e.rects.reference,i=e.rects.popper,o=e.modifiersData.preventOverflow,s=Oe(e,{elementContext:"reference"}),a=Oe(e,{altBoundary:!0}),l=vn(s,r),f=vn(a,i,o),u=bn(l),c=bn(f);e.modifiersData[t]={referenceClippingOffsets:l,popperEscapeOffsets:f,isReferenceHidden:u,hasPopperEscaped:c},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":c})}const dr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Do};function Io(n,e,t){var r=Q(n),i=[q,U].indexOf(r)>=0?-1:1,o=typeof t=="function"?t(Object.assign({},e,{placement:n})):t,s=o[0],a=o[1];return s=s||0,a=(a||0)*i,[q,X].indexOf(r)>=0?{x:a,y:s}:{x:s,y:a}}function Bo(n){var e=n.state,t=n.options,r=n.name,i=t.offset,o=i===void 0?[0,0]:i,s=mt.reduce(function(u,c){return u[c]=Io(c,e.rects,o),u},{}),a=s[e.placement],l=a.x,f=a.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=f),e.modifiersData[r]=s}const pr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Bo};function No(n){var e=n.state,t=n.name;e.modifiersData[t]=ur({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})}const $t={name:"popperOffsets",enabled:!0,phase:"read",fn:No,data:{}};function Fo(n){return n==="x"?"y":"x"}function Mo(n){var e=n.state,t=n.options,r=n.name,i=t.mainAxis,o=i===void 0?!0:i,s=t.altAxis,a=s===void 0?!1:s,l=t.boundary,f=t.rootBoundary,u=t.altBoundary,c=t.padding,h=t.tether,g=h===void 0?!0:h,m=t.tetherOffset,y=m===void 0?0:m,b=Oe(e,{boundary:l,rootBoundary:f,padding:c,altBoundary:u}),O=Q(e.placement),E=xe(e.placement),C=!E,x=wt(O),$=Fo(x),A=e.modifiersData.popperOffsets,j=e.rects.reference,D=e.rects.popper,R=typeof y=="function"?y(Object.assign({},e.rects,{placement:e.placement})):y,k=typeof R=="number"?{mainAxis:R,altAxis:R}:Object.assign({mainAxis:0,altAxis:0},R),M=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,I={x:0,y:0};if(A){if(o){var T,B=x==="y"?U:q,p=x==="y"?K:X,d=x==="y"?"height":"width",v=A[x],w=v+b[B],S=v-b[p],P=g?-D[d]/2:0,N=E===le?j[d]:D[d],H=E===le?-D[d]:-j[d],F=e.elements.arrow,_=g&&F?bt(F):{width:0,height:0},V=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:or(),Pe=V[B],Be=V[p],re=Re(0,j[d],_[d]),et=C?j[d]/2-P-re-Pe-k.mainAxis:N-re-Pe-k.mainAxis,gr=C?-j[d]/2+P+re+Be+k.mainAxis:H+re+Be+k.mainAxis,tt=e.elements.arrow&&Ie(e.elements.arrow),mr=tt?x==="y"?tt.clientTop||0:tt.clientLeft||0:0,Lt=(T=M==null?void 0:M[x])!=null?T:0,yr=v+et-Lt-mr,vr=v+gr-Lt,Rt=Re(g?Ke(w,yr):w,v,g?ae(S,vr):S);A[x]=Rt,I[x]=Rt-v}if(a){var At,br=x==="x"?U:q,wr=x==="x"?K:X,ie=A[$],Ne=$==="y"?"height":"width",Ct=ie+b[br],kt=ie-b[wr],nt=[U,q].indexOf(O)!==-1,jt=(At=M==null?void 0:M[$])!=null?At:0,Tt=nt?Ct:ie-j[Ne]-D[Ne]-jt+k.altAxis,Dt=nt?ie+j[Ne]+D[Ne]-jt-k.altAxis:kt,It=g&&nt?mo(Tt,ie,Dt):Re(g?Tt:Ct,ie,g?Dt:kt);A[$]=It,I[$]=It-ie}e.modifiersData[r]=I}}const hr={name:"preventOverflow",enabled:!0,phase:"main",fn:Mo,requiresIfExists:["offset"]};function _o(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function Ho(n){return n===J(n)||!Y(n)?St(n):_o(n)}function Vo(n){var e=n.getBoundingClientRect(),t=be(e.width)/n.offsetWidth||1,r=be(e.height)/n.offsetHeight||1;return t!==1||r!==1}function Uo(n,e,t){t===void 0&&(t=!1);var r=Y(e),i=Y(e)&&Vo(e),o=ne(e),s=we(n,i,t),a={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!t)&&((Z(e)!=="body"||Et(o))&&(a=Ho(e)),Y(e)?(l=we(e,!0),l.x+=e.clientLeft,l.y+=e.clientTop):o&&(l.x=Pt(o))),{x:s.left+a.scrollLeft-l.x,y:s.top+a.scrollTop-l.y,width:s.width,height:s.height}}function qo(n){var e=new Map,t=new Set,r=[];n.forEach(function(o){e.set(o.name,o)});function i(o){t.add(o.name);var s=[].concat(o.requires||[],o.requiresIfExists||[]);s.forEach(function(a){if(!t.has(a)){var l=e.get(a);l&&i(l)}}),r.push(o)}return n.forEach(function(o){t.has(o.name)||i(o)}),r}function Wo(n){var e=qo(n);return nr.reduce(function(t,r){return t.concat(e.filter(function(i){return i.phase===r}))},[])}function zo(n){var e;return function(){return e||(e=new Promise(function(t){Promise.resolve().then(function(){e=void 0,t(n())})})),e}}function Ko(n){var e=n.reduce(function(t,r){var i=t[r.name];return t[r.name]=i?Object.assign({},i,r,{options:Object.assign({},i.options,r.options),data:Object.assign({},i.data,r.data)}):r,t},{});return Object.keys(e).map(function(t){return e[t]})}var wn={placement:"bottom",modifiers:[],strategy:"absolute"};function xn(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];return!e.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function Ze(n){n===void 0&&(n={});var e=n,t=e.defaultModifiers,r=t===void 0?[]:t,i=e.defaultOptions,o=i===void 0?wn:i;return function(a,l,f){f===void 0&&(f=o);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},wn,o),modifiersData:{},elements:{reference:a,popper:l},attributes:{},styles:{}},c=[],h=!1,g={state:u,setOptions:function(O){var E=typeof O=="function"?O(u.options):O;y(),u.options=Object.assign({},o,u.options,E),u.scrollParents={reference:fe(a)?Ae(a):a.contextElement?Ae(a.contextElement):[],popper:Ae(l)};var C=Wo(Ko([].concat(r,u.options.modifiers)));return u.orderedModifiers=C.filter(function(x){return x.enabled}),m(),g.update()},forceUpdate:function(){if(!h){var O=u.elements,E=O.reference,C=O.popper;if(xn(E,C)){u.rects={reference:Uo(E,Ie(C),u.options.strategy==="fixed"),popper:bt(C)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(k){return u.modifiersData[k.name]=Object.assign({},k.data)});for(var x=0;x<u.orderedModifiers.length;x++){if(u.reset===!0){u.reset=!1,x=-1;continue}var $=u.orderedModifiers[x],A=$.fn,j=$.options,D=j===void 0?{}:j,R=$.name;typeof A=="function"&&(u=A({state:u,options:D,name:R,instance:g})||u)}}}},update:zo(function(){return new Promise(function(b){g.forceUpdate(),b(u)})}),destroy:function(){y(),h=!0}};if(!xn(a,l))return g;g.setOptions(f).then(function(b){!h&&f.onFirstUpdate&&f.onFirstUpdate(b)});function m(){u.orderedModifiers.forEach(function(b){var O=b.name,E=b.options,C=E===void 0?{}:E,x=b.effect;if(typeof x=="function"){var $=x({state:u,name:O,instance:g,options:C}),A=function(){};c.push($||A)}})}function y(){c.forEach(function(b){return b()}),c=[]}return g}}var Xo=Ze(),Jo=[Ot,$t,xt,vt],Yo=Ze({defaultModifiers:Jo}),Go=[Ot,$t,xt,vt,pr,cr,hr,lr,dr],Qo=Ze({defaultModifiers:Go});const ua=Object.freeze(Object.defineProperty({__proto__:null,afterMain:Qn,afterRead:Jn,afterWrite:tr,applyStyles:vt,arrow:lr,auto:Ge,basePlacements:Se,beforeMain:Yn,beforeRead:Kn,beforeWrite:Zn,bottom:K,clippingParents:Wn,computeStyles:xt,createPopper:Qo,createPopperBase:Xo,createPopperLite:Yo,detectOverflow:Oe,end:ve,eventListeners:Ot,flip:cr,hide:dr,left:q,main:Gn,modifierPhases:nr,offset:pr,placements:mt,popper:he,popperGenerator:Ze,popperOffsets:$t,preventOverflow:hr,read:Xn,reference:zn,right:X,start:le,top:U,variationPlacements:dt,viewport:gt,write:er},Symbol.toStringTag,{value:"Module"}));export{ge as A,bi as B,ta as P,Pr as _,oi as a,me as b,_e as c,Hi as d,aa as e,In as f,ia as g,na as h,W as i,sa as j,la as k,fa as l,ra as m,ua as n,Qo as o,Ye as p,oa as r,ea as s};
//# sourceMappingURL=vendor-misc-C-eNx6BD.js.map
