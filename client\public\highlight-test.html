<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Highlight.js Test</title>
    <!-- Include highlight.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/vs2015.min.css">
    <!-- Include highlight.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1e1e1e;
            color: #d4d4d4;
        }
        
        h1 {
            color: #569cd6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #333;
            border-radius: 8px;
        }
        
        pre {
            background: #1e1e1e !important;
            border: 2px solid yellow !important;
            border-radius: 8px !important;
            padding: 16px !important;
            overflow-x: auto;
        }
        
        code {
            font-family: 'Consolas', 'Courier New', monospace !important;
            font-size: 14px !important;
        }
        
        /* Force highlight.js colors */
        .hljs-keyword { color: #569cd6 !important; }
        .hljs-string { color: #ce9178 !important; }
        .hljs-number { color: #b5cea8 !important; }
        .hljs-comment { color: #6a9955 !important; }
        .hljs-tag { color: #569cd6 !important; }
        .hljs-name { color: #4fc1ff !important; }
        .hljs-attr { color: #9cdcfe !important; }
        .hljs-function { color: #dcdcaa !important; }
        .hljs-title { color: #dcdcaa !important; }
        .hljs-variable { color: #9cdcfe !important; }
        .hljs-property { color: #9cdcfe !important; }
    </style>
</head>
<body>
    <h1>Highlight.js Test Page</h1>
    
    <div class="test-section">
        <h2>Test 1: HTML Code (should have yellow border around pre)</h2>
        <pre><code class="language-html">&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;title&gt;Age Checker&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;h1&gt;Club Entry Checker&lt;/h1&gt;
  &lt;input type="number" id="ageInput" placeholder="Enter your age"&gt;
  &lt;button onclick="checkAge()"&gt;Check Age&lt;/button&gt;
  &lt;p id="result"&gt;&lt;/p&gt;
  &lt;script&gt;
    function checkAge() {
      let age = document.getElementById("ageInput").value;
      let message = age &gt;= 18 ? "Welcome to the club!" : "Sorry, you're too young!";
      document.getElementById("result").innerText = message;
    }
  &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
    </div>

    <div class="test-section">
        <h2>Test 2: JavaScript Code</h2>
        <pre><code class="language-javascript">function calculateAge(birthYear) {
    const currentYear = new Date().getFullYear();
    const age = currentYear - birthYear;
    
    if (age >= 18) {
        console.log("You are an adult!");
        return true;
    } else {
        console.log("You are a minor.");
        return false;
    }
}

// Test the function
const isAdult = calculateAge(1990);
console.log("Is adult:", isAdult);</code></pre>
    </div>

    <div class="test-section">
        <h2>Test 3: CSS Code</h2>
        <pre><code class="language-css">.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.button {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}</code></pre>
    </div>

    <div class="test-section">
        <h2>Debug Info</h2>
        <div id="debug-info">
            <p>Highlight.js version: <span id="hljs-version"></span></p>
            <p>Number of code blocks found: <span id="code-blocks-count"></span></p>
            <p>Highlighting status: <span id="highlighting-status"></span></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Test page loaded');
            
            // Debug info
            document.getElementById('hljs-version').textContent = hljs.versionString || 'Unknown';
            
            const codeBlocks = document.querySelectorAll('pre code');
            document.getElementById('code-blocks-count').textContent = codeBlocks.length;
            
            // Apply highlighting
            let highlightedCount = 0;
            codeBlocks.forEach((block, index) => {
                console.log(`🔍 Processing block ${index}:`, block);
                console.log(`🔍 Block classes before:`, block.className);
                
                try {
                    hljs.highlightElement(block);
                    highlightedCount++;
                    console.log(`✅ Block ${index} highlighted successfully`);
                    console.log(`🔍 Block classes after:`, block.className);
                    
                    // Check for highlight classes
                    const highlightSpans = block.querySelectorAll('[class*="hljs-"]');
                    console.log(`🔍 Found ${highlightSpans.length} highlighted elements in block ${index}`);
                    
                } catch (error) {
                    console.error(`❌ Error highlighting block ${index}:`, error);
                }
            });
            
            document.getElementById('highlighting-status').textContent = 
                `${highlightedCount}/${codeBlocks.length} blocks highlighted successfully`;
            
            console.log('🔍 Highlighting complete');
        });
    </script>
</body>
</html>
