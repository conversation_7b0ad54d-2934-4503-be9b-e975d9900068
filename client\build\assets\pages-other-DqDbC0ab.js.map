{"version": 3, "file": "pages-other-DqDbC0ab.js", "sources": ["../../src/pages/home/<USER>", "../../src/pages/portfolio/page.jsx", "../../src/pages/blogs/page.jsx", "../../src/pages/portfolio-single/page.jsx", "../../src/pages/blog-single/page.jsx", "../../src/pages/otherPages/page.jsx", "../../src/pages/AdminLogin.jsx", "../../src/pages/AdminDashboard.jsx", "../../src/pages/AdminBlogPosts.jsx", "../../src/pages/AdminBlogEditor.jsx", "../../src/pages/AdminCategories.jsx", "../../src/pages/AdminTags.jsx"], "sourcesContent": ["import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport Home from \"@/components/home\";\nimport Hero from \"@/components/home/<USER>\";\nimport { menuItems } from \"@/data/menu\";\nimport ParallaxContainer from \"@/components/common/ParallaxContainer\";\nimport SEO from \"@/components/common/SEO\";\n\n// JSON-LD structured data for the homepage\nconst homeSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"Organization\",\n  name: \"Ultimation Studio\",\n  url: \"https://devskills.ee\",\n  logo: {\n    \"@type\": \"ImageObject\",\n    url: \"https://devskills.ee/logo.png\",\n    width: \"180\",\n    height: \"60\",\n  },\n  description:\n    \"Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.\",\n  contactPoint: {\n    \"@type\": \"ContactPoint\",\n    telephone: \"******-567-8901\",\n    contactType: \"customer service\",\n    availableLanguage: [\"English\", \"Estonian\"],\n  },\n  sameAs: [\n    \"https://www.facebook.com/devskillsee\",\n    \"https://www.linkedin.com/company/devskills-ee\",\n    \"https://twitter.com/DevSkillsEE\",\n  ],\n};\n\n// Additional WebSite schema\nconst websiteSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"WebSite\",\n  name: \"Ultimation Studio\",\n  url: \"https://devskills.ee\",\n  potentialAction: {\n    \"@type\": \"SearchAction\",\n    target: \"https://devskills.ee/search?q={search_term_string}\",\n    \"query-input\": \"required name=search_term_string\",\n  },\n};\nexport default function Home5MainDemoMultiPageDark() {\n  return (\n    <>\n      <SEO\n        title=\"Ultimation Studio - Business Management System\"\n        description=\"Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.\"\n        canonical=\"https://devskills.ee\"\n        image=\"https://devskills.ee/og-images/home.png\"\n        imageAlt=\"Ultimation Studio - Business Management System\"\n        imageWidth=\"1200\"\n        imageHeight=\"630\"\n        schema={[homeSchema, websiteSchema]}\n        keywords={[\n          \"business management system\",\n          \"BMS\",\n          \"productivity\",\n          \"operations\",\n          \"ultimation\",\n          \"studio\",\n        ]}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <ParallaxContainer\n                className=\"home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <Hero />\n              </ParallaxContainer>\n\n              <Home dark />\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nconst dark = true;\nimport { menuItems } from \"@/data/menu\";\nimport Portfolio from \"@/components/home/<USER>\";\nimport MarqueeDark from \"@/components/home/<USER>\";\n\nimport MetaComponent from \"@/components/common/MetaComponent\";\nconst metadata = {\n  title:\n    \"Elegant Portfolio Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n  description: \"Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n};\nexport default function ElegantPortfolioPageDark() {\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    PORTFOLIO\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        Explore captivating web design solutions.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section\n                className={`page-section pb-0  scrollSpysection  ${\n                  dark ? \"bg-dark-1 light-content\" : \"\"\n                } `}\n                id=\"portfolio\"\n              >\n                <Portfolio />\n              </section>\n              <div className=\"page-section overflow-hidden\">\n                <MarqueeDark />\n              </div>\n              <section className=\"page-section bg-dark-1 light-content pt-0\">\n                <div className=\"container position-relative\">\n                  {/* Decorative Waves */}\n\n                  {/* End Decorative Waves */}\n                  <div className=\"row text-center wow fadeInUp\">\n                    <div className=\"col-md-10 offset-md-1 col-lg-6 offset-lg-3\">\n                      <p className=\"section-descr mb-50 mb-sm-30\">\n                        The power of design help us to solve complex problems\n                        and cultivate business solutions.\n                      </p>\n                      <div className=\"local-scroll\">\n                        <Link\n                          to={`/elegant-contact`}\n                          className=\"btn btn-mod btn-large btn-w btn-circle btn-hover-anim\"\n                        >\n                          <span>Contact us</span>\n                        </Link>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport { Link } from \"react-router-dom\";\nimport React, { useState, useEffect } from \"react\";\nimport { menuItems } from \"@/data/menu\";\nimport { categories } from \"@/data/categories\";\nimport { tags } from \"@/data/tags\";\nimport { archiveLinks } from \"@/data/archeve\";\nimport Pagination from \"@/components/common/Pagination\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\nimport MultilingualSEO from \"@/components/common/MultilingualSEO\";\n\nexport default function ElegantBlogPageDark() {\n  const { i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [blogPosts, setBlogPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        setLoading(true);\n        const result = await blogAPI.getBlogPosts(\n          currentLanguage,\n          currentPage,\n          9\n        );\n\n        if (result.response.ok && result.data) {\n          // Extract the posts array from the nested response structure\n          const posts = result.data.data?.data || result.data.data || [];\n          const pagination =\n            result.data.data?.pagination || result.data.pagination;\n          console.log(\"Blog listing API response:\", result.data);\n          console.log(\"Posts array:\", posts);\n          console.log(\"Pagination:\", pagination);\n          setBlogPosts(Array.isArray(posts) ? posts : []);\n          setTotalPages(pagination?.totalPages || 1);\n        } else {\n          console.error(\"Failed to fetch blog posts:\", result.response.status);\n          setBlogPosts([]);\n        }\n      } catch (error) {\n        console.error(\"Error fetching blog posts:\", error);\n        setBlogPosts([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, [currentLanguage, currentPage]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    const translation = post.translations?.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations?.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  return (\n    <>\n      <MultilingualSEO\n        title={\n          currentLanguage === \"et\" ? \"Blogi - DevSkills\" : \"Blog - DevSkills\"\n        }\n        description={\n          currentLanguage === \"et\"\n            ? \"Eksperditeadmised tarkvaraarenduse, mobiilirakenduste, AI ja tehnoloogiasuundade kohta.\"\n            : \"Expert insights on software development, mobile apps, AI, and technology trends.\"\n        }\n        slug=\"blog\"\n        type=\"website\"\n        keywords={\n          currentLanguage === \"et\"\n            ? [\n                \"blogi\",\n                \"tarkvaraarendus\",\n                \"mobiilirakendused\",\n                \"AI\",\n                \"tehnoloogia\",\n              ]\n            : [\n                \"blog\",\n                \"software development\",\n                \"mobile apps\",\n                \"AI\",\n                \"technology\",\n              ]\n        }\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    BLOG\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        nsights and inspiration at your fingertips.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <>\n                <section\n                  className=\"page-section bg-dark-1 light-content\"\n                  id=\"blog\"\n                >\n                  <div className=\"container\">\n                    {/* Blog Posts Grid */}\n                    <div\n                      className=\"row mt-n50 mb-50 wow fadeInUp\"\n                      data-wow-offset={0}\n                    >\n                      {/* Loading State */}\n                      {loading && (\n                        <div className=\"col-12 text-center\">\n                          <div className=\"text-gray\">Loading blog posts...</div>\n                        </div>\n                      )}\n\n                      {/* Empty State */}\n                      {!loading && blogPosts.length === 0 && (\n                        <div className=\"col-12 text-center\">\n                          <div className=\"text-gray\">\n                            No blog posts available yet.\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Post Items */}\n                      {!loading &&\n                        Array.isArray(blogPosts) &&\n                        blogPosts.map((post) => (\n                          <div\n                            key={post.id}\n                            className=\"post-prev col-md-6 col-lg-4 mt-50\"\n                          >\n                            <div className=\"post-prev-container\">\n                              <div className=\"post-prev-img\">\n                                <Link to={`/blog-single/${post.slug}`}>\n                                  <img\n                                    src={\n                                      post.featuredImage ||\n                                      \"/assets/images/demo-elegant/blog/1.jpg\"\n                                    }\n                                    width={607}\n                                    height={358}\n                                    alt={getTranslation(post, \"title\")}\n                                  />\n                                </Link>\n                              </div>\n                              <h3 className=\"post-prev-title\">\n                                <Link to={`/blog-single/${post.slug}`}>\n                                  {getTranslation(post, \"title\")}\n                                </Link>\n                              </h3>\n                              <div className=\"post-prev-text\">\n                                {getTranslation(post, \"excerpt\")}\n                              </div>\n                              <div className=\"post-prev-info clearfix\">\n                                <div className=\"float-start\">\n                                  <a href=\"#\" className=\"icon-author\">\n                                    <i className=\"mi-user size-14 align-middle\" />\n                                  </a>\n                                  <a href=\"#\">\n                                    {post.author?.name || \"DevSkills Team\"}\n                                  </a>\n                                </div>\n                                <div className=\"float-end\">\n                                  <i className=\"mi-calendar size-14 align-middle\" />\n                                  <a href=\"#\">\n                                    {new Date(\n                                      post.publishedAt || post.createdAt\n                                    ).toLocaleDateString()}\n                                  </a>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      {/* End Post Item */}\n\n                      {/* End Post Item */}\n                    </div>\n                    {/* End Blog Posts Grid */}\n                    {/* Pagination */}\n                    <Pagination />\n                    {/* End Pagination */}\n                  </div>\n                </section>\n                {/* End Blog Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Section */}\n                <section className=\"page-section bg-dark-1 light-content\">\n                  <div className=\"container relative\">\n                    <div className=\"row mt-n60\">\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">Categories</h3>\n                          <div className=\"widget-body\">\n                            <ul className=\"clearlist widget-menu\">\n                              {categories.map((category) => (\n                                <li key={category.id}>\n                                  <a href=\"#\" title=\"\">\n                                    {category.name}\n                                  </a>\n                                  <small> - {category.count} </small>\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">Tags</h3>\n                          <div className=\"widget-body\">\n                            <div className=\"tags\">\n                              {tags.map((tag) => (\n                                <a href=\"#\" key={tag.id}>\n                                  {tag.name}\n                                </a>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">Archive</h3>\n                          <div className=\"widget-body\">\n                            <ul className=\"clearlist widget-menu\">\n                              {archiveLinks.map((link) => (\n                                <li key={link.id}>\n                                  <a href=\"#\" title=\"\">\n                                    {link.date}\n                                  </a>\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">Text widget</h3>\n                          <div className=\"widget-body\">\n                            <div className=\"widget-text clearfix\">\n                              <img\n                                src=\"/assets/images/blog/previews/post-prev-6.jpg\"\n                                alt=\"Image Description\"\n                                height={140}\n                                style={{ height: \"fit-content\" }}\n                                width={100}\n                                className=\"left img-left\"\n                              />\n                              Consectetur adipiscing elit. Quisque magna ante\n                              eleifend eleifend. Purus ut dignissim consectetur,\n                              nulla erat ultrices purus, ut consequat sem elit\n                              non sem. Quisque magna ante eleifend eleifend.\n                            </div>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                    </div>\n                  </div>\n                </section>\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\nimport React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport { Link } from \"react-router-dom\";\nimport RelatedProjects from \"@/components/portfolio/RelatedProjects\";\nimport { allPortfolios } from \"@/data/portfolio\";\nimport MetaComponent from \"@/components/common/MetaComponent\";\nconst metadata = {\n  title:\n    \"Elegant Portfolio Single Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n  description: \"Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n};\nexport default function ElegantPortfolioSinglePageDark() {\n  let params = useParams();\n  const portfolioItem =\n    allPortfolios.filter((elm) => elm.id == params.id)[0] || allPortfolios[0];\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {portfolioItem.title}\n                  </h1>\n\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        Branding, UI/UX Design, No-code Development\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <>\n                {/* Section */}\n                <section className=\"page-section bg-dark-1 light-content\">\n                  <div className=\"container relative\">\n                    <div className=\"row mb-80 mb-sm-40\">\n                      {/* Project Details */}\n                      <div className=\"col-md-6 mb-sm-40\">\n                        <h2 className=\"section-title-small mb-20\">\n                          Project Details\n                        </h2>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Date:</b>\n                          </div>\n                          <div className=\"col-sm-8\">May 1th, 2023</div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Client:</b>\n                          </div>\n                          <div className=\"col-sm-8\">Envato Users</div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Services:</b>\n                          </div>\n                          <div className=\"col-sm-8\">\n                            Branding, UI/UX Design, Front-end Development,\n                            Back-end Development\n                          </div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                      </div>\n                      {/* End Project Details */}\n                      {/* Project Description */}\n                      <div className=\"col-md-6\">\n                        <h2 className=\"section-title-small mb-20\">\n                          Description\n                        </h2>\n                        <hr className=\"mb-20\" />\n                        <p className=\"text-gray mb-0\">\n                          Lorem ipsum dolor sit amet conseur adipisci inerene\n                          maximus ligula sempe metuse pelente mattis. Maecenas\n                          volutpat, diam eni sagittis quam porta quam. Sed id\n                          dolor consectetur fermentum volutpat accumsan purus\n                          iaculis libero. Donec vel ultricies purus iaculis\n                          libero. Etiam sit amet fringilla lacus susantebe sit\n                          ullamcorper pulvinar neque porttitor. Integere lectus.\n                          Praesent sede nisi eleifend fermum orci amet, iaculis\n                          libero. Donec vel ultricies purus quam.\n                        </p>\n                      </div>\n                      {/* End Project Description */}\n                    </div>\n                    <div className=\"row mb-n30\">\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/1-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/6-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/8-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/3-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                    </div>\n                  </div>\n                </section>\n                {/* End Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n              </>\n              <section className=\"page-section bg-dark-1 light-content\">\n                <RelatedProjects />\n              </section>\n              <>\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Work Navigation */}\n                <div className=\"work-navigation bg-dark-1 light-content clearfix z-index-1 position-relative\">\n                  <Link to={`/main-portfolio-single-1/1`} className=\"work-prev\">\n                    <span>\n                      <i className=\"mi-arrow-left size-24 align-middle\" />{\" \"}\n                      Previous\n                    </span>\n                  </Link>\n                  <a href=\"#\" className=\"work-all\">\n                    <span>\n                      <i className=\"mi-close size-24 align-middle\" /> All works\n                    </span>\n                  </a>\n                  <Link to={`/main-portfolio-single-3/1`} className=\"work-next\">\n                    <span>\n                      Next <i className=\"mi-arrow-right size-24 align-middle\" />\n                    </span>\n                  </Link>\n                </div>\n                {/* End Work Navigation */}\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { menuItems } from \"@/data/menu\";\nimport { useParams } from \"react-router-dom\";\nimport Comments from \"@/components/blog/Comments\";\nimport Form from \"@/components/blog/commentForm/Form\";\nimport Widget1 from \"@/components/blog/widgets/Widget1\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\nimport MultilingualSEO from \"@/components/common/MultilingualSEO\";\n\nexport default function ElegantBlogSinglePageDark() {\n  let params = useParams();\n  const { i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [blog, setBlog] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    const fetchBlogPost = async () => {\n      try {\n        setLoading(true);\n        const result = await blogAPI.getPost(params.id);\n\n        if (result.response.ok && result.data) {\n          console.log(\"Blog single API response:\", result.data);\n          setBlog(result.data.data || result.data);\n        } else {\n          console.error(\"Failed to fetch blog post:\", result.response.status);\n          setError(\"Blog post not found\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching blog post:\", error);\n        setError(\"Failed to load blog post\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (params.id) {\n      fetchBlogPost();\n    }\n  }, [params.id]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    if (!post || !post.translations) return \"\";\n    const translation = post.translations.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-12 text-center\">\n                      <h1>Loading...</h1>\n                      <p>Please wait while we load the blog post.</p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // If no blog post found or error, show 404 or redirect\n  if (!blog || error) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-12 text-center\">\n                      <h1>Blog Post Not Found</h1>\n                      <p>\n                        The blog post you&apos;re looking for doesn&apos;t\n                        exist.\n                      </p>\n                      <a\n                        href=\"/blog\"\n                        className=\"btn btn-mod btn-border btn-large btn-round\"\n                      >\n                        Back to Blog\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const metadata = {\n    title: `${getTranslation(blog, \"title\")} || DevSkills`,\n    description: getTranslation(blog, \"excerpt\"),\n  };\n  return (\n    <>\n      <MultilingualSEO\n        title={metadata.title}\n        description={metadata.description}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-10 offset-lg-1\">\n                      <h1\n                        className=\"hs-title-3a mb-0 wow fadeInUpShort\"\n                        data-wow-duration=\"0.6s\"\n                      >\n                        {getTranslation(blog, \"title\")}\n                      </h1>\n                    </div>\n                  </div>\n                  {/* Author, Categories, Comments */}\n                  <div\n                    className=\"blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn\"\n                    data-wow-delay=\"0.2s\"\n                  >\n                    <div className=\"d-inline-block me-3\">\n                      <a href=\"#\">\n                        <i className=\"mi-clock size-16\" />\n                        <span className=\"visually-hidden\">Date:</span>{\" \"}\n                        {new Date(\n                          blog.publishedAt || blog.createdAt\n                        ).toLocaleDateString(\"en-US\", {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </a>\n                    </div>\n                    <div className=\"d-inline-block me-3\">\n                      <a href=\"#\">\n                        <i className=\"mi-user size-16\" />\n                        <span className=\"visually-hidden\">Author:</span>{\" \"}\n                        {blog.author?.name || \"DevSkills Team\"}\n                      </a>\n                    </div>\n                    {blog.categories && blog.categories.length > 0 && (\n                      <div className=\"d-inline-block me-3\">\n                        <i className=\"mi-folder size-16\" />\n                        <span className=\"visually-hidden\">Category:</span>\n                        <a href=\"#\">{blog.categories[0].name}</a>\n                      </div>\n                    )}\n                    <div className=\"d-inline-block me-3\">\n                      <i className=\"mi-time size-16\" />\n                      <span className=\"visually-hidden\">Read time:</span>{\" \"}\n                      {blog.readTime || 5} min\n                    </div>\n                  </div>\n                  {/* End Author, Categories, Comments */}\n                </div>\n              </section>\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container relative\">\n                  <div className=\"row\">\n                    {/* Content */}\n                    <div className=\"col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last\">\n                      {/* Post */}\n                      <div className=\"blog-item mb-80 mb-xs-40\">\n                        <div className=\"blog-item-body\">\n                          {blog.featuredImage && (\n                            <div className=\"mb-40 mb-xs-30\">\n                              <img\n                                src={blog.featuredImage}\n                                alt={getTranslation(blog, \"title\")}\n                                width={1350}\n                                height={796}\n                              />\n                            </div>\n                          )}\n\n                          {/* Blog excerpt */}\n                          <div className=\"lead mb-40\">\n                            {getTranslation(blog, \"excerpt\")}\n                          </div>\n\n                          {/* Blog content */}\n                          <div\n                            className=\"blog-content\"\n                            style={{\n                              lineHeight: \"1.8\",\n                              fontSize: \"16px\",\n                            }}\n                            dangerouslySetInnerHTML={{\n                              __html: getTranslation(blog, \"content\"),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/* End Post */}\n                      {/* Comments */}\n                      <div className=\"mb-80 mb-xs-40\">\n                        <h4 className=\"blog-page-title\">\n                          Comments <small className=\"number\">(3)</small>\n                        </h4>\n                        <ul className=\"media-list comment-list clearlist\">\n                          <Comments />\n                        </ul>\n                      </div>\n                      {/* End Comments */}\n                      {/* Add Comment */}\n                      <div className=\"mb-80 mb-xs-40\">\n                        <h4 className=\"blog-page-title\">Leave a comment</h4>\n                        {/* Form */}\n                        <Form />\n                        {/* End Form */}\n                      </div>\n                      {/* End Add Comment */}\n                      {/* Prev/Next Post */}\n                      <div className=\"clearfix mt-40\">\n                        <a href=\"#\" className=\"blog-item-more left\">\n                          <i className=\"mi-chevron-left\" />\n                          &nbsp;Prev post\n                        </a>\n                        <a href=\"#\" className=\"blog-item-more right\">\n                          Next post&nbsp;\n                          <i className=\"mi-chevron-right\" />\n                        </a>\n                      </div>\n                      {/* End Prev/Next Post */}\n                    </div>\n                    {/* End Content */}\n                    {/* Sidebar */}\n                    <div className=\"col-lg-4 col-xl-3\">\n                      <Widget1 searchInputClass=\"form-control input-lg search-field round\" />\n                      {/* End Widget */}\n                    </div>\n                    {/* End Sidebar */}\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport { Link } from \"react-router-dom\";\nimport SEO from \"@/components/common/SEO\";\nexport default function MainPageNotFound() {\n  return (\n    <>\n      <SEO\n        title=\"Page Not Found - 404\"\n        description=\"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.\"\n        canonical=\"https://devskills.ee/404\"\n        image=\"https://devskills.ee/og-images/404.png\"\n      />\n      <div className=\"theme-main\">\n        <div className=\"page\" id=\"top\">\n          <>\n            <nav className=\"main-nav dark light-after-scroll transparent stick-fixed wow-menubar wch-unset\">\n              <div className=\"main-nav-sub full-wrapper\">\n                {/* Logo  (* Add your text or image to the link tag. Use SVG or PNG image format.\n              If you use a PNG logo image, the image resolution must be equal 200% of the visible logo\n              image size for support of retina screens. See details in the template documentation. *) */}\n                <div className=\"nav-logo-wrap local-scroll\">\n                  <Link to={`/`} className=\"logo\">\n                    <img\n                      src=\"/assets/images/logo-white.svg\"\n                      alt=\"Your Company Logo\"\n                      width={105}\n                      height={34}\n                      className=\"logo-white\"\n                    />\n                    <img\n                      src=\"/assets/images/logo-dark.svg\"\n                      alt=\"Your Company Logo\"\n                      width={105}\n                      height={34}\n                      className=\"logo-dark\"\n                    />\n                  </Link>\n                </div>\n                {/* Mobile Menu Button */}\n                <div className=\"mobile-nav\" role=\"button\" tabIndex={0}>\n                  <i className=\"mobile-nav-icon\" />\n                  <span className=\"visually-hidden\">Menu</span>\n                </div>\n                {/* Main Menu */}\n                <div className=\"inner-nav desktop-nav\">\n                  <ul className=\"clearlist scroll-nav local-scroll justify-content-end\">\n                    <li className=\"active\">\n                      <a href=\"mailto:<EMAIL>\">\n                        <i className=\"mi-email align-center\" />\n                        <EMAIL>\n                      </a>\n                    </li>\n                    <li>\n                      <a href=\"#\">\n                        <i className=\"mi-call align-center\" /> 0307-567-890\n                      </a>\n                    </li>\n                  </ul>\n                </div>\n                {/* End Main Menu */}\n              </div>\n            </nav>\n            {/* End Navigation Panel */}\n            <main id=\"main\">\n              {/* Home Section */}\n              <section\n                className=\"home-section bg-dark-1 bg-dark-alpha-60 light-content parallax-5\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/full-width-images/section-bg-3.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container min-height-100vh d-flex align-items-center pt-100 pb-100 pt-sm-120 pb-sm-120\">\n                  {/* Home Section Content */}\n                  <div className=\"home-content\">\n                    <div className=\"row\">\n                      <div className=\"col-sm-10 offset-sm-1 col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                        <div className=\"hs-wrap\">\n                          <div className=\"wow fadeInUp\" data-wow-delay=\".1s\">\n                            <h1 className=\"hs-title-12 mb-40 mb-sm-30\">404</h1>\n                          </div>\n                          <div\n                            className=\"mb-40 mb-sm-30 wow fadeInUp\"\n                            data-wow-delay=\".2s\"\n                          >\n                            <h2 className=\"section-descr mb-20\">\n                              The page you were looking for could not be found.\n                            </h2>\n                          </div>\n                          <div\n                            className=\"local-scroll wow fadeInUp\"\n                            data-wow-delay=\".3s\"\n                          >\n                            <Link\n                              to={`/`}\n                              className=\"btn btn-mod btn-w btn-round btn-medium btn-hover-anim\"\n                            >\n                              <i className=\"mi-arrow-left size-24 align-center\" />\n                              <span>Back To Home Page</span>\n                            </Link>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* End Home Section Content */}\n                </div>\n              </section>\n              {/* End Home Section */}\n            </main>\n          </>\n\n          <Footer />\n        </div>{\" \"}\n      </div>\n    </>\n  );\n}\n", "import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport { authAPI } from \"../utils/api\";\n\nconst AdminLogin = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n    // Clear error when user starts typing\n    if (error) setError(\"\");\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const { response, data } = await authAPI.login(formData);\n\n      if (data.success) {\n        // Store token in localStorage\n        localStorage.setItem(\"adminToken\", data.token);\n        localStorage.setItem(\"adminUser\", JSON.stringify(data.user));\n\n        // Redirect to admin dashboard\n        navigate(\"/admin/dashboard\");\n      } else {\n        setError(data.message || \"Login failed\");\n      }\n    } catch (err) {\n      console.error(\"Login error:\", err);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Admin Login - DevSkills\"\n        description=\"Admin login page for DevSkills content management\"\n        noIndex={true}\n      />\n\n      {/* Page Wrapper */}\n      <div id=\"page\" className=\"page\">\n        {/* Main Content */}\n        <main id=\"main\">\n          {/* Admin Login Section */}\n          <section\n            className=\"page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section\"\n            id=\"admin-login\"\n          >\n            <div className=\"container relative\">\n              <div className=\"row\">\n                <div className=\"col-md-6 offset-md-3 col-lg-4 offset-lg-4\">\n                  {/* Login Form */}\n                  <div className=\"form-container\">\n                    {/* Header */}\n                    <div className=\"text-center mb-60 mb-sm-40\">\n                      <div className=\"hs-line-4 font-alt black mb-20 mb-xs-10\">\n                        <span className=\"color-primary-1\">DevSkills</span> Admin\n                      </div>\n                      <p className=\"section-descr mb-0\">\n                        Sign in to access the admin dashboard\n                      </p>\n                    </div>\n\n                    {/* Error Message */}\n                    {error && (\n                      <div className=\"alert alert-danger mb-30\" role=\"alert\">\n                        <i className=\"mi-warning\"></i>\n                        {error}\n                      </div>\n                    )}\n\n                    {/* Login Form */}\n                    <form className=\"form contact-form\" onSubmit={handleSubmit}>\n                      {/* Email Field */}\n                      <div className=\"form-group\">\n                        <label htmlFor=\"email\" className=\"sr-only\">\n                          Email Address\n                        </label>\n                        <input\n                          type=\"email\"\n                          name=\"email\"\n                          id=\"email\"\n                          className=\"input-lg round form-control\"\n                          placeholder=\"Email Address\"\n                          value={formData.email}\n                          onChange={handleChange}\n                          required\n                          autoComplete=\"email\"\n                        />\n                      </div>\n\n                      {/* Password Field */}\n                      <div className=\"form-group\">\n                        <label htmlFor=\"password\" className=\"sr-only\">\n                          Password\n                        </label>\n                        <input\n                          type=\"password\"\n                          name=\"password\"\n                          id=\"password\"\n                          className=\"input-lg round form-control\"\n                          placeholder=\"Password\"\n                          value={formData.password}\n                          onChange={handleChange}\n                          required\n                          autoComplete=\"current-password\"\n                        />\n                      </div>\n\n                      {/* Submit Button */}\n                      <div className=\"form-group\">\n                        <button\n                          type=\"submit\"\n                          className=\"btn btn-mod btn-color btn-large btn-round btn-full-width\"\n                          disabled={loading}\n                        >\n                          {loading ? (\n                            <>\n                              <i className=\"fa fa-spinner fa-spin me-2\"></i>\n                              Signing in...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"mi-lock me-2\"></i>\n                              Sign In\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </form>\n\n                    {/* Footer */}\n                    <div className=\"text-center mt-40\">\n                      <p className=\"small opacity-07\">\n                        © 2024 DevSkills. All rights reserved.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n        </main>\n      </div>\n    </>\n  );\n};\n\nexport default AdminLogin;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { authAPI, adminAPI } from \"../utils/api\";\n\nconst AdminDashboard = () => {\n  const navigate = useNavigate();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem(\"adminToken\");\n      const userData = localStorage.getItem(\"adminUser\");\n\n      if (!token || !userData) {\n        navigate(\"/admin\");\n        return;\n      }\n\n      try {\n        // Verify token with backend\n        const { response, data } = await authAPI.getMe();\n\n        if (response.ok && data.success) {\n          setUser(data.user);\n        } else {\n          // Token invalid, redirect to login\n          localStorage.removeItem(\"adminToken\");\n          localStorage.removeItem(\"adminUser\");\n          navigate(\"/admin\");\n        }\n      } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        localStorage.removeItem(\"adminToken\");\n        localStorage.removeItem(\"adminUser\");\n        navigate(\"/admin\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [navigate]);\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"adminToken\");\n    localStorage.removeItem(\"adminUser\");\n    navigate(\"/admin\");\n  };\n\n  if (loading) {\n    return (\n      <div id=\"page\" className=\"page\">\n        <main id=\"main\">\n          <section className=\"page-section\">\n            <div className=\"container relative\">\n              <div className=\"row\">\n                <div className=\"col-12 text-center\">\n                  <div className=\"loading-animation\">\n                    <iconify-icon\n                      icon=\"solar:refresh-bold\"\n                      className=\"color-primary-1\"\n                      style={{\n                        fontSize: \"3rem\",\n                        animation: \"spin 1s linear infinite\",\n                      }}\n                    ></iconify-icon>\n                    <div className=\"mt-20\">\n                      <div className=\"hs-line-4 font-alt black\">Loading...</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <SEO\n        title=\"Admin Dashboard - DevSkills\"\n        description=\"DevSkills admin dashboard for content management\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Dashboard\">\n        {/* Stats Cards */}\n        <div className=\"row mb-40\">\n          {/* Total Posts */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:document-text-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Total Posts</div>\n              <div className=\"number-2-number\">0</div>\n            </div>\n          </div>\n\n          {/* Categories */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:folder-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Categories</div>\n              <div className=\"number-2-number\">5</div>\n            </div>\n          </div>\n\n          {/* Comments */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:chat-round-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Comments</div>\n              <div className=\"number-2-number\">0</div>\n            </div>\n          </div>\n\n          {/* Page Views */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:eye-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Page Views</div>\n              <div className=\"number-2-number\">-</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"mb-20\">\n              <h3 className=\"hs-line-4 font-alt black mb-20 mb-xs-10\">\n                Quick Actions\n              </h3>\n            </div>\n\n            <div className=\"row\">\n              {/* New Blog Post */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:add-circle-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">New Blog Post</h3>\n                  <div className=\"alt-features-descr\">\n                    Create a new multilingual blog post with rich content and\n                    scheduling.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/blog/new\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Create Post\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Manage Posts */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:documents-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">Manage Posts</h3>\n                  <div className=\"alt-features-descr\">\n                    Edit, publish, schedule, and organize your existing blog\n                    posts.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/posts\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Manage Posts\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Categories & Tags */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:folder-with-files-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">\n                    Categories & Tags\n                  </h3>\n                  <div className=\"alt-features-descr\">\n                    Organize your content with categories and tags for better\n                    navigation.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/categories\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Organize Content\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminDashboard;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminBlogPosts = () => {\n  const navigate = useNavigate();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 10,\n    status: \"all\",\n    search: \"\",\n  });\n  const [pagination, setPagination] = useState({});\n\n  useEffect(() => {\n    loadPosts();\n  }, [filters]);\n\n  const loadPosts = async () => {\n    try {\n      setLoading(true);\n\n      const params = {};\n      Object.entries(filters).forEach(([key, value]) => {\n        if (value && value !== \"all\") {\n          params[key] = value;\n        }\n      });\n\n      const { response, data } = await adminAPI.getPosts(params);\n\n      if (data.success) {\n        setPosts(data.data.posts);\n        setPagination(data.data.pagination);\n      } else {\n        setError(data.message || \"Failed to load posts\");\n      }\n    } catch (error) {\n      console.error(\"Load posts error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (postId) => {\n    if (\n      !confirm(\n        \"Are you sure you want to delete this blog post? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const response = await fetch(`/api/blog/${postId}`, {\n        method: \"DELETE\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        loadPosts(); // Reload the list\n      } else {\n        setError(data.message || \"Failed to delete post\");\n      }\n    } catch (error) {\n      console.error(\"Delete error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleToggleVisibility = async (postId) => {\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const response = await fetch(`/api/blog/${postId}/toggle-visibility`, {\n        method: \"PATCH\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        loadPosts(); // Reload the list\n      } else {\n        setError(data.message || \"Failed to toggle visibility\");\n      }\n    } catch (error) {\n      console.error(\"Toggle visibility error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const getStatusBadge = (post) => {\n    if (!post.published) {\n      return (\n        <span className=\"badge bg-secondary\">\n          <i className=\"mi-edit me-1\"></i>\n          Draft\n        </span>\n      );\n    }\n\n    if (post.scheduledAt && new Date(post.scheduledAt) > new Date()) {\n      return (\n        <span className=\"badge bg-warning\">\n          <i className=\"mi-clock me-1\"></i>\n          Scheduled\n        </span>\n      );\n    }\n\n    return (\n      <span className=\"badge bg-success\">\n        <i className=\"mi-check me-1\"></i>\n        Published\n      </span>\n    );\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Blog Posts - Admin\"\n        description=\"Manage blog posts in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Blog Posts\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-md-6\">\n              <p className=\"section-descr mb-0\">\n                Manage your blog posts, create new content, and organize your\n                articles.\n              </p>\n            </div>\n            <div className=\"col-md-6 text-md-end\">\n              <button\n                onClick={() => navigate(\"/admin/blog/new\")}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <i className=\"mi-plus me-2\"></i>\n                New Post\n              </button>\n            </div>\n          </div>\n        </div>\n        {/* Filters */}\n        <div className=\"admin-table mb-30\">\n          <div className=\"row\">\n            <div className=\"col-md-4 mb-20\">\n              <label className=\"form-label\">Search Posts</label>\n              <input\n                type=\"text\"\n                value={filters.search}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    search: e.target.value,\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n                placeholder=\"Search by title...\"\n              />\n            </div>\n\n            <div className=\"col-md-3 mb-20\">\n              <label className=\"form-label\">Status</label>\n              <select\n                value={filters.status}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    status: e.target.value,\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n              >\n                <option value=\"all\">All Posts</option>\n                <option value=\"published\">Published</option>\n                <option value=\"draft\">Drafts</option>\n              </select>\n            </div>\n\n            <div className=\"col-md-3 mb-20\">\n              <label className=\"form-label\">Per Page</label>\n              <select\n                value={filters.limit}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    limit: parseInt(e.target.value),\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n              >\n                <option value={10}>10</option>\n                <option value={25}>25</option>\n                <option value={50}>50</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <i className=\"mi-warning me-2\"></i>\n            {error}\n          </div>\n        )}\n\n        {/* Posts Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\">\n              <i className=\"fa fa-spinner fa-spin fa-2x color-primary-1 mb-20\"></i>\n              <div className=\"hs-line-4 font-alt black\">Loading posts...</div>\n            </div>\n          ) : posts.length === 0 ? (\n            <div className=\"text-center py-60\">\n              <i className=\"mi-edit fa-3x color-gray-light-1 mb-20\"></i>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No blog posts found\n              </div>\n              <p className=\"section-descr mb-30\">\n                {filters.search || filters.status !== \"all\"\n                  ? \"Try adjusting your search filters or create your first blog post.\"\n                  : \"Get started by creating your first blog post.\"}\n              </p>\n              <button\n                onClick={() => navigate(\"/admin/blog/new\")}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <i className=\"mi-plus me-2\"></i>\n                Create First Post\n              </button>\n            </div>\n          ) : (\n            <div className=\"table-responsive\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>Title</th>\n                    <th>Status</th>\n                    <th>Author</th>\n                    <th>Created</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {posts.map((post) => {\n                    const englishTranslation =\n                      post.translations.find((t) => t.language === \"en\") ||\n                      post.translations[0];\n\n                    return (\n                      <tr key={post.id}>\n                        <td>\n                          <div className=\"d-flex align-items-center\">\n                            {post.featuredImage && (\n                              <img\n                                className=\"rounded me-3\"\n                                src={post.featuredImage}\n                                alt=\"\"\n                                style={{\n                                  width: \"50px\",\n                                  height: \"50px\",\n                                  objectFit: \"cover\",\n                                }}\n                              />\n                            )}\n                            <div>\n                              <div className=\"fw-bold\">\n                                {englishTranslation?.title || \"Untitled\"}\n                              </div>\n                              <small className=\"text-muted\">/{post.slug}</small>\n                            </div>\n                          </div>\n                        </td>\n                        <td>\n                          {getStatusBadge(post)}\n                          {post.featured && (\n                            <span className=\"badge bg-primary ms-2\">\n                              <i className=\"mi-star me-1\"></i>\n                              Featured\n                            </span>\n                          )}\n                        </td>\n                        <td>{post.author.name || post.author.email}</td>\n                        <td>{formatDate(post.createdAt)}</td>\n                        <td>\n                          <div className=\"btn-group\" role=\"group\">\n                            <button\n                              onClick={() =>\n                                navigate(`/admin/blog/edit/${post.id}`)\n                              }\n                              className=\"btn btn-sm btn-outline-primary\"\n                              title=\"Edit\"\n                            >\n                              <i className=\"mi-edit\"></i>\n                            </button>\n\n                            <button\n                              onClick={() => handleToggleVisibility(post.id)}\n                              className={`btn btn-sm ${\n                                post.published\n                                  ? \"btn-outline-warning\"\n                                  : \"btn-outline-success\"\n                              }`}\n                              title={post.published ? \"Unpublish\" : \"Publish\"}\n                            >\n                              <i\n                                className={\n                                  post.published ? \"mi-eye-off\" : \"mi-eye\"\n                                }\n                              ></i>\n                            </button>\n\n                            <button\n                              onClick={() => handleDelete(post.id)}\n                              className=\"btn btn-sm btn-outline-danger\"\n                              title=\"Delete\"\n                            >\n                              <i className=\"mi-trash\"></i>\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {pagination.pages > 1 && (\n          <div className=\"row mt-30\">\n            <div className=\"col-md-6\">\n              <p className=\"small text-muted mb-0\">\n                Showing {(pagination.page - 1) * pagination.limit + 1} to{\" \"}\n                {Math.min(pagination.page * pagination.limit, pagination.total)}{\" \"}\n                of {pagination.total} results\n              </p>\n            </div>\n            <div className=\"col-md-6 text-md-end\">\n              <nav aria-label=\"Blog posts pagination\">\n                <ul className=\"pagination pagination-sm justify-content-md-end\">\n                  <li\n                    className={`page-item ${\n                      pagination.page <= 1 ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))\n                      }\n                      disabled={pagination.page <= 1}\n                    >\n                      Previous\n                    </button>\n                  </li>\n\n                  <li className=\"page-item active\">\n                    <span className=\"page-link\">\n                      Page {pagination.page} of {pagination.pages}\n                    </span>\n                  </li>\n\n                  <li\n                    className={`page-item ${\n                      pagination.page >= pagination.pages ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))\n                      }\n                      disabled={pagination.page >= pagination.pages}\n                    >\n                      Next\n                    </button>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminBlogPosts;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI, blogAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminBlogEditor = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { id } = useParams(); // For editing existing posts\n  const isEditing = Boolean(id);\n\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  // Available languages from i18n (memoized to prevent infinite re-renders)\n  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));\n\n  // Form state\n  const [formData, setFormData] = useState(() => {\n    // Initialize translations for all available languages\n    const initialTranslations = {};\n    availableLanguages.forEach((lang) => {\n      initialTranslations[lang] = {\n        title: \"\",\n        excerpt: \"\",\n        content: \"\",\n        metaTitle: \"\",\n        metaDesc: \"\",\n        keywords: [],\n      };\n    });\n\n    return {\n      slug: \"\",\n      featured: false,\n      published: false,\n      scheduledAt: \"\",\n      featuredImage: null,\n      featuredImageAlt: \"\",\n      readTime: \"\",\n      categoryIds: [],\n      tagIds: [],\n      translations: initialTranslations,\n    };\n  });\n\n  const [activeLanguage, setActiveLanguage] = useState(\"en\");\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n\n  // Load categories and tags\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n\n        // Check if user is authenticated\n        const token = localStorage.getItem(\"adminToken\");\n        if (!token) {\n          setError(\n            \"Authentication required. Please log in to access this page.\"\n          );\n          setLoading(false);\n          return;\n        }\n\n        // Load categories and tags using the API utility\n        const [categoriesResult, tagsResult] = await Promise.all([\n          adminAPI.getCategories(),\n          adminAPI.getTags(),\n        ]);\n\n        // Handle categories response\n        if (categoriesResult.response.ok && categoriesResult.data) {\n          setCategories(categoriesResult.data.data || []);\n        } else {\n          console.error(\n            \"Categories API failed:\",\n            categoriesResult.response.status,\n            categoriesResult.response.statusText\n          );\n          if (\n            categoriesResult.response.status === 401 ||\n            categoriesResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setCategories([]);\n        }\n\n        // Handle tags response\n        if (tagsResult.response.ok && tagsResult.data) {\n          setTags(tagsResult.data.data || []);\n        } else {\n          console.error(\n            \"Tags API failed:\",\n            tagsResult.response.status,\n            tagsResult.response.statusText\n          );\n          if (\n            tagsResult.response.status === 401 ||\n            tagsResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setTags([]);\n        }\n\n        // Load existing post if editing\n        if (isEditing) {\n          // Note: We'll need to add a getPost method to adminAPI for this\n          // For now, using direct fetch with proper API base URL\n          const postRes = await fetch(`${API_BASE_URL}/admin/posts/${id}`, {\n            headers: { Authorization: `Bearer ${token}` },\n          });\n\n          if (postRes.ok) {\n            try {\n              const postData = await postRes.json();\n              const post = postData.data;\n\n              // Convert translations array to object\n              const translationsObj = {};\n              if (post.translations && Array.isArray(post.translations)) {\n                post.translations.forEach((t) => {\n                  translationsObj[t.language] = t;\n                });\n              }\n\n              setFormData((prev) => ({\n                ...prev,\n                slug: post.slug || \"\",\n                featured: post.featured || false,\n                published: post.published || false,\n                scheduledAt: post.scheduledAt\n                  ? new Date(post.scheduledAt).toISOString().slice(0, 16)\n                  : \"\",\n                featuredImage: null,\n                featuredImageAlt: post.featuredImageAlt || \"\",\n                readTime: post.readTime || \"\",\n                categoryIds: post.categories\n                  ? post.categories.map((c) => c.id)\n                  : [],\n                tagIds: post.tags ? post.tags.map((t) => t.id) : [],\n                translations: { ...prev.translations, ...translationsObj },\n              }));\n\n              if (post.featuredImage) {\n                setImagePreview(post.featuredImage);\n              }\n            } catch (jsonError) {\n              console.error(\"Failed to parse post response:\", jsonError);\n              setError(\"Failed to load post data - invalid response format\");\n            }\n          } else {\n            console.error(\n              \"Post API failed:\",\n              postRes.status,\n              postRes.statusText\n            );\n            setError(\n              `Failed to load post: ${postRes.status} ${postRes.statusText}`\n            );\n          }\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n        if (error.message && error.message.includes(\"fetch\")) {\n          setError(\n            \"Failed to connect to the server. Please check if the backend is running on localhost:4004\"\n          );\n        } else {\n          setError(\"Failed to load data. Please try again.\");\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [id, isEditing]);\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleTranslationChange = (language, field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      translations: {\n        ...prev.translations,\n        [language]: {\n          ...prev.translations[language],\n          [field]: value,\n        },\n      },\n    }));\n  };\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData((prev) => ({\n        ...prev,\n        featuredImage: file,\n      }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreview(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const formDataToSend = new FormData();\n\n      // Add basic fields\n      formDataToSend.append(\"slug\", formData.slug);\n      formDataToSend.append(\"featured\", formData.featured);\n      formDataToSend.append(\"published\", formData.published);\n      if (formData.scheduledAt) {\n        formDataToSend.append(\"scheduledAt\", formData.scheduledAt);\n      }\n      formDataToSend.append(\"featuredImageAlt\", formData.featuredImageAlt);\n      if (formData.readTime) {\n        formDataToSend.append(\"readTime\", formData.readTime);\n      }\n\n      // Add arrays\n      formDataToSend.append(\n        \"categoryIds\",\n        JSON.stringify(formData.categoryIds)\n      );\n      formDataToSend.append(\"tagIds\", JSON.stringify(formData.tagIds));\n      formDataToSend.append(\n        \"translations\",\n        JSON.stringify(formData.translations)\n      );\n\n      // Add image if selected\n      if (formData.featuredImage) {\n        formDataToSend.append(\"featuredImage\", formData.featuredImage);\n      }\n\n      // Use the proper API utility\n      let result;\n      if (isEditing) {\n        result = await blogAPI.updatePost(id, formDataToSend);\n      } else {\n        result = await blogAPI.createPost(formDataToSend);\n      }\n\n      const { response, data } = result;\n\n      if (response.ok && data && data.success) {\n        setSuccess(\n          `Blog post ${isEditing ? \"updated\" : \"created\"} successfully!`\n        );\n        setTimeout(() => {\n          navigate(\"/admin/posts\");\n        }, 2000);\n      } else {\n        const errorMessage =\n          data?.message ||\n          `Failed to ${isEditing ? \"update\" : \"create\"} blog post`;\n        setError(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Save error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <>\n      <SEO\n        title={`${isEditing ? \"Edit\" : \"Create\"} Blog Post - Admin`}\n        description=\"Create or edit blog posts in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout\n        title={isEditing ? \"Edit Blog Post\" : \"Create New Blog Post\"}\n      >\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:danger-triangle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"alert alert-success mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:check-circle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {success}\n            </div>\n          )}\n\n          {/* Basic Settings */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:settings-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Basic Settings\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Configure the basic properties of your blog post\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Slug (URL)\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.slug}\n                  onChange={(e) => handleInputChange(\"slug\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"blog-post-url\"\n                />\n                <small className=\"form-text text-muted\">\n                  This will be the URL path for your blog post (e.g.,\n                  /blog/your-slug)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:clock-circle-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Read Time (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.readTime}\n                  onChange={(e) =>\n                    handleInputChange(\"readTime\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"5\"\n                  min=\"1\"\n                  max=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  Estimated reading time for this post\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:calendar-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Schedule Publication\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  value={formData.scheduledAt}\n                  onChange={(e) =>\n                    handleInputChange(\"scheduledAt\", e.target.value)\n                  }\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Leave empty to publish immediately when published is checked\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:star-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Post Options\n                </label>\n                <div className=\"d-flex flex-column gap-2\">\n                  <div className=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"featured\"\n                      checked={formData.featured}\n                      onChange={(e) =>\n                        handleInputChange(\"featured\", e.target.checked)\n                      }\n                      className=\"form-check-input\"\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"featured\">\n                      <iconify-icon\n                        icon=\"solar:star-bold\"\n                        className=\"me-1\"\n                      ></iconify-icon>\n                      Featured Post\n                    </label>\n                    <small className=\"form-text text-muted d-block\">\n                      Show this post prominently on the homepage\n                    </small>\n                  </div>\n\n                  <div className=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"published\"\n                      checked={formData.published}\n                      onChange={(e) =>\n                        handleInputChange(\"published\", e.target.checked)\n                      }\n                      className=\"form-check-input\"\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"published\">\n                      <iconify-icon\n                        icon=\"solar:check-circle-bold\"\n                        className=\"me-1\"\n                      ></iconify-icon>\n                      Published\n                    </label>\n                    <small className=\"form-text text-muted d-block\">\n                      Make this post visible to the public\n                    </small>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Featured Image */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <i className=\"mi-image me-2 color-primary-1\"></i>\n                  Featured Image\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Upload a featured image that will be displayed with your blog\n                  post\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-upload me-2\"></i>\n                  Upload Image\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageChange}\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Recommended size: 1200x630px. Supported formats: JPG, PNG,\n                  WebP\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-accessibility me-2\"></i>\n                  Alt Text\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.featuredImageAlt}\n                  onChange={(e) =>\n                    handleInputChange(\"featuredImageAlt\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Describe the image for accessibility\"\n                />\n                <small className=\"form-text text-muted\">\n                  Describe the image for screen readers and SEO\n                </small>\n              </div>\n\n              {imagePreview && (\n                <div className=\"col-12\">\n                  <div className=\"mb-20\">\n                    <label className=\"form-label\">Image Preview</label>\n                  </div>\n                  <div className=\"text-center\">\n                    <img\n                      src={imagePreview}\n                      alt=\"Preview\"\n                      className=\"image-preview\"\n                      style={{ maxWidth: \"400px\", height: \"auto\" }}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Language Tabs */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <i className=\"mi-globe me-2 color-primary-1\"></i>\n                  Content (Multi-language)\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Create content in multiple languages. At least English content\n                  is required.\n                </p>\n              </div>\n            </div>\n\n            {/* Language Selector */}\n            <div className=\"language-tabs mb-30\">\n              {availableLanguages.map((lang) => (\n                <button\n                  key={lang}\n                  type=\"button\"\n                  onClick={() => setActiveLanguage(lang)}\n                  className={`language-tab ${\n                    activeLanguage === lang ? \"active\" : \"\"\n                  }`}\n                >\n                  <i className=\"mi-globe me-2\"></i>\n                  {lang.toUpperCase()}\n                  {lang === \"en\" && (\n                    <span className=\"ms-1 small\">(Required)</span>\n                  )}\n                </button>\n              ))}\n            </div>\n\n            {/* Content for Active Language */}\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-edit me-2\"></i>\n                  Title ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.title || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"title\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Enter blog post title\"\n                  required={activeLanguage === \"en\"}\n                />\n                <small className=\"form-text text-muted\">\n                  The main title of your blog post in{\" \"}\n                  {activeLanguage.toUpperCase()}\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-text me-2\"></i>\n                  Excerpt ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.excerpt || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"excerpt\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"Brief description of the blog post\"\n                />\n                <small className=\"form-text text-muted\">\n                  A short summary that will appear in blog listings and social\n                  media previews\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-document me-2\"></i>\n                  Content ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.content || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"content\",\n                      e.target.value\n                    )\n                  }\n                  rows={20}\n                  className=\"form-control content-editor\"\n                  placeholder=\"Write your blog post content here. You can paste formatted text from Word documents.\"\n                  required={activeLanguage === \"en\"}\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-info me-1\"></i>\n                  Tip: You can paste formatted content from Word documents and\n                  the formatting will be preserved. Use Markdown syntax for\n                  additional formatting.\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Title ({activeLanguage.toUpperCase()})\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.metaTitle || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaTitle\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"SEO title (optional)\"\n                  maxLength=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-search me-1\"></i>\n                  Title that appears in search engine results (max 60\n                  characters)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-description me-2\"></i>\n                  Meta Description ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.metaDesc || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaDesc\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"SEO description (optional)\"\n                  maxLength=\"160\"\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-search me-1\"></i>\n                  Description that appears in search engine results (max 160\n                  characters)\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"row mt-40\">\n            <div className=\"col-12 text-end\">\n              <button\n                type=\"button\"\n                onClick={() => navigate(\"/admin/posts\")}\n                className=\"btn btn-mod btn-gray btn-round me-3\"\n              >\n                Cancel\n              </button>\n\n              <button\n                type=\"submit\"\n                disabled={saving}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                {saving ? (\n                  <>\n                    <i className=\"fa fa-spinner fa-spin me-2\"></i>\n                    Saving...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"mi-check me-2\"></i>\n                    {isEditing ? \"Update Post\" : \"Create Post\"}\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </form>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminBlogEditor;\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport SEO from '../components/common/SEO';\nimport AdminLayout from '../components/admin/AdminLayout';\nimport { adminAPI } from '../utils/api';\n\nconst AdminCategories = () => {\n  const navigate = useNavigate();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: '#4567e7'\n  });\n\n  // Load categories on component mount\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await adminAPI.getCategories();\n\n      if (data.success) {\n        setCategories(data.data || []);\n      } else {\n        setError(data.message || 'Failed to load categories');\n      }\n    } catch (error) {\n      console.error('Load categories error:', error);\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    try {\n      let response, data;\n\n      if (editingCategory) {\n        ({ response, data } = await adminAPI.updateCategory(editingCategory.id, formData));\n      } else {\n        ({ response, data } = await adminAPI.createCategory(formData));\n      }\n\n      if (data.success) {\n        setSuccess(editingCategory ? 'Category updated successfully!' : 'Category created successfully!');\n        setShowModal(false);\n        setEditingCategory(null);\n        setFormData({ name: '', description: '', color: '#4567e7' });\n        loadCategories();\n      } else {\n        setError(data.message || 'Failed to save category');\n      }\n    } catch (error) {\n      console.error('Save category error:', error);\n      setError('Network error. Please try again.');\n    }\n  };\n\n  const handleEdit = (category) => {\n    setEditingCategory(category);\n    setFormData({\n      name: category.name,\n      description: category.description || '',\n      color: category.color || '#4567e7'\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (categoryId) => {\n    if (!window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteCategory(categoryId);\n\n      if (data.success) {\n        setSuccess('Category deleted successfully!');\n        loadCategories();\n      } else {\n        setError(data.message || 'Failed to delete category');\n      }\n    } catch (error) {\n      console.error('Delete category error:', error);\n      setError('Network error. Please try again.');\n    }\n  };\n\n  const openCreateModal = () => {\n    setEditingCategory(null);\n    setFormData({ name: '', description: '', color: '#4567e7' });\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    setEditingCategory(null);\n    setFormData({ name: '', description: '', color: '#4567e7' });\n    setError('');\n  };\n\n  return (\n    <>\n      <SEO \n        title=\"Manage Categories - Admin\"\n        description=\"Manage blog categories in the admin panel\"\n        noIndex={true}\n      />\n      \n      <AdminLayout title=\"Categories\">\n        \n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-md-6\">\n              <p className=\"section-descr mb-0\">\n                Organize your blog posts with categories. Create, edit, and manage content categories.\n              </p>\n            </div>\n            <div className=\"col-md-6 text-md-end\">\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <i className=\"mi-plus me-2\"></i>\n                New Category\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <i className=\"mi-warning me-2\"></i>\n            {error}\n          </div>\n        )}\n        \n        {success && (\n          <div className=\"alert alert-success mb-30\" role=\"alert\">\n            <i className=\"mi-check me-2\"></i>\n            {success}\n          </div>\n        )}\n\n        {/* Categories Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\">\n              <i className=\"fa fa-spinner fa-spin fa-2x color-primary-1 mb-20\"></i>\n              <div className=\"hs-line-4 font-alt black\">Loading categories...</div>\n            </div>\n          ) : categories.length === 0 ? (\n            <div className=\"text-center py-60\">\n              <i className=\"mi-folder fa-3x color-gray-light-1 mb-20\"></i>\n              <div className=\"hs-line-4 font-alt black mb-10\">No categories found</div>\n              <p className=\"section-descr mb-30\">\n                Create your first category to start organizing your blog posts.\n              </p>\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <i className=\"mi-plus me-2\"></i>\n                Create First Category\n              </button>\n            </div>\n          ) : (\n            <div className=\"table-responsive\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>Category</th>\n                    <th>Description</th>\n                    <th>Posts</th>\n                    <th>Created</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {categories.map((category) => (\n                    <tr key={category.id}>\n                      <td>\n                        <div className=\"d-flex align-items-center\">\n                          <div \n                            className=\"rounded me-3\"\n                            style={{ \n                              width: '20px', \n                              height: '20px', \n                              backgroundColor: category.color || '#4567e7' \n                            }}\n                          ></div>\n                          <div>\n                            <div className=\"fw-bold\">{category.name}</div>\n                            <small className=\"text-muted\">/{category.slug}</small>\n                          </div>\n                        </div>\n                      </td>\n                      <td>\n                        <span className=\"text-muted\">\n                          {category.description || 'No description'}\n                        </span>\n                      </td>\n                      <td>\n                        <span className=\"badge bg-secondary\">\n                          {category._count?.posts || 0} posts\n                        </span>\n                      </td>\n                      <td>\n                        {new Date(category.createdAt).toLocaleDateString()}\n                      </td>\n                      <td>\n                        <div className=\"btn-group\" role=\"group\">\n                          <button\n                            onClick={() => handleEdit(category)}\n                            className=\"btn btn-sm btn-outline-primary\"\n                            title=\"Edit\"\n                          >\n                            <i className=\"mi-edit\"></i>\n                          </button>\n                          <button\n                            onClick={() => handleDelete(category.id)}\n                            className=\"btn btn-sm btn-outline-danger\"\n                            title=\"Delete\"\n                          >\n                            <i className=\"mi-trash\"></i>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Modal */}\n        {showModal && (\n          <div className=\"modal-overlay\" onClick={closeModal}>\n            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"modal-header\">\n                <h4 className=\"modal-title\">\n                  <i className=\"mi-folder me-2\"></i>\n                  {editingCategory ? 'Edit Category' : 'Create New Category'}\n                </h4>\n                <button \n                  type=\"button\" \n                  className=\"modal-close\"\n                  onClick={closeModal}\n                >\n                  <i className=\"mi-close\"></i>\n                </button>\n              </div>\n              \n              <form onSubmit={handleSubmit}>\n                <div className=\"modal-body\">\n                  <div className=\"row\">\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <i className=\"mi-edit me-2\"></i>\n                        Category Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) => handleInputChange('name', e.target.value)}\n                        className=\"form-control\"\n                        placeholder=\"Enter category name\"\n                        required\n                      />\n                    </div>\n                    \n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <i className=\"mi-text me-2\"></i>\n                        Description\n                      </label>\n                      <textarea\n                        value={formData.description}\n                        onChange={(e) => handleInputChange('description', e.target.value)}\n                        className=\"form-control\"\n                        rows={3}\n                        placeholder=\"Brief description of this category\"\n                      />\n                    </div>\n                    \n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <i className=\"mi-palette me-2\"></i>\n                        Color\n                      </label>\n                      <div className=\"d-flex align-items-center gap-3\">\n                        <input\n                          type=\"color\"\n                          value={formData.color}\n                          onChange={(e) => handleInputChange('color', e.target.value)}\n                          className=\"form-control form-control-color\"\n                          style={{ width: '60px', height: '40px' }}\n                        />\n                        <input\n                          type=\"text\"\n                          value={formData.color}\n                          onChange={(e) => handleInputChange('color', e.target.value)}\n                          className=\"form-control\"\n                          placeholder=\"#4567e7\"\n                        />\n                      </div>\n                      <small className=\"form-text text-muted\">\n                        Choose a color to represent this category\n                      </small>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-mod btn-gray btn-round me-3\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-mod btn-color btn-round\"\n                  >\n                    <i className=\"mi-check me-2\"></i>\n                    {editingCategory ? 'Update Category' : 'Create Category'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n        \n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminCategories;\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport SEO from '../components/common/SEO';\nimport AdminLayout from '../components/admin/AdminLayout';\nimport { adminAPI } from '../utils/api';\n\nconst AdminTags = () => {\n  const navigate = useNavigate();\n  const [tags, setTags] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingTag, setEditingTag] = useState(null);\n  const [formData, setFormData] = useState({\n    name: ''\n  });\n\n  // Load tags on component mount\n  useEffect(() => {\n    loadTags();\n  }, []);\n\n  const loadTags = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await adminAPI.getTags();\n\n      if (data.success) {\n        setTags(data.data || []);\n      } else {\n        setError(data.message || 'Failed to load tags');\n      }\n    } catch (error) {\n      console.error('Load tags error:', error);\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    try {\n      let response, data;\n\n      if (editingTag) {\n        ({ response, data } = await adminAPI.updateTag(editingTag.id, formData));\n      } else {\n        ({ response, data } = await adminAPI.createTag(formData));\n      }\n\n      if (data.success) {\n        setSuccess(editingTag ? 'Tag updated successfully!' : 'Tag created successfully!');\n        setShowModal(false);\n        setEditingTag(null);\n        setFormData({ name: '' });\n        loadTags();\n      } else {\n        setError(data.message || 'Failed to save tag');\n      }\n    } catch (error) {\n      console.error('Save tag error:', error);\n      setError('Network error. Please try again.');\n    }\n  };\n\n  const handleEdit = (tag) => {\n    setEditingTag(tag);\n    setFormData({\n      name: tag.name\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (tagId) => {\n    if (!window.confirm('Are you sure you want to delete this tag? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteTag(tagId);\n\n      if (data.success) {\n        setSuccess('Tag deleted successfully!');\n        loadTags();\n      } else {\n        setError(data.message || 'Failed to delete tag');\n      }\n    } catch (error) {\n      console.error('Delete tag error:', error);\n      setError('Network error. Please try again.');\n    }\n  };\n\n  const openCreateModal = () => {\n    setEditingTag(null);\n    setFormData({ name: '' });\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    setEditingTag(null);\n    setFormData({ name: '' });\n    setError('');\n  };\n\n  return (\n    <>\n      <SEO \n        title=\"Manage Tags - Admin\"\n        description=\"Manage blog tags in the admin panel\"\n        noIndex={true}\n      />\n      \n      <AdminLayout title=\"Tags\">\n        \n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-md-6\">\n              <p className=\"section-descr mb-0\">\n                Tag your blog posts for better organization and discoverability. Create and manage content tags.\n              </p>\n            </div>\n            <div className=\"col-md-6 text-md-end\">\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <i className=\"mi-plus me-2\"></i>\n                New Tag\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <i className=\"mi-warning me-2\"></i>\n            {error}\n          </div>\n        )}\n        \n        {success && (\n          <div className=\"alert alert-success mb-30\" role=\"alert\">\n            <i className=\"mi-check me-2\"></i>\n            {success}\n          </div>\n        )}\n\n        {/* Tags Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\">\n              <i className=\"fa fa-spinner fa-spin fa-2x color-primary-1 mb-20\"></i>\n              <div className=\"hs-line-4 font-alt black\">Loading tags...</div>\n            </div>\n          ) : tags.length === 0 ? (\n            <div className=\"text-center py-60\">\n              <i className=\"mi-tag fa-3x color-gray-light-1 mb-20\"></i>\n              <div className=\"hs-line-4 font-alt black mb-10\">No tags found</div>\n              <p className=\"section-descr mb-30\">\n                Create your first tag to start organizing your blog posts.\n              </p>\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <i className=\"mi-plus me-2\"></i>\n                Create First Tag\n              </button>\n            </div>\n          ) : (\n            <div className=\"table-responsive\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>Tag Name</th>\n                    <th>Posts</th>\n                    <th>Created</th>\n                    <th>Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {tags.map((tag) => (\n                    <tr key={tag.id}>\n                      <td>\n                        <div className=\"d-flex align-items-center\">\n                          <i className=\"mi-tag me-3 color-primary-1\"></i>\n                          <div>\n                            <div className=\"fw-bold\">{tag.name}</div>\n                            <small className=\"text-muted\">/{tag.slug}</small>\n                          </div>\n                        </div>\n                      </td>\n                      <td>\n                        <span className=\"badge bg-secondary\">\n                          {tag._count?.posts || 0} posts\n                        </span>\n                      </td>\n                      <td>\n                        {new Date(tag.createdAt).toLocaleDateString()}\n                      </td>\n                      <td>\n                        <div className=\"btn-group\" role=\"group\">\n                          <button\n                            onClick={() => handleEdit(tag)}\n                            className=\"btn btn-sm btn-outline-primary\"\n                            title=\"Edit\"\n                          >\n                            <i className=\"mi-edit\"></i>\n                          </button>\n                          <button\n                            onClick={() => handleDelete(tag.id)}\n                            className=\"btn btn-sm btn-outline-danger\"\n                            title=\"Delete\"\n                          >\n                            <i className=\"mi-trash\"></i>\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Modal */}\n        {showModal && (\n          <div className=\"modal-overlay\" onClick={closeModal}>\n            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"modal-header\">\n                <h4 className=\"modal-title\">\n                  <i className=\"mi-tag me-2\"></i>\n                  {editingTag ? 'Edit Tag' : 'Create New Tag'}\n                </h4>\n                <button \n                  type=\"button\" \n                  className=\"modal-close\"\n                  onClick={closeModal}\n                >\n                  <i className=\"mi-close\"></i>\n                </button>\n              </div>\n              \n              <form onSubmit={handleSubmit}>\n                <div className=\"modal-body\">\n                  <div className=\"row\">\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <i className=\"mi-edit me-2\"></i>\n                        Tag Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) => handleInputChange('name', e.target.value)}\n                        className=\"form-control\"\n                        placeholder=\"Enter tag name\"\n                        required\n                      />\n                      <small className=\"form-text text-muted\">\n                        Keep it short and descriptive (e.g., \"JavaScript\", \"Tutorial\", \"News\")\n                      </small>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-mod btn-gray btn-round me-3\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-mod btn-color btn-round\"\n                  >\n                    <i className=\"mi-check me-2\"></i>\n                    {editingTag ? 'Update Tag' : 'Create Tag'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n        \n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminTags;\n"], "names": ["homeSchema", "websiteSchema", "Home5MainDemoMultiPageDark", "jsxs", "Fragment", "jsx", "SEO", "Header", "menuItems", "ParallaxContainer", "Hero", "Home", "Footer", "metadata", "ElegantPortfolioPageDark", "MetaComponent", "Portfolio", "MarqueeDark", "Link", "ElegantBlogPageDark", "i18n", "useTranslation", "currentLanguage", "blogPosts", "setBlogPosts", "useState", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "useEffect", "result", "blogAPI", "posts", "_a", "pagination", "_b", "error", "getTranslation", "post", "field", "translation", "t", "_c", "MultilingualSEO", "Pagination", "categories", "category", "tags", "tag", "archiveLinks", "link", "ElegantPortfolioSinglePageDark", "params", "useParams", "portfolioItem", "allPortfolios", "elm", "RelatedProjects", "ElegantBlogSinglePageDark", "blog", "setBlog", "setError", "fetchBlogPost", "Comments", "Form", "Widget1", "MainPageNotFound", "AdminLogin", "navigate", "useNavigate", "formData", "setFormData", "handleChange", "e", "handleSubmit", "response", "data", "authAPI", "err", "AdminDashboard", "user", "setUser", "token", "userData", "AdminLayout", "AdminBlogPosts", "setPosts", "filters", "setFilters", "setPagination", "loadPosts", "key", "value", "adminAPI", "handleDelete", "postId", "handleToggleVisibility", "formatDate", "dateString", "getStatusBadge", "prev", "englishTranslation", "AdminBlogEditor", "id", "isEditing", "saving", "setSaving", "success", "setSuccess", "availableLanguages", "initialTranslations", "lang", "activeLanguage", "setActiveLanguage", "setCategories", "setTags", "imagePreview", "setImagePreview", "categoriesResult", "tagsResult", "postRes", "API_BASE_URL", "translationsObj", "c", "jsonError", "handleInputChange", "handleTranslationChange", "language", "handleImageChange", "file", "reader", "formDataToSend", "errorMessage", "_d", "_e", "AdminCategories", "showModal", "setShowModal", "editingCategory", "setEditingCategory", "loadCategories", "handleEdit", "categoryId", "openCreateModal", "closeModal", "AdminTags", "editingTag", "setEditingTag", "loadTags", "tagId"], "mappings": "wZAUA,MAAMA,GAAa,CACjB,WAAY,qBACZ,QAAS,eACT,KAAM,oBACN,IAAK,uBACL,KAAM,CACJ,QAAS,cACT,IAAK,gCACL,MAAO,MACP,OAAQ,IACV,EACA,YACE,kIACF,aAAc,CACZ,QAAS,eACT,UAAW,kBACX,YAAa,mBACb,kBAAmB,CAAC,UAAW,UAAU,CAC3C,EACA,OAAQ,CACN,uCACA,gDACA,iCAAA,CAEJ,EAGMC,GAAgB,CACpB,WAAY,qBACZ,QAAS,UACT,KAAM,oBACN,IAAK,uBACL,gBAAiB,CACf,QAAS,eACT,OAAQ,qDACR,cAAe,kCAAA,CAEnB,EACA,SAAwBC,IAA6B,CACnD,OAEIC,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,iDACN,YAAY,kIACZ,UAAU,uBACV,MAAM,0CACN,SAAS,iDACT,WAAW,OACX,YAAY,MACZ,OAAQ,CAACN,GAAYC,EAAa,EAClC,SAAU,CACR,6BACA,MACA,eACA,aACA,aACA,QAAA,CACF,CACF,QACC,MAAI,CAAA,UAAU,gBACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,8DACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAACI,GAAA,CACC,UAAU,oFACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,eAACC,GAAK,CAAA,CAAA,CAAA,CACR,EAEAL,EAAAA,IAACM,GAAK,CAAA,KAAI,EAAC,CAAA,CAAA,EACb,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAN,EAAA,IAACO,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,CCpFA,MAAMC,GAAW,CACf,MACE,yFACF,YAAa,8DACf,EACA,SAAwBC,IAA2B,CACjD,OAEIX,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACC,EAAAA,IAAAU,GAAA,CAAc,KAAMF,EAAU,CAAA,QAC9B,MAAI,CAAA,UAAU,gBACb,SAACV,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OACnB,SAAA,WAAA,CAED,QACC,MAAI,CAAA,UAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,MAAA,CAAI,UAAU,4CACb,eAAC,IAAE,CAAA,UAAU,sCAAsC,SAAA,4CAEnD,EACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAA,EAAA,IAAC,UAAA,CACC,UAAW,gEAGX,GAAG,YAEH,eAACW,GAAU,CAAA,CAAA,CAAA,CACb,QACC,MAAI,CAAA,UAAU,+BACb,SAAAX,MAACY,IAAY,CAAA,EACf,EACCZ,MAAA,UAAA,CAAQ,UAAU,4CACjB,eAAC,MAAI,CAAA,UAAU,8BAIb,SAAAA,EAAA,IAAC,OAAI,UAAU,+BACb,SAACF,OAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAACE,EAAA,IAAA,IAAA,CAAE,UAAU,+BAA+B,SAG5C,0FAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,eACb,SAAAA,EAAA,IAACa,EAAA,CACC,GAAI,mBACJ,UAAU,wDAEV,SAAAb,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAEpB,CAAA,CAAA,EACF,CAAA,CACF,EACF,CACF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACO,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,gHChFA,SAAwBO,IAAsB,CACtC,KAAA,CAAE,KAAAC,CAAK,EAAIC,EAAe,EAC1BC,EAAkBF,EAAK,UAAY,KACnC,CAACG,EAAWC,CAAY,EAAIC,EAAAA,SAAS,CAAA,CAAE,EACvC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAaC,CAAc,EAAIJ,EAAAA,SAAS,CAAC,EAC1C,CAACK,EAAYC,CAAa,EAAIN,EAAAA,SAAS,CAAC,EAE9CO,EAAAA,UAAU,IAAM,EACS,SAAY,SAC7B,GAAA,CACFL,EAAW,EAAI,EACT,MAAAM,EAAS,MAAMC,EAAQ,aAC3BZ,EACAM,EACA,CACF,EAEA,GAAIK,EAAO,SAAS,IAAMA,EAAO,KAAM,CAE/B,MAAAE,IAAQC,EAAAH,EAAO,KAAK,OAAZ,YAAAG,EAAkB,OAAQH,EAAO,KAAK,MAAQ,CAAC,EACvDI,IACJC,EAAAL,EAAO,KAAK,OAAZ,YAAAK,EAAkB,aAAcL,EAAO,KAAK,WACtC,QAAA,IAAI,6BAA8BA,EAAO,IAAI,EAC7C,QAAA,IAAI,eAAgBE,CAAK,EACzB,QAAA,IAAI,cAAeE,CAAU,EACrCb,EAAa,MAAM,QAAQW,CAAK,EAAIA,EAAQ,CAAA,CAAE,EAChCJ,GAAAM,GAAA,YAAAA,EAAY,aAAc,CAAC,CAAA,MAEzC,QAAQ,MAAM,8BAA+BJ,EAAO,SAAS,MAAM,EACnET,EAAa,CAAA,CAAE,QAEVe,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjDf,EAAa,CAAA,CAAE,CAAA,QACf,CACAG,EAAW,EAAK,CAAA,CAEpB,GAEe,CAAA,EACd,CAACL,EAAiBM,CAAW,CAAC,EAG3B,MAAAY,EAAiB,CAACC,EAAMC,IAAU,WAChC,MAAAC,GAAcP,EAAAK,EAAK,eAAL,YAAAL,EAAmB,KACpCQ,GAAMA,EAAE,WAAatB,GAExB,OACEqB,GAAA,YAAAA,EAAcD,OACdG,GAAAP,EAAAG,EAAK,eAAL,YAAAH,EAAmB,KAAMM,GAAMA,EAAE,WAAa,QAA9C,YAAAC,EAAsDH,KACtD,EAEJ,EAEA,OAEIvC,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACyC,GAAA,CACC,MACExB,IAAoB,KAAO,oBAAsB,mBAEnD,YACEA,IAAoB,KAChB,0FACA,mFAEN,KAAK,OACL,KAAK,UACL,SACEA,IAAoB,KAChB,CACE,QACA,kBACA,oBACA,KACA,aAAA,EAEF,CACE,OACA,uBACA,cACA,KACA,YAAA,CACF,CAER,QACC,MAAI,CAAA,UAAU,gBACb,SAACnB,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OACnB,SAAA,MAAA,CAED,QACC,MAAI,CAAA,UAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,MAAA,CAAI,UAAU,4CACb,eAAC,IAAE,CAAA,UAAU,sCAAsC,SAAA,8CAEnD,EACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAEEF,OAAAC,EAAAA,SAAA,CAAA,SAAA,CAAAC,EAAA,IAAC,UAAA,CACC,UAAU,uCACV,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAU,gCACV,kBAAiB,EAGhB,SAAA,CACCuB,GAAArB,EAAA,IAAC,OAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,YAAY,SAAA,uBAAA,CAAqB,CAClD,CAAA,EAID,CAACqB,GAAWH,EAAU,SAAW,GAC/BlB,MAAA,MAAA,CAAI,UAAU,qBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YAAY,uCAE3B,CAAA,EACF,EAID,CAACqB,GACA,MAAM,QAAQH,CAAS,GACvBA,EAAU,IAAKkB,UACbpC,OAAAA,EAAA,IAAC,MAAA,CAEC,UAAU,oCAEV,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAAAA,EAAAA,IAACa,GAAK,GAAI,gBAAgBuB,EAAK,IAAI,GACjC,SAAApC,EAAA,IAAC,MAAA,CACC,IACEoC,EAAK,eACL,yCAEF,MAAO,IACP,OAAQ,IACR,IAAKD,EAAeC,EAAM,OAAO,CAAA,GAErC,CACF,CAAA,EACCpC,MAAA,KAAA,CAAG,UAAU,kBACZ,eAACa,EAAK,CAAA,GAAI,gBAAgBuB,EAAK,IAAI,GAChC,SAAAD,EAAeC,EAAM,OAAO,CAC/B,CAAA,EACF,QACC,MAAI,CAAA,UAAU,iBACZ,SAAeD,EAAAC,EAAM,SAAS,EACjC,EACAtC,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,cACpB,SAACA,EAAAA,IAAA,IAAA,CAAE,UAAU,8BAAA,CAA+B,CAC9C,CAAA,QACC,IAAE,CAAA,KAAK,IACL,WAAK+B,EAAAK,EAAA,SAAA,YAAAL,EAAQ,OAAQ,gBACxB,CAAA,CAAA,EACF,EACAjC,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,kCAAmC,CAAA,EAC/CA,EAAA,IAAA,IAAA,CAAE,KAAK,IACL,SAAI,IAAA,KACHoC,EAAK,aAAeA,EAAK,SAC3B,EAAE,oBACJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EA3CKA,EAAK,EA6Cb,EAAA,CAAA,CAAA,CAIL,QAGCM,GAAW,CAAA,CAAA,CAAA,CAEd,CAAA,CAAA,CACF,EAGA1C,EAAAA,IAAC,KAAG,CAAA,UAAU,iBAAkB,CAAA,EAGhCA,EAAA,IAAC,UAAQ,CAAA,UAAU,uCACjB,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAF,OAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,0BAEb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAU,aAAA,EACtCA,EAAA,IAAA,MAAA,CAAI,UAAU,cACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,wBACX,SAAW2C,GAAA,IAAKC,UACd,KACC,CAAA,SAAA,CAAA5C,MAAC,KAAE,KAAK,IAAI,MAAM,GACf,WAAS,KACZ,SACC,QAAM,CAAA,SAAA,CAAA,MAAI4C,EAAS,MAAM,GAAA,CAAC,CAAA,CAAA,CAAA,EAJpBA,EAAS,EAKlB,CACD,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,QACC,MAAI,CAAA,UAAU,0BAEb,SAAC9C,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAI,OAAA,EACjCA,EAAAA,IAAC,OAAI,UAAU,cACb,eAAC,MAAI,CAAA,UAAU,OACZ,SAAA6C,GAAK,IAAKC,GACR9C,EAAA,IAAA,IAAA,CAAE,KAAK,IACL,SAAA8C,EAAI,MADUA,EAAI,EAErB,CACD,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,QACC,MAAI,CAAA,UAAU,0BAEb,SAAChD,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAO,UAAA,EACpCA,EAAA,IAAC,MAAI,CAAA,UAAU,cACb,SAAAA,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACX,SAAA+C,GAAa,IAAKC,GACjBhD,MAAC,KACC,CAAA,SAAAA,EAAA,IAAC,IAAE,CAAA,KAAK,IAAI,MAAM,GACf,SAAAgD,EAAK,IACR,CAAA,CAAA,EAHOA,EAAK,EAId,CACD,CAAA,CACH,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,QACC,MAAI,CAAA,UAAU,0BAEb,SAAClD,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAW,cAAA,QACvC,MAAI,CAAA,UAAU,cACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,IAAI,+CACJ,IAAI,oBACJ,OAAQ,IACR,MAAO,CAAE,OAAQ,aAAc,EAC/B,MAAO,IACP,UAAU,eAAA,CACZ,EAAE,oMAAA,CAAA,CAKJ,CACF,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACO,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,gHCnTMC,GAAW,CACf,MACE,gGACF,YAAa,8DACf,EACA,SAAwByC,IAAiC,CACvD,IAAIC,EAASC,EAAU,EACvB,MAAMC,EACJC,GAAc,OAAQC,GAAQA,EAAI,IAAMJ,EAAO,EAAE,EAAE,CAAC,GAAKG,GAAc,CAAC,EAC1E,OAEIvD,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACC,EAAAA,IAAAU,GAAA,CAAc,KAAMF,EAAU,CAAA,QAC9B,MAAI,CAAA,UAAU,gBACb,SAACV,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OAEjB,SAAcoD,EAAA,KAAA,CACjB,QAEC,MAAI,CAAA,UAAU,iBAAiB,iBAAe,OAC7C,SAACpD,MAAA,MAAA,CAAI,UAAU,4CACb,eAAC,IAAE,CAAA,UAAU,sCAAsC,SAAA,8CAEnD,EACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGEF,OAAAC,EAAAA,SAAA,CAAA,SAAA,CAAAC,EAAAA,IAAC,WAAQ,UAAU,uCACjB,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAE1C,kBAAA,EACAA,EAAAA,IAAC,KAAG,CAAA,UAAU,OAAQ,CAAA,EACtBF,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,WACb,SAACA,MAAA,IAAA,CAAE,iBAAK,CACV,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,WAAW,SAAa,eAAA,CAAA,CAAA,EACzC,EACAA,EAAAA,IAAC,KAAG,CAAA,UAAU,OAAQ,CAAA,EACtBF,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,WACb,SAACA,MAAA,IAAA,CAAE,mBAAO,CACZ,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,WAAW,SAAY,cAAA,CAAA,CAAA,EACxC,EACAA,EAAAA,IAAC,KAAG,CAAA,UAAU,OAAQ,CAAA,EACtBF,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,WACb,SAACA,MAAA,IAAA,CAAE,qBAAS,CACd,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,WAAW,SAG1B,qEAAA,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,KAAG,CAAA,UAAU,OAAQ,CAAA,CAAA,EACxB,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,4BAA4B,SAE1C,cAAA,EACAA,EAAAA,IAAC,KAAG,CAAA,UAAU,OAAQ,CAAA,EACrBA,EAAA,IAAA,IAAA,CAAE,UAAU,iBAAiB,SAU9B,8cAAA,CAAA,CAAA,CACF,CAAA,CAAA,EAEF,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,aAEb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,IAAA,CAAA,EAEZ,EAGAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,IAAA,CAAA,EAEZ,EAGAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,IAAA,CAAA,EAEZ,EAGAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,IAAA,CAAA,CAEZ,CAAA,CAAA,CAEF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAA,EAAAA,IAAC,KAAG,CAAA,UAAU,iBAAkB,CAAA,CAAA,EAElC,QACC,UAAQ,CAAA,UAAU,uCACjB,SAAAA,MAACuD,IAAgB,CAAA,EACnB,EAGEzD,OAAAC,EAAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,iBAAkB,CAAA,EAGhCF,EAAAA,KAAC,MAAI,CAAA,UAAU,+EACb,SAAA,CAAAE,EAAAA,IAACa,GAAK,GAAI,6BAA8B,UAAU,YAChD,gBAAC,OACC,CAAA,SAAA,CAACb,EAAAA,IAAA,IAAA,CAAE,UAAU,oCAAqC,CAAA,EAAG,IAAI,UAAA,CAAA,CAE3D,CACF,CAAA,QACC,IAAE,CAAA,KAAK,IAAI,UAAU,WACpB,gBAAC,OACC,CAAA,SAAA,CAACA,EAAAA,IAAA,IAAA,CAAE,UAAU,+BAAgC,CAAA,EAAE,YAAA,CAAA,CACjD,CACF,CAAA,QACCa,EAAK,CAAA,GAAI,6BAA8B,UAAU,YAChD,gBAAC,OAAK,CAAA,SAAA,CAAA,QACCb,EAAAA,IAAC,IAAE,CAAA,UAAU,qCAAsC,CAAA,CAAA,CAAA,CAC1D,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAEF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAA,EAAA,IAACO,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,gHCvLA,SAAwBiD,IAA4B,OAClD,IAAIN,EAASC,EAAU,EACjB,KAAA,CAAE,KAAApC,CAAK,EAAIC,EAAe,EAC1BC,EAAkBF,EAAK,UAAY,KACnC,CAAC0C,EAAMC,CAAO,EAAItC,EAAAA,SAAS,IAAI,EAC/B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACc,EAAOyB,CAAQ,EAAIvC,EAAAA,SAAS,EAAE,EAErCO,EAAAA,UAAU,IAAM,CACd,MAAMiC,EAAgB,SAAY,CAC5B,GAAA,CACFtC,EAAW,EAAI,EACf,MAAMM,EAAS,MAAMC,EAAQ,QAAQqB,EAAO,EAAE,EAE1CtB,EAAO,SAAS,IAAMA,EAAO,MACvB,QAAA,IAAI,4BAA6BA,EAAO,IAAI,EACpD8B,EAAQ9B,EAAO,KAAK,MAAQA,EAAO,IAAI,IAEvC,QAAQ,MAAM,6BAA8BA,EAAO,SAAS,MAAM,EAClE+B,EAAS,qBAAqB,SAEzBzB,EAAO,CACN,QAAA,MAAM,4BAA6BA,CAAK,EAChDyB,EAAS,0BAA0B,CAAA,QACnC,CACArC,EAAW,EAAK,CAAA,CAEpB,EAEI4B,EAAO,IACKU,EAAA,CAChB,EACC,CAACV,EAAO,EAAE,CAAC,EAGR,MAAAf,EAAiB,CAACC,EAAMC,IAAU,OACtC,GAAI,CAACD,GAAQ,CAACA,EAAK,aAAqB,MAAA,GAClC,MAAAE,EAAcF,EAAK,aAAa,KACnCG,GAAMA,EAAE,WAAatB,CACxB,EACA,OACEqB,GAAA,YAAAA,EAAcD,OACdN,EAAAK,EAAK,aAAa,KAAMG,GAAMA,EAAE,WAAa,IAAI,IAAjD,YAAAR,EAAqDM,KACrD,EAEJ,EAGA,GAAIhB,EACF,OACGrB,EAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QACC,OAAK,CAAA,GAAG,OACP,SAACH,EAAAA,IAAA,UAAA,CAAQ,UAAU,uCACjB,SAAAA,EAAAA,IAAC,OAAI,UAAU,YACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAU,YAAA,CAAA,EACdA,EAAAA,IAAC,KAAE,SAAwC,0CAAA,CAAA,CAC7C,CAAA,CAAA,CACF,CAAA,EACF,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAKA,GAAA,CAACyD,GAAQvB,EACX,OACGlC,EAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,QACC,OAAK,CAAA,GAAG,OACP,SAACH,EAAAA,IAAA,UAAA,CAAQ,UAAU,uCACjB,SAAAA,EAAAA,IAAC,OAAI,UAAU,YACb,eAAC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAmB,qBAAA,CAAA,EACvBA,EAAAA,IAAC,KAAE,SAGH,iDAAA,CAAA,EACAA,EAAA,IAAC,IAAA,CACC,KAAK,QACL,UAAU,6CACX,SAAA,cAAA,CAAA,CAGH,CAAA,CAAA,CACF,CAAA,EACF,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAIJ,MAAMQ,EAAW,CACf,MAAO,GAAG2B,EAAesB,EAAM,OAAO,CAAC,gBACvC,YAAatB,EAAesB,EAAM,SAAS,CAC7C,EACA,OAEI3D,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACyC,GAAA,CACC,MAAOjC,EAAS,MAChB,YAAaA,EAAS,WAAA,CACxB,QACC,MAAI,CAAA,UAAU,gBACb,SAACV,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,MACb,SAACA,MAAA,MAAA,CAAI,UAAU,wBACb,SAAAA,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OAEjB,SAAAmC,EAAesB,EAAM,OAAO,CAAA,GAEjC,CACF,CAAA,EAEA3D,EAAA,KAAC,MAAA,CACC,UAAU,gDACV,iBAAe,OAEf,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,sBACb,SAACF,EAAA,KAAA,IAAA,CAAE,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,kBAAmB,CAAA,EAC/BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAK,QAAA,EAAQ,IAC9C,IAAI,KACHyD,EAAK,aAAeA,EAAK,SAAA,EACzB,mBAAmB,QAAS,CAC5B,KAAM,UACN,MAAO,OACP,IAAK,SACN,CAAA,CAAA,CAAA,CACH,CACF,CAAA,QACC,MAAI,CAAA,UAAU,sBACb,SAAC3D,EAAA,KAAA,IAAA,CAAE,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAO,UAAA,EAAQ,MAChD+B,EAAA0B,EAAK,SAAL,YAAA1B,EAAa,OAAQ,gBAAA,CAAA,CACxB,CACF,CAAA,EACC0B,EAAK,YAAcA,EAAK,WAAW,OAAS,GAC3C3D,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,mBAAoB,CAAA,EAChCA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,YAAA,EAC3CA,MAAC,KAAE,KAAK,IAAK,WAAK,WAAW,CAAC,EAAE,IAAK,CAAA,CAAA,EACvC,EAEFF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAU,aAAA,EAAQ,IACnDyD,EAAK,UAAY,EAAE,MAAA,CACtB,CAAA,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,CAAA,CACF,EACAzD,EAAA,IAAC,UAAQ,CAAA,UAAU,uCACjB,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAF,OAAC,MAAI,CAAA,UAAU,MAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0DAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,2BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iBACZ,SAAA,CAAA2D,EAAK,eACJzD,MAAC,MAAI,CAAA,UAAU,iBACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAKyD,EAAK,cACV,IAAKtB,EAAesB,EAAM,OAAO,EACjC,MAAO,KACP,OAAQ,GAAA,CAAA,EAEZ,QAID,MAAI,CAAA,UAAU,aACZ,SAAetB,EAAAsB,EAAM,SAAS,EACjC,EAGAzD,EAAA,IAAC,MAAA,CACC,UAAU,eACV,MAAO,CACL,WAAY,MACZ,SAAU,MACZ,EACA,wBAAyB,CACvB,OAAQmC,EAAesB,EAAM,SAAS,CAAA,CACxC,CAAA,CACF,CAAA,CACF,CACF,CAAA,EAGA3D,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,kBAAkB,SAAA,CAAA,YACpBE,EAAA,IAAA,QAAA,CAAM,UAAU,SAAS,SAAG,KAAA,CAAA,CAAA,EACxC,QACC,KAAG,CAAA,UAAU,oCACZ,SAAAA,EAAA,IAAC6D,KAAS,CACZ,CAAA,CAAA,EACF,EAGA/D,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAe,kBAAA,QAE9C8D,GAAK,CAAA,CAAA,CAAA,EAER,EAGAhE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAA,EAAA,KAAC,IAAE,CAAA,KAAK,IAAI,UAAU,sBACpB,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAAE,YAAA,EAEnC,EACCF,EAAA,KAAA,IAAA,CAAE,KAAK,IAAI,UAAU,uBAAuB,SAAA,CAAA,aAE3CE,EAAAA,IAAC,IAAE,CAAA,UAAU,kBAAmB,CAAA,CAAA,CAClC,CAAA,CAAA,CACF,CAAA,CAAA,EAEF,EAGAA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC+D,GAAQ,CAAA,iBAAiB,0CAA2C,CAAA,CAEvE,CAAA,CAAA,CAEF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAA/D,EAAA,IAACO,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,gHC7RA,SAAwByD,IAAmB,CACzC,OAEIlE,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,uBACN,YAAY,6GACZ,UAAU,2BACV,MAAM,wCAAA,CACR,EACAH,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,OAAO,GAAG,MACvB,SAAA,CACEA,OAAAC,EAAAA,SAAA,CAAA,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,iFACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,4BAIb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,6BACb,SAAAF,EAAA,KAACe,GAAK,GAAI,IAAK,UAAU,OACvB,SAAA,CAAAb,EAAA,IAAC,MAAA,CACC,IAAI,gCACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,GACR,UAAU,YAAA,CACZ,EACAA,EAAA,IAAC,MAAA,CACC,IAAI,+BACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,GACR,UAAU,WAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,SAEC,MAAI,CAAA,UAAU,aAAa,KAAK,SAAS,SAAU,EAClD,SAAA,CAACA,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAI,MAAA,CAAA,CAAA,EACxC,QAEC,MAAI,CAAA,UAAU,wBACb,SAACF,EAAA,KAAA,KAAA,CAAG,UAAU,wDACZ,SAAA,CAAAE,EAAAA,IAAC,MAAG,UAAU,SACZ,SAACF,EAAA,KAAA,IAAA,CAAE,KAAK,iCACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,uBAAwB,CAAA,EAAE,yBAAA,CAAA,CAEzC,CACF,CAAA,EACCA,MAAA,KAAA,CACC,SAACF,EAAAA,KAAA,IAAA,CAAE,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,sBAAuB,CAAA,EAAE,eAAA,CAAA,CACxC,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CAEF,CACF,CAAA,EAEAA,EAAAA,IAAC,OAAK,CAAA,GAAG,OAEP,SAAAA,EAAA,IAAC,UAAA,CACC,UAAU,mEACV,MAAO,CACL,gBACE,wDACJ,EACA,GAAG,OAEH,eAAC,MAAI,CAAA,UAAU,yFAEb,SAACA,MAAA,MAAA,CAAI,UAAU,eACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,eAAC,MAAI,CAAA,UAAU,kEACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,UACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,eAAe,iBAAe,MAC3C,eAAC,KAAG,CAAA,UAAU,6BAA6B,SAAA,KAAG,CAAA,EAChD,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,iBAAe,MAEf,SAACA,EAAA,IAAA,KAAA,CAAG,UAAU,sBAAsB,SAEpC,mDAAA,CAAA,CAAA,CACF,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,4BACV,iBAAe,MAEf,SAAAF,EAAA,KAACe,EAAA,CACC,GAAI,IACJ,UAAU,wDAEV,SAAA,CAACb,EAAAA,IAAA,IAAA,CAAE,UAAU,oCAAqC,CAAA,EAClDA,EAAAA,IAAC,QAAK,SAAiB,mBAAA,CAAA,CAAA,CAAA,CAAA,CACzB,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,EACF,CAAA,CACF,CAEF,CAAA,CAAA,CAAA,CAGJ,CAAA,CAAA,EACF,QAECO,EAAO,CAAA,CAAA,CAAA,EACV,EAAO,GAAA,CACT,CAAA,CAAA,EACF,CAEJ,gHCjHM0D,GAAa,IAAM,CACjB,KAAA,CAAE,EAAA1B,CAAE,EAAIvB,EAAe,EACvBkD,EAAWC,EAAY,EACvB,CAACC,EAAUC,CAAW,EAAIjD,WAAS,CACvC,MAAO,GACP,SAAU,EAAA,CACX,EACK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACc,EAAOyB,CAAQ,EAAIvC,EAAAA,SAAS,EAAE,EAE/BkD,EAAgBC,GAAM,CACdF,EAAA,CACV,GAAGD,EACH,CAACG,EAAE,OAAO,IAAI,EAAGA,EAAE,OAAO,KAAA,CAC3B,EAEGrC,KAAgB,EAAE,CACxB,EAEMsC,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBjD,EAAW,EAAI,EACfqC,EAAS,EAAE,EAEP,GAAA,CACF,KAAM,CAAE,SAAAc,EAAU,KAAAC,CAAA,EAAS,MAAMC,GAAQ,MAAMP,CAAQ,EAEnDM,EAAK,SAEM,aAAA,QAAQ,aAAcA,EAAK,KAAK,EAC7C,aAAa,QAAQ,YAAa,KAAK,UAAUA,EAAK,IAAI,CAAC,EAG3DR,EAAS,kBAAkB,GAElBP,EAAAe,EAAK,SAAW,cAAc,QAElCE,EAAK,CACJ,QAAA,MAAM,eAAgBA,CAAG,EACjCjB,EAAS,kCAAkC,CAAA,QAC3C,CACArC,EAAW,EAAK,CAAA,CAEpB,EAEA,OAEIxB,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,0BACN,YAAY,oDACZ,QAAS,EAAA,CACX,EAGAD,EAAAA,IAAC,OAAI,GAAG,OAAO,UAAU,OAEvB,SAAAA,EAAAA,IAAC,OAAK,CAAA,GAAG,OAEP,SAAAA,EAAA,IAAC,UAAA,CACC,UAAU,4EACV,GAAG,cAEH,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,4CAEb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0CACb,SAAA,CAACE,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,YAAA,EAAO,QAAA,EACpD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,uCAAA,CAAA,CAAA,EACF,EAGCkC,GACEpC,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,YAAa,CAAA,EACzBkC,CAAA,EACH,EAIDpC,EAAA,KAAA,OAAA,CAAK,UAAU,oBAAoB,SAAU0E,EAE5C,SAAA,CAAC1E,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAE,MAAC,QAAM,CAAA,QAAQ,QAAQ,UAAU,UAAU,SAE3C,gBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAY,gBACZ,MAAOoE,EAAS,MAChB,SAAUE,EACV,SAAQ,GACR,aAAa,OAAA,CAAA,CACf,EACF,EAGAxE,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,MAAC,QAAM,CAAA,QAAQ,WAAW,UAAU,UAAU,SAE9C,WAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,WACL,KAAK,WACL,GAAG,WACH,UAAU,8BACV,YAAY,WACZ,MAAOoE,EAAS,SAChB,SAAUE,EACV,SAAQ,GACR,aAAa,kBAAA,CAAA,CACf,EACF,EAGAtE,EAAAA,IAAC,MAAI,CAAA,UAAU,aACb,SAAAA,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,2DACV,SAAUqB,EAET,WAEGvB,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,4BAA6B,CAAA,EAAI,eAAA,CAAA,CAEhD,EAGEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,SAAA,CAElC,CAAA,CAAA,CAAA,CAGN,CAAA,CAAA,EACF,EAGAA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,mBAAmB,SAAA,wCAEhC,CAAA,CACF,CAAA,CAAA,EACF,CAAA,CACF,EACF,CACF,CAAA,CAAA,GAEJ,CACF,CAAA,CAAA,EACF,CAEJ,2GChKM6E,GAAiB,IAAM,CAC3B,MAAMX,EAAWC,EAAY,EACvB,CAACW,EAAMC,CAAO,EAAI3D,EAAAA,SAAS,IAAI,EAC/B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EA2C3C,OAzCAO,EAAAA,UAAU,IAAM,EACI,SAAY,CACtB,MAAAqD,EAAQ,aAAa,QAAQ,YAAY,EACzCC,EAAW,aAAa,QAAQ,WAAW,EAE7C,GAAA,CAACD,GAAS,CAACC,EAAU,CACvBf,EAAS,QAAQ,EACjB,MAAA,CAGE,GAAA,CAEF,KAAM,CAAE,SAAAO,EAAU,KAAAC,CAAS,EAAA,MAAMC,GAAQ,MAAM,EAE3CF,EAAS,IAAMC,EAAK,QACtBK,EAAQL,EAAK,IAAI,GAGjB,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnCR,EAAS,QAAQ,SAEZhC,EAAO,CACN,QAAA,MAAM,qBAAsBA,CAAK,EACzC,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnCgC,EAAS,QAAQ,CAAA,QACjB,CACA5C,EAAW,EAAK,CAAA,CAEpB,GAEU,CAAA,EACT,CAAC4C,CAAQ,CAAC,EAQT7C,EAECrB,EAAA,IAAA,MAAA,CAAI,GAAG,OAAO,UAAU,OACvB,SAACA,MAAA,OAAA,CAAK,GAAG,OACP,SAACA,EAAA,IAAA,UAAA,CAAQ,UAAU,eACjB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,kBACV,MAAO,CACL,SAAU,OACV,UAAW,yBAAA,CACb,CACD,EACDA,EAAAA,IAAC,OAAI,UAAU,QACb,eAAC,MAAI,CAAA,UAAU,2BAA2B,SAAA,YAAU,CAAA,CACtD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAMAF,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,8BACN,YAAY,mDACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACoF,EAAY,CAAA,MAAM,YAEjB,SAAA,CAACpF,EAAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,2BAA2B,CAChD,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAW,cAAA,EAC1CA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,oBAAoB,CACzC,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAU,aAAA,EACzCA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,wBAAwB,CAC7C,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAQ,WAAA,EACvCA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,iBAAiB,CACtC,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAU,aAAA,EACzCA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,QACb,SAAAA,EAAA,IAAC,MAAG,UAAU,0CAA0C,yBAExD,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,wBAAwB,CAC7C,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAa,gBAAA,EACxDA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,wEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMkE,EAAS,iBAAiB,EACzC,UAAU,kCACX,SAAA,aAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACpE,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,uBAAuB,CAC5C,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAY,eAAA,EACvDA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,kEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMkE,EAAS,cAAc,EACtC,UAAU,kCACX,SAAA,cAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACpE,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,+BAA+B,CACpD,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAE5C,oBAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,wEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMkE,EAAS,mBAAmB,EAC3C,UAAU,kCACX,SAAA,kBAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,2GCvNMiB,GAAiB,IAAM,CAC3B,MAAMjB,EAAWC,EAAY,EACvB,CAACrC,EAAOsD,CAAQ,EAAIhE,EAAAA,SAAS,CAAA,CAAE,EAC/B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACc,EAAOyB,CAAQ,EAAIvC,EAAAA,SAAS,EAAE,EAC/B,CAACiE,EAASC,CAAU,EAAIlE,WAAS,CACrC,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,EAAA,CACT,EACK,CAACY,EAAYuD,CAAa,EAAInE,EAAAA,SAAS,CAAA,CAAE,EAE/CO,EAAAA,UAAU,IAAM,CACJ6D,EAAA,CAAA,EACT,CAACH,CAAO,CAAC,EAEZ,MAAMG,EAAY,SAAY,CACxB,GAAA,CACFlE,EAAW,EAAI,EAEf,MAAM4B,EAAS,CAAC,EACT,OAAA,QAAQmC,CAAO,EAAE,QAAQ,CAAC,CAACI,EAAKC,CAAK,IAAM,CAC5CA,GAASA,IAAU,QACrBxC,EAAOuC,CAAG,EAAIC,EAChB,CACD,EAED,KAAM,CAAE,SAAAjB,EAAU,KAAAC,CAAA,EAAS,MAAMiB,EAAS,SAASzC,CAAM,EAErDwB,EAAK,SACEU,EAAAV,EAAK,KAAK,KAAK,EACVa,EAAAb,EAAK,KAAK,UAAU,GAEzBf,EAAAe,EAAK,SAAW,sBAAsB,QAE1CxC,EAAO,CACN,QAAA,MAAM,oBAAqBA,CAAK,EACxCyB,EAAS,kCAAkC,CAAA,QAC3C,CACArC,EAAW,EAAK,CAAA,CAEpB,EAEMsE,EAAe,MAAOC,GAAW,CACrC,GACG,QACC,+EAAA,EAMA,GAAA,CACI,MAAAb,EAAQ,aAAa,QAAQ,YAAY,EAQzCN,EAAO,MAPI,MAAM,MAAM,aAAamB,CAAM,GAAI,CAClD,OAAQ,SACR,QAAS,CACP,cAAe,UAAUb,CAAK,EAAA,CAChC,CACD,GAE2B,KAAK,EAE7BN,EAAK,QACGc,EAAA,EAED7B,EAAAe,EAAK,SAAW,uBAAuB,QAE3CxC,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EACpCyB,EAAS,kCAAkC,CAAA,CAE/C,EAEMmC,EAAyB,MAAOD,GAAW,CAC3C,GAAA,CACI,MAAAb,EAAQ,aAAa,QAAQ,YAAY,EAQzCN,EAAO,MAPI,MAAM,MAAM,aAAamB,CAAM,qBAAsB,CACpE,OAAQ,QACR,QAAS,CACP,cAAe,UAAUb,CAAK,EAAA,CAChC,CACD,GAE2B,KAAK,EAE7BN,EAAK,QACGc,EAAA,EAED7B,EAAAe,EAAK,SAAW,6BAA6B,QAEjDxC,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,EAC/CyB,EAAS,kCAAkC,CAAA,CAE/C,EAEMoC,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,SAAA,CACT,EAGGC,EAAkB7D,GACjBA,EAAK,UASNA,EAAK,aAAe,IAAI,KAAKA,EAAK,WAAW,EAAQ,IAAA,KAErDtC,EAAA,KAAC,OAAK,CAAA,UAAU,mBACd,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAAI,WAAA,EAEnC,EAKFF,EAAA,KAAC,OAAK,CAAA,UAAU,mBACd,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAAI,WAAA,EAEnC,EApBEF,EAAA,KAAC,OAAK,CAAA,UAAU,qBACd,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,OAAA,EAElC,EAqBN,OAEIF,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,4BACN,YAAY,uCACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACoF,EAAY,CAAA,MAAM,aAEjB,SAAA,CAAAlF,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,WACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,mFAGlC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,uBACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMoE,EAAS,iBAAiB,EACzC,UAAU,kCAEV,SAAA,CAAClE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,UAAA,CAAA,CAAA,CAGpC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,oBACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAY,eAAA,EAC1CA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOqF,EAAQ,OACf,SAAWd,GACTe,EAAYY,IAAU,CACpB,GAAGA,EACH,OAAQ3B,EAAE,OAAO,MACjB,KAAM,CAAA,EACN,EAEJ,UAAU,eACV,YAAY,oBAAA,CAAA,CACd,EACF,EAEAzE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAM,SAAA,EACpCF,EAAA,KAAC,SAAA,CACC,MAAOuF,EAAQ,OACf,SAAWd,GACTe,EAAYY,IAAU,CACpB,GAAGA,EACH,OAAQ3B,EAAE,OAAO,MACjB,KAAM,CAAA,EACN,EAEJ,UAAU,eAEV,SAAA,CAACvE,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAS,YAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAS,YAAA,EAClCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAM,QAAA,CAAA,CAAA,CAAA,CAAA,CAC9B,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAQ,WAAA,EACtCF,EAAA,KAAC,SAAA,CACC,MAAOuF,EAAQ,MACf,SAAWd,GACTe,EAAYY,IAAU,CACpB,GAAGA,EACH,MAAO,SAAS3B,EAAE,OAAO,KAAK,EAC9B,KAAM,CAAA,EACN,EAEJ,UAAU,eAEV,SAAA,CAACvE,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAE,KAAA,EACpBA,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAE,KAAA,EACpBA,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAE,IAAA,CAAA,CAAA,CAAA,CAAA,CACvB,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCkC,GACEpC,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BkC,CAAA,EACH,EAIFlC,EAAAA,IAAC,OAAI,UAAU,cACZ,WACEF,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,mDAAoD,CAAA,EAChEA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAAgB,kBAAA,CAAA,CAAA,EAC5D,EACE8B,EAAM,SAAW,EAClBhC,OAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,wCAAyC,CAAA,EACrDA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,sBAAA,EACAA,EAAAA,IAAC,IAAE,CAAA,UAAU,sBACV,SAAAqF,EAAQ,QAAUA,EAAQ,SAAW,MAClC,oEACA,+CACN,CAAA,EACAvF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMoE,EAAS,iBAAiB,EACzC,UAAU,kCAEV,SAAA,CAAClE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,mBAAA,CAAA,CAAA,CAGpC,CAAA,CAAA,QAEC,MAAI,CAAA,UAAU,mBACb,SAACF,EAAAA,KAAA,QAAA,CAAM,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAM8B,EAAA,IAAKM,GAAS,CACnB,MAAM+D,EACJ/D,EAAK,aAAa,KAAMG,GAAMA,EAAE,WAAa,IAAI,GACjDH,EAAK,aAAa,CAAC,EAErB,cACG,KACC,CAAA,SAAA,CAAApC,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACZ,SAAA,CAAAsC,EAAK,eACJpC,EAAA,IAAC,MAAA,CACC,UAAU,eACV,IAAKoC,EAAK,cACV,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OAAA,CACb,CACF,SAED,MACC,CAAA,SAAA,CAAApC,MAAC,MAAI,CAAA,UAAU,UACZ,UAAAmG,GAAA,YAAAA,EAAoB,QAAS,WAChC,EACArG,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAAEsC,EAAK,IAAA,CAAK,CAAA,CAAA,CAC5C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,SACC,KACE,CAAA,SAAA,CAAA6D,EAAe7D,CAAI,EACnBA,EAAK,UACHtC,OAAA,OAAA,CAAK,UAAU,wBACd,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,UAAA,CAElC,CAAA,CAAA,EAEJ,QACC,KAAI,CAAA,SAAAoC,EAAK,OAAO,MAAQA,EAAK,OAAO,MAAM,EAC1CpC,EAAA,IAAA,KAAA,CAAI,SAAW+F,EAAA3D,EAAK,SAAS,EAAE,QAC/B,KACC,CAAA,SAAAtC,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IACPkE,EAAS,oBAAoB9B,EAAK,EAAE,EAAE,EAExC,UAAU,iCACV,MAAM,OAEN,SAAApC,EAAAA,IAAC,IAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,EAEAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM8F,EAAuB1D,EAAK,EAAE,EAC7C,UAAW,cACTA,EAAK,UACD,sBACA,qBACN,GACA,MAAOA,EAAK,UAAY,YAAc,UAEtC,SAAApC,EAAA,IAAC,IAAA,CACC,UACEoC,EAAK,UAAY,aAAe,QAAA,CAAA,CAEnC,CACH,EAEApC,EAAA,IAAC,SAAA,CACC,QAAS,IAAM4F,EAAaxD,EAAK,EAAE,EACnC,UAAU,gCACV,MAAM,SAEN,SAAApC,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAC1B,CAAA,CACF,CACF,CAAA,CAAA,CAAA,EAtEOoC,EAAK,EAuEd,CAAA,CAEH,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EAEJ,EAGCJ,EAAW,MAAQ,GACjBlC,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,WACb,SAACF,EAAA,KAAA,IAAA,CAAE,UAAU,wBAAwB,SAAA,CAAA,YACzBkC,EAAW,KAAO,GAAKA,EAAW,MAAQ,EAAE,MAAI,IACzD,KAAK,IAAIA,EAAW,KAAOA,EAAW,MAAOA,EAAW,KAAK,EAAG,IAAI,MACjEA,EAAW,MAAM,UAAA,CAAA,CACvB,CACF,CAAA,EACAhC,EAAA,IAAC,MAAI,CAAA,UAAU,uBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,aAAW,wBACd,SAAAF,OAAC,KAAG,CAAA,UAAU,kDACZ,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAW,aACTgC,EAAW,MAAQ,EAAI,WAAa,EACtC,GAEA,SAAAhC,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IACPsF,EAAYY,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,EAAA,EAEzD,SAAUlE,EAAW,MAAQ,EAC9B,SAAA,UAAA,CAAA,CAED,CACF,QAEC,KAAG,CAAA,UAAU,mBACZ,SAAClC,EAAA,KAAA,OAAA,CAAK,UAAU,YAAY,SAAA,CAAA,QACpBkC,EAAW,KAAK,OAAKA,EAAW,KAAA,CAAA,CACxC,CACF,CAAA,EAEAhC,EAAA,IAAC,KAAA,CACC,UAAW,aACTgC,EAAW,MAAQA,EAAW,MAAQ,WAAa,EACrD,GAEA,SAAAhC,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IACPsF,EAAYY,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,EAAA,EAEzD,SAAUlE,EAAW,MAAQA,EAAW,MACzC,SAAA,MAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,2GC9ZMoE,GAAkB,IAAM,eAC5B,KAAM,CAAE,EAAA7D,EAAG,KAAAxB,CAAK,EAAIC,EAAe,EAC7BkD,EAAWC,EAAY,EACvB,CAAE,GAAAkC,CAAG,EAAIlD,EAAU,EACnBmD,EAAY,EAAQD,EAEpB,CAAChF,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACmF,EAAQC,CAAS,EAAIpF,EAAAA,SAAS,EAAK,EACpC,CAACc,EAAOyB,CAAQ,EAAIvC,EAAAA,SAAS,EAAE,EAC/B,CAACqF,EAASC,CAAU,EAAItF,EAAAA,SAAS,EAAE,EAGnC,CAACuF,CAAkB,EAAIvF,EAAAA,SAAS,IAAM,OAAO,KAAKL,EAAK,MAAM,IAAI,CAAC,EAGlE,CAACqD,EAAUC,CAAW,EAAIjD,WAAS,IAAM,CAE7C,MAAMwF,EAAsB,CAAC,EACV,OAAAD,EAAA,QAASE,GAAS,CACnCD,EAAoBC,CAAI,EAAI,CAC1B,MAAO,GACP,QAAS,GACT,QAAS,GACT,UAAW,GACX,SAAU,GACV,SAAU,CAAA,CACZ,CAAA,CACD,EAEM,CACL,KAAM,GACN,SAAU,GACV,UAAW,GACX,YAAa,GACb,cAAe,KACf,iBAAkB,GAClB,SAAU,GACV,YAAa,CAAC,EACd,OAAQ,CAAC,EACT,aAAcD,CAChB,CAAA,CACD,EAEK,CAACE,EAAgBC,CAAiB,EAAI3F,EAAAA,SAAS,IAAI,EACnD,CAACuB,EAAYqE,CAAa,EAAI5F,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACyB,EAAMoE,CAAO,EAAI7F,EAAAA,SAAS,CAAA,CAAE,EAC7B,CAAC8F,EAAcC,CAAe,EAAI/F,EAAAA,SAAS,IAAI,EAGrDO,EAAAA,UAAU,IAAM,EACG,SAAY,CACvB,GAAA,CACFL,EAAW,EAAI,EACfqC,EAAS,EAAE,EAGL,MAAAqB,EAAQ,aAAa,QAAQ,YAAY,EAC/C,GAAI,CAACA,EAAO,CACVrB,EACE,6DACF,EACArC,EAAW,EAAK,EAChB,MAAA,CAIF,KAAM,CAAC8F,EAAkBC,CAAU,EAAI,MAAM,QAAQ,IAAI,CACvD1B,EAAS,cAAc,EACvBA,EAAS,QAAQ,CAAA,CAClB,EAGD,GAAIyB,EAAiB,SAAS,IAAMA,EAAiB,KACnDJ,EAAcI,EAAiB,KAAK,MAAQ,CAAA,CAAE,MACzC,CAML,GALQ,QAAA,MACN,yBACAA,EAAiB,SAAS,OAC1BA,EAAiB,SAAS,UAC5B,EAEEA,EAAiB,SAAS,SAAW,KACrCA,EAAiB,SAAS,SAAW,IACrC,CACAzD,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEFqD,EAAc,CAAA,CAAE,CAAA,CAIlB,GAAIK,EAAW,SAAS,IAAMA,EAAW,KACvCJ,EAAQI,EAAW,KAAK,MAAQ,CAAA,CAAE,MAC7B,CAML,GALQ,QAAA,MACN,mBACAA,EAAW,SAAS,OACpBA,EAAW,SAAS,UACtB,EAEEA,EAAW,SAAS,SAAW,KAC/BA,EAAW,SAAS,SAAW,IAC/B,CACA1D,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEFsD,EAAQ,CAAA,CAAE,CAAA,CAIZ,GAAIX,EAAW,CAGb,MAAMgB,EAAU,MAAM,MAAM,GAAGC,EAAY,gBAAgBlB,CAAE,GAAI,CAC/D,QAAS,CAAE,cAAe,UAAUrB,CAAK,EAAG,CAAA,CAC7C,EAED,GAAIsC,EAAQ,GACN,GAAA,CAEF,MAAMlF,GADW,MAAMkF,EAAQ,KAAK,GACd,KAGhBE,EAAkB,CAAC,EACrBpF,EAAK,cAAgB,MAAM,QAAQA,EAAK,YAAY,GACjDA,EAAA,aAAa,QAASG,GAAM,CACfA,EAAAA,EAAE,QAAQ,EAAIA,CAAA,CAC/B,EAGH8B,EAAa6B,IAAU,CACrB,GAAGA,EACH,KAAM9D,EAAK,MAAQ,GACnB,SAAUA,EAAK,UAAY,GAC3B,UAAWA,EAAK,WAAa,GAC7B,YAAaA,EAAK,YACd,IAAI,KAAKA,EAAK,WAAW,EAAE,YAAY,EAAE,MAAM,EAAG,EAAE,EACpD,GACJ,cAAe,KACf,iBAAkBA,EAAK,kBAAoB,GAC3C,SAAUA,EAAK,UAAY,GAC3B,YAAaA,EAAK,WACdA,EAAK,WAAW,IAAKqF,GAAMA,EAAE,EAAE,EAC/B,CAAC,EACL,OAAQrF,EAAK,KAAOA,EAAK,KAAK,IAAKG,GAAMA,EAAE,EAAE,EAAI,CAAC,EAClD,aAAc,CAAE,GAAG2D,EAAK,aAAc,GAAGsB,CAAgB,CAAA,EACzD,EAEEpF,EAAK,eACP+E,EAAgB/E,EAAK,aAAa,QAE7BsF,EAAW,CACV,QAAA,MAAM,iCAAkCA,CAAS,EACzD/D,EAAS,oDAAoD,CAAA,MAGvD,QAAA,MACN,mBACA2D,EAAQ,OACRA,EAAQ,UACV,EACA3D,EACE,wBAAwB2D,EAAQ,MAAM,IAAIA,EAAQ,UAAU,EAC9D,CACF,QAEKpF,EAAO,CACN,QAAA,MAAM,sBAAuBA,CAAK,EACtCA,EAAM,SAAWA,EAAM,QAAQ,SAAS,OAAO,EACjDyB,EACE,2FACF,EAEAA,EAAS,wCAAwC,CACnD,QACA,CACArC,EAAW,EAAK,CAAA,CAEpB,GAES,CAAA,EACR,CAAC+E,EAAIC,CAAS,CAAC,EAEZ,MAAAqB,EAAoB,CAACtF,EAAOqD,IAAU,CAC1CrB,EAAa6B,IAAU,CACrB,GAAGA,EACH,CAAC7D,CAAK,EAAGqD,CAAA,EACT,CACJ,EAEMkC,EAA0B,CAACC,EAAUxF,EAAOqD,IAAU,CAC1DrB,EAAa6B,IAAU,CACrB,GAAGA,EACH,aAAc,CACZ,GAAGA,EAAK,aACR,CAAC2B,CAAQ,EAAG,CACV,GAAG3B,EAAK,aAAa2B,CAAQ,EAC7B,CAACxF,CAAK,EAAGqD,CAAA,CACX,CACF,EACA,CACJ,EAEMoC,GAAqBvD,GAAM,CAC/B,MAAMwD,EAAOxD,EAAE,OAAO,MAAM,CAAC,EAC7B,GAAIwD,EAAM,CACR1D,EAAa6B,IAAU,CACrB,GAAGA,EACH,cAAe6B,CAAA,EACf,EAGI,MAAAC,EAAS,IAAI,WACZA,EAAA,OAAUzD,GAAM,CACLA,EAAAA,EAAE,OAAO,MAAM,CACjC,EACAyD,EAAO,cAAcD,CAAI,CAAA,CAE7B,EAEMvD,GAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBiC,EAAU,EAAI,EACd7C,EAAS,EAAE,EACX+C,EAAW,EAAE,EAET,GAAA,CACI,MAAA1B,EAAQ,aAAa,QAAQ,YAAY,EACzCiD,EAAiB,IAAI,SAGZA,EAAA,OAAO,OAAQ7D,EAAS,IAAI,EAC5B6D,EAAA,OAAO,WAAY7D,EAAS,QAAQ,EACpC6D,EAAA,OAAO,YAAa7D,EAAS,SAAS,EACjDA,EAAS,aACI6D,EAAA,OAAO,cAAe7D,EAAS,WAAW,EAE5C6D,EAAA,OAAO,mBAAoB7D,EAAS,gBAAgB,EAC/DA,EAAS,UACI6D,EAAA,OAAO,WAAY7D,EAAS,QAAQ,EAItC6D,EAAA,OACb,cACA,KAAK,UAAU7D,EAAS,WAAW,CACrC,EACA6D,EAAe,OAAO,SAAU,KAAK,UAAU7D,EAAS,MAAM,CAAC,EAChD6D,EAAA,OACb,eACA,KAAK,UAAU7D,EAAS,YAAY,CACtC,EAGIA,EAAS,eACI6D,EAAA,OAAO,gBAAiB7D,EAAS,aAAa,EAI3D,IAAAxC,EACA0E,EACF1E,EAAS,MAAMC,EAAQ,WAAWwE,EAAI4B,CAAc,EAE3CrG,EAAA,MAAMC,EAAQ,WAAWoG,CAAc,EAG5C,KAAA,CAAE,SAAAxD,EAAU,KAAAC,CAAA,EAAS9C,EAE3B,GAAI6C,EAAS,IAAMC,GAAQA,EAAK,QAC9BgC,EACE,aAAaJ,EAAY,UAAY,SAAS,gBAChD,EACA,WAAW,IAAM,CACfpC,EAAS,cAAc,GACtB,GAAI,MACF,CACL,MAAMgE,GACJxD,GAAA,YAAAA,EAAM,UACN,aAAa4B,EAAY,SAAW,QAAQ,aAC9C3C,EAASuE,CAAY,CAAA,QAEhBhG,EAAO,CACN,QAAA,MAAM,cAAeA,CAAK,EAClCyB,EAAS,kCAAkC,CAAA,QAC3C,CACA6C,EAAU,EAAK,CAAA,CAEnB,EAEA,OAEI1G,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAO,GAAGqG,EAAY,OAAS,QAAQ,qBACvC,YAAY,+CACZ,QAAS,EAAA,CACX,EAEAtG,EAAA,IAACkF,EAAA,CACC,MAAOoB,EAAY,iBAAmB,uBAEtC,SAACxG,EAAAA,KAAA,OAAA,CAAK,SAAU0E,GAAc,UAAU,aAErC,SAAA,CAAAtC,GACEpC,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAkC,CAAA,EACH,EAGDuE,GACE3G,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAyG,CAAA,EACH,EAIF3G,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,sBAAA,CACX,EAAe,gBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,kDAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,YAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoE,EAAS,KAChB,SAAWG,GAAMoD,EAAkB,OAAQpD,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,eAAA,CACd,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,sEAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,qBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOoE,EAAS,SAChB,SAAWG,GACToD,EAAkB,WAAYpD,EAAE,OAAO,KAAK,EAE9C,UAAU,eACV,YAAY,IACZ,IAAI,IACJ,IAAI,IAAA,CACN,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,sCAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,MAAA,CACX,EAAe,sBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,iBACL,MAAOoE,EAAS,YAChB,SAAWG,GACToD,EAAkB,cAAepD,EAAE,OAAO,KAAK,EAEjD,UAAU,cAAA,CACZ,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,8DAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,cAAA,EAElB,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,WACH,QAASoE,EAAS,SAClB,SAAWG,GACToD,EAAkB,WAAYpD,EAAE,OAAO,OAAO,EAEhD,UAAU,kBAAA,CACZ,EACCzE,EAAA,KAAA,QAAA,CAAM,UAAU,mBAAmB,QAAQ,WAC1C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,eAAA,EAElB,EACCA,EAAA,IAAA,QAAA,CAAM,UAAU,+BAA+B,SAEhD,4CAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,YACH,QAASoE,EAAS,UAClB,SAAWG,GACToD,EAAkB,YAAapD,EAAE,OAAO,OAAO,EAEjD,UAAU,kBAAA,CACZ,EACCzE,EAAA,KAAA,QAAA,CAAM,UAAU,mBAAmB,QAAQ,YAC1C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,WAAA,EAElB,EACCA,EAAA,IAAA,QAAA,CAAM,UAAU,+BAA+B,SAEhD,sCAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,+BAAgC,CAAA,EAAI,gBAAA,EAEnD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,oEAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,gBAAiB,CAAA,EAAI,cAAA,EAEpC,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,OAAO,UACP,SAAU8H,GACV,UAAU,cAAA,CACZ,EACC9H,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,iEAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,uBAAwB,CAAA,EAAI,UAAA,EAE3C,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoE,EAAS,iBAChB,SAAWG,GACToD,EAAkB,mBAAoBpD,EAAE,OAAO,KAAK,EAEtD,UAAU,eACV,YAAY,sCAAA,CACd,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,+CAAA,CAAA,CAAA,EACF,EAECkH,GACCpH,EAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,QACb,SAAAA,EAAA,IAAC,SAAM,UAAU,aAAa,yBAAa,CAC7C,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,cACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAKkH,EACL,IAAI,UACJ,UAAU,gBACV,MAAO,CAAE,SAAU,QAAS,OAAQ,MAAO,CAAA,CAAA,CAE/C,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGApH,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,+BAAgC,CAAA,EAAI,0BAAA,EAEnD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,6EAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,sBACZ,SAAmB2G,EAAA,IAAKE,GACvB/G,EAAA,KAAC,SAAA,CAEC,KAAK,SACL,QAAS,IAAMiH,EAAkBF,CAAI,EACrC,UAAW,gBACTC,IAAmBD,EAAO,SAAW,EACvC,GAEA,SAAA,CAAC7G,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5B6G,EAAK,YAAY,EACjBA,IAAS,MACR7G,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAa,SAAU,YAAA,CAAA,CAAA,CAAA,EAVpC6G,CAaR,CAAA,EACH,EAGA/G,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,UACxB8G,EAAe,YAAY,EAAE,IACpCA,IAAmB,MAClB9G,EAAAA,IAAC,OAAK,CAAA,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAExC,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAO+B,EAAAqC,EAAS,aAAa0C,CAAc,IAApC,YAAA/E,EAAuC,QAAS,GACvD,SAAWwC,GACTqD,EACEd,EACA,QACAvC,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,wBACZ,SAAUuC,IAAmB,IAAA,CAC/B,EACAhH,EAAAA,KAAC,QAAM,CAAA,UAAU,uBAAuB,SAAA,CAAA,sCACF,IACnCgH,EAAe,YAAY,CAAA,CAC9B,CAAA,CAAA,EACF,EAEAhH,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,YACtB8G,EAAe,YAAY,EAAE,GAAA,EACzC,EACA9G,EAAA,IAAC,WAAA,CACC,QAAOiC,EAAAmC,EAAS,aAAa0C,CAAc,IAApC,YAAA7E,EAAuC,UAAW,GACzD,SAAWsC,GACTqD,EACEd,EACA,UACAvC,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,oCAAA,CACd,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,6EAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,kBAAmB,CAAA,EAAI,YAC1B8G,EAAe,YAAY,EAAE,IACtCA,IAAmB,MAClB9G,EAAAA,IAAC,OAAK,CAAA,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAExC,EACAA,EAAA,IAAC,WAAA,CACC,QAAOwC,EAAA4B,EAAS,aAAa0C,CAAc,IAApC,YAAAtE,EAAuC,UAAW,GACzD,SAAW+B,GACTqD,EACEd,EACA,UACAvC,EAAE,OAAO,KACX,EAEF,KAAM,GACN,UAAU,8BACV,YAAY,uFACZ,SAAUuC,IAAmB,IAAA,CAC/B,EACAhH,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,+IAAA,CAIlC,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,aAAc,CAAA,EAAI,eAClB8G,EAAe,YAAY,EAAE,GAAA,EAC5C,EACA9G,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAOmI,EAAA/D,EAAS,aAAa0C,CAAc,IAApC,YAAAqB,EAAuC,YAAa,GAC3D,SAAW5D,GACTqD,EACEd,EACA,YACAvC,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,uBACZ,UAAU,IAAA,CACZ,EACAzE,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,gBAAiB,CAAA,EAAI,iEAAA,CAGpC,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,CAAA,EAAI,qBACpB8G,EAAe,YAAY,EAAE,GAAA,EAClD,EACA9G,EAAA,IAAC,WAAA,CACC,QAAOoI,EAAAhE,EAAS,aAAa0C,CAAc,IAApC,YAAAsB,EAAuC,WAAY,GAC1D,SAAW7D,GACTqD,EACEd,EACA,WACAvC,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,6BACZ,UAAU,KAAA,CACZ,EACAzE,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,gBAAiB,CAAA,EAAI,wEAAA,CAGpC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,kBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMkE,EAAS,cAAc,EACtC,UAAU,sCACX,SAAA,QAAA,CAED,EAEAlE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,SAAUuG,EACV,UAAU,kCAET,WAEGzG,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,4BAA6B,CAAA,EAAI,WAAA,CAAA,CAEhD,EAGEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5BsG,EAAY,cAAgB,aAAA,CAC/B,CAAA,CAAA,CAAA,CAEJ,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACF,CAEJ,2GCvuBM+B,GAAkB,IAAM,CACXlE,EAAY,EAC7B,KAAM,CAACxB,EAAYqE,CAAa,EAAI5F,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACc,EAAOyB,CAAQ,EAAIvC,EAAAA,SAAS,EAAE,EAC/B,CAACqF,EAASC,CAAU,EAAItF,EAAAA,SAAS,EAAE,EACnC,CAACkH,EAAWC,CAAY,EAAInH,EAAAA,SAAS,EAAK,EAC1C,CAACoH,EAAiBC,CAAkB,EAAIrH,EAAAA,SAAS,IAAI,EACrD,CAACgD,EAAUC,CAAW,EAAIjD,WAAS,CACvC,KAAM,GACN,YAAa,GACb,MAAO,SAAA,CACR,EAGDO,EAAAA,UAAU,IAAM,CACC+G,EAAA,CACjB,EAAG,EAAE,EAEL,MAAMA,EAAiB,SAAY,CAC7B,GAAA,CACFpH,EAAW,EAAI,EACf,KAAM,CAAE,SAAAmD,EAAU,KAAAC,CAAS,EAAA,MAAMiB,EAAS,cAAc,EAEpDjB,EAAK,QACOsC,EAAAtC,EAAK,MAAQ,EAAE,EAEpBf,EAAAe,EAAK,SAAW,2BAA2B,QAE/CxC,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,EAC7CyB,EAAS,kCAAkC,CAAA,QAC3C,CACArC,EAAW,EAAK,CAAA,CAEpB,EAEMqG,EAAoB,CAACtF,EAAOqD,IAAU,CAC1CrB,EAAqB6B,IAAA,CACnB,GAAGA,EACH,CAAC7D,CAAK,EAAGqD,CAAA,EACT,CACJ,EAEMlB,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBZ,EAAS,EAAE,EACX+C,EAAW,EAAE,EAET,GAAA,CACF,IAAIjC,EAAUC,EAEV8D,EACD,CAAE,SAAA/D,EAAU,KAAAC,GAAS,MAAMiB,EAAS,eAAe6C,EAAgB,GAAIpE,CAAQ,EAE/E,CAAE,SAAAK,EAAU,KAAAC,CAAA,EAAS,MAAMiB,EAAS,eAAevB,CAAQ,EAG1DM,EAAK,SACIgC,EAAA8B,EAAkB,iCAAmC,gCAAgC,EAChGD,EAAa,EAAK,EAClBE,EAAmB,IAAI,EACvBpE,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC5CqE,EAAA,GAEN/E,EAAAe,EAAK,SAAW,yBAAyB,QAE7CxC,EAAO,CACN,QAAA,MAAM,uBAAwBA,CAAK,EAC3CyB,EAAS,kCAAkC,CAAA,CAE/C,EAEMgF,EAAc/F,GAAa,CAC/B6F,EAAmB7F,CAAQ,EACfyB,EAAA,CACV,KAAMzB,EAAS,KACf,YAAaA,EAAS,aAAe,GACrC,MAAOA,EAAS,OAAS,SAAA,CAC1B,EACD2F,EAAa,EAAI,CACnB,EAEM3C,EAAe,MAAOgD,GAAe,CACzC,GAAK,OAAO,QAAQ,8EAA8E,EAI9F,GAAA,CACF,KAAM,CAAE,SAAAnE,EAAU,KAAAC,CAAA,EAAS,MAAMiB,EAAS,eAAeiD,CAAU,EAE/DlE,EAAK,SACPgC,EAAW,gCAAgC,EAC5BgC,EAAA,GAEN/E,EAAAe,EAAK,SAAW,2BAA2B,QAE/CxC,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,EAC7CyB,EAAS,kCAAkC,CAAA,CAE/C,EAEMkF,EAAkB,IAAM,CAC5BJ,EAAmB,IAAI,EACvBpE,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC3DkE,EAAa,EAAI,CACnB,EAEMO,EAAa,IAAM,CACvBP,EAAa,EAAK,EAClBE,EAAmB,IAAI,EACvBpE,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC3DV,EAAS,EAAE,CACb,EAEA,OAEI7D,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,4BACN,YAAY,4CACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACoF,EAAY,CAAA,MAAM,aAGjB,SAAA,CAAAlF,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,WACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,kGAElC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,uBACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAAS+I,EACT,UAAU,kCAEV,SAAA,CAAC7I,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,cAAA,CAAA,CAAA,CAGpC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCkC,GACEpC,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BkC,CAAA,EACH,EAGDuE,GACE3G,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5ByG,CAAA,EACH,EAIFzG,EAAAA,IAAC,OAAI,UAAU,cACZ,WACEF,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,mDAAoD,CAAA,EAChEA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAAqB,uBAAA,CAAA,CAAA,EACjE,EACE2C,EAAW,SAAW,EACvB7C,OAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,0CAA2C,CAAA,EACvDA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAAmB,sBAAA,EAClEA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,kEAAA,EACAF,EAAA,KAAC,SAAA,CACC,QAAS+I,EACT,UAAU,kCAEV,SAAA,CAAC7I,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,uBAAA,CAAA,CAAA,CAGpC,CAAA,CAAA,QAEC,MAAI,CAAA,UAAU,mBACb,SAACF,EAAAA,KAAA,QAAA,CAAM,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAQ,UAAA,CAAA,EACZA,EAAAA,IAAC,MAAG,SAAW,aAAA,CAAA,EACfA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,QACC,QACE,CAAA,SAAA2C,EAAW,IAAKC,wBACd,KACC,CAAA,SAAA,CAAA5C,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,UAAU,eACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,gBAAiB4C,EAAS,OAAS,SAAA,CACrC,CACD,SACA,MACC,CAAA,SAAA,CAAA5C,EAAA,IAAC,MAAI,CAAA,UAAU,UAAW,SAAA4C,EAAS,KAAK,EACxC9C,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAAE8C,EAAS,IAAA,CAAK,CAAA,CAAA,CAChD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACA5C,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,OAAA,CAAK,UAAU,aACb,SAAA4C,EAAS,aAAe,gBAAA,CAC3B,CACF,CAAA,EACC5C,MAAA,KAAA,CACC,SAACF,EAAAA,KAAA,OAAA,CAAK,UAAU,qBACb,SAAA,GAAAiC,EAAAa,EAAS,SAAT,YAAAb,EAAiB,QAAS,EAAE,QAAA,CAAA,CAC/B,CACF,CAAA,EACA/B,MAAC,MACE,SAAI,IAAA,KAAK4C,EAAS,SAAS,EAAE,qBAChC,QACC,KACC,CAAA,SAAA9C,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IAAM2I,EAAW/F,CAAQ,EAClC,UAAU,iCACV,MAAM,OAEN,SAAA5C,EAAAA,IAAC,IAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM4F,EAAahD,EAAS,EAAE,EACvC,UAAU,gCACV,MAAM,SAEN,SAAA5C,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAC1B,CAAA,CACF,CACF,CAAA,CAAA,GA/CO4C,EAAS,EAgDlB,EACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EAEJ,EAGC0F,GACEtI,EAAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,QAAS8I,EACtC,SAAAhJ,EAAAA,KAAC,MAAI,CAAA,UAAU,gBAAgB,QAAUyE,GAAMA,EAAE,gBAC/C,EAAA,SAAA,CAACzE,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,cACZ,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,gBAAiB,CAAA,EAC7BwI,EAAkB,gBAAkB,qBAAA,EACvC,EACAxI,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,cACV,QAAS8I,EAET,SAAA9I,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAC1B,EACF,EAEAF,EAAAA,KAAC,OAAK,CAAA,SAAU0E,EACd,SAAA,CAAAxE,EAAAA,IAAC,OAAI,UAAU,aACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,iBAAA,EAElC,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoE,EAAS,KAChB,SAAWG,GAAMoD,EAAkB,OAAQpD,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,sBACZ,SAAQ,EAAA,CAAA,CACV,EACF,EAEAzE,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,aAAA,EAElC,EACAA,EAAA,IAAC,WAAA,CACC,MAAOoE,EAAS,YAChB,SAAWG,GAAMoD,EAAkB,cAAepD,EAAE,OAAO,KAAK,EAChE,UAAU,eACV,KAAM,EACN,YAAY,oCAAA,CAAA,CACd,EACF,EAEAzE,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAAI,OAAA,EAErC,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,QACL,MAAOoE,EAAS,MAChB,SAAWG,GAAMoD,EAAkB,QAASpD,EAAE,OAAO,KAAK,EAC1D,UAAU,kCACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,CAAA,CACzC,EACAvE,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoE,EAAS,MAChB,SAAWG,GAAMoD,EAAkB,QAASpD,EAAE,OAAO,KAAK,EAC1D,UAAU,eACV,YAAY,SAAA,CAAA,CACd,EACF,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,2CAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS8I,EACT,UAAU,sCACX,SAAA,QAAA,CAED,EACAhJ,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAU,kCAEV,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5BwI,EAAkB,kBAAoB,iBAAA,CAAA,CAAA,CACzC,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAGJ,CAAA,CAAA,EACF,CAEJ,2GCnWMO,GAAY,IAAM,CACL5E,EAAY,EAC7B,KAAM,CAACtB,EAAMoE,CAAO,EAAI7F,EAAAA,SAAS,CAAA,CAAE,EAC7B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACc,EAAOyB,CAAQ,EAAIvC,EAAAA,SAAS,EAAE,EAC/B,CAACqF,EAASC,CAAU,EAAItF,EAAAA,SAAS,EAAE,EACnC,CAACkH,EAAWC,CAAY,EAAInH,EAAAA,SAAS,EAAK,EAC1C,CAAC4H,EAAYC,CAAa,EAAI7H,EAAAA,SAAS,IAAI,EAC3C,CAACgD,EAAUC,CAAW,EAAIjD,WAAS,CACvC,KAAM,EAAA,CACP,EAGDO,EAAAA,UAAU,IAAM,CACLuH,EAAA,CACX,EAAG,EAAE,EAEL,MAAMA,EAAW,SAAY,CACvB,GAAA,CACF5H,EAAW,EAAI,EACf,KAAM,CAAE,SAAAmD,EAAU,KAAAC,CAAS,EAAA,MAAMiB,EAAS,QAAQ,EAE9CjB,EAAK,QACCuC,EAAAvC,EAAK,MAAQ,EAAE,EAEdf,EAAAe,EAAK,SAAW,qBAAqB,QAEzCxC,EAAO,CACN,QAAA,MAAM,mBAAoBA,CAAK,EACvCyB,EAAS,kCAAkC,CAAA,QAC3C,CACArC,EAAW,EAAK,CAAA,CAEpB,EAEMqG,EAAoB,CAACtF,EAAOqD,IAAU,CAC1CrB,EAAqB6B,IAAA,CACnB,GAAGA,EACH,CAAC7D,CAAK,EAAGqD,CAAA,EACT,CACJ,EAEMlB,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBZ,EAAS,EAAE,EACX+C,EAAW,EAAE,EAET,GAAA,CACF,IAAIjC,EAAUC,EAEVsE,EACD,CAAE,SAAAvE,EAAU,KAAAC,GAAS,MAAMiB,EAAS,UAAUqD,EAAW,GAAI5E,CAAQ,EAErE,CAAE,SAAAK,EAAU,KAAAC,CAAA,EAAS,MAAMiB,EAAS,UAAUvB,CAAQ,EAGrDM,EAAK,SACIgC,EAAAsC,EAAa,4BAA8B,2BAA2B,EACjFT,EAAa,EAAK,EAClBU,EAAc,IAAI,EACN5E,EAAA,CAAE,KAAM,GAAI,EACf6E,EAAA,GAEAvF,EAAAe,EAAK,SAAW,oBAAoB,QAExCxC,EAAO,CACN,QAAA,MAAM,kBAAmBA,CAAK,EACtCyB,EAAS,kCAAkC,CAAA,CAE/C,EAEMgF,EAAc7F,GAAQ,CAC1BmG,EAAcnG,CAAG,EACLuB,EAAA,CACV,KAAMvB,EAAI,IAAA,CACX,EACDyF,EAAa,EAAI,CACnB,EAEM3C,EAAe,MAAOuD,GAAU,CACpC,GAAK,OAAO,QAAQ,yEAAyE,EAIzF,GAAA,CACF,KAAM,CAAE,SAAA1E,EAAU,KAAAC,CAAA,EAAS,MAAMiB,EAAS,UAAUwD,CAAK,EAErDzE,EAAK,SACPgC,EAAW,2BAA2B,EAC7BwC,EAAA,GAEAvF,EAAAe,EAAK,SAAW,sBAAsB,QAE1CxC,EAAO,CACN,QAAA,MAAM,oBAAqBA,CAAK,EACxCyB,EAAS,kCAAkC,CAAA,CAE/C,EAEMkF,EAAkB,IAAM,CAC5BI,EAAc,IAAI,EACN5E,EAAA,CAAE,KAAM,GAAI,EACxBkE,EAAa,EAAI,CACnB,EAEMO,EAAa,IAAM,CACvBP,EAAa,EAAK,EAClBU,EAAc,IAAI,EACN5E,EAAA,CAAE,KAAM,GAAI,EACxBV,EAAS,EAAE,CACb,EAEA,OAEI7D,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,sBACN,YAAY,sCACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACoF,EAAY,CAAA,MAAM,OAGjB,SAAA,CAAAlF,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,WACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,4GAElC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,uBACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAAS+I,EACT,UAAU,kCAEV,SAAA,CAAC7I,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,SAAA,CAAA,CAAA,CAGpC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCkC,GACEpC,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BkC,CAAA,EACH,EAGDuE,GACE3G,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5ByG,CAAA,EACH,EAIFzG,EAAAA,IAAC,OAAI,UAAU,cACZ,WACEF,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,mDAAoD,CAAA,EAChEA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAAe,iBAAA,CAAA,CAAA,EAC3D,EACE6C,EAAK,SAAW,EACjB/C,OAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,uCAAwC,CAAA,EACpDA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAAa,gBAAA,EAC5DA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,6DAAA,EACAF,EAAA,KAAC,SAAA,CACC,QAAS+I,EACT,UAAU,kCAEV,SAAA,CAAC7I,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,kBAAA,CAAA,CAAA,CAGpC,CAAA,CAAA,QAEC,MAAI,CAAA,UAAU,mBACb,SAACF,EAAAA,KAAA,QAAA,CAAM,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAQ,UAAA,CAAA,EACZA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,QACC,QACE,CAAA,SAAA6C,EAAK,IAAKC,wBACR,KACC,CAAA,SAAA,CAAA9C,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,6BAA8B,CAAA,SAC1C,MACC,CAAA,SAAA,CAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,UAAW,SAAA8C,EAAI,KAAK,EACnChD,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAAEgD,EAAI,IAAA,CAAK,CAAA,CAAA,CAC3C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACC9C,MAAA,KAAA,CACC,SAACF,EAAAA,KAAA,OAAA,CAAK,UAAU,qBACb,SAAA,GAAAiC,EAAAe,EAAI,SAAJ,YAAAf,EAAY,QAAS,EAAE,QAAA,CAAA,CAC1B,CACF,CAAA,EACA/B,MAAC,MACE,SAAI,IAAA,KAAK8C,EAAI,SAAS,EAAE,qBAC3B,QACC,KACC,CAAA,SAAAhD,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IAAM2I,EAAW7F,CAAG,EAC7B,UAAU,iCACV,MAAM,OAEN,SAAA9C,EAAAA,IAAC,IAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM4F,EAAa9C,EAAI,EAAE,EAClC,UAAU,gCACV,MAAM,SAEN,SAAA9C,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAC1B,CAAA,CACF,CACF,CAAA,CAAA,GAnCO8C,EAAI,EAoCb,EACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EAEJ,EAGCwF,GACEtI,EAAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,QAAS8I,EACtC,SAAAhJ,EAAAA,KAAC,MAAI,CAAA,UAAU,gBAAgB,QAAUyE,GAAMA,EAAE,gBAC/C,EAAA,SAAA,CAACzE,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,cACZ,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,aAAc,CAAA,EAC1BgJ,EAAa,WAAa,gBAAA,EAC7B,EACAhJ,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,cACV,QAAS8I,EAET,SAAA9I,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAC1B,EACF,EAEAF,EAAAA,KAAC,OAAK,CAAA,SAAU0E,EACd,SAAA,CAACxE,EAAA,IAAA,MAAA,CAAI,UAAU,aACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAAAF,OAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,YAAA,EAElC,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoE,EAAS,KAChB,SAAWG,GAAMoD,EAAkB,OAAQpD,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,iBACZ,SAAQ,EAAA,CACV,EACCvE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,wEAAA,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS8I,EACT,UAAU,sCACX,SAAA,QAAA,CAED,EACAhJ,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAU,kCAEV,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5BgJ,EAAa,aAAe,YAAA,CAAA,CAAA,CAC/B,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAGJ,CAAA,CAAA,EACF,CAEJ"}