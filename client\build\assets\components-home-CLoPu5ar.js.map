{"version": 3, "file": "components-home-CLoPu5ar.js", "sources": ["../../src/components/home/<USER>", "../../src/hooks/usePageAnalytics.js", "../../src/data/services.js", "../../src/components/home/<USER>", "../../src/data/bms.js", "../../src/components/home/<USER>", "../../src/utils/api.jsx", "../../src/components/home/<USER>", "../../src/data/contact.js", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/data/team.js", "../../src/components/home/<USER>", "../../src/components/home/<USER>"], "sourcesContent": ["import React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function About() {\n  const { t } = useTranslation();\n  return (\n    <div className=\"col-lg-6 offset-lg-1\">\n      <div className=\"row\">\n        <div\n          className=\"col-sm-6 pt-60 pt-xs-0 mb-xs-40\"\n          data-rellax-y=\"\"\n          data-rellax-speed=\"-0.5\"\n          data-rellax-percentage=\"0.5\"\n        >\n          <div className=\"spot-box clearfix mb-30\">\n            <div className=\"spot-box-icon float-end ms-3\" />\n            <div className=\"spot-box-text text-end\">\n              <span className=\"text-gray\">{t(\"hero.brands\")}</span>\n            </div>\n          </div>\n          <img\n            src=\"/assets/img/2.jpg\"\n            width={400}\n            height={489}\n            className=\"w-100 round grayscale\"\n            alt=\"Image Description\"\n          />\n        </div>\n        <div\n          className=\"col-sm-6\"\n          data-rellax-y=\"\"\n          data-rellax-speed=\"0.5\"\n          data-rellax-percentage=\"0.5\"\n        >\n          <img\n            src=\"/assets/img/3.jpg\"\n            width={400}\n            height={489}\n            className=\"w-100 round grayscale\"\n            alt=\"Image Description\"\n          />\n          <div className=\"spot-box clearfix mt-30\">\n            <div className=\"spot-box-icon float-start me-3\" />\n            <div className=\"spot-box-text\">\n              <span className=\"text-gray\">{t(\"hero.interfaces\")}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import { useEffect, useRef } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { trackPageView, trackTimeOnPage, trackScrollDepth } from '@/utils/analytics';\n\nexport const usePageAnalytics = (pageTitle) => {\n  const location = useLocation();\n  const startTimeRef = useRef(null);\n  const scrollDepthRef = useRef(0);\n  const scrollMilestonesRef = useRef(new Set());\n\n  useEffect(() => {\n    // Track page view\n    const fullPageTitle = pageTitle || document.title;\n    const pageLocation = window.location.href;\n    \n    trackPageView(fullPageTitle, pageLocation);\n    startTimeRef.current = Date.now();\n\n    // Reset scroll tracking for new page\n    scrollDepthRef.current = 0;\n    scrollMilestonesRef.current.clear();\n\n    // Scroll depth tracking\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);\n\n      // Update max scroll depth\n      if (scrollPercentage > scrollDepthRef.current) {\n        scrollDepthRef.current = scrollPercentage;\n      }\n\n      // Track milestone percentages (25%, 50%, 75%, 90%, 100%)\n      const milestones = [25, 50, 75, 90, 100];\n      milestones.forEach(milestone => {\n        if (scrollPercentage >= milestone && !scrollMilestonesRef.current.has(milestone)) {\n          scrollMilestonesRef.current.add(milestone);\n          trackScrollDepth(milestone, pageLocation);\n        }\n      });\n    };\n\n    // Add scroll listener\n    window.addEventListener('scroll', handleScroll, { passive: true });\n\n    // Track time on page when user leaves\n    const handleBeforeUnload = () => {\n      if (startTimeRef.current) {\n        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);\n        trackTimeOnPage(timeOnPage, pageLocation);\n      }\n    };\n\n    // Track time on page when component unmounts (route change)\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === 'hidden' && startTimeRef.current) {\n        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);\n        trackTimeOnPage(timeOnPage, pageLocation);\n      }\n    };\n\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n\n    // Cleanup function\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n\n      // Track final time on page\n      if (startTimeRef.current) {\n        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);\n        trackTimeOnPage(timeOnPage, pageLocation);\n      }\n    };\n  }, [location.pathname, pageTitle]);\n\n  // Return analytics functions for manual tracking\n  return {\n    trackCustomEvent: (eventName, category, label, value, additionalParams) => {\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', eventName, {\n          event_category: category,\n          event_label: label,\n          value: value,\n          page_location: window.location.href,\n          ...additionalParams,\n          send_to: 'G-8NEGL4LL8Q'\n        });\n      }\n    },\n    getCurrentScrollDepth: () => scrollDepthRef.current,\n    getTimeOnPage: () => startTimeRef.current ? Math.round((Date.now() - startTimeRef.current) / 1000) : 0\n  };\n};\n", "export const services6 = [\n  {\n    width: 48,\n    height: 64,\n    path: \"M47.7 23.5h-10V5.8c0-1.6-1.3-2.8-2.8-2.8H5.8C4.3 3 3 4.3 3 5.8v29.7c0 1.6 1.3 2.8 2.8 2.8h10v10c0 1.6 1.3 2.8 2.8 2.8h29.1c1.6 0 2.8-1.3 2.8-2.8V26.4c.1-1.6-1.2-2.9-2.8-2.9zm-34.7-9.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm11.1-16.6h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm11.1-16.6h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm11.1 16.7h-4.2v-4.2h4.2v4.2zm0-8.4h-4.2v-4.2h4.2v4.2z\",\n    key: \"web\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M36 0H12C5.4 0 0 5.4 0 12v40c0 6.6 5.4 12 12 12h24c6.6 0 12-5.4 12-12V12c0-6.6-5.4-12-12-12zm-24 4h24c4.4 0 8 3.6 8 8v40c0 4.4-3.6 8-8 8H12c-4.4 0-8-3.6-8-8V12c0-4.4 3.6-8 8-8z M24 56c2.2 0 4 1.8 4 4s-1.8 4-4 4-4-1.8-4-4 1.8-4 4-4z\",\n    key: \"mobile\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M44 0H4C1.8 0 0 1.8 0 4v56c0 2.2 1.8 4 4 4h40c2.2 0 4-1.8 4-4V4c0-2.2-1.8-4-4-4zm-4 56H8V8h32v48z M16 16h16v4H16z M16 24h16v4H16z M16 32h16v4H16z\",\n    key: \"backend\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M24 0C10.7 0 0 10.7 0 24s10.7 24 24 24 24-10.7 24-24S37.3 0 24 0zm0 44c-11 0-20-9-20-20S13 4 24 4s20 9 20 20-9 20-20 20zm8-22c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z\",\n    key: \"ai\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M44 16L24 0 4 16v32l20 16 20-16V16zm-20 4c2.2 0 4 1.8 4 4s-1.8 4-4 4-4-1.8-4-4 1.8-4 4-4zm0 24c-6.6 0-12-5.4-12-12s5.4-12 12-12 12 5.4 12 12-5.4 12-12 12z\",\n    key: \"blockchain\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M42 48h-4V24c0-1.1-.9-2-2-2H24v-4h4c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h4v4H12c-1.1 0-2 .9-2 2v24H6c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2h-4V26h20v22h-4c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2z\",\n    key: \"bms\",\n  },\n];\n", "import { services6 } from \"@/data/services\";\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Service() {\n  const { t } = useTranslation();\n  return (\n    <>\n      <div\n        className=\"page-section bg-dark-1 bg-dark-alpha-80 light-content parallax-7 pb-140\"\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container position-relative\">\n          <div className=\"row mb-70 mb-sm-50\">\n            <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n              <h2 className=\"section-title mb-30 mb-sm-20\">\n                {t(\"services.title\")}\n              </h2>\n              <div className=\"text-gray\">{t(\"services.description\")}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"container mt-n140 position-relative z-index-1\">\n        <div className=\"row mb-n30\">\n          {services6.map((elm, i) => (\n            <div\n              key={i}\n              className=\"col-md-6 col-lg-4 d-flex align-items-stretch mb-30\"\n            >\n              <div className=\"services-3-item round text-center\">\n                <div className=\"wow fadeInUpShort\" data-wow-offset={50}>\n                  <div className=\"services-3-icon\">\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width={elm.width}\n                      height={elm.height}\n                      viewBox={`0 0 ${elm.width} ${elm.height}`}\n                    >\n                      <path d={elm.path} />\n                    </svg>\n                  </div>\n                  <h3 className=\"services-3-title\">\n                    {t(`services.${elm.key}.title`)}\n                  </h3>\n                  <div className=\"services-3-text\">\n                    {t(`services.${elm.key}.text`)}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </>\n  );\n}\n", "export const bmsItems = [\r\n  {\r\n    id: 1,\r\n    imageSrc: \"transparent\",\r\n    title: \"Core Module\",\r\n    type: \"Lightbox\",\r\n    categories: [\"core\"],\r\n    description: {\r\n      heading: \"Core Module\",\r\n      subheading: \"The Foundation of Your Business\",\r\n      text: \"The Core Module is the foundation of the Ultimation Studio platform. It provides the essential framework for managing your entire business ecosystem, including user management, permissions, system settings, and integration capabilities. With the Core Module, you can configure your business rules, policies, and strategies, while the AI assistant helps you manage tasks, monitor compliance, and track performance metrics.\",\r\n    },\r\n  },\r\n  {\r\n    id: 2,\r\n    imageSrc: \"transparent\",\r\n    title: \"Accounting Module\",\r\n    type: \"External Page\",\r\n    categories: [\"accounting\"],\r\n    description: {\r\n      heading: \"Accounting Module\",\r\n      subheading: \"Financial Management Simplified\",\r\n      text: \"The Accounting Module provides a comprehensive solution for managing your organization's financial operations. From journal entries and reconciliation to financial reporting and tax management, this module streamlines your accounting processes. With features like chart of accounts, balance sheets, income statements, and cash flow analysis, you'll have complete visibility into your financial health and can make informed decisions to drive your business forward.\",\r\n    },\r\n  },\r\n  {\r\n    id: 3,\r\n    imageSrc: \"transparent\",\r\n    title: \"Budget Module\",\r\n    type: \"External Page\",\r\n    categories: [\"budget\"],\r\n    description: {\r\n      heading: \"Budget Module\",\r\n      subheading: \"Strategic Financial Planning\",\r\n      text: \"Take control of your finances with our Budget Module. This powerful tool enables you to create, manage, and track budgets across your organization. Monitor income and expenses, analyze cash flow, and manage assets with ease. The forecasting capabilities help you plan for the future, while detailed reports provide insights into spending patterns and financial performance. With the Budget Module, you can make data-driven decisions to optimize your financial resources.\",\r\n    },\r\n  },\r\n  {\r\n    id: 4,\r\n    imageSrc: \"transparent\",\r\n    title: \"Human Resources\",\r\n    type: \"External Page\",\r\n    categories: [\"hr\", \"management\"],\r\n    description: {\r\n      heading: \"Human Resources Module\",\r\n      subheading: \"Empowering Your Workforce\",\r\n      text: \"The Human Resources Module helps you manage your most valuable asset - your people. From employee records and department structures to leave management and attendance tracking, this module streamlines HR operations. Handle compensation, benefits, and payroll with ease, while fostering employee development through performance management and training programs. The HR Module provides the tools you need to build and maintain a productive, engaged workforce.\",\r\n    },\r\n  },\r\n  {\r\n    id: 5,\r\n    imageSrc: \"transparent\",\r\n    title: \"Recruitment Module\",\r\n    type: \"External Page\",\r\n    categories: [\"hr\"],\r\n    description: {\r\n      heading: \"Recruitment Module\",\r\n      subheading: \"Talent Acquisition Reimagined\",\r\n      text: \"Transform your hiring process with our Recruitment Module. This comprehensive solution helps you manage job postings, track applications, and streamline candidate evaluations. The AI-powered matching system connects the right candidates with the right positions, while the customizable form builder allows you to create tailored application forms. With features for workforce intermediation and detailed analytics, you can optimize your recruitment strategy and find the best talent for your organization.\",\r\n    },\r\n  },\r\n  {\r\n    id: 6,\r\n    imageSrc: \"transparent\",\r\n    title: \"Production Module\",\r\n    type: \"Lightbox\",\r\n    categories: [\"production\", \"management\"],\r\n    description: {\r\n      heading: \"Production Module\",\r\n      subheading: \"Streamlining Operations\",\r\n      text: \"The Production Module optimizes your manufacturing and project management processes. Track projects from inception to completion, manage tasks and assignments, and ensure quality control at every step. With features for resource management, equipment tracking, and materials planning, you can maximize efficiency and minimize waste. The AI insights provide valuable recommendations for process improvements, while detailed reports help you monitor performance and make data-driven decisions.\",\r\n    },\r\n  },\r\n  {\r\n    id: 7,\r\n    imageSrc: \"transparent\",\r\n    title: \"Sales Module\",\r\n    type: \"External Page\",\r\n    categories: [\"sales\"],\r\n    description: {\r\n      heading: \"Sales Module\",\r\n      subheading: \"Revenue Growth Engine\",\r\n      text: \"Accelerate your sales cycle and boost revenue with our Sales Module. This powerful tool helps you manage leads, track opportunities, and close deals more efficiently. Monitor sales performance, analyze trends, and forecast future revenue with intuitive dashboards and reports. The customer relationship management features enable you to build stronger connections with clients, while the integration with other modules ensures seamless order processing and financial tracking.\",\r\n    },\r\n  },\r\n  {\r\n    id: 8,\r\n    imageSrc: \"transparent\",\r\n    title: \"Quality Control\",\r\n    type: \"Lightbox\",\r\n    categories: [\"production\"],\r\n    description: {\r\n      heading: \"Quality Control Module\",\r\n      subheading: \"Excellence in Every Detail\",\r\n      text: \"Ensure consistent quality across all your products and services with our Quality Control Module. Create and manage comprehensive checklists, document inspection results, and track quality metrics over time. The photo evidence feature allows you to visually document quality issues and resolutions, while the integration with the Production Module ensures that quality control is embedded in your manufacturing process. With detailed analytics and reporting, you can identify trends and continuously improve your quality standards.\",\r\n    },\r\n  },\r\n  {\r\n    id: 9,\r\n    imageSrc: \"transparent\",\r\n    title: \"Communication Module\",\r\n    type: \"External Page\",\r\n    categories: [\"communication\", \"management\"],\r\n    description: {\r\n      heading: \"Communication Module\",\r\n      subheading: \"Seamless Connectivity\",\r\n      text: \"The Communication Module enhances collaboration and information sharing across your organization. Manage emails, chat messages, and customer inquiries from a centralized dashboard. Create and manage custom API endpoints to integrate with external systems and services. With real-time notifications and message tracking, you can ensure that important communications are never missed. The module's intuitive interface makes it easy to stay connected with team members, partners, and customers.\",\r\n    },\r\n  },\r\n  {\r\n    id: 10,\r\n    imageSrc: \"transparent\",\r\n    title: \"Companies Module\",\r\n    type: \"External Page\",\r\n    categories: [\"companies\", \"management\"],\r\n    description: {\r\n      heading: \"Companies Module\",\r\n      subheading: \"Partner Relationship Management\",\r\n      text: \"The Companies Module helps you manage relationships with clients, suppliers, and other business partners. Track company details, monitor interactions, and assess risks with comprehensive tools for partner relationship management. The workforce needs assessment feature helps you plan resource allocation, while the compliance tracking ensures that all partnerships meet regulatory requirements. With detailed analytics and reporting, you can optimize your business relationships and drive mutual growth.\",\r\n    },\r\n  },\r\n  {\r\n    id: 11,\r\n    imageSrc: \"transparent\",\r\n    title: \"All Modules\",\r\n    type: \"External Page\",\r\n    categories: [\"all\"],\r\n    description: {\r\n      heading: \"Ultimation Studio\",\r\n      subheading: \"The Ultimate Business Management System\",\r\n      text: \"Ultimation Studio is our next-generation business management system that combines AI-driven automation with modular flexibility. It features a smart core that manages your business rules, policies, and strategies, while the AI assistant handles tasks on command, monitoring compliance and performance. Choose from essential modules including accounting, HR, recruiting, production, and quality control - all designed to grow with your business needs. Experience the future of business management, where complexity meets simplicity.\",\r\n    },\r\n  },\r\n];\r\n", "import { bmsItems } from \"@/data/bms\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { Gallery, Item } from \"react-photoswipe-gallery\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nexport default function DevskillsBMS() {\r\n  // Use reactive translation hook\r\n  const { t: translate, i18n } = useTranslation();\r\n  const currentLanguage = i18n.language || \"et\";\r\n\r\n  const [currentCategory, setCurrentCategory] = useState(\"all\");\r\n  const [filtered, setFiltered] = useState(bmsItems);\r\n  const [activeModule, setActiveModule] = useState(\"all\");\r\n\r\n  // Store the current module translation keys\r\n  const [moduleKeys, setModuleKeys] = useState({\r\n    heading: \"module.all.heading\",\r\n    subheading: \"module.all.subheading\",\r\n    text: \"module.all.text\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (currentCategory === \"all\") {\r\n      setFiltered(\r\n        [...bmsItems].filter((item) => !item.categories.includes(\"all\"))\r\n      );\r\n    } else {\r\n      setFiltered(\r\n        [...bmsItems].filter(\r\n          (elm) =>\r\n            elm.categories.includes(currentCategory) &&\r\n            !elm.categories.includes(\"all\")\r\n        )\r\n      );\r\n    }\r\n  }, [currentCategory, currentLanguage]);\r\n\r\n  // Function to handle module button click\r\n  const handleModuleClick = (moduleCategory) => {\r\n    setActiveModule(moduleCategory);\r\n\r\n    // Update the module keys based on the selected module\r\n    setModuleKeys({\r\n      heading: `module.${moduleCategory}.heading`,\r\n      subheading: `module.${moduleCategory}.subheading`,\r\n      text: `module.${moduleCategory}.text`,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"container\">\r\n        {/* Module Description */}\r\n        <div className=\"row mb-70 mb-sm-50\">\r\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\r\n            <h3 className=\"text-white opacity-05 mb-3 fs-5 text-uppercase\">\r\n              {translate(\"introducing\")}\r\n            </h3>\r\n            <h1 className=\"section-title mb-3\">\r\n              <span className=\"text-white opacity-06 fs-1\">DEVSKILLS</span>\r\n              <span className=\"text-white opacity-09 fs-1\">\r\n                {\" \"}\r\n                {translate(moduleKeys.heading)}\r\n              </span>\r\n            </h1>\r\n            <h3 className=\"text-white opacity-07 mb-4 fs-5 text-uppercase\">\r\n              {translate(moduleKeys.subheading)}\r\n            </h3>\r\n            <div className=\"text-white opacity-07\">\r\n              {translate(moduleKeys.text)}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Works Filter - Original Rounded Buttons */}\r\n        <div className=\"works-filter works-filter-elegant text-center mb-50\">\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"all\");\r\n              setCurrentCategory(\"all\");\r\n            }}\r\n            className={`filter ${activeModule === \"all\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.all\")}\r\n          </a>\r\n\r\n          {/* Core Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"core\");\r\n              setCurrentCategory(\"core\");\r\n            }}\r\n            className={`filter ${activeModule === \"core\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.core\")}\r\n          </a>\r\n\r\n          {/* Accounting Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"accounting\");\r\n              setCurrentCategory(\"accounting\");\r\n            }}\r\n            className={`filter ${\r\n              activeModule === \"accounting\" ? \"active\" : \"\"\r\n            }`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.accounting\")}\r\n          </a>\r\n\r\n          {/* Budget Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"budget\");\r\n              setCurrentCategory(\"budget\");\r\n            }}\r\n            className={`filter ${activeModule === \"budget\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.budget\")}\r\n          </a>\r\n\r\n          {/* HR Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"hr\");\r\n              setCurrentCategory(\"hr\");\r\n            }}\r\n            className={`filter ${activeModule === \"hr\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.hr\")}\r\n          </a>\r\n\r\n          {/* Recruitment Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"recruiting\");\r\n              setCurrentCategory(\"recruiting\");\r\n            }}\r\n            className={`filter ${\r\n              activeModule === \"recruiting\" ? \"active\" : \"\"\r\n            }`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.recruiting\")}\r\n          </a>\r\n\r\n          {/* Production Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"production\");\r\n              setCurrentCategory(\"production\");\r\n            }}\r\n            className={`filter ${\r\n              activeModule === \"production\" ? \"active\" : \"\"\r\n            }`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.production\")}\r\n          </a>\r\n\r\n          {/* Sales Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"sales\");\r\n              setCurrentCategory(\"sales\");\r\n            }}\r\n            className={`filter ${activeModule === \"sales\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.sales\")}\r\n          </a>\r\n\r\n          {/* Quality Control Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"quality\");\r\n              setCurrentCategory(\"quality\");\r\n            }}\r\n            className={`filter ${activeModule === \"quality\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.quality\")}\r\n          </a>\r\n\r\n          {/* Communication Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"communication\");\r\n              setCurrentCategory(\"communication\");\r\n            }}\r\n            className={`filter ${\r\n              activeModule === \"communication\" ? \"active\" : \"\"\r\n            }`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.communication\")}\r\n          </a>\r\n\r\n          {/* Companies Module */}\r\n          <a\r\n            onClick={() => {\r\n              handleModuleClick(\"companies\");\r\n              setCurrentCategory(\"companies\");\r\n            }}\r\n            className={`filter ${activeModule === \"companies\" ? \"active\" : \"\"}`}\r\n            style={{ cursor: \"pointer\" }}\r\n          >\r\n            {translate(\"bms.module.companies\")}\r\n          </a>\r\n        </div>\r\n        {/* End Works Filter */}\r\n      </div>\r\n      <div\r\n        className=\"position-relative\"\r\n        style={{\r\n          width: \"100%\",\r\n          maxWidth: \"1600px\",\r\n          margin: \"0 auto\",\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            width: \"100%\",\r\n            position: \"relative\",\r\n            borderRadius: \"20px 20px 0 0\",\r\n            overflow: \"hidden\",\r\n          }}\r\n        >\r\n          <img\r\n            src=\"/assets/img/devskills-bms.jpg\"\r\n            alt=\"BMS Background\"\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"auto\",\r\n              display: \"block\",\r\n              borderRadius: \"20px 20px 0 0\",\r\n            }}\r\n          />\r\n          <ul\r\n            className=\"works-grid work-grid-gut-sm hide-titles\"\r\n            id=\"work-grid\"\r\n            style={{\r\n              position: \"absolute\",\r\n              top: 0,\r\n              left: 0,\r\n              width: \"100%\",\r\n              height: \"100%\",\r\n              display: \"grid\",\r\n              gridTemplateColumns: \"repeat(4, 1fr)\",\r\n              gap: \"5px\",\r\n              background: \"transparent\",\r\n              borderRadius: \"20px 20px 0 0\",\r\n              overflow: \"hidden\",\r\n              margin: 0,\r\n              padding: 0,\r\n            }}\r\n          >\r\n            <Gallery>\r\n              {filtered.map((item, index) => (\r\n                <li\r\n                  key={index}\r\n                  className={`work-item mix ${item.categories.join(\" \")}`}\r\n                  style={{\r\n                    background: \"transparent\",\r\n                    width: \"100%\",\r\n                    height: \"100%\",\r\n                  }}\r\n                >\r\n                  {item.type === \"Lightbox\" ? (\r\n                    <Item\r\n                      original={item.imgUrl}\r\n                      thumbnail={item.imgUrl}\r\n                      width={400}\r\n                      height={400}\r\n                    >\r\n                      {({ open }) => (\r\n                        <a\r\n                          onClick={open}\r\n                          className=\"work-lightbox-link mfp-image\"\r\n                          style={{\r\n                            display: \"flex\",\r\n                            flexDirection: \"column\",\r\n                            width: \"100%\",\r\n                            height: \"100%\",\r\n                            borderRadius: \"10px\",\r\n                            overflow: \"hidden\",\r\n                          }}\r\n                        >\r\n                          <div\r\n                            className=\"work-img\"\r\n                            style={{\r\n                              flex: 1,\r\n                              background: \"transparent\",\r\n                            }}\r\n                          />\r\n                          <div\r\n                            className=\"work-intro\"\r\n                            style={{\r\n                              padding: \"15px\",\r\n                            }}\r\n                          >\r\n                            <h3 className=\"work-title\">\r\n                              {translate(\r\n                                `bms.module.${item.title\r\n                                  .split(\" \")[0]\r\n                                  .toLowerCase()}`\r\n                              )}\r\n                            </h3>\r\n                            <div className=\"work-descr\">{item.type}</div>\r\n                          </div>\r\n                        </a>\r\n                      )}\r\n                    </Item>\r\n                  ) : (\r\n                    <Link\r\n                      to={`/portfolio-single/${item.id}`}\r\n                      className=\"work-ext-link\"\r\n                      style={{ background: \"transparent\" }}\r\n                    >\r\n                      <div\r\n                        className=\"work-img\"\r\n                        style={{\r\n                          height: \"400px\", // Square dimensions\r\n                          background: \"transparent\",\r\n                        }}\r\n                      >\r\n                        <div\r\n                          className=\"work-img-bg\"\r\n                          style={{ background: \"transparent\" }}\r\n                        />\r\n                      </div>\r\n                      <div className=\"work-intro\">\r\n                        <h3 className=\"work-title\">\r\n                          {translate(\r\n                            `bms.module.${item.title\r\n                              .split(\" \")[0]\r\n                              .toLowerCase()}`\r\n                          )}\r\n                        </h3>\r\n                        <div className=\"work-descr\">{item.type}</div>\r\n                      </div>\r\n                    </Link>\r\n                  )}\r\n                </li>\r\n              ))}\r\n            </Gallery>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n", "// API configuration for both development and production\n\nconst getApiBaseUrl = () => {\n  // In production, both frontend and backend are served from the same domain\n  if (import.meta.env.PROD) {\n    return \"https://devskills.ee/api\";\n  }\n\n  // In development, backend runs on port 4004\n  return \"http://localhost:4004/api\";\n};\n\nexport const API_BASE_URL = getApiBaseUrl();\n\n// Helper function to make API calls with proper headers\nexport const apiCall = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  // Check if this is a FormData request (file upload)\n  const isFormData = options.body instanceof FormData;\n\n  const defaultHeaders = {};\n\n  // Only set Content-Type for non-FormData requests\n  // FormData requests need the browser to set the Content-Type with boundary\n  if (!isFormData) {\n    defaultHeaders[\"Content-Type\"] = \"application/json\";\n  }\n\n  // Add API key for contact form\n  if (endpoint.includes(\"/contact\")) {\n    defaultHeaders[\"X-API-Key\"] = \"9afe34d2134b43e19163c50924df6714\";\n  }\n\n  // Add auth token if available\n  const token = localStorage.getItem(\"adminToken\");\n  if (token) {\n    defaultHeaders[\"Authorization\"] = `Bearer ${token}`;\n  }\n\n  const config = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    // Handle non-JSON responses (like file uploads)\n    const contentType = response.headers.get(\"content-type\");\n    if (contentType && contentType.includes(\"application/json\")) {\n      const data = await response.json();\n      return { response, data };\n    } else {\n      return { response, data: null };\n    }\n  } catch (error) {\n    console.error(\"API call failed:\", error);\n    throw error;\n  }\n};\n\n// Specific API functions\nexport const authAPI = {\n  login: (credentials) =>\n    apiCall(\"/auth/login\", {\n      method: \"POST\",\n      body: JSON.stringify(credentials),\n    }),\n\n  getMe: () => apiCall(\"/auth/me\"),\n\n  logout: () => apiCall(\"/auth/logout\", { method: \"POST\" }),\n};\n\nexport const blogAPI = {\n  getPosts: (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    return apiCall(`/blog${queryString ? `?${queryString}` : \"\"}`);\n  },\n\n  getPost: (slug) => apiCall(`/blog/${slug}`),\n\n  createPost: (formData) =>\n    apiCall(\"/blog\", {\n      method: \"POST\",\n      body: formData, // FormData for file upload\n      headers: {}, // Let browser set Content-Type for FormData\n    }),\n\n  updatePost: (id, formData) =>\n    apiCall(`/blog/${id}`, {\n      method: \"PUT\",\n      body: formData,\n      headers: {},\n    }),\n\n  deletePost: (id) => apiCall(`/blog/${id}`, { method: \"DELETE\" }),\n\n  toggleVisibility: (id) =>\n    apiCall(`/blog/${id}/toggle-visibility`, {\n      method: \"PATCH\",\n    }),\n\n  // Helper function to get posts for homepage (featured posts)\n  getFeaturedPosts: (language = \"en\", limit = 3) => {\n    return apiCall(`/blog?featured=true&limit=${limit}&published=true`);\n  },\n\n  // Helper function to get posts for blog listing page\n  getBlogPosts: (language = \"en\", page = 1, limit = 9) => {\n    return apiCall(`/blog?page=${page}&limit=${limit}&published=true`);\n  },\n};\n\nexport const adminAPI = {\n  getDashboard: () => apiCall(\"/admin/dashboard\"),\n\n  getPosts: (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    return apiCall(`/admin/posts${queryString ? `?${queryString}` : \"\"}`);\n  },\n\n  uploadImage: (file) => {\n    const formData = new FormData();\n    formData.append(\"image\", file);\n    return apiCall(\"/admin/upload-image\", {\n      method: \"POST\",\n      body: formData,\n      headers: {},\n    });\n  },\n\n  getCategories: () => apiCall(\"/admin/categories\"),\n\n  createCategory: (category) =>\n    apiCall(\"/admin/categories\", {\n      method: \"POST\",\n      body: JSON.stringify(category),\n    }),\n\n  updateCategory: (id, category) =>\n    apiCall(`/admin/categories/${id}`, {\n      method: \"PUT\",\n      body: JSON.stringify(category),\n    }),\n\n  deleteCategory: (id) =>\n    apiCall(`/admin/categories/${id}`, {\n      method: \"DELETE\",\n    }),\n\n  getTags: () => apiCall(\"/admin/tags\"),\n\n  createTag: (tag) =>\n    apiCall(\"/admin/tags\", {\n      method: \"POST\",\n      body: JSON.stringify(tag),\n    }),\n\n  updateTag: (id, tag) =>\n    apiCall(`/admin/tags/${id}`, {\n      method: \"PUT\",\n      body: JSON.stringify(tag),\n    }),\n\n  deleteTag: (id) =>\n    apiCall(`/admin/tags/${id}`, {\n      method: \"DELETE\",\n    }),\n};\n\nexport const contactAPI = {\n  sendMessage: (message) =>\n    apiCall(\"/contact\", {\n      method: \"POST\",\n      body: JSON.stringify(message),\n    }),\n};\n", "import React, { useState, useEffect } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Blog() {\n  const { i18n, t } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [blogPosts, setBlogPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        setLoading(true);\n        const result = await blogAPI.getFeaturedPosts(currentLanguage, 3);\n\n        if (result.response.ok && result.data) {\n          // Extract the posts array from the nested response structure\n          const posts = result.data.data?.data || result.data.data || [];\n          console.log(\"Blog API response:\", result.data);\n          console.log(\"Posts array:\", posts);\n          setBlogPosts(Array.isArray(posts) ? posts : []);\n        } else {\n          console.error(\"Failed to fetch blog posts:\", result.response.status);\n          setBlogPosts([]);\n        }\n      } catch (error) {\n        console.error(\"Error fetching blog posts:\", error);\n        setError(\"Failed to load blog posts\");\n        setBlogPosts([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, [currentLanguage]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    const translation = post.translations?.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations?.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h2 className=\"section-title mb-30 mb-sm-20\">\n              <span className=\"text-gray\">{t(\"blog.title\")}</span>\n              <span className=\"text-gray\">.</span>\n            </h2>\n            <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n          </div>\n        </div>\n        <div className=\"row mt-n30\">\n          <div className=\"col-12 text-center\">\n            <div className=\"text-gray\">Loading blog posts...</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"container\">\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h2 className=\"section-title mb-30 mb-sm-20\">\n              <span className=\"text-gray\">{t(\"blog.title\")}</span>\n              <span className=\"text-gray\">.</span>\n            </h2>\n            <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n          </div>\n        </div>\n        <div className=\"row mt-n30\">\n          <div className=\"col-12 text-center\">\n            <div className=\"text-gray\">{error}</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show empty state if no posts\n  if (blogPosts.length === 0) {\n    return (\n      <div className=\"container\">\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h2 className=\"section-title mb-30 mb-sm-20\">\n              <span className=\"text-gray\">{t(\"blog.title\")}</span>\n              <span className=\"text-gray\">.</span>\n            </h2>\n            <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n          </div>\n        </div>\n        <div className=\"row mt-n30\">\n          <div className=\"col-12 text-center\">\n            <div className=\"text-gray\">No blog posts available yet.</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n  return (\n    <div className=\"container\">\n      <div className=\"row mb-70 mb-sm-50\">\n        <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n          <h2 className=\"section-title mb-30 mb-sm-20\">\n            <span className=\"text-gray\">{t(\"blog.title\")}</span>\n            <span className=\"text-gray\">.</span>\n          </h2>\n          <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n        </div>\n        <div className=\"col-md-2 col-lg-3 text-center text-md-end mt-10 mt-sm-30\">\n          <Link to={`/blog`} className=\"section-more\">\n            {t(\"blog.all_posts\")} <i className=\"mi-chevron-right size-14\" />\n          </Link>\n        </div>\n      </div>\n      <div className=\"row mt-n30\">\n        {/* Post Item */}\n        {Array.isArray(blogPosts) &&\n          blogPosts.map((post, index) => (\n            <div\n              key={post.id}\n              className={`post-prev col-md-6 col-lg-4 mt-30 wow fadeInLeft`}\n              data-wow-delay={`${index * 0.1}s`}\n            >\n              <div className=\"post-prev-container\">\n                <div className=\"post-prev-img\">\n                  <Link to={`/blog-single/${post.slug}`}>\n                    <img\n                      src={\n                        post.featuredImage ||\n                        \"/assets/images/demo-elegant/blog/1.jpg\"\n                      }\n                      width={607}\n                      height={358}\n                      alt={getTranslation(post, \"title\")}\n                    />\n                  </Link>\n                </div>\n                <h3 className=\"post-prev-title\">\n                  <Link to={`/blog-single/${post.slug}`}>\n                    {getTranslation(post, \"title\")}\n                  </Link>\n                </h3>\n                <div className=\"post-prev-text\">\n                  {getTranslation(post, \"excerpt\")}\n                </div>\n                <div className=\"post-prev-info clearfix\">\n                  <div className=\"float-start\">\n                    <a href=\"#\" className=\"icon-author\">\n                      <i className=\"mi-user size-14 align-middle\" />\n                    </a>\n                    <a href=\"#\">{post.author?.name || \"DevSkills Team\"}</a>\n                  </div>\n                  <div className=\"float-end\">\n                    <i className=\"mi-calendar size-14 align-middle\" />\n                    <a href=\"#\">\n                      {new Date(\n                        post.publishedAt || post.createdAt\n                      ).toLocaleDateString()}\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        {/* End Post Item */}\n      </div>\n    </div>\n  );\n}\n", "import { useTranslation } from \"react-i18next\";\n\nexport const useContactItems = () => {\n  const { t } = useTranslation();\n\n  return [\n    {\n      iconClass: \"mi-location\",\n      title: t(\"contact.address.title\"),\n      text: t(\"contact.address.text\"),\n      link: {\n        url: \"https://maps.app.goo.gl/kjvsp1j2f15nff1B8\",\n        text: t(\"contact.address.link\"),\n        rel: \"nofollow noopener\",\n        target: \"_blank\",\n      },\n    },\n    {\n      iconClass: \"mi-email\",\n      title: t(\"contact.email.title\"),\n      text: t(\"contact.email.text\"),\n      link: {\n        url: \"mailto:<EMAIL>\",\n        text: t(\"contact.email.link\"),\n      },\n    },\n    {\n      iconClass: \"mi-mobile\",\n      title: t(\"contact.phone.title\"),\n      text: t(\"contact.phone.text\"),\n      link: {\n        url: \"tel:+37256282038\",\n        text: t(\"contact.phone.link\"),\n      },\n    },\n  ];\n};\n\n// For backward compatibility\nexport const contactItems = [\n  {\n    iconClass: \"mi-location\",\n    title: \"Address\",\n    text: \"Devskills OÜ, Tornimäe tn 7, 10145 Tallinn\",\n    link: {\n      url: \"https://maps.app.goo.gl/kjvsp1j2f15nff1B8\",\n      text: \"See Map\",\n      rel: \"nofollow noopener\",\n      target: \"_blank\",\n    },\n  },\n  {\n    iconClass: \"mi-email\",\n    title: \"Email\",\n    text: \"<EMAIL>\",\n    link: {\n      url: \"mailto:<EMAIL>\",\n      text: \"Say Hello\",\n    },\n  },\n  {\n    iconClass: \"mi-mobile\",\n    title: \"Phone\",\n    text: \"+372 5628 2038\",\n    link: {\n      url: \"tel:+37256282038\",\n      text: \"Call now\",\n    },\n  },\n];\n", "import { useContactItems } from \"@/data/contact\";\nimport React, { useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport axios from \"axios\";\nimport {\n  trackContactFormSubmission,\n  trackFormSubmission,\n} from \"@/utils/analytics\";\n\nexport default function Contact() {\n  const { t } = useTranslation();\n  const contactItems = useContactItems();\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\",\n  });\n  const [formStatus, setFormStatus] = useState({\n    submitting: false,\n    success: false,\n    error: false,\n    message: \"\",\n  });\n  // Handle form input changes\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value,\n    });\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Set submitting state\n    setFormStatus({\n      submitting: true,\n      success: false,\n      error: false,\n      message: \"\",\n    });\n\n    try {\n      // Use environment-based URL\n      const isDevelopment = window.location.hostname === \"localhost\";\n      const endpointUrl = isDevelopment\n        ? \"http://localhost:4004/api/v1/communication/public/contact-form\" // Local dev (keep existing)\n        : \"https://devskills.ee/api/v1/communication/public/contact-form\"; // Production URL\n      const apiKey = \"9afe34d2134b43e19163c50924df6714\";\n\n      // Send the form data to the endpoint\n      await axios.post(endpointUrl, formData, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"X-API-Key\": apiKey,\n        },\n      });\n\n      // Track successful form submission\n      trackContactFormSubmission(formData.email, formData.message.length);\n      trackFormSubmission(\"Contact Form\", window.location.pathname, true);\n\n      // Handle success\n      setFormStatus({\n        submitting: false,\n        success: true,\n        error: false,\n        message:\n          t(\"contact.success\") ||\n          \"Thank you! Your message has been sent successfully.\",\n      });\n\n      // Reset form\n      setFormData({\n        name: \"\",\n        email: \"\",\n        message: \"\",\n      });\n    } catch (error) {\n      console.error(\"Contact form error:\", error);\n\n      // Track failed form submission\n      trackFormSubmission(\"Contact Form\", window.location.pathname, false);\n\n      // Handle different types of errors\n      let errorMessage =\n        t(\"contact.error\") ||\n        \"Sorry, there was an error sending your message. Please try again.\";\n\n      if (error.response?.status === 429) {\n        errorMessage =\n          t(\"contact.rateLimit\") ||\n          \"Too many requests. Please wait a moment before trying again.\";\n      } else if (error.response?.status === 400) {\n        errorMessage =\n          t(\"contact.validation\") || \"Please check your input and try again.\";\n      }\n\n      setFormStatus({\n        submitting: false,\n        success: false,\n        error: true,\n        message: errorMessage,\n      });\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <div className=\"row mt-n10 mb-60 mb-xs-40\">\n        <div className=\"col-md-10 offset-md-1\">\n          <div className=\"row\">\n            {/* Phone */}\n            {contactItems.map((item, index) => (\n              <React.Fragment key={index}>\n                <div className={`col-md-6 col-lg-4 mb-md-30 `}>\n                  <div className=\"contact-item wow fadeScaleIn\">\n                    <div className=\"ci-icon\">\n                      <i className={item.iconClass} />\n                    </div>\n                    <h4 className=\"ci-title\">{item.title}</h4>\n                    <div className=\"ci-text large\">{item.text}</div>\n                    <div className=\"ci-link\">\n                      <a\n                        href={item.link.url}\n                        target={item.link.target}\n                        rel={item.link.rel}\n                      >\n                        {item.link.text}\n                      </a>\n                    </div>{\" \"}\n                  </div>\n                </div>{\" \"}\n              </React.Fragment>\n            ))}\n\n            {/* End Email */}\n          </div>\n        </div>\n      </div>\n      {/* Contact Form */}\n      <div className=\"row\">\n        <div className=\"col-md-10 offset-md-1\">\n          <form\n            onSubmit={handleSubmit}\n            className=\"form contact-form wow fadeInUp wch-unset\"\n            data-wow-delay=\".5s\"\n            id=\"contact_form\"\n          >\n            <div className=\"row\">\n              <div className=\"col-md-6\">\n                {/* Name */}\n                <div className=\"form-group\">\n                  <label htmlFor=\"name\">{t(\"contact.name\")}</label>\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    id=\"name\"\n                    className=\"input-lg round form-control\"\n                    placeholder={t(\"contact.name.placeholder\")}\n                    pattern=\".{3,100}\"\n                    required\n                    aria-required=\"true\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n              <div className=\"col-md-6\">\n                {/* Email */}\n                <div className=\"form-group\">\n                  <label htmlFor=\"email\">{t(\"contact.email\")}</label>\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    id=\"email\"\n                    className=\"input-lg round form-control\"\n                    placeholder={t(\"contact.email.placeholder\")}\n                    pattern=\".{5,100}\"\n                    required\n                    aria-required=\"true\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n            </div>\n            {/* Message */}\n            <div className=\"form-group\">\n              <label htmlFor=\"message\">{t(\"contact.message\")}</label>\n              <textarea\n                name=\"message\"\n                id=\"message\"\n                className=\"input-lg round form-control\"\n                style={{ height: 200 }}\n                placeholder={t(\"contact.message.placeholder\")}\n                value={formData.message}\n                onChange={handleInputChange}\n                minLength={5}\n                maxLength={5000}\n                required\n              />\n              <div className=\"form-text text-muted small\">\n                {formData.message.length}/5000 characters\n              </div>\n            </div>\n            <div className=\"row\">\n              <div className=\"col-sm-6\">\n                {/* Inform Tip */}\n                <div className=\"form-tip pt-20 pt-sm-0\">\n                  <i className=\"icon-info size-16\" />\n                  {t(\"contact.terms\")}\n                </div>\n              </div>\n              <div className=\"col-sm-6\">\n                {/* Send Button */}\n                <div className=\"text-end pt-10\">\n                  <button\n                    type=\"submit\"\n                    id=\"submit_btn\"\n                    aria-controls=\"result\"\n                    className=\"submit_btn link-hover-anim link-circle-1 align-middle\"\n                    data-link-animate=\"y\"\n                    disabled={formStatus.submitting}\n                  >\n                    <span className=\"link-strong link-strong-unhovered\">\n                      {formStatus.submitting ? \"Sending...\" : t(\"contact.send\")}\n                      <i\n                        className=\"mi-arrow-right size-18 align-middle\"\n                        aria-hidden=\"true\"\n                      ></i>\n                    </span>\n                    <span\n                      className=\"link-strong link-strong-hovered\"\n                      aria-hidden=\"true\"\n                    >\n                      {formStatus.submitting ? \"Sending...\" : t(\"contact.send\")}\n                      <i\n                        className=\"mi-arrow-right size-18 align-middle\"\n                        aria-hidden=\"true\"\n                      ></i>\n                    </span>\n                  </button>\n                </div>\n              </div>\n            </div>\n            <div\n              id=\"result\"\n              role=\"region\"\n              aria-live=\"polite\"\n              aria-atomic=\"true\"\n              className={`mt-4 p-3 rounded ${\n                formStatus.success\n                  ? \"bg-success-100 text-success-700\"\n                  : formStatus.error\n                  ? \"bg-danger-100 text-danger-700\"\n                  : \"\"\n              }`}\n              style={{ display: formStatus.message ? \"block\" : \"none\" }}\n            >\n              {formStatus.message}\n            </div>\n          </form>\n        </div>\n      </div>\n      {/* End Contact Form */}\n    </div>\n  );\n}\n", "import React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function MarqueeDark() {\n  const { t } = useTranslation();\n  return (\n    <>\n      <div className=\"marquee marquee-style-1 bg-dark-2 mb-30\">\n        <div className=\"marquee-track marquee-animation\">\n          <div>{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n        </div>\n      </div>\n      {/* End Marquee Text Line */}\n      {/* Marquee Text Line */}\n      <div className=\"marquee marquee-style-1 bg-dark-2\">\n        <div className=\"marquee-track marquee-animation\">\n          <div>{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client\\src\\components\\home\\index.jsx\n\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport About from \"./About\";\nimport { useTranslation } from \"react-i18next\";\nimport { usePageAnalytics } from \"@/hooks/usePageAnalytics\";\n\n// Keeping imports for potential future use\n// import Team from \"./Team\";\nimport Service from \"./Service\";\n// import Portfolio from \"./Portfolio\";\nimport DevskillsBMS from \"./DevskillsBMS\";\n\nimport Blog from \"./Blog\";\n// import NewsLetter from \"./NewsLetter\";\nimport Contact from \"./Contact\";\nimport { Link } from \"react-router-dom\";\nimport MarqueeDark from \"./MarqueeDark\";\n\nexport default function Home({ onePage = false, dark = false }) {\n  const { t } = useTranslation();\n\n  // Add page analytics tracking\n  usePageAnalytics(\"DevSkills - Business Management Solutions\");\n  return (\n    <>\n      <section\n        className={`page-section  scrollSpysection pb-40 ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"about\"\n      >\n        <div className=\"container position-relative\">\n          <div className=\"row\">\n            <div className=\"col-lg-5 d-flex align-items-center mb-md-50\">\n              <div>\n                <div className=\"wow linesAnimIn\" data-splitting=\"lines\">\n                  <h2 className=\"section-title mb-30 mb-sm-20\">\n                    <span className=\"text-gray\">{t(\"menu.about\")}</span>{\" \"}\n                    DEVSKILLS STUDIO\n                    <span className=\"text-gray\">.</span>\n                  </h2>\n                  <div className=\"text-gray mb-30 mb-sm-20\">\n                    <p className=\"mb-0\">{t(\"home.about.description\")}</p>\n                  </div>\n                </div>\n                <div className=\"local-scroll wow fadeInUpShort wch-unset\">\n                  {onePage ? (\n                    <>\n                      {\" \"}\n                      <a\n                        href=\"#team\"\n                        className=\"link-hover-anim link-circle-1 align-middle\"\n                        data-link-animate=\"y\"\n                      >\n                        <span className=\"link-strong link-strong-unhovered\">\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                        <span\n                          className=\"link-strong link-strong-hovered\"\n                          aria-hidden=\"true\"\n                        >\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                      </a>\n                    </>\n                  ) : (\n                    <>\n                      {\" \"}\n                      <Link\n                        to={`/elegant-about${dark ? \"-dark\" : \"\"}`}\n                        className=\"link-hover-anim link-circle-1 align-middle\"\n                        data-link-animate=\"y\"\n                      >\n                        <span className=\"link-strong link-strong-unhovered\">\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                        <span\n                          className=\"link-strong link-strong-hovered\"\n                          aria-hidden=\"true\"\n                        >\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                      </Link>\n                    </>\n                  )}\n                </div>\n              </div>\n            </div>\n            <About />\n          </div>\n        </div>\n      </section>\n\n      <MarqueeDark />\n\n      <section\n        className={`page-section pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content scrollSpysection ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        }`}\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n        id=\"bms\"\n      >\n        <DevskillsBMS />\n      </section>\n\n      <div className=\"page-section overflow-hidden\">\n        <MarqueeDark />\n      </div>\n\n      <section\n        className=\"page-section pt-0 pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content\"\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container position-relative\">\n          <div className=\"row\">\n            <div className=\"col-md-6 col-xl-5\">\n              <div className=\"call-action-1-images pb-60 pb-md-0 mt-n30 mt-md-70 mb-n30 mb-md-70 mb-sm-0\">\n                <div className=\"call-action-1-image-1 round grayscale\">\n                  <img\n                    src=\"/assets/images/demo-elegant/code2.jpg\"\n                    width={678}\n                    height={840}\n                    alt=\"Image Description\"\n                  />\n                </div>\n                <div className=\"call-action-1-image-2\">\n                  <div\n                    className=\"call-action-1-image-2-inner\"\n                    data-rellax-y=\"\"\n                    data-rellax-speed=\"0.7\"\n                    data-rellax-percentage=\"0.427\"\n                  >\n                    <img\n                      src=\"/assets/images/demo-elegant/code3.jpg\"\n                      alt=\"Image Description\"\n                      width={300}\n                      height={409}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6 offset-xl-1 d-flex align-items-center\">\n              <div className=\"row small-section\">\n                <div className=\"col-xl-11\">\n                  <h2 className=\"section-title mb-30 mb-sm-20\">\n                    {t(\"home.services.title\")}\n                  </h2>\n                  <div className=\"text-gray mb-30 mb-sm-20\">\n                    <p className=\"mb-0\">{t(\"home.services.description\")}</p>\n                  </div>\n                  <div className=\"local-scroll\">\n                    {onePage ? (\n                      <>\n                        {\" \"}\n                        <a\n                          href=\"#services\"\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                        </a>\n                      </>\n                    ) : (\n                      <>\n                        {\" \"}\n                        <Link\n                          to={`/alegant-services${dark ? \"-dark\" : \"\"}`}\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                        </Link>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      {/* Commenting out Team section */}\n\n      {/* <section\n        className={`page-section pb-0  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"team\"\n      >\n        <Team />\n      </section> */}\n\n      <div className=\"page-section overflow-hidden\">\n        <MarqueeDark />\n      </div>\n      <section\n        className={`page-section pt-0  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"services\"\n      >\n        <Service />\n      </section>\n      <hr className=\"mt-0 mb-0\" />\n\n      {/* Commenting out Portfolio section */}\n      {/*\n      <section\n        className={`page-section pb-0  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"portfolio\"\n      >\n        <div className=\"container\">\n          <div className=\"row mb-70 mb-sm-50\">\n            <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n              <h2 className=\"section-title mb-30 mb-sm-20\">\n                <span className=\"text-gray\">Our</span> Portfolio\n                <span className=\"text-gray\">.</span>\n              </h2>\n              <div className=\"text-gray\">\n                The action centric perspective is a label given to a collection\n                of concepts, which are antithetical to the rational model.\n              </div>\n            </div>\n          </div>\n        </div>\n        <Portfolio />\n      </section>\n      */}\n      <section\n        className={`small-section ${\n          dark ? \"bg-dark-2 light-content\" : \"bg-dark-1 light-content\"\n        } bg-dark-alpha-80 parallax-6`}\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container\">\n          <div className=\"row mb-n10\">\n            <div className=\"col-md-6 offset-md-1 col-lg-5 offset-lg-2 mb-sm-30 text-center text-md-start\">\n              <h2 className=\"section-title-small mb-0 opacity-08\">\n                {t(\"home.cta.title\")}\n              </h2>\n            </div>\n            <div className=\"col-md-4 col-lg-3 text-center text-md-end\">\n              <div className=\"mt-n20\">\n                {onePage ? (\n                  <>\n                    {\" \"}\n                    <a\n                      href=\"#contact\"\n                      className=\"link-hover-anim link-circle-1 align-middle\"\n                      data-link-animate=\"y\"\n                    >\n                      <span className=\"link-strong link-strong-unhovered\">\n                        {t(\"home.cta.button\")}\n                      </span>\n                      <span\n                        className=\"link-strong link-strong-hovered\"\n                        aria-hidden=\"true\"\n                      >\n                        {t(\"home.cta.button\")}\n                      </span>\n                    </a>\n                  </>\n                ) : (\n                  <>\n                    {\" \"}\n                    <Link\n                      to={`/elegant-contact${dark ? \"-dark\" : \"\"}`}\n                      className=\"link-hover-anim link-circle-1 align-middle\"\n                      data-link-animate=\"y\"\n                    >\n                      <span className=\"link-strong link-strong-unhovered\">\n                        {t(\"home.cta.button\")}\n                      </span>\n                      <span\n                        className=\"link-strong link-strong-hovered\"\n                        aria-hidden=\"true\"\n                      >\n                        {t(\"home.cta.button\")}\n                      </span>\n                    </Link>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      {/* Commenting out Newsletter section */}\n      {/*\n      <section\n        className={`small-section pb-0 ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n      >\n        <NewsLetter />\n      </section>\n      */}\n\n      {/* Blog Section */}\n      <section\n        className={`page-section scrollSpysection ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        }`}\n        id=\"blog\"\n      >\n        <Blog />\n      </section>\n\n      <section\n        className={`page-section  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"contact\"\n      >\n        <div className=\"container\">\n          <div className=\"row mb-70 mb-sm-50\">\n            <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n              <h2 className=\"section-title mb-30 mb-sm-20\">\n                <span className=\"text-gray\">{t(\"home.contact.title\")}</span>\n                <span className=\"text-gray\">.</span>\n              </h2>\n            </div>\n          </div>\n        </div>\n        <Contact />\n      </section>\n    </>\n  );\n}\n\nHome.propTypes = {\n  onePage: PropTypes.bool,\n  dark: PropTypes.bool,\n};\n", "import AnimatedText from \"@/components/common/AnimatedText\";\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { trackButtonClick } from \"@/utils/analytics\";\n\nexport default function Hero() {\n  const { t } = useTranslation();\n\n  const handleDiscoverClick = () => {\n    trackButtonClick(\"Discover Now\", \"Hero Section\", {\n      button_type: \"cta\",\n      section: \"hero\",\n    });\n  };\n  return (\n    <div className=\"container min-height-100vh d-flex align-items-center pt-100 pb-100 pt-sm-120 pb-sm-120\">\n      {/* Home Section Content */}\n      <div className=\"home-content text-center\">\n        <h2 className=\"section-title-tiny mb-50 mb-sm-30 wow fadeInDownShort\">\n          {t(\"hero.welcome\")}\n        </h2>\n        <h1 className=\"hs-title-3 mb-120 mb-sm-80 mb-xs-140\">\n          <span className=\"wow charsAnimInLong\" data-splitting=\"chars\">\n            <AnimatedText text={t(\"hero.studio\")} />\n          </span>\n        </h1>\n        <div className=\"local-scroll wow fadeInUpShort\" data-wow-delay=\"0.57s\">\n          <a\n            href=\"#about\"\n            className=\"link-hover-anim link-circle-1 align-middle\"\n            data-link-animate=\"y\"\n            onClick={handleDiscoverClick}\n          >\n            <span className=\"link-strong link-strong-unhovered\">\n              {t(\"hero.discover\")}{\" \"}\n              <i\n                className=\"mi-arrow-right size-18 align-middle\"\n                aria-hidden=\"true\"\n              ></i>\n            </span>\n            <span\n              className=\"link-strong link-strong-hovered\"\n              aria-hidden=\"true\"\n            >\n              {t(\"hero.discover\")}{\" \"}\n              <i\n                className=\"mi-arrow-right size-18 align-middle\"\n                aria-hidden=\"true\"\n              ></i>\n            </span>\n          </a>\n        </div>\n      </div>\n      {/* End Home Section Content */}\n      {/* Scroll Down */}\n      <div\n        className=\"local-scroll scroll-down-3-wrap wow fadeInUp\"\n        data-wow-offset={0}\n      >\n        <a href=\"#about\" className=\"scroll-down-3\">\n          {t(\"hero.scrollDown\") || \"Scroll Down\"}\n        </a>\n      </div>\n      {/* End Scroll Down */}\n    </div>\n  );\n}\n", "export const teamMembers = [\n  {\n    name: \"<PERSON>\",\n    role: \"<PERSON><PERSON><PERSON>, co-founder\",\n    image: \"/assets/images/team/team-1.jpg\",\n    socials: [\n      { platform: \"Facebook\", icon: \"fa-facebook-f\", url: \"#\" },\n      { platform: \"Twitter\", icon: \"fa-twitter\", url: \"#\" },\n      { platform: \"Pinterest\", icon: \"fa-pinterest-p\", url: \"#\" },\n    ],\n  },\n  {\n    name: \"<PERSON>\",\n    role: \"UI/UX Designer, co-founder\",\n    image: \"/assets/images/team/team-2.jpg\",\n    socials: [\n      { platform: \"Facebook\", icon: \"fa-facebook-f\", url: \"#\" },\n      { platform: \"Twitter\", icon: \"fa-twitter\", url: \"#\" },\n      { platform: \"Pinterest\", icon: \"fa-pinterest-p\", url: \"#\" },\n    ],\n  },\n  {\n    name: \"<PERSON>\",\n    role: \"Web developer\",\n    image: \"/assets/images/team/team-4.jpg\",\n    socials: [\n      { platform: \"Facebook\", icon: \"fa-facebook-f\", url: \"#\" },\n      { platform: \"Twitter\", icon: \"fa-twitter\", url: \"#\" },\n      { platform: \"Pinterest\", icon: \"fa-pinterest-p\", url: \"#\" },\n    ],\n  },\n  {\n    name: \"<PERSON> Laning\",\n    role: \"Art director, designer\",\n    image: \"/assets/images/team/team-3.jpg\",\n    socials: [\n      { platform: \"Facebook\", icon: \"fa-facebook-f\", url: \"#\" },\n      { platform: \"Twitter\", icon: \"fa-twitter\", url: \"#\" },\n      { platform: \"Pinterest\", icon: \"fa-pinterest-p\", url: \"#\" },\n    ],\n  },\n];\nexport const teamMembers2 = [\n  {\n    name: \"Timo Lambing\",\n    role: \"Founder & CEO\",\n    image: \"/assets/img/timo.jpg\",\n    socials: [\n      { name: \"LinkedIn\", url: \"https://www.linkedin.com/in/timolambing/\" },\n      { name: \"Twitter\", url: \"#\" },\n      { name: \"Github\", url: \"https://github.com/TimoLambing\" },\n    ],\n  },\n  {\n    name: \"Ann-Kristiin Reimann\",\n    role: \"Chief of Marketing\",\n    image: \"/assets/img/ann-kristiin.jpg\",\n    socials: [\n      { name: \"LinkedIn\", url: \"#\" },\n      { name: \"Twitter\", url: \"#\" },\n      { name: \"Instagram\", url: \"#\" },\n    ],\n  },\n  // Add more team members as needed\n];\n\nexport const teamMembers3 = [\n  {\n    name: \"Thomas Anderson\",\n    role: \"Creative director\",\n    description:\n      \"Sed eget ipsum vel urna viverra iaculis. Aenean ligula arcu, porta in scelerisque vehicula eget metus.\",\n    imgSrc: \"/assets/images/demo-strong/team/team-1.jpg\",\n  },\n  {\n    name: \"Lukas Goodman\",\n    role: \"Product designer\",\n    description:\n      \"Etiam nec purus lacus curabitur facilisis dolor odio, in cursus sem viverra nec maximus pretium.\",\n    imgSrc: \"/assets/images/demo-strong/team/team-2.jpg\",\n  },\n  {\n    name: \"Kelsie Rogers\",\n    role: \"Web engineer\",\n    description:\n      \"Etiam at orli at tellus iaculis bibendum quis id ante proin posuere eros, eget blandit vestibulum vel.\",\n    imgSrc: \"/assets/images/demo-strong/team/team-3.jpg\",\n  },\n  {\n    name: \"Marta Carlson\",\n    role: \"UI/UX designer\",\n    description:\n      \"Sed eget ipsum vel urna viverra iaculis. Aenean ligula arcu, porta in scelerisque vehicula eget metus.\",\n    imgSrc: \"/assets/images/demo-strong/team/team-4.jpg\",\n  },\n];\n", "import { teamMembers2 } from \"@/data/team\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Team() {\n  const { t } = useTranslation();\n  return (\n    <div className=\"container\">\n      <div className=\"row mb-70 mb-sm-50\">\n        <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n          <h2 className=\"section-title mb-30 mb-sm-20\">\n            <span className=\"text-gray\">\n              {t(\"about.team.title\").split(\" \")[0]}\n            </span>{\" \"}\n            {t(\"about.team.title\").split(\" \")[1]}\n            <span className=\"text-gray\">.</span>\n          </h2>\n          <div className=\"text-gray\">{t(\"about.team.description\")}</div>\n        </div>\n      </div>\n      <div className=\"row justify-content-center mt-n40\">\n        {/* Team item */}\n        {teamMembers2.map((member, index) => (\n          <div key={index} className=\"col-md-5 col-lg-4 mt-40 mx-md-4\">\n            <div className=\"team-item\">\n              <div\n                className={`team-item-image ${\n                  member.name === \"Timo Lambing\" ? \"timo-image-container\" : \"\"\n                }`}\n              >\n                <img\n                  src={member.image}\n                  width={625}\n                  height={767}\n                  className=\"wow scaleOutIn grayscale-effect team-image\"\n                  data-wow-duration=\"1.2s\"\n                  alt={`Image of ${member.name}`}\n                  style={{ objectFit: \"cover\", width: \"100%\", height: \"100%\" }}\n                />\n                <div className=\"team-item-detail\">\n                  <div className=\"team-social-links\">\n                    {member.socials.map((social, idx) => (\n                      <a\n                        key={idx}\n                        href={social.url}\n                        target=\"_blank\"\n                        rel=\"noreferrer nofollow\"\n                      >\n                        <div className=\"visually-hidden\">{social.name}</div>\n                        <i className={`fa-${social.name.toLowerCase()}`} />\n                      </a>\n                    ))}\n                  </div>\n                </div>\n              </div>\n              <div className=\"team-item-descr\">\n                <div className=\"team-item-name\">{member.name}</div>\n                <div className=\"team-item-role\">{member.role}</div>\n              </div>\n            </div>\n          </div>\n        ))}\n        {/* End Team item */}\n      </div>\n    </div>\n  );\n}\n", "import { portfolios5 } from \"@/data/portfolio\";\nimport React, { useEffect, useState } from \"react\";\n\nimport { Link } from \"react-router-dom\";\nimport { Gallery, Item } from \"react-photoswipe-gallery\";\nconst filters = [\n  { name: \"All works\", category: \"all\" },\n  { name: \"Branding\", category: \"branding\" },\n  { name: \"Design\", category: \"design\" },\n  { name: \"Development\", category: \"development\" },\n];\nexport default function Portfolio() {\n  const [currentCategory, setCurrentCategory] = useState(\"all\");\n  const [filtered, setFiltered] = useState(portfolios5);\n  useEffect(() => {\n    if (currentCategory == \"all\") {\n      setFiltered(portfolios5);\n    } else {\n      setFiltered(\n        [...portfolios5].filter((elm) =>\n          elm.categories.includes(currentCategory)\n        )\n      );\n    }\n  }, [currentCategory]);\n  return (\n    <>\n      <div className=\"container\">\n        {/* Works Filter */}\n        <div className=\"works-filter works-filter-elegant text-center mb-50\">\n          {filters.map((elm, i) => (\n            <a\n              onClick={() => setCurrentCategory(elm.category)}\n              key={i}\n              className={`filter ${currentCategory == elm.category ? \"active\" : \"\"\n                }`}\n            >\n              {elm.name}\n            </a>\n          ))}\n        </div>\n        {/* End Works Filter */}\n      </div>\n      <div className=\"position-relative\">\n        {/* Works Grid */}\n        <ul\n          className=\"works-grid work-grid-4 work-grid-gut-sm hide-titles\"\n          id=\"work-grid\"\n        >\n          <Gallery>\n            {/* Work Item (Lightbox) */}\n            {filtered.map((item, index) => (\n              <li\n                key={index}\n                className={`work-item mix ${item.categories.join(\" \")}`}\n              >\n                {item.type === \"Lightbox\" ? (\n                  <Item\n                    original={item.imageSrc}\n                    thumbnail={item.imageSrc}\n                    width={650}\n                    height={773}\n                  >\n                    {({ ref, open }) => (\n                      <a\n                        onClick={open}\n                        className=\"work-lightbox-link mfp-image\"\n                      >\n                        <div className=\"work-img\">\n                          <div className=\"work-img-bg wow-p scalexIn\" />\n\n                          <img\n                            src={item.imageSrc}\n                            ref={ref}\n                            width={650}\n                            height={761}\n                            alt=\"Work Description\"\n                          />\n                        </div>\n                        <div className=\"work-intro\">\n                          <h3 className=\"work-title\">{item.title}</h3>\n                          <div className=\"work-descr\">{item.type}</div>\n                        </div>\n                      </a>\n                    )}\n                  </Item>\n                ) : (\n                  <Link\n                    to={`/portfolio-single/${item.id}`}\n                    className=\"work-ext-link\"\n                  >\n                    <div className=\"work-img\">\n                      <div className=\"work-img-bg\" />\n                      <img\n                        src={item.imageSrc}\n                        width={650}\n                        height={761}\n                        alt=\"Work Description\"\n                      />\n                    </div>\n                    <div className=\"work-intro\">\n                      <h3 className=\"work-title\">{item.title}</h3>\n                      <div className=\"work-descr\">{item.type}</div>\n                    </div>\n                  </Link>\n                )}\n              </li>\n            ))}{\" \"}\n          </Gallery>\n          {/* End Work Item */}\n        </ul>\n        {/* End Works Grid */}\n      </div>\n    </>\n  );\n}\n"], "names": ["About", "t", "useTranslation", "jsxs", "jsx", "usePageAnalytics", "pageTitle", "location", "useLocation", "startTimeRef", "useRef", "scrollDepthRef", "scrollMilestonesRef", "useEffect", "fullPageTitle", "pageLocation", "trackPageView", "handleScroll", "scrollTop", "documentHeight", "scrollPercentage", "milestone", "trackScrollDepth", "handleBeforeUnload", "timeOnPage", "trackTimeOnPage", "handleVisibilityChange", "eventName", "category", "label", "value", "additionalParams", "services6", "Service", "Fragment", "elm", "bmsItems", "DevskillsBMS", "translate", "i18n", "currentLanguage", "currentCategory", "setCurrentCategory", "useState", "filtered", "setFiltered", "activeModule", "setActiveModule", "moduleKeys", "setModuleKeys", "item", "handleModuleClick", "moduleCategory", "Gallery", "index", "<PERSON><PERSON>", "open", "Link", "getApiBaseUrl", "API_BASE_URL", "apiCall", "endpoint", "options", "url", "isFormData", "defaultHeaders", "token", "config", "response", "contentType", "data", "error", "authAPI", "credentials", "blogAPI", "params", "queryString", "slug", "formData", "id", "language", "limit", "page", "adminAPI", "file", "tag", "Blog", "blogPosts", "setBlogPosts", "loading", "setLoading", "setError", "result", "posts", "_a", "getTranslation", "post", "field", "translation", "_c", "_b", "useContactItems", "Contact", "contactItems", "setFormData", "formStatus", "setFormStatus", "handleInputChange", "e", "name", "handleSubmit", "endpointUrl", "axios", "trackContactFormSubmission", "trackFormSubmission", "errorMessage", "React", "MarqueeDark", "Home", "onePage", "dark", "PropTypes", "Hero", "handleDiscoverClick", "trackButtonClick", "AnimatedText", "teamMembers2", "Team", "member", "social", "idx", "filters", "Portfolio", "portfolios5", "i", "ref"], "mappings": "kSAGA,SAAwBA,GAAQ,CACxB,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EAC7B,aACG,MAAI,CAAA,UAAU,uBACb,SAACC,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAU,kCACV,gBAAc,GACd,oBAAkB,OAClB,yBAAuB,MAEvB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,8BAA+B,CAAA,EAC9CA,EAAA,IAAC,MAAI,CAAA,UAAU,yBACb,SAAAA,EAAAA,IAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,aAAa,CAAE,CAAA,CAChD,CAAA,CAAA,EACF,EACAG,EAAA,IAAC,MAAA,CACC,IAAI,oBACJ,MAAO,IACP,OAAQ,IACR,UAAU,wBACV,IAAI,mBAAA,CAAA,CACN,CAAA,CACF,EACAD,EAAA,KAAC,MAAA,CACC,UAAU,WACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,MAEvB,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,IAAI,oBACJ,MAAO,IACP,OAAQ,IACR,UAAU,wBACV,IAAI,mBAAA,CACN,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gCAAiC,CAAA,EAChDA,EAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAA,EAAAA,IAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,iBAAiB,CAAE,CAAA,CACpD,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAEJ,CC/CO,MAAMI,EAAoBC,GAAc,CAC7C,MAAMC,EAAWC,EAAa,EACxBC,EAAeC,EAAM,OAAC,IAAI,EAC1BC,EAAiBD,EAAM,OAAC,CAAC,EACzBE,EAAsBF,EAAAA,OAAO,IAAI,GAAK,EAE5CG,OAAAA,EAAAA,UAAU,IAAM,CAEd,MAAMC,EAAgBR,EAChBS,EAAe,OAAO,SAAS,KAErCC,EAAcF,EAAeC,CAAY,EACzCN,EAAa,QAAU,KAAK,IAAK,EAGjCE,EAAe,QAAU,EACzBC,EAAoB,QAAQ,MAAO,EAGnC,MAAMK,EAAe,IAAM,CACzB,MAAMC,EAAY,OAAO,aAAe,SAAS,gBAAgB,UAC3DC,EAAiB,SAAS,gBAAgB,aAAe,OAAO,YAChEC,EAAmB,KAAK,MAAOF,EAAYC,EAAkB,GAAG,EAGlEC,EAAmBT,EAAe,UACpCA,EAAe,QAAUS,GAIR,CAAC,GAAI,GAAI,GAAI,GAAI,GAAG,EAC5B,QAAQC,GAAa,CAC1BD,GAAoBC,GAAa,CAACT,EAAoB,QAAQ,IAAIS,CAAS,IAC7ET,EAAoB,QAAQ,IAAIS,CAAS,EACzCC,EAAiBD,EAAWN,CAAY,EAElD,CAAO,CACF,EAGD,OAAO,iBAAiB,SAAUE,EAAc,CAAE,QAAS,GAAM,EAGjE,MAAMM,EAAqB,IAAM,CAC/B,GAAId,EAAa,QAAS,CACxB,MAAMe,EAAa,KAAK,OAAO,KAAK,MAAQf,EAAa,SAAW,GAAI,EACxEgB,EAAgBD,EAAYT,CAAY,CAChD,CACK,EAGKW,EAAyB,IAAM,CACnC,GAAI,SAAS,kBAAoB,UAAYjB,EAAa,QAAS,CACjE,MAAMe,EAAa,KAAK,OAAO,KAAK,MAAQf,EAAa,SAAW,GAAI,EACxEgB,EAAgBD,EAAYT,CAAY,CAChD,CACK,EAED,cAAO,iBAAiB,eAAgBQ,CAAkB,EAC1D,SAAS,iBAAiB,mBAAoBG,CAAsB,EAG7D,IAAM,CAMX,GALA,OAAO,oBAAoB,SAAUT,CAAY,EACjD,OAAO,oBAAoB,eAAgBM,CAAkB,EAC7D,SAAS,oBAAoB,mBAAoBG,CAAsB,EAGnEjB,EAAa,QAAS,CACxB,MAAMe,EAAa,KAAK,OAAO,KAAK,MAAQf,EAAa,SAAW,GAAI,EACxEgB,EAAgBD,EAAYT,CAAY,CAChD,CACK,CACF,EAAE,CAACR,EAAS,SAAUD,CAAS,CAAC,EAG1B,CACL,iBAAkB,CAACqB,EAAWC,EAAUC,EAAOC,EAAOC,IAAqB,CACrE,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAASJ,EAAW,CAC9B,eAAgBC,EAChB,YAAaC,EACb,MAAOC,EACP,cAAe,OAAO,SAAS,KAC/B,GAAGC,EACH,QAAS,cACnB,CAAS,CAEJ,EACD,sBAAuB,IAAMpB,EAAe,QAC5C,cAAe,IAAMF,EAAa,QAAU,KAAK,OAAO,KAAK,IAAG,EAAKA,EAAa,SAAW,GAAI,EAAI,CACtG,CACH,EChGauB,EAAY,CACvB,CACE,MAAO,GACP,OAAQ,GACR,KAAM,mdACN,IAAK,KACN,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,0OACN,IAAK,QACN,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,oJACN,IAAK,SACN,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,gLACN,IAAK,IACN,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,6JACN,IAAK,YACN,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,2RACN,IAAK,KACN,CACH,ECjCA,SAAwBC,GAAU,CAC1B,KAAA,CAAE,EAAAhC,CAAE,EAAIC,EAAe,EAC7B,OAEIC,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAA9B,EAAA,IAAC,MAAA,CACC,UAAU,0EACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,wDACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,+BACX,SAAAH,EAAE,gBAAgB,EACrB,QACC,MAAI,CAAA,UAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,CAAA,CAAA,CACxD,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EACCG,EAAA,IAAA,MAAA,CAAI,UAAU,gDACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,aACZ,SAAU4B,EAAA,IAAI,CAACG,EAAK,IACnB/B,EAAA,IAAC,MAAA,CAEC,UAAU,qDAEV,SAAAA,EAAAA,IAAC,OAAI,UAAU,oCACb,gBAAC,MAAI,CAAA,UAAU,oBAAoB,kBAAiB,GAClD,SAAA,CAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,kBACb,SAAAA,EAAA,IAAC,MAAA,CACC,MAAM,6BACN,MAAO+B,EAAI,MACX,OAAQA,EAAI,OACZ,QAAS,OAAOA,EAAI,KAAK,IAAIA,EAAI,MAAM,GAEvC,SAAC/B,EAAA,IAAA,OAAA,CAAK,EAAG+B,EAAI,IAAM,CAAA,CAAA,CAAA,EAEvB,EACA/B,EAAAA,IAAC,MAAG,UAAU,mBACX,WAAE,YAAY+B,EAAI,GAAG,QAAQ,CAChC,CAAA,EACA/B,EAAAA,IAAC,OAAI,UAAU,kBACZ,WAAE,YAAY+B,EAAI,GAAG,OAAO,CAC/B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EAtBK,CAwBR,CAAA,CACH,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,CC1DO,MAAMC,EAAW,CACtB,CACE,GAAI,EACJ,SAAU,cACV,MAAO,cACP,KAAM,WACN,WAAY,CAAC,MAAM,EACnB,YAAa,CACX,QAAS,cACT,WAAY,kCACZ,KAAM,uaACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,oBACP,KAAM,gBACN,WAAY,CAAC,YAAY,EACzB,YAAa,CACX,QAAS,oBACT,WAAY,kCACZ,KAAM,kdACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,gBACP,KAAM,gBACN,WAAY,CAAC,QAAQ,EACrB,YAAa,CACX,QAAS,gBACT,WAAY,+BACZ,KAAM,wdACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,kBACP,KAAM,gBACN,WAAY,CAAC,KAAM,YAAY,EAC/B,YAAa,CACX,QAAS,yBACT,WAAY,4BACZ,KAAM,2cACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,qBACP,KAAM,gBACN,WAAY,CAAC,IAAI,EACjB,YAAa,CACX,QAAS,qBACT,WAAY,gCACZ,KAAM,2fACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,oBACP,KAAM,WACN,WAAY,CAAC,aAAc,YAAY,EACvC,YAAa,CACX,QAAS,oBACT,WAAY,0BACZ,KAAM,6eACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,eACP,KAAM,gBACN,WAAY,CAAC,OAAO,EACpB,YAAa,CACX,QAAS,eACT,WAAY,wBACZ,KAAM,8dACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,kBACP,KAAM,WACN,WAAY,CAAC,YAAY,EACzB,YAAa,CACX,QAAS,yBACT,WAAY,6BACZ,KAAM,ohBACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,uBACP,KAAM,gBACN,WAAY,CAAC,gBAAiB,YAAY,EAC1C,YAAa,CACX,QAAS,uBACT,WAAY,wBACZ,KAAM,6eACP,CACF,EACD,CACE,GAAI,GACJ,SAAU,cACV,MAAO,mBACP,KAAM,gBACN,WAAY,CAAC,YAAa,YAAY,EACtC,YAAa,CACX,QAAS,mBACT,WAAY,kCACZ,KAAM,yfACP,CACF,EACD,CACE,GAAI,GACJ,SAAU,cACV,MAAO,cACP,KAAM,gBACN,WAAY,CAAC,KAAK,EAClB,YAAa,CACX,QAAS,oBACT,WAAY,0CACZ,KAAM,qhBACP,CACF,CACH,EC/HA,SAAwBC,GAAe,CAErC,KAAM,CAAE,EAAGC,EAAW,KAAAC,CAAA,EAASrC,EAAe,EACxCsC,EAAkBD,EAAK,UAAY,KAEnC,CAACE,EAAiBC,CAAkB,EAAIC,EAAAA,SAAS,KAAK,EACtD,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAASP,CAAQ,EAC3C,CAACU,EAAcC,CAAe,EAAIJ,EAAAA,SAAS,KAAK,EAGhD,CAACK,EAAYC,CAAa,EAAIN,WAAS,CAC3C,QAAS,qBACT,WAAY,wBACZ,KAAM,iBAAA,CACP,EAED9B,EAAAA,UAAU,IAAM,CAEZgC,EADEJ,IAAoB,MAEpB,CAAC,GAAGL,CAAQ,EAAE,OAAQc,GAAS,CAACA,EAAK,WAAW,SAAS,KAAK,CAAC,EAI/D,CAAC,GAAGd,CAAQ,EAAE,OACXD,GACCA,EAAI,WAAW,SAASM,CAAe,GACvC,CAACN,EAAI,WAAW,SAAS,KAAK,CAAA,CANpC,CASF,EACC,CAACM,EAAiBD,CAAe,CAAC,EAG/B,MAAAW,EAAqBC,GAAmB,CAC5CL,EAAgBK,CAAc,EAGhBH,EAAA,CACZ,QAAS,UAAUG,CAAc,WACjC,WAAY,UAAUA,CAAc,cACpC,KAAM,UAAUA,CAAc,OAAA,CAC/B,CACH,EAEA,OAEIjD,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAC/B,EAAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,iDACX,SAAAkC,EAAU,aAAa,EAC1B,EACAnC,EAAAA,KAAC,KAAG,CAAA,UAAU,qBACZ,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,6BAA6B,SAAS,YAAA,EACtDD,EAAAA,KAAC,OAAK,CAAA,UAAU,6BACb,SAAA,CAAA,IACAmC,EAAUU,EAAW,OAAO,CAAA,CAC/B,CAAA,CAAA,EACF,QACC,KAAG,CAAA,UAAU,iDACX,SAAUV,EAAAU,EAAW,UAAU,EAClC,QACC,MAAI,CAAA,UAAU,wBACZ,SAAUV,EAAAU,EAAW,IAAI,CAC5B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGA7C,EAAAA,KAAC,MAAI,CAAA,UAAU,sDACb,SAAA,CAAAC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,KAAK,EACvBT,EAAmB,KAAK,CAC1B,EACA,UAAW,UAAUI,IAAiB,MAAQ,SAAW,EAAE,GAC3D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,gBAAgB,CAAA,CAC7B,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,MAAM,EACxBT,EAAmB,MAAM,CAC3B,EACA,UAAW,UAAUI,IAAiB,OAAS,SAAW,EAAE,GAC5D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,iBAAiB,CAAA,CAC9B,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,YAAY,EAC9BT,EAAmB,YAAY,CACjC,EACA,UAAW,UACTI,IAAiB,aAAe,SAAW,EAC7C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,uBAAuB,CAAA,CACpC,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,QAAQ,EAC1BT,EAAmB,QAAQ,CAC7B,EACA,UAAW,UAAUI,IAAiB,SAAW,SAAW,EAAE,GAC9D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,mBAAmB,CAAA,CAChC,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,IAAI,EACtBT,EAAmB,IAAI,CACzB,EACA,UAAW,UAAUI,IAAiB,KAAO,SAAW,EAAE,GAC1D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,eAAe,CAAA,CAC5B,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,YAAY,EAC9BT,EAAmB,YAAY,CACjC,EACA,UAAW,UACTI,IAAiB,aAAe,SAAW,EAC7C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,uBAAuB,CAAA,CACpC,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,YAAY,EAC9BT,EAAmB,YAAY,CACjC,EACA,UAAW,UACTI,IAAiB,aAAe,SAAW,EAC7C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,uBAAuB,CAAA,CACpC,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,OAAO,EACzBT,EAAmB,OAAO,CAC5B,EACA,UAAW,UAAUI,IAAiB,QAAU,SAAW,EAAE,GAC7D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,kBAAkB,CAAA,CAC/B,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,SAAS,EAC3BT,EAAmB,SAAS,CAC9B,EACA,UAAW,UAAUI,IAAiB,UAAY,SAAW,EAAE,GAC/D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,oBAAoB,CAAA,CACjC,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,eAAe,EACjCT,EAAmB,eAAe,CACpC,EACA,UAAW,UACTI,IAAiB,gBAAkB,SAAW,EAChD,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,0BAA0B,CAAA,CACvC,EAGA1C,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb+C,EAAkB,WAAW,EAC7BT,EAAmB,WAAW,CAChC,EACA,UAAW,UAAUI,IAAiB,YAAc,SAAW,EAAE,GACjE,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,sBAAsB,CAAA,CAAA,CACnC,CACF,CAAA,CAAA,EAEF,EACA1C,EAAA,IAAC,MAAA,CACC,UAAU,oBACV,MAAO,CACL,MAAO,OACP,SAAU,SACV,OAAQ,QACV,EAEA,SAAAD,EAAA,KAAC,MAAA,CACC,MAAO,CACL,MAAO,OACP,SAAU,WACV,aAAc,gBACd,SAAU,QACZ,EAEA,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,IAAI,gCACJ,IAAI,iBACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,QAAS,QACT,aAAc,eAAA,CAChB,CACF,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,0CACV,GAAG,YACH,MAAO,CACL,SAAU,WACV,IAAK,EACL,KAAM,EACN,MAAO,OACP,OAAQ,OACR,QAAS,OACT,oBAAqB,iBACrB,IAAK,MACL,WAAY,cACZ,aAAc,gBACd,SAAU,SACV,OAAQ,EACR,QAAS,CACX,EAEA,eAACiD,EACE,CAAA,SAAAT,EAAS,IAAI,CAACM,EAAMI,IACnBlD,EAAA,IAAC,KAAA,CAEC,UAAW,iBAAiB8C,EAAK,WAAW,KAAK,GAAG,CAAC,GACrD,MAAO,CACL,WAAY,cACZ,MAAO,OACP,OAAQ,MACV,EAEC,SAAAA,EAAK,OAAS,WACb9C,EAAA,IAACmD,EAAA,CACC,SAAUL,EAAK,OACf,UAAWA,EAAK,OAChB,MAAO,IACP,OAAQ,IAEP,SAAA,CAAC,CAAE,KAAAM,CAAA,IACFrD,EAAA,KAAC,IAAA,CACC,QAASqD,EACT,UAAU,+BACV,MAAO,CACL,QAAS,OACT,cAAe,SACf,MAAO,OACP,OAAQ,OACR,aAAc,OACd,SAAU,QACZ,EAEA,SAAA,CAAApD,EAAA,IAAC,MAAA,CACC,UAAU,WACV,MAAO,CACL,KAAM,EACN,WAAY,aAAA,CACd,CACF,EACAD,EAAA,KAAC,MAAA,CACC,UAAU,aACV,MAAO,CACL,QAAS,MACX,EAEA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,aACX,SAAAkC,EACC,cAAcY,EAAK,MAChB,MAAM,GAAG,EAAE,CAAC,EACZ,aAAa,EAAA,EAEpB,EACC9C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CAAA,CAAA,CACzC,CAAA,CAAA,CACF,CAAA,EAIJD,EAAA,KAACsD,EAAA,CACC,GAAI,qBAAqBP,EAAK,EAAE,GAChC,UAAU,gBACV,MAAO,CAAE,WAAY,aAAc,EAEnC,SAAA,CAAA9C,EAAA,IAAC,MAAA,CACC,UAAU,WACV,MAAO,CACL,OAAQ,QACR,WAAY,aACd,EAEA,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,cACV,MAAO,CAAE,WAAY,aAAc,CAAA,CAAA,CACrC,CACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,aACX,SAAAkC,EACC,cAAcY,EAAK,MAChB,MAAM,GAAG,EAAE,CAAC,EACZ,aAAa,EAAA,EAEpB,EACC9C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,EAjFGkD,CAAA,CAoFR,CACH,CAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EACF,CAEJ,CCnWA,MAAMI,EAAgB,IAGX,2BAOEC,EAAeD,EAAc,EAG7BE,EAAU,MAAOC,EAAUC,EAAU,KAAO,CACvD,MAAMC,EAAM,GAAGJ,CAAY,GAAGE,CAAQ,GAGhCG,EAAaF,EAAQ,gBAAgB,SAErCG,EAAiB,CAAC,EAInBD,IACHC,EAAe,cAAc,EAAI,oBAI/BJ,EAAS,SAAS,UAAU,IAC9BI,EAAe,WAAW,EAAI,oCAI1B,MAAAC,EAAQ,aAAa,QAAQ,YAAY,EAC3CA,IACaD,EAAA,cAAmB,UAAUC,CAAK,IAGnD,MAAMC,EAAS,CACb,GAAGL,EACH,QAAS,CACP,GAAGG,EACH,GAAGH,EAAQ,OAAA,CAEf,EAEI,GAAA,CACF,MAAMM,EAAW,MAAM,MAAML,EAAKI,CAAM,EAGlCE,EAAcD,EAAS,QAAQ,IAAI,cAAc,EACvD,GAAIC,GAAeA,EAAY,SAAS,kBAAkB,EAAG,CACrD,MAAAC,EAAO,MAAMF,EAAS,KAAK,EAC1B,MAAA,CAAE,SAAAA,EAAU,KAAAE,CAAK,CAAA,KAEjB,OAAA,CAAE,SAAAF,EAAU,KAAM,IAAK,QAEzBG,EAAO,CACN,cAAA,MAAM,mBAAoBA,CAAK,EACjCA,CAAA,CAEV,EAGaC,GAAU,CACrB,MAAQC,GACNb,EAAQ,cAAe,CACrB,OAAQ,OACR,KAAM,KAAK,UAAUa,CAAW,CAAA,CACjC,EAEH,MAAO,IAAMb,EAAQ,UAAU,EAE/B,OAAQ,IAAMA,EAAQ,eAAgB,CAAE,OAAQ,MAAQ,CAAA,CAC1D,EAEac,EAAU,CACrB,SAAU,CAACC,EAAS,KAAO,CACzB,MAAMC,EAAc,IAAI,gBAAgBD,CAAM,EAAE,SAAS,EACzD,OAAOf,EAAQ,QAAQgB,EAAc,IAAIA,CAAW,GAAK,EAAE,EAAE,CAC/D,EAEA,QAAUC,GAASjB,EAAQ,SAASiB,CAAI,EAAE,EAE1C,WAAaC,GACXlB,EAAQ,QAAS,CACf,OAAQ,OACR,KAAMkB,EACN,QAAS,CAAA,CAAC,CACX,EAEH,WAAY,CAACC,EAAID,IACflB,EAAQ,SAASmB,CAAE,GAAI,CACrB,OAAQ,MACR,KAAMD,EACN,QAAS,CAAA,CAAC,CACX,EAEH,WAAaC,GAAOnB,EAAQ,SAASmB,CAAE,GAAI,CAAE,OAAQ,SAAU,EAE/D,iBAAmBA,GACjBnB,EAAQ,SAASmB,CAAE,qBAAsB,CACvC,OAAQ,OAAA,CACT,EAGH,iBAAkB,CAACC,EAAW,KAAMC,EAAQ,IACnCrB,EAAQ,6BAA6BqB,CAAK,iBAAiB,EAIpE,aAAc,CAACD,EAAW,KAAME,EAAO,EAAGD,EAAQ,IACzCrB,EAAQ,cAAcsB,CAAI,UAAUD,CAAK,iBAAiB,CAErE,EAEaE,GAAW,CACtB,aAAc,IAAMvB,EAAQ,kBAAkB,EAE9C,SAAU,CAACe,EAAS,KAAO,CACzB,MAAMC,EAAc,IAAI,gBAAgBD,CAAM,EAAE,SAAS,EACzD,OAAOf,EAAQ,eAAegB,EAAc,IAAIA,CAAW,GAAK,EAAE,EAAE,CACtE,EAEA,YAAcQ,GAAS,CACf,MAAAN,EAAW,IAAI,SACZ,OAAAA,EAAA,OAAO,QAASM,CAAI,EACtBxB,EAAQ,sBAAuB,CACpC,OAAQ,OACR,KAAMkB,EACN,QAAS,CAAA,CAAC,CACX,CACH,EAEA,cAAe,IAAMlB,EAAQ,mBAAmB,EAEhD,eAAiBhC,GACfgC,EAAQ,oBAAqB,CAC3B,OAAQ,OACR,KAAM,KAAK,UAAUhC,CAAQ,CAAA,CAC9B,EAEH,eAAgB,CAACmD,EAAInD,IACnBgC,EAAQ,qBAAqBmB,CAAE,GAAI,CACjC,OAAQ,MACR,KAAM,KAAK,UAAUnD,CAAQ,CAAA,CAC9B,EAEH,eAAiBmD,GACfnB,EAAQ,qBAAqBmB,CAAE,GAAI,CACjC,OAAQ,QAAA,CACT,EAEH,QAAS,IAAMnB,EAAQ,aAAa,EAEpC,UAAYyB,GACVzB,EAAQ,cAAe,CACrB,OAAQ,OACR,KAAM,KAAK,UAAUyB,CAAG,CAAA,CACzB,EAEH,UAAW,CAACN,EAAIM,IACdzB,EAAQ,eAAemB,CAAE,GAAI,CAC3B,OAAQ,MACR,KAAM,KAAK,UAAUM,CAAG,CAAA,CACzB,EAEH,UAAYN,GACVnB,EAAQ,eAAemB,CAAE,GAAI,CAC3B,OAAQ,QACT,CAAA,CACL,ECxKA,SAAwBO,GAAO,CAC7B,KAAM,CAAE,KAAA/C,EAAM,EAAAtC,CAAE,EAAIC,EAAe,EAC7BsC,EAAkBD,EAAK,UAAY,KACnC,CAACgD,EAAWC,CAAY,EAAI7C,EAAAA,SAAS,CAAA,CAAE,EACvC,CAAC8C,EAASC,CAAU,EAAI/C,EAAAA,SAAS,EAAI,EACrC,CAAC4B,EAAOoB,CAAQ,EAAIhD,EAAAA,SAAS,EAAE,EAErC9B,EAAAA,UAAU,IAAM,EACS,SAAY,OAC7B,GAAA,CACF6E,EAAW,EAAI,EACf,MAAME,EAAS,MAAMlB,EAAQ,iBAAiBlC,EAAiB,CAAC,EAEhE,GAAIoD,EAAO,SAAS,IAAMA,EAAO,KAAM,CAE/B,MAAAC,IAAQC,EAAAF,EAAO,KAAK,OAAZ,YAAAE,EAAkB,OAAQF,EAAO,KAAK,MAAQ,CAAC,EACrD,QAAA,IAAI,qBAAsBA,EAAO,IAAI,EACrC,QAAA,IAAI,eAAgBC,CAAK,EACjCL,EAAa,MAAM,QAAQK,CAAK,EAAIA,EAAQ,CAAA,CAAE,CAAA,MAE9C,QAAQ,MAAM,8BAA+BD,EAAO,SAAS,MAAM,EACnEJ,EAAa,CAAA,CAAE,QAEVjB,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjDoB,EAAS,2BAA2B,EACpCH,EAAa,CAAA,CAAE,CAAA,QACf,CACAE,EAAW,EAAK,CAAA,CAEpB,GAEe,CAAA,EACd,CAAClD,CAAe,CAAC,EAGd,MAAAuD,EAAiB,CAACC,EAAMC,IAAU,WAChC,MAAAC,GAAcJ,EAAAE,EAAK,eAAL,YAAAF,EAAmB,KACpC7F,GAAMA,EAAE,WAAauC,GAExB,OACE0D,GAAA,YAAAA,EAAcD,OACdE,GAAAC,EAAAJ,EAAK,eAAL,YAAAI,EAAmB,KAAMnG,GAAMA,EAAE,WAAa,QAA9C,YAAAkG,EAAsDF,KACtD,EAEJ,EAGA,OAAIR,EAEAtF,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,EACCG,MAAA,MAAA,CAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YAAY,SAAA,wBAAqB,EAClD,CACF,CAAA,CAAA,EACF,EAKAmE,EAEApE,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,EACCG,MAAA,MAAA,CAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YAAa,SAAAmE,EAAM,EACpC,CACF,CAAA,CAAA,EACF,EAKAgB,EAAU,SAAW,EAErBpF,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,EACCG,MAAA,MAAA,CAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YAAY,SAAA,+BAA4B,EACzD,CACF,CAAA,CAAA,EACF,EAIFD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,EACjD,EACAG,EAAAA,IAAC,OAAI,UAAU,2DACb,gBAACqD,EAAK,CAAA,GAAI,QAAS,UAAU,eAC1B,SAAA,CAAAxD,EAAE,gBAAgB,EAAE,IAACG,EAAAA,IAAC,IAAE,CAAA,UAAU,0BAA2B,CAAA,CAAA,CAAA,CAChE,CACF,CAAA,CAAA,EACF,EACCA,EAAAA,IAAA,MAAA,CAAI,UAAU,aAEZ,SAAM,MAAA,QAAQmF,CAAS,GACtBA,EAAU,IAAI,CAACS,EAAM1C,IACnB,OAAAlD,OAAAA,EAAA,IAAC,MAAA,CAEC,UAAW,mDACX,iBAAgB,GAAGkD,EAAQ,EAAG,IAE9B,SAAAnD,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAAAA,EAAAA,IAACqD,GAAK,GAAI,gBAAgBuC,EAAK,IAAI,GACjC,SAAA5F,EAAA,IAAC,MAAA,CACC,IACE4F,EAAK,eACL,yCAEF,MAAO,IACP,OAAQ,IACR,IAAKD,EAAeC,EAAM,OAAO,CAAA,GAErC,CACF,CAAA,EACC5F,MAAA,KAAA,CAAG,UAAU,kBACZ,eAACqD,EAAK,CAAA,GAAI,gBAAgBuC,EAAK,IAAI,GAChC,SAAAD,EAAeC,EAAM,OAAO,CAC/B,CAAA,EACF,QACC,MAAI,CAAA,UAAU,iBACZ,SAAeD,EAAAC,EAAM,SAAS,EACjC,EACA7F,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,cACpB,SAACA,EAAAA,IAAA,IAAA,CAAE,UAAU,8BAAA,CAA+B,CAC9C,CAAA,QACC,IAAE,CAAA,KAAK,IAAK,WAAK0F,EAAAE,EAAA,SAAA,YAAAF,EAAQ,OAAQ,gBAAiB,CAAA,CAAA,EACrD,EACA3F,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,kCAAmC,CAAA,EAC/CA,EAAA,IAAA,IAAA,CAAE,KAAK,IACL,SAAI,IAAA,KACH4F,EAAK,aAAeA,EAAK,SAC3B,EAAE,oBACJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EA1CKA,EAAK,EAAA,EA4Cb,CAEL,CAAA,CAAA,EACF,CAEJ,CCzLO,MAAMK,EAAkB,IAAM,CACnC,KAAM,CAAE,EAAApG,CAAG,EAAGC,EAAgB,EAE9B,MAAO,CACL,CACE,UAAW,cACX,MAAOD,EAAE,uBAAuB,EAChC,KAAMA,EAAE,sBAAsB,EAC9B,KAAM,CACJ,IAAK,4CACL,KAAMA,EAAE,sBAAsB,EAC9B,IAAK,oBACL,OAAQ,QACT,CACF,EACD,CACE,UAAW,WACX,MAAOA,EAAE,qBAAqB,EAC9B,KAAMA,EAAE,oBAAoB,EAC5B,KAAM,CACJ,IAAK,2BACL,KAAMA,EAAE,oBAAoB,CAC7B,CACF,EACD,CACE,UAAW,YACX,MAAOA,EAAE,qBAAqB,EAC9B,KAAMA,EAAE,oBAAoB,EAC5B,KAAM,CACJ,IAAK,mBACL,KAAMA,EAAE,oBAAoB,CAC7B,CACF,CACF,CACH,EC3BA,SAAwBqG,GAAU,CAC1B,KAAA,CAAE,EAAArG,CAAE,EAAIC,EAAe,EACvBqG,EAAeF,EAAgB,EAC/B,CAACvB,EAAU0B,CAAW,EAAI7D,WAAS,CACvC,KAAM,GACN,MAAO,GACP,QAAS,EAAA,CACV,EACK,CAAC8D,EAAYC,CAAa,EAAI/D,WAAS,CAC3C,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAAS,EAAA,CACV,EAEKgE,EAAqBC,GAAM,CAC/B,KAAM,CAAE,KAAAC,EAAM,MAAA/E,CAAM,EAAI8E,EAAE,OACdJ,EAAA,CACV,GAAG1B,EACH,CAAC+B,CAAI,EAAG/E,CAAA,CACT,CACH,EAGMgF,EAAe,MAAOF,GAAM,SAChCA,EAAE,eAAe,EAGHF,EAAA,CACZ,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAAS,EAAA,CACV,EAEG,GAAA,CAGI,MAAAK,EADgB,OAAO,SAAS,WAAa,YAE/C,iEACA,gEAIE,MAAAC,EAAM,KAAKD,EAAajC,EAAU,CACtC,QAAS,CACP,eAAgB,mBAChB,YANW,kCAME,CACf,CACD,EAGDmC,EAA2BnC,EAAS,MAAOA,EAAS,QAAQ,MAAM,EAClEoC,EAAoB,eAAgB,OAAO,SAAS,SAAU,EAAI,EAGpDR,EAAA,CACZ,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QACEzG,EAAE,iBAAiB,GACnB,qDAAA,CACH,EAGWuG,EAAA,CACV,KAAM,GACN,MAAO,GACP,QAAS,EAAA,CACV,QACMjC,EAAO,CACN,QAAA,MAAM,sBAAuBA,CAAK,EAG1C2C,EAAoB,eAAgB,OAAO,SAAS,SAAU,EAAK,EAG/D,IAAAC,EACFlH,EAAE,eAAe,GACjB,sEAEE6F,EAAAvB,EAAM,WAAN,YAAAuB,EAAgB,UAAW,IAE3BqB,EAAAlH,EAAE,mBAAmB,GACrB,iEACOmG,EAAA7B,EAAM,WAAN,YAAA6B,EAAgB,UAAW,MAElCe,EAAAlH,EAAE,oBAAoB,GAAK,0CAGjByG,EAAA,CACZ,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAASS,CAAA,CACV,CAAA,CAEL,EAGE,OAAAhH,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,4BACb,eAAC,MAAI,CAAA,UAAU,wBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MAEZ,SAAAmG,EAAa,IAAI,CAACrD,EAAMI,IACtBnD,EAAAA,KAAAiH,EAAM,SAAN,CACC,SAAA,CAAAhH,EAAAA,IAAC,OAAI,UAAW,8BACd,SAACD,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,UACb,SAAAA,EAAAA,IAAC,KAAE,UAAW8C,EAAK,UAAW,CAChC,CAAA,EACC9C,EAAA,IAAA,KAAA,CAAG,UAAU,WAAY,WAAK,MAAM,EACpCA,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAiB,WAAK,KAAK,EAC1CA,EAAAA,IAAC,MAAI,CAAA,UAAU,UACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAM8C,EAAK,KAAK,IAChB,OAAQA,EAAK,KAAK,OAClB,IAAKA,EAAK,KAAK,IAEd,WAAK,KAAK,IAAA,CAAA,EAEf,EAAO,GAAA,CAAA,CACT,CACF,CAAA,EAAO,GAAA,CAAA,EAlBYI,CAmBrB,CACD,EAGH,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,MACb,SAAClD,MAAA,MAAA,CAAI,UAAU,wBACb,SAAAD,EAAA,KAAC,OAAA,CACC,SAAU2G,EACV,UAAU,2CACV,iBAAe,MACf,GAAG,eAEH,SAAA,CAAC3G,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,WAEb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAC,MAAC,QAAM,CAAA,QAAQ,OAAQ,SAAAH,EAAE,cAAc,EAAE,EACzCG,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,OACL,GAAG,OACH,UAAU,8BACV,YAAaH,EAAE,0BAA0B,EACzC,QAAQ,WACR,SAAQ,GACR,gBAAc,OACd,MAAO6E,EAAS,KAChB,SAAU6B,CAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,QACC,MAAI,CAAA,UAAU,WAEb,SAACxG,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAC,MAAC,QAAM,CAAA,QAAQ,QAAS,SAAAH,EAAE,eAAe,EAAE,EAC3CG,EAAA,IAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAaH,EAAE,2BAA2B,EAC1C,QAAQ,WACR,SAAQ,GACR,gBAAc,OACd,MAAO6E,EAAS,MAChB,SAAU6B,CAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAEAxG,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,MAAC,QAAM,CAAA,QAAQ,UAAW,SAAAH,EAAE,iBAAiB,EAAE,EAC/CG,EAAA,IAAC,WAAA,CACC,KAAK,UACL,GAAG,UACH,UAAU,8BACV,MAAO,CAAE,OAAQ,GAAI,EACrB,YAAaH,EAAE,6BAA6B,EAC5C,MAAO6E,EAAS,QAChB,SAAU6B,EACV,UAAW,EACX,UAAW,IACX,SAAQ,EAAA,CACV,EACAxG,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACZ,SAAA,CAAA2E,EAAS,QAAQ,OAAO,kBAAA,CAC3B,CAAA,CAAA,EACF,EACA3E,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,WAEb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,mBAAoB,CAAA,EAChCH,EAAE,eAAe,CAAA,CAAA,CACpB,CACF,CAAA,QACC,MAAI,CAAA,UAAU,WAEb,SAACG,MAAA,MAAA,CAAI,UAAU,iBACb,SAAAD,EAAA,KAAC,SAAA,CACC,KAAK,SACL,GAAG,aACH,gBAAc,SACd,UAAU,wDACV,oBAAkB,IAClB,SAAUsG,EAAW,WAErB,SAAA,CAACtG,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAWsG,EAAA,WAAa,aAAexG,EAAE,cAAc,EACxDG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAWsG,EAAA,WAAa,aAAexG,EAAE,cAAc,EACxDG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,GAEJ,CACF,CAAA,CAAA,EACF,EACAA,EAAA,IAAC,MAAA,CACC,GAAG,SACH,KAAK,SACL,YAAU,SACV,cAAY,OACZ,UAAW,oBACTqG,EAAW,QACP,kCACAA,EAAW,MACX,gCACA,EACN,GACA,MAAO,CAAE,QAASA,EAAW,QAAU,QAAU,MAAO,EAEvD,SAAWA,EAAA,OAAA,CAAA,CACd,CAAA,GAEJ,CACF,CAAA,CAAA,EAEF,CAEJ,CC3QA,SAAwBY,GAAc,CAC9B,KAAA,CAAE,EAAApH,CAAE,EAAIC,EAAe,EAC7B,OAEIC,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAA9B,EAAAA,IAAC,OAAI,UAAU,0CACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAK,SAAEH,EAAA,sBAAsB,CAAE,CAAA,QAC/B,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,CAAE,CAAA,CAAA,CAAA,CACrD,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,oCACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAK,SAAEH,EAAA,iBAAiB,CAAE,CAAA,QAC1B,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,CAAE,CAAA,CAAA,CAAA,CAChD,CACF,CAAA,CAAA,EACF,CAEJ,CC/CA,SAAwBqH,EAAK,CAAE,QAAAC,EAAU,GAAO,KAAAC,EAAO,IAAS,CACxD,KAAA,CAAE,EAAAvH,CAAE,EAAIC,EAAe,EAG7B,OAAAG,EAAiB,2CAA2C,EAGxDF,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAA9B,EAAA,IAAC,UAAA,CACC,UAAW,wCACToH,EAAO,0BAA4B,EACrC,IACA,GAAG,QAEH,eAAC,MAAI,CAAA,UAAU,8BACb,SAACrH,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,8CACb,SAAAD,EAAA,KAAC,MACC,CAAA,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,kBAAkB,iBAAe,QAC9C,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAAQ,IAAI,mBAExDG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,2BACb,SAAAA,EAAAA,IAAC,IAAE,CAAA,UAAU,OAAQ,SAAAH,EAAE,wBAAwB,CAAE,CAAA,CACnD,CAAA,CAAA,EACF,EACCG,MAAA,MAAA,CAAI,UAAU,2CACZ,WAEID,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAA,IACD/B,EAAA,KAAC,IAAA,CACC,KAAK,QACL,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,EAGGD,EAAAA,KAAA+B,EAAA,SAAA,CAAA,SAAA,CAAA,IACD/B,EAAA,KAACsD,EAAA,CACC,GAAI,iBAAiB+D,EAAO,QAAU,EAAE,GACxC,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACrH,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACCJ,EAAM,CAAA,CAAA,CAAA,CAAA,CACT,CACF,CAAA,CAAA,CACF,QAECqH,EAAY,EAAA,EAEbjH,EAAA,IAAC,UAAA,CACC,UAAW,0FACToH,EAAO,0BAA4B,EACrC,GACA,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,MAEH,eAACnF,EAAa,CAAA,CAAA,CAAA,CAChB,QAEC,MAAI,CAAA,UAAU,+BACb,SAAAjC,MAACiH,GAAY,CAAA,EACf,EAEAjH,EAAA,IAAC,UAAA,CACC,UAAU,6EACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,eAAC,MAAI,CAAA,UAAU,8BACb,SAACD,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,oBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,6EACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,wCACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,wCACJ,MAAO,IACP,OAAQ,IACR,IAAI,mBAAA,CAAA,EAER,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,wBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,QAEvB,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,wCACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,GAAA,CAAA,CACV,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,iDACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,oBACb,SAAAD,OAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,+BACX,SAAAH,EAAE,qBAAqB,EAC1B,EACAG,EAAA,IAAC,MAAI,CAAA,UAAU,2BACb,SAAAA,EAAAA,IAAC,IAAE,CAAA,UAAU,OAAQ,SAAAH,EAAE,2BAA2B,CAAE,CAAA,EACtD,EACCG,MAAA,MAAA,CAAI,UAAU,eACZ,WAEID,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAA,IACD/B,EAAA,KAAC,IAAA,CACC,KAAK,YACL,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,EAGGD,EAAAA,KAAA+B,EAAA,SAAA,CAAA,SAAA,CAAA,IACD/B,EAAA,KAACsD,EAAA,CACC,GAAI,oBAAoB+D,EAAO,QAAU,EAAE,GAC3C,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACrH,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,QAYC,MAAI,CAAA,UAAU,+BACb,SAAAA,MAACiH,GAAY,CAAA,EACf,EACAjH,EAAA,IAAC,UAAA,CACC,UAAW,wCACToH,EAAO,0BAA4B,EACrC,IACA,GAAG,WAEH,eAACvF,EAAQ,CAAA,CAAA,CAAA,CACX,EACA7B,EAAAA,IAAC,KAAG,CAAA,UAAU,WAAY,CAAA,EA2B1BA,EAAA,IAAC,UAAA,CACC,UAAW,iBACToH,EAAO,0BAA4B,yBACrC,+BACA,MAAO,CACL,gBAAiB,wCACnB,EAEA,eAAC,MAAI,CAAA,UAAU,YACb,SAACrH,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,+EACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,sCACX,SAAAH,EAAE,gBAAgB,CACrB,CAAA,EACF,EACAG,EAAA,IAAC,OAAI,UAAU,4CACb,eAAC,MAAI,CAAA,UAAU,SACZ,SAAAmH,EAEIpH,EAAAA,KAAA+B,EAAAA,SAAA,CAAA,SAAA,CAAA,IACD/B,EAAA,KAAC,IAAA,CACC,KAAK,WACL,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,oCACb,SAAAH,EAAE,iBAAiB,EACtB,EACAG,EAAA,IAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,WAAE,iBAAiB,CAAA,CAAA,CACtB,CAAA,CAAA,CACF,CAAA,CACF,EAGGD,EAAAA,KAAA+B,EAAA,SAAA,CAAA,SAAA,CAAA,IACD/B,EAAA,KAACsD,EAAA,CACC,GAAI,mBAAmB+D,EAAO,QAAU,EAAE,GAC1C,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAAApH,MAAC,OAAK,CAAA,UAAU,oCACb,SAAAH,EAAE,iBAAiB,EACtB,EACAG,EAAA,IAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,WAAE,iBAAiB,CAAA,CAAA,CACtB,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,EAaAA,EAAA,IAAC,UAAA,CACC,UAAW,iCACToH,EAAO,0BAA4B,EACrC,GACA,GAAG,OAEH,eAAClC,EAAK,CAAA,CAAA,CAAA,CACR,EAEAnF,EAAA,KAAC,UAAA,CACC,UAAW,mCACTqH,EAAO,0BAA4B,EACrC,IACA,GAAG,UAEH,SAAA,CAAApH,MAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wDACb,SAACD,OAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,oBAAoB,EAAE,EACpDG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,CAAA,CACF,CACF,CAAA,EACF,QACCkG,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CACX,EACF,CAEJ,CAEAgB,EAAK,UAAY,CACf,QAASG,EAAU,KACnB,KAAMA,EAAU,IAClB,EC/YA,SAAwBC,IAAO,CACvB,KAAA,CAAE,EAAAzH,CAAE,EAAIC,EAAe,EAEvByH,EAAsB,IAAM,CAChCC,EAAiB,eAAgB,eAAgB,CAC/C,YAAa,MACb,QAAS,MAAA,CACV,CACH,EAEE,OAAAzH,EAAA,KAAC,MAAI,CAAA,UAAU,yFAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,wDACX,SAAAH,EAAE,cAAc,EACnB,QACC,KAAG,CAAA,UAAU,uCACZ,SAAAG,EAAAA,IAAC,QAAK,UAAU,sBAAsB,iBAAe,QACnD,eAACyH,EAAa,CAAA,KAAM5H,EAAE,aAAa,CAAA,CAAG,CACxC,CAAA,EACF,EACCG,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,iBAAe,QAC7D,SAAAD,EAAA,KAAC,IAAA,CACC,KAAK,SACL,UAAU,6CACV,oBAAkB,IAClB,QAASwH,EAET,SAAA,CAACxH,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,eAAe,EAAG,IACrBG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,eAAe,EAAG,IACrBG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAA,EAAA,IAAC,MAAA,CACC,UAAU,+CACV,kBAAiB,EAEjB,SAAAA,EAAA,IAAC,KAAE,KAAK,SAAS,UAAU,gBACxB,SAAAH,EAAE,iBAAiB,GAAK,aAC3B,CAAA,CAAA,CAAA,CACF,EAEF,CAEJ,CCxBO,MAAM6H,EAAe,CAC1B,CACE,KAAM,eACN,KAAM,gBACN,MAAO,uBACP,QAAS,CACP,CAAE,KAAM,WAAY,IAAK,0CAA4C,EACrE,CAAE,KAAM,UAAW,IAAK,GAAK,EAC7B,CAAE,KAAM,SAAU,IAAK,gCAAkC,CAC1D,CACF,EACD,CACE,KAAM,uBACN,KAAM,qBACN,MAAO,+BACP,QAAS,CACP,CAAE,KAAM,WAAY,IAAK,GAAK,EAC9B,CAAE,KAAM,UAAW,IAAK,GAAK,EAC7B,CAAE,KAAM,YAAa,IAAK,GAAK,CAChC,CACF,CAEH,EC7DA,SAAwBC,IAAO,CACvB,KAAA,CAAE,EAAA9H,CAAE,EAAIC,EAAe,EAE3B,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,UAAU,YACb,SAAEH,EAAA,kBAAkB,EAAE,MAAM,GAAG,EAAE,CAAC,CACrC,CAAA,EAAQ,IACPA,EAAE,kBAAkB,EAAE,MAAM,GAAG,EAAE,CAAC,EAClCG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,wBAAwB,CAAE,CAAA,CAAA,CAAA,CAC1D,CACF,CAAA,QACC,MAAI,CAAA,UAAU,oCAEZ,SAAA6H,EAAa,IAAI,CAACE,EAAQ1E,IACzBlD,EAAA,IAAC,OAAgB,UAAU,kCACzB,SAACD,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAW,mBACT6H,EAAO,OAAS,eAAiB,uBAAyB,EAC5D,GAEA,SAAA,CAAA5H,EAAA,IAAC,MAAA,CACC,IAAK4H,EAAO,MACZ,MAAO,IACP,OAAQ,IACR,UAAU,6CACV,oBAAkB,OAClB,IAAK,YAAYA,EAAO,IAAI,GAC5B,MAAO,CAAE,UAAW,QAAS,MAAO,OAAQ,OAAQ,MAAO,CAAA,CAC7D,EACC5H,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,oBACZ,SAAO4H,EAAA,QAAQ,IAAI,CAACC,EAAQC,IAC3B/H,EAAA,KAAC,IAAA,CAEC,KAAM8H,EAAO,IACb,OAAO,SACP,IAAI,sBAEJ,SAAA,CAAA7H,EAAA,IAAC,MAAI,CAAA,UAAU,kBAAmB,SAAA6H,EAAO,KAAK,EAC9C7H,MAAC,KAAE,UAAW,MAAM6H,EAAO,KAAK,YAAa,CAAA,EAAI,CAAA,CAAA,CAAA,EAN5CC,CAQR,CAAA,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACA/H,EAAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,iBAAkB,SAAA4H,EAAO,KAAK,EAC5C5H,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAkB,WAAO,IAAK,CAAA,CAAA,CAC/C,CAAA,CAAA,EACF,CAAA,EApCQkD,CAqCV,CACD,CAEH,CAAA,CAAA,EACF,CAEJ,CC5DA,MAAM6E,EAAU,CACd,CAAE,KAAM,YAAa,SAAU,KAAM,EACrC,CAAE,KAAM,WAAY,SAAU,UAAW,EACzC,CAAE,KAAM,SAAU,SAAU,QAAS,EACrC,CAAE,KAAM,cAAe,SAAU,aAAc,CACjD,EACA,SAAwBC,IAAY,CAClC,KAAM,CAAC3F,EAAiBC,CAAkB,EAAIC,EAAAA,SAAS,KAAK,EACtD,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAAS0F,CAAW,EACpDxH,OAAAA,EAAAA,UAAU,IAAM,CACV4B,GAAmB,MACrBI,EAAYwF,CAAW,EAEvBxF,EACE,CAAC,GAAGwF,CAAW,EAAE,OAAQlG,GACvBA,EAAI,WAAW,SAASM,CAAe,CAAA,CAE3C,CACF,EACC,CAACA,CAAe,CAAC,EAGhBtC,EAAA,KAAA+B,WAAA,CAAA,SAAA,CAAC9B,EAAA,IAAA,MAAA,CAAI,UAAU,YAEb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,sDACZ,SAAQ+H,EAAA,IAAI,CAAChG,EAAKmG,IACjBlI,EAAA,IAAC,IAAA,CACC,QAAS,IAAMsC,EAAmBP,EAAI,QAAQ,EAE9C,UAAW,UAAUM,GAAmBN,EAAI,SAAW,SAAW,EAChE,GAED,SAAIA,EAAA,IAAA,EAJAmG,CAAA,CAMR,EACH,CAEF,CAAA,EACAlI,EAAAA,IAAC,MAAI,CAAA,UAAU,oBAEb,SAAAA,EAAA,IAAC,KAAA,CACC,UAAU,sDACV,GAAG,YAEH,gBAACiD,EAEE,CAAA,SAAA,CAAST,EAAA,IAAI,CAACM,EAAMI,IACnBlD,EAAA,IAAC,KAAA,CAEC,UAAW,iBAAiB8C,EAAK,WAAW,KAAK,GAAG,CAAC,GAEpD,SAAAA,EAAK,OAAS,WACb9C,EAAA,IAACmD,EAAA,CACC,SAAUL,EAAK,SACf,UAAWA,EAAK,SAChB,MAAO,IACP,OAAQ,IAEP,SAAC,CAAA,CAAE,IAAAqF,EAAK,KAAA/E,CACP,IAAArD,EAAA,KAAC,IAAA,CACC,QAASqD,EACT,UAAU,+BAEV,SAAA,CAACrD,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,CAAA,EAE5CA,EAAA,IAAC,MAAA,CACC,IAAK8C,EAAK,SACV,IAAAqF,EACA,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACApI,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAA8C,EAAK,MAAM,EACtC9C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,EAIJD,EAAA,KAACsD,EAAA,CACC,GAAI,qBAAqBP,EAAK,EAAE,GAChC,UAAU,gBAEV,SAAA,CAAC/C,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,aAAc,CAAA,EAC7BA,EAAA,IAAC,MAAA,CACC,IAAK8C,EAAK,SACV,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACA/C,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAA8C,EAAK,MAAM,EACtC9C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,EAnDGkD,CAAA,CAsDR,EAAG,GAAA,CACN,CAAA,CAAA,CAAA,CAIJ,CAAA,CAAA,EACF,CAEJ"}