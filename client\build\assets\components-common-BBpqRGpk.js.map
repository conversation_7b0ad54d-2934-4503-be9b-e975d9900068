{"version": 3, "file": "components-common-BBpqRGpk.js", "sources": ["../../src/i18n/index.js", "../../src/components/common/GDPRConsent.jsx", "../../src/utils/analytics.js", "../../src/components/common/ScrollTopBehaviour.jsx", "../../src/components/common/LanguageSelector.jsx", "../../src/components/common/AnimatedText.jsx", "../../src/components/common/ParallaxContainer.jsx", "../../src/components/common/SEO.jsx", "../../src/components/common/ErrorBoundary.jsx", "../../src/components/common/MetaComponent.jsx", "../../src/data/portfolio.js", "../../src/data/categories.js", "../../src/data/tags.js", "../../src/data/archeve.js", "../../src/components/common/Pagination.jsx", "../../src/components/common/MultilingualSEO.jsx", "../../src/components/portfolio/RelatedProjects.jsx", "../../src/components/blog/Comments.jsx", "../../src/components/blog/commentForm/Form.jsx", "../../src/data/blogs.js", "../../src/data/comments.js", "../../src/components/blog/widgets/Widget1.jsx", "../../src/components/common/Map.jsx", "../../src/components/admin/AdminLayout.jsx"], "sourcesContent": ["import i18n from \"i18next\";\nimport { initReactI18next } from \"react-i18next\";\nimport LanguageDetector from \"i18next-browser-languagedetector\";\nimport Backend from \"i18next-http-backend\";\n\n// Language configuration\nconst languages = {\n  en: { name: \"English\", flag: \"🇬🇧\" },\n  et: { name: \"<PERSON><PERSON><PERSON>\", flag: \"🇪🇪\" },\n  fi: { name: \"<PERSON><PERSON>\", flag: \"🇫🇮\" },\n  de: { name: \"<PERSON><PERSON><PERSON>\", flag: \"🇩🇪\" },\n  sv: { name: \"<PERSON><PERSON>\", flag: \"🇸🇪\" },\n};\n\n// Initialize i18next\ni18n\n  .use(Backend) // Load translations from files\n  .use(LanguageDetector) // Detect user language\n  .use(initReactI18next) // Pass i18n instance to react-i18next\n  .init({\n    // Language settings\n    lng: \"et\", // Default language (Estonian as requested)\n    fallbackLng: \"en\", // Fallback language\n    supportedLngs: Object.keys(languages),\n\n    // Namespace settings\n    ns: [\"translation\"],\n    defaultNS: \"translation\",\n\n    // Backend settings for loading translations\n    backend: {\n      loadPath: \"/locales/{{lng}}/{{ns}}.json\",\n    },\n\n    // Language detection settings\n    detection: {\n      // Order of language detection methods\n      order: [\n        \"localStorage\", // Check localStorage first\n        \"navigator\", // Then browser language\n        \"htmlTag\", // Then HTML lang attribute\n        \"path\", // Then URL path\n        \"subdomain\", // Finally subdomain\n      ],\n\n      // Cache user language preference\n      caches: [\"localStorage\"],\n\n      // localStorage key\n      lookupLocalStorage: \"i18nextLng\",\n\n      // Don't lookup from path by default (we'll handle this manually)\n      lookupFromPathIndex: 0,\n\n      // Check all fallbacks\n      checkWhitelist: true,\n    },\n\n    // Interpolation settings\n    interpolation: {\n      escapeValue: false, // React already escapes values\n      formatSeparator: \",\",\n      format: (value, format, lng) => {\n        if (format === \"uppercase\") return value.toUpperCase();\n        if (format === \"lowercase\") return value.toLowerCase();\n        if (format === \"capitalize\")\n          return value.charAt(0).toUpperCase() + value.slice(1);\n        return value;\n      },\n    },\n\n    // React settings\n    react: {\n      useSuspense: false, // Disable suspense for SSR compatibility\n      bindI18n: \"languageChanged\",\n      bindI18nStore: \"\",\n      transEmptyNodeValue: \"\",\n      transSupportBasicHtmlNodes: true,\n      transKeepBasicHtmlNodesFor: [\"br\", \"strong\", \"i\", \"em\", \"span\"],\n    },\n\n    // Debug settings (disable in production)\n    debug: process.env.NODE_ENV === \"development\",\n\n    // Load settings\n    load: \"languageOnly\", // Load only language codes (en, not en-US)\n    preload: Object.keys(languages), // Preload all supported languages\n\n    // Cleanup settings\n    cleanCode: true,\n\n    // Key separator (use dots for nested keys)\n    keySeparator: \".\",\n    nsSeparator: \":\",\n\n    // Pluralization\n    pluralSeparator: \"_\",\n    contextSeparator: \"_\",\n\n    // Return objects for missing keys\n    returnObjects: false,\n    returnEmptyString: false,\n    returnNull: false,\n\n    // Join arrays\n    joinArrays: false,\n\n    // Post processing\n    postProcess: false,\n\n    // Save missing keys\n    saveMissing: process.env.NODE_ENV === \"development\",\n    saveMissingTo: \"current\",\n\n    // Missing key handler\n    missingKeyHandler: (lng, ns, key, fallbackValue) => {\n      if (process.env.NODE_ENV === \"development\") {\n        console.warn(`Missing translation key: ${key} for language: ${lng}`);\n      }\n    },\n\n    // Update missing keys\n    updateMissing: false,\n\n    // Ignore JSON structure\n    ignoreJSONStructure: true,\n  });\n\n// Export language configuration\nexport { languages };\n\n// Export i18n instance\nexport default i18n;\n", "import React, { useState, useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { useTranslation } from \"react-i18next\";\n\nconst GDPRConsent = () => {\n  const { t } = useTranslation();\n  const [showBanner, setShowBanner] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n\n  useEffect(() => {\n    // Check if user has already given consent\n    const consent = localStorage.getItem(\"gdpr-consent\");\n    if (!consent) {\n      setShowBanner(true);\n    } else {\n      // If consent exists, initialize Google Analytics\n      initializeGoogleAnalytics(JSON.parse(consent));\n    }\n  }, []);\n\n  const initializeGoogleAnalytics = (consentSettings) => {\n    // Initialize Google Analytics with consent settings\n    if (typeof window !== \"undefined\" && window.gtag) {\n      window.gtag(\"consent\", \"update\", {\n        analytics_storage: consentSettings.analytics ? \"granted\" : \"denied\",\n        ad_storage: consentSettings.marketing ? \"granted\" : \"denied\",\n        ad_user_data: consentSettings.marketing ? \"granted\" : \"denied\",\n        ad_personalization: consentSettings.marketing ? \"granted\" : \"denied\",\n      });\n    }\n  };\n\n  const handleAcceptAll = () => {\n    const consent = {\n      necessary: true,\n      analytics: true,\n      marketing: true,\n      timestamp: new Date().toISOString(),\n    };\n\n    localStorage.setItem(\"gdpr-consent\", JSON.stringify(consent));\n    initializeGoogleAnalytics(consent);\n    setShowBanner(false);\n\n    // Track consent acceptance\n    if (typeof window !== \"undefined\" && window.gtag) {\n      window.gtag(\"event\", \"consent_granted\", {\n        event_category: \"GDPR\",\n        event_label: \"Accept All\",\n      });\n    }\n  };\n\n  const handleRejectAll = () => {\n    const consent = {\n      necessary: true,\n      analytics: false,\n      marketing: false,\n      timestamp: new Date().toISOString(),\n    };\n\n    localStorage.setItem(\"gdpr-consent\", JSON.stringify(consent));\n    initializeGoogleAnalytics(consent);\n    setShowBanner(false);\n\n    // Track consent rejection\n    if (typeof window !== \"undefined\" && window.gtag) {\n      window.gtag(\"event\", \"consent_denied\", {\n        event_category: \"GDPR\",\n        event_label: \"Reject All\",\n      });\n    }\n  };\n\n  const handleCustomize = (settings) => {\n    const consent = {\n      necessary: true,\n      analytics: settings.analytics,\n      marketing: settings.marketing,\n      timestamp: new Date().toISOString(),\n    };\n\n    localStorage.setItem(\"gdpr-consent\", JSON.stringify(consent));\n    initializeGoogleAnalytics(consent);\n    setShowBanner(false);\n    setShowDetails(false);\n\n    // Track custom consent\n    if (typeof window !== \"undefined\" && window.gtag) {\n      window.gtag(\"event\", \"consent_customized\", {\n        event_category: \"GDPR\",\n        event_label: `Analytics: ${settings.analytics}, Marketing: ${settings.marketing}`,\n      });\n    }\n  };\n\n  if (!showBanner) return null;\n\n  return (\n    <>\n      {/* Slim Bottom Banner */}\n      <div className=\"gdpr-banner\">\n        <div className=\"container\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-lg-8 col-md-7\">\n              <div className=\"gdpr-content\">\n                <h6 className=\"gdpr-title mb-2\">{t(\"gdpr.title\")}</h6>\n                <p className=\"gdpr-description mb-0\">{t(\"gdpr.description\")}</p>\n              </div>\n            </div>\n            <div className=\"col-lg-4 col-md-5\">\n              <div className=\"gdpr-buttons\">\n                <button\n                  className=\"btn btn-mod btn-small btn-w btn-circle gdpr-btn-accept\"\n                  onClick={handleAcceptAll}\n                  data-btn-animate=\"y\"\n                >\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {t(\"gdpr.acceptAll\")}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {t(\"gdpr.acceptAll\")}\n                    </span>\n                  </span>\n                </button>\n                <button\n                  className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                  onClick={handleRejectAll}\n                  data-btn-animate=\"y\"\n                >\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {t(\"gdpr.rejectAll\")}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {t(\"gdpr.rejectAll\")}\n                    </span>\n                  </span>\n                </button>\n                <button\n                  className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                  onClick={() => setShowDetails(true)}\n                  data-btn-animate=\"y\"\n                >\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {t(\"gdpr.customize\")}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {t(\"gdpr.customize\")}\n                    </span>\n                  </span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detailed Settings Modal */}\n      {showDetails && (\n        <GDPRDetailsModal\n          onClose={() => setShowDetails(false)}\n          onSave={handleCustomize}\n          t={t}\n        />\n      )}\n    </>\n  );\n};\n\nconst GDPRDetailsModal = ({ onClose, onSave, t }) => {\n  const [settings, setSettings] = useState({\n    analytics: false,\n    marketing: false,\n  });\n\n  const handleSave = () => {\n    onSave(settings);\n  };\n\n  return (\n    <div className=\"gdpr-modal\">\n      <div className=\"gdpr-modal-content\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-lg-6 offset-lg-3\">\n              <div className=\"gdpr-modal-inner\">\n                <div className=\"gdpr-modal-header mb-30\">\n                  <div className=\"d-flex justify-content-between align-items-center\">\n                    <h5 className=\"gdpr-modal-title mb-0\">\n                      {t(\"gdpr.customizeTitle\")}\n                    </h5>\n                    <button className=\"gdpr-close\" onClick={onClose}>\n                      <i className=\"mi-close size-18\"></i>\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"gdpr-modal-body\">\n                  <div className=\"gdpr-category mb-25\">\n                    <div className=\"gdpr-category-header mb-10\">\n                      <div className=\"d-flex justify-content-between align-items-center\">\n                        <h6 className=\"gdpr-category-title mb-0 mr-2\">\n                          {t(\"gdpr.necessary\")}\n                        </h6>\n                        <span className=\"gdpr-required-badge\">\n                          {t(\"gdpr.required\")}\n                        </span>\n                      </div>\n                    </div>\n                    <p className=\"gdpr-category-desc\">\n                      {t(\"gdpr.necessaryDesc\")}\n                    </p>\n                  </div>\n\n                  <div className=\"gdpr-category mb-25\">\n                    <div className=\"gdpr-category-header mb-10\">\n                      <div className=\"d-flex justify-content-between align-items-center\">\n                        <h6 className=\"gdpr-category-title mb-0\">\n                          {t(\"gdpr.analytics\")}\n                        </h6>\n                        <label className=\"gdpr-switch\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.analytics}\n                            onChange={(e) =>\n                              setSettings((prev) => ({\n                                ...prev,\n                                analytics: e.target.checked,\n                              }))\n                            }\n                          />\n                          <span className=\"gdpr-slider\"></span>\n                        </label>\n                      </div>\n                    </div>\n                    <p className=\"gdpr-category-desc\">\n                      {t(\"gdpr.analyticsDesc\")}\n                    </p>\n                  </div>\n\n                  <div className=\"gdpr-category mb-30\">\n                    <div className=\"gdpr-category-header mb-10\">\n                      <div className=\"d-flex justify-content-between align-items-center\">\n                        <h6 className=\"gdpr-category-title mb-0\">\n                          {t(\"gdpr.marketing\")}\n                        </h6>\n                        <label className=\"gdpr-switch\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.marketing}\n                            onChange={(e) =>\n                              setSettings((prev) => ({\n                                ...prev,\n                                marketing: e.target.checked,\n                              }))\n                            }\n                          />\n                          <span className=\"gdpr-slider\"></span>\n                        </label>\n                      </div>\n                    </div>\n                    <p className=\"gdpr-category-desc\">\n                      {t(\"gdpr.marketingDesc\")}\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"gdpr-modal-footer\">\n                  <div className=\"d-flex justify-content-end gap-2\">\n                    <button\n                      className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                      onClick={onClose}\n                      data-btn-animate=\"y\"\n                    >\n                      <span className=\"btn-animate-y\">\n                        <span className=\"btn-animate-y-1\">\n                          {t(\"gdpr.cancel\")}\n                        </span>\n                        <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                          {t(\"gdpr.cancel\")}\n                        </span>\n                      </span>\n                    </button>\n                    <button\n                      className=\"btn btn-mod btn-small btn-w btn-circle\"\n                      onClick={handleSave}\n                      data-btn-animate=\"y\"\n                    >\n                      <span className=\"btn-animate-y\">\n                        <span className=\"btn-animate-y-1\">\n                          {t(\"gdpr.savePreferences\")}\n                        </span>\n                        <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                          {t(\"gdpr.savePreferences\")}\n                        </span>\n                      </span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nGDPRDetailsModal.propTypes = {\n  onClose: PropTypes.func.isRequired,\n  onSave: PropTypes.func.isRequired,\n  t: PropTypes.func.isRequired,\n};\n\nexport default GDPRConsent;\n", "// Google Analytics 4 utility functions\n\n// Track page views\nexport const trackPageView = (page_title, page_location) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'page_view', {\n      page_title: page_title,\n      page_location: page_location,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track button clicks\nexport const trackButtonClick = (button_name, button_location, additional_params = {}) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'click', {\n      event_category: 'Button',\n      event_label: button_name,\n      button_location: button_location,\n      ...additional_params,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track form submissions\nexport const trackFormSubmission = (form_name, form_location, success = true) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', success ? 'form_submit' : 'form_error', {\n      event_category: 'Form',\n      event_label: form_name,\n      form_location: form_location,\n      success: success,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track contact form submissions (conversion)\nexport const trackContactFormSubmission = (email, message_length) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    // Track as conversion\n    window.gtag('event', 'conversion', {\n      send_to: 'G-8NEGL4LL8Q',\n      event_category: 'Contact',\n      event_label: 'Contact Form Submission',\n      value: 1,\n      currency: 'EUR',\n      user_email: email,\n      message_length: message_length\n    });\n\n    // Also track as generate_lead\n    window.gtag('event', 'generate_lead', {\n      send_to: 'G-8NEGL4LL8Q',\n      event_category: 'Lead Generation',\n      event_label: 'Contact Form',\n      value: 1,\n      currency: 'EUR'\n    });\n  }\n};\n\n// Track language changes\nexport const trackLanguageChange = (from_language, to_language) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'language_change', {\n      event_category: 'User Interaction',\n      event_label: `${from_language} to ${to_language}`,\n      from_language: from_language,\n      to_language: to_language,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track navigation menu interactions\nexport const trackNavigation = (menu_item, menu_location) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'navigation_click', {\n      event_category: 'Navigation',\n      event_label: menu_item,\n      menu_location: menu_location,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track scroll depth\nexport const trackScrollDepth = (scroll_percentage, page_location) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'scroll', {\n      event_category: 'User Engagement',\n      event_label: `${scroll_percentage}% scrolled`,\n      scroll_percentage: scroll_percentage,\n      page_location: page_location,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track time on page\nexport const trackTimeOnPage = (time_seconds, page_location) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'timing_complete', {\n      name: 'page_view_time',\n      value: time_seconds,\n      event_category: 'User Engagement',\n      event_label: page_location,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track file downloads\nexport const trackFileDownload = (file_name, file_type, download_location) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'file_download', {\n      event_category: 'Downloads',\n      event_label: file_name,\n      file_type: file_type,\n      download_location: download_location,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track external link clicks\nexport const trackExternalLink = (link_url, link_text, link_location) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'click', {\n      event_category: 'External Link',\n      event_label: link_text,\n      link_url: link_url,\n      link_location: link_location,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track search queries (if you have search functionality)\nexport const trackSearch = (search_term, search_results_count) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'search', {\n      search_term: search_term,\n      event_category: 'Search',\n      event_label: search_term,\n      search_results: search_results_count,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Track video interactions\nexport const trackVideoInteraction = (video_title, action, video_progress = null) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    const eventData = {\n      event_category: 'Video',\n      event_label: video_title,\n      video_action: action,\n      send_to: 'G-8NEGL4LL8Q'\n    };\n\n    if (video_progress !== null) {\n      eventData.video_progress = video_progress;\n    }\n\n    window.gtag('event', `video_${action}`, eventData);\n  }\n};\n\n// Track custom events\nexport const trackCustomEvent = (event_name, event_category, event_label, value = null, additional_params = {}) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    const eventData = {\n      event_category: event_category,\n      event_label: event_label,\n      send_to: 'G-8NEGL4LL8Q',\n      ...additional_params\n    };\n\n    if (value !== null) {\n      eventData.value = value;\n    }\n\n    window.gtag('event', event_name, eventData);\n  }\n};\n\n// Track user engagement milestones\nexport const trackEngagementMilestone = (milestone_name, page_location, additional_data = {}) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'engagement_milestone', {\n      event_category: 'User Engagement',\n      event_label: milestone_name,\n      page_location: page_location,\n      ...additional_data,\n      send_to: 'G-8NEGL4LL8Q'\n    });\n  }\n};\n\n// Debug function to check if analytics is working\nexport const debugAnalytics = () => {\n  if (typeof window !== 'undefined') {\n    console.log('Google Analytics Debug Info:');\n    console.log('gtag available:', typeof window.gtag !== 'undefined');\n    console.log('dataLayer:', window.dataLayer);\n    \n    if (window.gtag) {\n      // Test event\n      window.gtag('event', 'debug_test', {\n        event_category: 'Debug',\n        event_label: 'Analytics Test',\n        send_to: 'G-8NEGL4LL8Q'\n      });\n      console.log('Test event sent');\n    }\n  }\n};\n", "import React from \"react\";\nimport { useEffect } from \"react\";\nimport { useLocation } from \"react-router-dom\";\n\nexport default function ScrollTopBehaviour() {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n\n  return <></>;\n}\n", "import React, { useState, useRef, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { languages } from \"@/i18n\";\n\nexport default function LanguageSelector() {\n  const { i18n } = useTranslation();\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef(null);\n  const [isMobile, setIsMobile] = useState(\n    typeof window !== \"undefined\" ? window.innerWidth <= 1024 : false\n  );\n\n  // Update isMobile state on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 1024);\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    }\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  const toggleDropdown = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const handleLanguageChange = (langCode) => {\n    i18n.changeLanguage(langCode);\n    setIsOpen(false);\n\n    // Track language change in analytics\n    if (typeof window !== \"undefined\" && window.gtag) {\n      window.gtag(\"event\", \"language_change\", {\n        event_category: \"User Interaction\",\n        event_label: `${i18n.language} to ${langCode}`,\n        from_language: i18n.language,\n        to_language: langCode,\n        send_to: \"G-8NEGL4LL8Q\",\n      });\n    }\n  };\n\n  const currentLanguage = i18n.language || \"et\";\n\n  // Mobile view - show languages side by side\n  if (isMobile) {\n    return (\n      <div className=\"language-selector-mobile d-flex align-items-center\">\n        {Object.entries(languages).map(([langCode, langData]) => (\n          <button\n            key={langCode}\n            className={`btn btn-link p-0 mx-2 ${\n              langCode === currentLanguage\n                ? \"text-white\"\n                : \"text-muted opacity-50\"\n            }`}\n            onClick={() => handleLanguageChange(langCode)}\n            aria-label={`Switch to ${langData.name}`}\n          >\n            <span className=\"language-code text-uppercase font-weight-bold\">\n              {langCode}\n            </span>\n          </button>\n        ))}\n      </div>\n    );\n  }\n\n  // Desktop view - dropdown\n  return (\n    <div className=\"language-selector position-relative\" ref={dropdownRef}>\n      <button\n        className=\"language-toggle btn btn-link p-0 d-flex align-items-center\"\n        onClick={toggleDropdown}\n        aria-expanded={isOpen}\n        aria-haspopup=\"true\"\n      >\n        <span className=\"language-code text-uppercase\">{currentLanguage}</span>\n      </button>\n\n      {isOpen && (\n        <div className=\"language-dropdown position-absolute bg-dark-1 py-2 rounded shadow-sm\">\n          {Object.entries(languages).map(([langCode, langData]) => (\n            <button\n              key={langCode}\n              className={`dropdown-item d-flex align-items-center px-3 py-1 ${\n                langCode === currentLanguage ? \"active\" : \"\"\n              }`}\n              onClick={() => handleLanguageChange(langCode)}\n            >\n              {/* <span className=\"me-2\">{langData.flag}</span> */}\n              <span className=\"language-name\">{langData.name}</span>\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nexport default function AnimatedText({\n  text = \"Grow your business with a new website.\",\n}) {\n  return (\n    <>\n      <span\n        className=\"wow charsAnimIn words chars splitting\"\n        data-splitting=\"chars\"\n        aria-hidden=\"true\"\n        style={{\n          \"--word-total\": text.split(\" \").length,\n          \"--char-total\": text.split(\"\").length,\n          visibility: \"visible\",\n        }}\n      >\n        {text\n          .trim()\n          .split(\" \")\n          .map((elm, i) => (\n            <React.Fragment key={i}>\n              <span\n                className=\"word\"\n                data-word=\"Grow\"\n                style={{ \"--word-index\": i }}\n              >\n                {elm.split(\"\").map((elm2, i2) => (\n                  <span\n                    key={i2}\n                    className=\"char\"\n                    data-char=\"G\"\n                    style={{ \"--char-index\": i + i2 }}\n                  >\n                    {elm2}\n                  </span>\n                ))}\n              </span>\n              <span className=\"whitespace\"> </span>\n            </React.Fragment>\n          ))}\n      </span>\n    </>\n  );\n}\n\nAnimatedText.propTypes = {\n  text: PropTypes.string,\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { jarallax } from \"jarallax\";\nimport { useEffect } from \"react\";\n\nexport default function ParallaxContainer(props) {\n  useEffect(() => {\n    jarallax(document.querySelectorAll(\".parallax-5\"), {\n      speed: 0.5,\n    });\n  }, []);\n  return (\n    <div\n      //   ref={parallax.ref}\n      {...props}\n    >\n      {props.children}\n    </div>\n  );\n}\n\nParallaxContainer.propTypes = {\n  children: PropTypes.node,\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Helmet } from \"react-helmet-async\";\n\n/**\n * Enhanced SEO component for managing all meta tags with advanced social media support\n *\n * @param {Object} props - Component props\n * @param {string} props.title - Page title\n * @param {string} props.description - Page description\n * @param {string} props.canonical - Canonical URL\n * @param {string} props.image - OG image URL\n * @param {string} props.type - OG type (website, article, etc.)\n * @param {Object} props.schema - JSON-LD structured data\n * @param {string} props.imageAlt - Alt text for the OG image\n * @param {string} props.imageWidth - Width of the OG image\n * @param {string} props.imageHeight - Height of the OG image\n * @param {string} props.twitterHandle - Twitter handle\n * @param {string} props.publishedAt - Article published date (ISO format)\n * @param {string} props.modifiedAt - Article modified date (ISO format)\n * @param {string} props.author - Article author\n * @param {string[]} props.keywords - Keywords for SEO\n * @param {string} props.locale - Content locale\n */\nconst SEO = ({\n  title = \"Ultimation Studio - Business Management System\",\n  description = \"Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.\",\n  canonical = \"https://devskills.ee\",\n  image = \"https://devskills.ee/og-image.jpg\",\n  imageAlt = \"Ultimation Studio\",\n  imageWidth = \"1200\",\n  imageHeight = \"630\",\n  type = \"website\",\n  schema = null,\n  twitterHandle = \"@DevSkillsEE\",\n  publishedAt = \"\",\n  modifiedAt = \"\",\n  author = \"Ultimation Studio\",\n  keywords = [\n    \"business management system\",\n    \"BMS\",\n    \"productivity\",\n    \"operations\",\n  ],\n  locale = \"en_US\",\n}) => {\n  // Format the title\n  const formattedTitle = `${title} | Ultimation Studio`;\n\n  return (\n    <Helmet>\n      {/* Basic meta tags */}\n      <title>{formattedTitle}</title>\n      <meta name=\"description\" content={description} />\n      <link rel=\"canonical\" href={canonical} />\n      <meta name=\"keywords\" content={keywords.join(\", \")} />\n\n      {/* Open Graph meta tags for social sharing */}\n      <meta property=\"og:title\" content={formattedTitle} />\n      <meta property=\"og:description\" content={description} />\n      <meta property=\"og:type\" content={type} />\n      <meta property=\"og:url\" content={canonical} />\n      <meta property=\"og:image\" content={image} />\n      <meta property=\"og:image:alt\" content={imageAlt} />\n      <meta property=\"og:image:width\" content={imageWidth} />\n      <meta property=\"og:image:height\" content={imageHeight} />\n      <meta property=\"og:site_name\" content=\"Ultimation Studio\" />\n      <meta property=\"og:locale\" content={locale} />\n\n      {/* Article specific meta tags */}\n      {type === \"article\" && publishedAt && (\n        <meta property=\"article:published_time\" content={publishedAt} />\n      )}\n      {type === \"article\" && modifiedAt && (\n        <meta property=\"article:modified_time\" content={modifiedAt} />\n      )}\n      {type === \"article\" && author && (\n        <meta property=\"article:author\" content={author} />\n      )}\n\n      {/* Twitter Card meta tags */}\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\n      <meta name=\"twitter:site\" content={twitterHandle} />\n      <meta name=\"twitter:creator\" content={twitterHandle} />\n      <meta name=\"twitter:title\" content={formattedTitle} />\n      <meta name=\"twitter:description\" content={description} />\n      <meta name=\"twitter:image\" content={image} />\n      <meta name=\"twitter:image:alt\" content={imageAlt} />\n\n      {/* Additional meta tags for SEO */}\n      <meta name=\"robots\" content=\"index, follow, max-image-preview:large\" />\n      <meta name=\"googlebot\" content=\"index, follow\" />\n      <meta httpEquiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n      <meta name=\"language\" content=\"English\" />\n\n      {/* Mobile specific meta tags */}\n      <meta\n        name=\"viewport\"\n        content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\"\n      />\n      <meta name=\"theme-color\" content=\"#06B6D4\" />\n      <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n      <meta\n        name=\"apple-mobile-web-app-status-bar-style\"\n        content=\"black-translucent\"\n      />\n\n      {/* JSON-LD structured data */}\n      {schema && Array.isArray(schema) ? (\n        // Handle array of schema objects\n        schema.map((schemaItem, index) => (\n          <script key={index} type=\"application/ld+json\">\n            {JSON.stringify(schemaItem)}\n          </script>\n        ))\n      ) : schema ? (\n        // Handle single schema object\n        <script type=\"application/ld+json\">{JSON.stringify(schema)}</script>\n      ) : null}\n    </Helmet>\n  );\n};\n\nSEO.propTypes = {\n  title: PropTypes.string,\n  description: PropTypes.string,\n  canonical: PropTypes.string,\n  image: PropTypes.string,\n  imageAlt: PropTypes.string,\n  imageWidth: PropTypes.string,\n  imageHeight: PropTypes.string,\n  type: PropTypes.string,\n  schema: PropTypes.oneOfType([\n    PropTypes.object,\n    PropTypes.arrayOf(PropTypes.object),\n  ]),\n  twitterHandle: PropTypes.string,\n  publishedAt: PropTypes.string,\n  modifiedAt: PropTypes.string,\n  author: PropTypes.string,\n  keywords: PropTypes.arrayOf(PropTypes.string),\n  locale: PropTypes.string,\n};\n\nexport default SEO;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error for debugging\n    console.error(\"Error caught by boundary:\", error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Render fallback UI that doesn't crash\n      return (\n        <div\n          style={{\n            padding: \"20px\",\n            textAlign: \"center\",\n            color: \"#fff\",\n            backgroundColor: \"#1a1a1a\",\n            minHeight: \"100vh\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            flexDirection: \"column\",\n          }}\n        >\n          <h2>Something went wrong</h2>\n          <p>The page is recovering...</p>\n          <button\n            onClick={() => window.location.reload()}\n            style={{\n              padding: \"10px 20px\",\n              backgroundColor: \"#06B6D4\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              marginTop: \"20px\",\n            }}\n          >\n            Reload Page\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nErrorBoundary.propTypes = {\n  children: PropTypes.node.isRequired,\n};\n\nexport default ErrorBoundary;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Helmet, Helmet<PERSON>rovider } from \"react-helmet-async\";\n\nexport default function MetaComponent({ meta }) {\n  return (\n    <HelmetProvider>\n      <Helmet>\n        <title>{meta?.title}</title>\n        <meta name=\"description\" content={meta?.description} />\n      </Helmet>\n    </HelmetProvider>\n  );\n}\n\nMetaComponent.propTypes = {\n  meta: PropTypes.shape({\n    title: PropTypes.string,\n    description: PropTypes.string,\n  }),\n};\n", "export const portfolioItems = [\n  {\n    id: 1,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/1.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Rise of Design\",\n    descr: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 2,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/2.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Amplitude\",\n    descr: \"UI/UX Design, Development\",\n  },\n  {\n    id: 3,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/3.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Medium Scene\",\n    descr: \"Branding, Design\",\n  },\n  {\n    id: 4,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/4.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Rise of Design\",\n    descr: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 5,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/5.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Amplitude\",\n    descr: \"UI/UX Design, Development\",\n  },\n  {\n    id: 6,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/6.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Medium Scene\",\n    descr: \"Branding, Design\",\n  },\n];\n\nexport const portfolios1 = [\n  {\n    id: 7,\n    className: \"work-item mt-90 mt-md-0 mix development\",\n    href: \"/assets/images/portfolio/masonry/full-project-1.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-1.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 8,\n    className: \"work-item mix branding design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-2.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n  },\n  {\n    id: 9,\n    className: \"work-item mt-90 mt-md-0 mix branding\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-3.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n  },\n  {\n    id: 10,\n    className: \"work-item mix design development\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-4.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 11,\n    className: \"work-item mix design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-5.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n  {\n    id: 12,\n    className: \"work-item mix design branding\",\n    href: \"/assets/images/portfolio/masonry/full-project-6.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 13,\n    className: \"work-item mix mix design\",\n    href: \"/assets/images/portfolio/masonry/full-project-7.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 14,\n    className: \"work-item mix design development\",\n    href: \"/assets/images/portfolio/masonry/full-project-8.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n];\n\nexport const portfolios2 = [\n  {\n    id: 15,\n    imageUrl: \"/assets/images/demo-bold/portfolio/1.jpg\",\n    title: \"Medium Scene\",\n    description:\n      \"Lorem ipsum dolor siter amet consectetur adipiscing elit sed do eiusmod tempor incididunt labore dolore magna aliqua.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"development\"],\n  },\n  {\n    id: 16,\n    imageUrl: \"/assets/images/demo-bold/portfolio/2.jpg\",\n    title: \"Rise of Design\",\n    description:\n      \"Proin elementum ipsum vel mauris pellentesque accumsan. Nulla in erat ligula vivamus sed egestas elit, sit amet convallis metus.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"branding\"],\n  },\n  {\n    id: 17,\n    imageUrl: \"/assets/images/demo-bold/portfolio/3.jpg\",\n    title: \"Visual Stranger\",\n    description:\n      \"Suspendisse scelerisque convallis nibh. Maecenas bibendum porta mattis. Donec quis nibh porta dolor ultrices bibendum vel quis leo.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"design\", \"development\"],\n  },\n  {\n    id: 18,\n    imageUrl: \"/assets/images/demo-bold/portfolio/4.jpg\",\n    title: \"Amplitude\",\n    description:\n      \"Aliquam tempus nunc nec rutrum malesuada. Proin pulvinar augue quis pharetra vulputate. Sed lacinia convallis orci vitae condimentum.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"branding\", \"design\"],\n  },\n  {\n    id: 19,\n    imageUrl: \"/assets/images/demo-bold/portfolio/5.jpg\",\n    title: \"Super Awards\",\n    description:\n      \"Praesent est lacus, fringilla et justo vel, scelerisque aliquet elit. Mauris malesuada eleifend sapien irere semper a orci ac turpis luctus.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"design\", \"development\"],\n  },\n];\n\nexport const portfolios3 = [\n  {\n    id: 20,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/1.jpg\",\n    imgWidth: 700,\n    imgHeight: 848,\n    title: \"Medium Scene\",\n    description:\n      \"Take maximus ligula semper metus pellente mattis. Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet adipiscing elit.\",\n  },\n  {\n    id: 21,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/2.jpg\",\n    imgWidth: 848,\n    imgHeight: 700,\n    title: \"Rise of Design\",\n    description:\n      \"Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet, cetere adipiscing elit. Maximus ligula semper metus pellentesque mattis.\",\n  },\n  {\n    id: 22,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/3.jpg\",\n    imgWidth: 700,\n    imgHeight: 848,\n    title: \"Visual Stranger\",\n    description:\n      \"Curabitur iaculis accumsan augue, finibus mauris pretium eu. Duis placerat ex gravida nibh tristique porta nulla facilisi.\",\n  },\n  {\n    id: 23,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/4.jpg\",\n    imgWidth: 848,\n    imgHeight: 700,\n    title: \"Rise of Design\",\n    description:\n      \"Take maximus ligula semper metus pellente mattis. Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet adipiscing elit.\",\n  },\n  {\n    id: 24,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/5.jpg\",\n    imgWidth: 700,\n    imgHeight: 848,\n    title: \"Amplitude\",\n    description:\n      \"Posuere felis id arcu blandit sagittis. Eleifeni vestibulum purus, sit amet vulputate risusece fusce aliquet quam eget neque.\",\n  },\n];\n\nexport const portfolios4 = [\n  {\n    id: 25,\n    imageSrc: \"/assets/images/demo-corporate/portfolio/project-1.jpg\",\n    title: \"How Marketing Wire Support Increased Data Accuracy by 70%\",\n    number: \"70%\",\n    description: \"growth with Resonance\",\n  },\n  {\n    id: 26,\n    imageSrc: \"/assets/images/demo-corporate/portfolio/project-2.jpg\",\n    title:\n      \"How Surface Mobility Increased Sales 3X During the Latest Six Months\",\n    number: \"3x\",\n    description: \"sales increased with Resonance\",\n  },\n  {\n    id: 27,\n    imageSrc: \"/assets/images/demo-corporate/portfolio/project-3.jpg\",\n    title: \"How Gen Machine Uses Automations to Grow Their Subscriber Base\",\n    number: \"Zero\",\n    description: \"negative reviews with Resonance\",\n  },\n];\n\nexport const portfolios5 = [\n  {\n    id: 28,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/1.jpg\",\n    title: \"Medium Scene\",\n    type: \"Lightbox\",\n    categories: [\"development\"],\n  },\n  {\n    id: 29,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/2.jpg\",\n    title: \"Rise of Design\",\n    type: \"External Page\",\n    categories: [\"branding\", \"design\"],\n  },\n  {\n    id: 30,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/3.jpg\",\n    title: \"Visual Stranger\",\n    type: \"External Page\",\n    categories: [\"branding\"],\n  },\n  {\n    id: 31,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/4.jpg\",\n    title: \"Amplitude\",\n    type: \"External Page\",\n    categories: [\"design\", \"development\"],\n  },\n  {\n    id: 32,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/5.jpg\",\n    title: \"Super Awards\",\n    type: \"External Page\",\n    categories: [\"design\"],\n  },\n  {\n    id: 33,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/6.jpg\",\n    title: \"Design System\",\n    type: \"Lightbox\",\n    categories: [\"design\", \"branding\"],\n  },\n  {\n    id: 34,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/7.jpg\",\n    title: \"Rise of Design\",\n    type: \"External Page\",\n    categories: [\"branding\", \"design\"],\n  },\n  {\n    id: 35,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/8.jpg\",\n    title: \"Medium Scene\",\n    type: \"Lightbox\",\n    categories: [\"development\"],\n  },\n];\n\nexport const portfolios6 = [\n  {\n    id: 36,\n    categories: [\"development\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-1.jpg\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n    lightbox: true,\n    lightboxLink: \"/assets/images/demo-fancy/portfolio/project-1-large.jpg\",\n  },\n  {\n    id: 37,\n    categories: [\"branding\", \"design\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-2.jpg\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 38,\n    categories: [\"branding\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-3.jpg\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 39,\n    categories: [\"design\", \"development\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-4.jpg\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 40,\n    categories: [\"design\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-5.jpg\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 41,\n    categories: [\"design\", \"branding\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-6.jpg\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n    lightbox: true,\n    lightboxLink: \"/assets/images/demo-fancy/portfolio/project-6-large.jpg\",\n  },\n];\n\nexport const portfolios7 = [\n  {\n    id: 42,\n    categories: [\"development\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-1.jpg\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 43,\n    categories: [\"branding\", \"design\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-2.jpg\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 44,\n    categories: [\"branding\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-3.jpg\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 45,\n    categories: [\"design\", \"development\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-4.jpg\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 46,\n    categories: [\"design\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-5.jpg\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 47,\n    categories: [\"design\", \"branding\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-6.jpg\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n    dataWowDelay: \"1s\",\n  },\n];\n\nexport const portfolios8 = [\n  {\n    id: 48,\n    imageSrc: \"/assets/images/demo-modern/portfolio/1.jpg\",\n    title: \"Medium Scene\",\n    categories: \"Branding, Design\",\n    align: \"text-center\",\n  },\n  {\n    id: 49,\n    imageSrc: \"/assets/images/demo-modern/portfolio/2.jpg\",\n    title: \"The Rise of Design\",\n    categories: \"Branding, Design\",\n    align: \"text-end\",\n  },\n  {\n    id: 50,\n    imageSrc: \"/assets/images/demo-modern/portfolio/3.jpg\",\n    title: \"Visual Stranger\",\n    categories: \"Branding, Design, Development\",\n    align: \"text-start\",\n  },\n  {\n    id: 51,\n    imageSrc: \"/assets/images/demo-modern/portfolio/4.jpg\",\n    title: \"Amplitude Studios\",\n    categories: \"Branding, Design\",\n    align: \"text-end\",\n  },\n  {\n    id: 52,\n    imageSrc: \"/assets/images/demo-modern/portfolio/5.jpg\",\n    title: \"Super Awards\",\n    categories: \"Design, Development\",\n    align: \"text-center\",\n  },\n];\n\nexport const portfolios9 = [\n  {\n    id: 53,\n    className: \"work-item\",\n    categories: [\"mix\", \"development\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-1.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n    isLightbox: true,\n  },\n  {\n    id: 54,\n    className: \"work-item\",\n    categories: [\"mt-80\", \"mt-sm-0\", \"mix\", \"branding\", \"design\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-2.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 55,\n    className: \"work-item\",\n    categories: [\"mix\", \"branding\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-3.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 56,\n    className: \"work-item\",\n    categories: [\"mix\", \"design\", \"development\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-4.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 57,\n    className: \"work-item\",\n    categories: [\"mix\", \"design\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-5.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 58,\n    className: \"work-item\",\n    categories: [\"mix\", \"design\", \"branding\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-6.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n    isLightbox: true,\n  },\n];\nexport const portfolios10 = [\n  {\n    id: 59,\n    imgSrc: \"/assets/images/demo-strong/portfolio/1.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Rise of Design\",\n    description: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 60,\n    imgSrc: \"/assets/images/demo-strong/portfolio/2.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Amplitude\",\n    description: \"UI/UX Design, Development\",\n  },\n  {\n    id: 61,\n    imgSrc: \"/assets/images/demo-strong/portfolio/3.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Medium Scene\",\n    description: \"Branding, Design\",\n  },\n  {\n    id: 62,\n    imgSrc: \"/assets/images/demo-strong/portfolio/4.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Visual Stranger\",\n    description: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 63,\n    imgSrc: \"/assets/images/demo-strong/portfolio/5.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Super Awards\",\n    description: \"UI/UX Design, Development\",\n  },\n  {\n    id: 64,\n    imgSrc: \"/assets/images/demo-strong/portfolio/6.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Design System\",\n    description: \"Branding, Design\",\n  },\n];\n\nexport const portfolios11 = [\n  {\n    id: 65,\n    title: \"How Marketing Wire Support Increased Data Accuracy by 70%\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-1.jpg\",\n    number: \"70%\",\n    description: \"growth with Resonance\",\n  },\n  {\n    id: 66,\n    title:\n      \"How Surface Mobility Increased Sales 3X During the Latest Six Months\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-2.jpg\",\n    number: \"3x\",\n    description: \"sales increased with Resonance\",\n  },\n  {\n    id: 67,\n    title: \"How Gen Machine Uses Automations to Grow Their Subscriber Base\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-3.jpg\",\n    number: \"Zero\",\n    description: \"negative reviews with Resonance\",\n  },\n  {\n    id: 68,\n    title:\n      \"How Surface Mobility Increased Sales 3X During the Latest Six Months\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-4.jpg\",\n    number: \"2x\",\n    description: \"sales increased with Resonance\",\n  },\n  {\n    id: 69,\n    title: \"How Gen Machine Uses Automations to Grow Their Subscriber Base\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-5.jpg\",\n    number: \"Zero\",\n    description: \"negative reviews with Resonance\",\n  },\n  {\n    id: 70,\n    title: \"How Marketing Wire Support Increased Data Accuracy by 70%\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-6.jpg\",\n    number: \"80%\",\n    description: \"growth with Resonance\",\n  },\n];\n\nexport const portfolios12 = [\n  {\n    id: 71,\n    className: \"work-item mix development\",\n    href: \"/assets/images/portfolio/masonry/full-project-1.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-1.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 72,\n    className: \"work-item mix branding design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-2.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n  },\n  {\n    id: 73,\n    className: \"work-item  mix branding\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-3.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n  },\n  {\n    id: 74,\n    className: \"work-item mix design development\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-4.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 75,\n    className: \"work-item mix design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-5.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n  {\n    id: 76,\n    className: \"work-item mix design branding\",\n    href: \"/assets/images/portfolio/masonry/full-project-6.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 77,\n    className: \"work-item mix mix design\",\n    href: \"/assets/images/portfolio/masonry/full-project-7.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-7.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 78,\n    className: \"work-item mix design development\",\n    href: \"/assets/images/portfolio/masonry/full-project-8.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-8.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n];\n\nexport const portfolios13 = [\n  {\n    id: 79,\n    type: \"lightbox\",\n    mix: \"development\",\n    href: \"/assets/images/portfolio/full-project-1.jpg\",\n    imgSrc: \"/assets/images/portfolio/projects-1.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Green Leaf\",\n    descr: \"Lightbox\",\n  },\n  {\n    id: 80,\n    type: \"external\",\n    mix: \"branding design\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-2.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Photo Lighting\",\n    descr: \"External Page\",\n  },\n  {\n    id: 81,\n    type: \"external\",\n    mix: \"branding\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-3.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Green Branch\",\n    descr: \"External Page\",\n  },\n  {\n    id: 82,\n    type: \"external\",\n    mix: \"design development\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-4.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"White Chair\",\n    descr: \"External Page\",\n  },\n  {\n    id: 83,\n    type: \"external\",\n    mix: \"design\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-5.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"White Table\",\n    descr: \"External Page\",\n  },\n  {\n    id: 84,\n    type: \"lightbox\",\n    mix: \"design branding\",\n    href: \"/assets/images/portfolio/full-project-6.jpg\",\n    imgSrc: \"/assets/images/portfolio/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"The Book\",\n    descr: \"Lightbox\",\n  },\n  {\n    id: 85,\n    type: \"external\",\n    mix: \"branding\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-7.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Green Branch\",\n    descr: \"External Page\",\n  },\n  {\n    id: 86,\n    type: \"external\",\n    mix: \"design development\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-8.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"White Chair\",\n    descr: \"External Page\",\n  },\n];\n\nexport const allPortfolios = [\n  ...portfolioItems,\n  ...portfolios1,\n  ...portfolios2,\n  ...portfolios3,\n  ...portfolios4,\n  ...portfolios5,\n  ...portfolios6,\n  ...portfolios7,\n  ...portfolios8,\n  ...portfolios9,\n  ...portfolios10,\n  ...portfolios11,\n  ...portfolios12,\n  ...portfolios13,\n];\n", "export const categories = [\n  { id: 1, name: \"Branding\", count: 7 },\n  { id: 2, name: \"Design\", count: 15 },\n  { id: 3, name: \"Development\", count: 3 },\n  { id: 4, name: \"<PERSON>\", count: 5 },\n  { id: 5, name: \"Other\", count: 1 },\n];\n", "export const tags = [\n  { id: 1, name: \"Design\" },\n  { id: 2, name: \"Portfolio\" },\n  { id: 3, name: \"Digital\" },\n  { id: 4, name: \"<PERSON><PERSON>\" },\n  { id: 5, name: \"<PERSON>\" },\n  { id: 6, name: \"Clean\" },\n  { id: 7, name: \"UI & UX\" },\n  { id: 8, name: \"<PERSON>\" },\n];\n", "export const archiveLinks = [\n  { id: 1, date: \"February 2021\" },\n  { id: 2, date: \"January 2021\" },\n  { id: 3, date: \"December 2020\" },\n];\n", "import React, { useState } from \"react\";\nimport PropTypes from \"prop-types\";\n\nexport default function Pagination({ className }) {\n  const [activePage, setActivePage] = useState(1); // Initialize active page\n\n  // Function to handle page change\n  const handlePageChange = (page) => {\n    setActivePage(page);\n  };\n\n  return (\n    <div\n      className={className ? className : \"pagination justify-content-center\"}\n    >\n      {/* Previous Page Button */}\n      <a\n        onClick={() => activePage > 1 && handlePageChange(activePage - 1)}\n        className={activePage === 1 ? \"disabled\" : \"\"}\n      >\n        <i className=\"mi-chevron-left\" />\n        <span className=\"visually-hidden\">Previous page</span>\n      </a>\n\n      {/* Page Number 1 */}\n      <a\n        onClick={() => handlePageChange(1)}\n        className={activePage === 1 ? \"active\" : \"\"}\n      >\n        1\n      </a>\n\n      {/* Page Number 2 */}\n      <a\n        onClick={() => handlePageChange(2)}\n        className={activePage === 2 ? \"active\" : \"\"}\n      >\n        2\n      </a>\n\n      {/* Page Number 3 */}\n      <a\n        onClick={() => handlePageChange(3)}\n        className={activePage === 3 ? \"active\" : \"\"}\n      >\n        3\n      </a>\n\n      {activePage > 4 && activePage < 8 && (\n        <span className=\"no-active\">...</span>\n      )}\n\n      {activePage > 3 && activePage < 8 && (\n        <a className={\"active\"}>{activePage}</a>\n      )}\n\n      {/* Ellipsis */}\n      <span className=\"no-active\">...</span>\n      {activePage == 8 && <a className={\"active\"}>{8}</a>}\n      {/* Page Number 9 */}\n      <a\n        onClick={() => handlePageChange(9)}\n        className={activePage === 9 ? \"active\" : \"\"}\n      >\n        9\n      </a>\n\n      {/* Next Page Button */}\n      <a\n        onClick={() => activePage < 9 && handlePageChange(activePage + 1)}\n        className={activePage === 9 ? \"disabled\" : \"\"}\n      >\n        <i className=\"mi-chevron-right\" />\n        <span className=\"visually-hidden\">Next page</span>\n      </a>\n    </div>\n  );\n}\n\nPagination.propTypes = {\n  className: PropTypes.string,\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Helmet } from \"react-helmet-async\";\nimport { useTranslation } from \"react-i18next\";\n\n/**\n * Multilingual SEO component with proper hreflang tags and language-specific meta data\n *\n * @param {Object} props - Component props\n * @param {string} props.title - Page title (will be translated if key provided)\n * @param {string} props.description - Page description (will be translated if key provided)\n * @param {string} props.slug - Page slug for generating language URLs\n * @param {string} props.type - Page type (website, article, etc.)\n * @param {Object} props.schema - JSON-LD structured data\n * @param {string[]} props.keywords - Keywords for SEO\n * @param {string} props.image - OG image URL\n * @param {Object} props.alternateUrls - Custom alternate URLs for languages\n */\nconst MultilingualSEO = ({\n  title,\n  description,\n  slug = \"\",\n  type = \"website\",\n  schema = null,\n  keywords = [],\n  image = \"https://devskills.ee/og-image.jpg\",\n  alternateUrls = null,\n}) => {\n  const { currentLanguage } = useTranslation();\n\n  // Generate language-specific URLs\n  const baseUrl = \"https://devskills.ee\";\n  const generateLanguageUrls = () => {\n    if (alternateUrls) {\n      return alternateUrls;\n    }\n\n    const urls = {\n      en: `${baseUrl}${slug ? `/${slug}` : \"\"}`,\n      et: `${baseUrl}/et${slug ? `/${slug}` : \"\"}`,\n      de: `${baseUrl}/de${slug ? `/${slug}` : \"\"}`,\n    };\n\n    return urls;\n  };\n\n  const languageUrls = generateLanguageUrls();\n  const currentUrl = languageUrls[currentLanguage];\n\n  // Language-specific meta data\n  const getLanguageSpecificData = () => {\n    const localeMap = {\n      en: \"en_US\",\n      et: \"et_EE\",\n      de: \"de_DE\",\n    };\n\n    const languageMap = {\n      en: \"English\",\n      et: \"Estonian\",\n      de: \"German\",\n    };\n\n    return {\n      locale: localeMap[currentLanguage] || \"en_US\",\n      language: languageMap[currentLanguage] || \"English\",\n    };\n  };\n\n  const { locale, language } = getLanguageSpecificData();\n\n  // Format the title with company name\n  const formattedTitle = `${title} | DevSkills`;\n\n  return (\n    <Helmet>\n      {/* Basic meta tags */}\n      <title>{formattedTitle}</title>\n      <meta name=\"description\" content={description} />\n      <link rel=\"canonical\" href={currentUrl} />\n      <meta name=\"keywords\" content={keywords.join(\", \")} />\n      <meta name=\"language\" content={language} />\n\n      {/* Hreflang tags for multilingual SEO */}\n      {Object.entries(languageUrls).map(([lang, url]) => (\n        <link key={lang} rel=\"alternate\" hrefLang={lang} href={url} />\n      ))}\n\n      {/* x-default for international targeting */}\n      <link rel=\"alternate\" hrefLang=\"x-default\" href={languageUrls.en} />\n\n      {/* Open Graph meta tags */}\n      <meta property=\"og:title\" content={formattedTitle} />\n      <meta property=\"og:description\" content={description} />\n      <meta property=\"og:type\" content={type} />\n      <meta property=\"og:url\" content={currentUrl} />\n      <meta property=\"og:image\" content={image} />\n      <meta property=\"og:image:alt\" content={title} />\n      <meta property=\"og:image:width\" content=\"1200\" />\n      <meta property=\"og:image:height\" content=\"630\" />\n      <meta property=\"og:site_name\" content=\"DevSkills\" />\n      <meta property=\"og:locale\" content={locale} />\n\n      {/* Alternate locales for Facebook */}\n      {Object.keys(languageUrls)\n        .filter((lang) => lang !== currentLanguage)\n        .map((lang) => {\n          const altLocale = {\n            en: \"en_US\",\n            et: \"et_EE\",\n            de: \"de_DE\",\n          }[lang];\n          return (\n            <meta\n              key={lang}\n              property=\"og:locale:alternate\"\n              content={altLocale}\n            />\n          );\n        })}\n\n      {/* Twitter Card meta tags */}\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\n      <meta name=\"twitter:site\" content=\"@DevSkillsEE\" />\n      <meta name=\"twitter:creator\" content=\"@DevSkillsEE\" />\n      <meta name=\"twitter:title\" content={formattedTitle} />\n      <meta name=\"twitter:description\" content={description} />\n      <meta name=\"twitter:image\" content={image} />\n      <meta name=\"twitter:image:alt\" content={title} />\n\n      {/* Additional SEO meta tags */}\n      <meta name=\"robots\" content=\"index, follow, max-image-preview:large\" />\n      <meta name=\"googlebot\" content=\"index, follow\" />\n      <meta httpEquiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n      <meta httpEquiv=\"Content-Language\" content={currentLanguage} />\n\n      {/* Mobile and theme meta tags */}\n      <meta\n        name=\"viewport\"\n        content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\"\n      />\n      <meta name=\"theme-color\" content=\"#06B6D4\" />\n      <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n      <meta\n        name=\"apple-mobile-web-app-status-bar-style\"\n        content=\"black-translucent\"\n      />\n\n      {/* JSON-LD structured data */}\n      {schema && (\n        <script type=\"application/ld+json\">\n          {JSON.stringify({\n            ...schema,\n            \"@context\": \"https://schema.org\",\n            inLanguage: currentLanguage,\n            url: currentUrl,\n          })}\n        </script>\n      )}\n    </Helmet>\n  );\n};\n\nMultilingualSEO.propTypes = {\n  title: PropTypes.string.isRequired,\n  description: PropTypes.string.isRequired,\n  slug: PropTypes.string,\n  type: PropTypes.string,\n  schema: PropTypes.object,\n  keywords: PropTypes.arrayOf(PropTypes.string),\n  image: PropTypes.string,\n  alternateUrls: PropTypes.object,\n};\n\nexport default MultilingualSEO;\n", "import { portfolios5 } from \"@/data/portfolio\";\nimport React from \"react\";\n\nimport { Link } from \"react-router-dom\";\nimport { Gallery, Item } from \"react-photoswipe-gallery\";\n\nexport default function RelatedProjects() {\n  return (\n    <div className=\"container relative\">\n      <div className=\"text-center mb-60 mb-sm-40\">\n        <h2 className=\"section-title-small\">Related Projects</h2>\n      </div>\n      {/* Works Grid */}\n      <ul\n        className=\"works-grid work-grid-4 work-grid-gut-sm hide-titles\"\n        id=\"work-grid\"\n      >\n        {/* Work Item (External Page) */}\n        <Gallery>\n          {/* Work Item (Lightbox) */}\n          {portfolios5.slice(1, 5).map((item, index) => (\n            <li\n              key={index}\n              className={`work-item mix ${item.categories.join(\" \")}`}\n            >\n              {item.type === \"Lightbox\" ? (\n                <Item\n                  original={item.imageSrc}\n                  thumbnail={item.imageSrc}\n                  width={650}\n                  height={773}\n                >\n                  {({ ref, open }) => (\n                    <a onClick={open} className=\"work-lightbox-link mfp-image\">\n                      <div className=\"work-img\">\n                        <div className=\"work-img-bg wow-p scalexIn\" />\n\n                        <img\n                          src={item.imageSrc}\n                          ref={ref}\n                          width={650}\n                          height={761}\n                          alt=\"Work Description\"\n                        />\n                      </div>\n                      <div className=\"work-intro\">\n                        <h3 className=\"work-title\">{item.title}</h3>\n                        <div className=\"work-descr\">{item.type}</div>\n                      </div>\n                    </a>\n                  )}\n                </Item>\n              ) : (\n                <Link\n                  to={`/elegant-portfolio-single/${item.id}`}\n                  className=\"work-ext-link\"\n                >\n                  <div className=\"work-img\">\n                    <div className=\"work-img-bg\" />\n                    <img\n                      src={item.imageSrc}\n                      width={650}\n                      height={761}\n                      alt=\"Work Description\"\n                    />\n                  </div>\n                  <div className=\"work-intro\">\n                    <h3 className=\"work-title\">{item.title}</h3>\n                    <div className=\"work-descr\">{item.type}</div>\n                  </div>\n                </Link>\n              )}\n            </li>\n          ))}{\" \"}\n        </Gallery>\n        {/* End Work Item */}\n      </ul>\n      {/* End Works Grid */}\n    </div>\n  );\n}\n", "import React from \"react\";\n\nconst comments = [\n  {\n    author: \"<PERSON>\",\n    date: \"Feb 9, 2023 at 10:23\",\n    text: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque at magna ut ante eleifend eleifend.\",\n    avatar: \"/assets/images/user-avatar.png\",\n    replies: [\n      {\n        author: \"<PERSON> Brin\",\n        date: \"Feb 9, 2023 at 10:27\",\n        text: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque at magna ut ante eleifend eleifend.\",\n        avatar: \"/assets/images/user-avatar.png\",\n      },\n    ],\n  },\n  {\n    author: \"<PERSON>\",\n    date: \"Feb 9, 2023 at 10:37\",\n    text: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque at magna ut ante eleifend eleifend.\",\n    avatar: \"/assets/images/user-avatar.png\",\n    replies: [],\n  },\n  {\n    author: \"<PERSON>\",\n    date: \"Feb 9, 2023 at 10:3\",\n    text: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque at magna ut ante eleifend eleifend.\",\n    avatar: \"/assets/images/user-avatar.png\",\n    replies: [],\n  },\n];\n\nexport default function Comments() {\n  return (\n    <>\n      {comments.map((comment, i) => (\n        <li key={i} className=\"media comment-item\">\n          <a className=\"float-start\" href=\"#\">\n            <img\n              className=\"media-object comment-avatar\"\n              src={comment.avatar}\n              alt=\"\"\n              width={50}\n              height={50}\n            />\n          </a>\n          <div className=\"media-body\">\n            <div className=\"comment-item-data\">\n              <div className=\"comment-author\">\n                <a href=\"#\">{comment.author}</a>\n              </div>\n              {comment.date} <span className=\"separator\">—</span>\n              <a href=\"#\">\n                <i className=\"fa fa-comment\" />\n                &nbsp;Reply\n              </a>\n            </div>\n            <p>{comment.text}</p>\n            {comment.replies &&\n              comment.replies.length > 0 &&\n              comment.replies.map((reply, index) => (\n                <div key={index} className=\"media comment-item\">\n                  <a className=\"float-start\" href=\"#\">\n                    <img\n                      className=\"media-object comment-avatar\"\n                      src={reply.avatar}\n                      alt=\"\"\n                      width={100}\n                      height={100}\n                    />\n                  </a>\n                  <div className=\"media-body\">\n                    <div className=\"comment-item-data\">\n                      <div className=\"comment-author\">\n                        <a href=\"#\">{reply.author}</a>\n                      </div>\n                      {reply.date} <span className=\"separator\">—</span>\n                      <a href=\"#\">\n                        <i className=\"fa fa-comment\" />\n                        &nbsp;Reply\n                      </a>\n                    </div>\n                    <p>{reply.text}</p>\n                  </div>\n                </div>\n              ))}\n          </div>\n        </li>\n      ))}\n    </>\n  );\n}\n", "import React from \"react\";\n\nexport default function Form() {\n  return (\n    <form className=\"form\" onSubmit={(e) => e.preventDefault()}>\n      <div className=\"row mb-30 mb-md-20\">\n        <div className=\"col-md-6 mb-md-20\">\n          {/* Name */}\n          <label htmlFor=\"name\">Name *</label>\n          <input\n            type=\"text\"\n            name=\"name\"\n            id=\"name\"\n            className=\"input-lg round form-control\"\n            placeholder=\"Enter your name\"\n            maxLength={100}\n            required\n            aria-required=\"true\"\n          />\n        </div>\n        <div className=\"col-md-6\">\n          {/* Email */}\n          <label htmlFor=\"email\">Email *</label>\n          <input\n            type=\"email\"\n            name=\"email\"\n            id=\"email\"\n            className=\"input-lg round form-control\"\n            placeholder=\"Enter your email\"\n            maxLength={100}\n            required\n            aria-required=\"true\"\n          />\n        </div>\n      </div>\n      <div className=\"mb-30 mb-md-20\">\n        {/* Website */}\n        <label htmlFor=\"website\">Website</label>\n        <input\n          type=\"text\"\n          name=\"website\"\n          id=\"website\"\n          className=\"input-lg round form-control\"\n          placeholder=\"Enter your website\"\n          maxLength={100}\n        />\n      </div>\n      {/* Comment */}\n      <div className=\"mb-30 mb-md-20\">\n        <label htmlFor=\"comment\">Comment</label>\n        <textarea\n          name=\"comment\"\n          id=\"comment\"\n          className=\"input-lg round form-control\"\n          rows={6}\n          placeholder=\"Enter your comment\"\n          maxLength={400}\n          defaultValue={\"\"}\n        />\n      </div>\n      {/* Send Button */}\n      <button\n        type=\"submit\"\n        className=\"submit_btn link-hover-anim link-circle-1 align-middle\"\n        data-link-animate=\"y\"\n      >\n        <span className=\"link-strong link-strong-unhovered\">\n          Send Comment{\" \"}\n          <i\n            className=\"mi-arrow-right size-18 align-middle\"\n            aria-hidden=\"true\"\n          ></i>\n        </span>\n        <span className=\"link-strong link-strong-hovered\" aria-hidden=\"true\">\n          Send Comment{\" \"}\n          <i\n            className=\"mi-arrow-right size-18 align-middle\"\n            aria-hidden=\"true\"\n          ></i>\n        </span>\n      </button>\n      {/* Inform Tip */}\n      <div className=\"form-tip form-tip-2   bg-gray-light-1 round mt-30 p-3\">\n        * - these fields are required. By sending the form you agree to the{\" \"}\n        <a href=\"#\">Terms &amp; Conditions</a> and{\" \"}\n        <a href=\"#\">Privacy Policy</a>.\n      </div>\n    </form>\n  );\n}\n", "export const blogs1 = [\n  {\n    id: 1,\n    delay: \"0.1s\",\n    imgSrc: \"/assets/images/blog/post-prev-1.jpg\",\n    title: \"Spotlight — Equinox Collection by <PERSON>\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of <PERSON>, an artist and director based in New York...\",\n    authorImg: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"<PERSON>\",\n    date: \"August 3\",\n  },\n  {\n    id: 2,\n    delay: \"0.2s\",\n    imgSrc: \"/assets/images/blog/post-prev-2.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"<PERSON><PERSON> is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImg: \"/assets/images/blog/author/author-2.jpg\",\n    authorName: \"<PERSON>\",\n    date: \"August 2\",\n  },\n  {\n    id: 3,\n    delay: \"0.3s\",\n    imgSrc: \"/assets/images/blog/post-prev-3.jpg\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"<PERSON><PERSON> Petrenco shared a beautiful visual identity, branding and packaging design project on their <PERSON><PERSON><PERSON> profile...\",\n    authorImg: \"/assets/images/blog/author/author-3.jpg\",\n    authorName: \"<PERSON>\",\n    date: \"August 1\",\n  },\n];\n\nexport const blogs2 = [\n  {\n    id: 4,\n    imgSrc: \"/assets/images/demo-modern/blog/1.jpg\",\n    title: \"Natura Insects Series: Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n    rellaxSpeed: -1,\n    rellaxPercentage: 0.37,\n  },\n  {\n    id: 5,\n    imgSrc: \"/assets/images/demo-modern/blog/2.jpg\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 2, 2023\",\n  },\n  {\n    id: 6,\n    imgSrc: \"/assets/images/demo-modern/blog/3.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    date: \"November 29, 2023\",\n    rellaxSpeed: 1,\n    rellaxPercentage: 0.37,\n  },\n];\n\nexport const blogs3 = [\n  {\n    id: 7,\n    imgSrc: \"/assets/images/demo-brutalist/blog/1.jpg\",\n    alt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Natura Insects Series: Crafting Insects Made by Flowers\",\n    delay: \"\",\n  },\n  {\n    id: 8,\n    imgSrc: \"/assets/images/demo-brutalist/blog/2.jpg\",\n    alt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    delay: \"0.2s\",\n  },\n  {\n    id: 9,\n    imgSrc: \"/assets/images/demo-brutalist/blog/3.jpg\",\n    alt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Stylish Newspaper Cover Illustration\",\n    delay: \"0.4s\",\n  },\n];\n\nexport const blogs4 = [\n  {\n    id: 10,\n    imageUrl: \"/assets/images/demo-corporate/blog/post-prev-1.jpg\",\n    title: \"Content Marketing Steps That Will Help You to Grow Your Business\",\n    description:\n      \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n    date: \"February 13, 2022\",\n    category: \"Articles\",\n    colorClass: \"color-primary-1\",\n  },\n  {\n    id: 11,\n    imageUrl: \"/assets/images/demo-corporate/blog/post-prev-2.jpg\",\n    title: \"Top Five Trends for Small Investment Companies Marketing\",\n    description:\n      \"The micro-environment, over which a firm holds a greater amount control, typically includes Employees, Suppliers and the Media.\",\n    date: \"February 11, 2023\",\n    category: \"Tutorials\",\n    colorClass: \"color-primary-2\",\n  },\n];\n\n// Legacy blog data - now using blogPosts.js for bilingual support\n// This file is kept for backward compatibility with other components\n\nexport const blogs5 = []; // Empty array - components should use getBlogPostsByLanguage() instead\n\nexport const blogs6 = [\n  {\n    id: 15,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-1.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/demo-fancy/user-1.jpg\",\n    authorImgAlt: \"Image Description\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n    delay: \"0s\",\n  },\n  {\n    id: 16,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-2.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImgSrc: \"/assets/images/demo-fancy/user-2.jpg\",\n    authorImgAlt: \"Image Description\",\n    author: \"Emma Kandel\",\n    date: \"August 2\",\n    delay: \"0.1s\",\n  },\n  {\n    id: 17,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-3.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImgSrc: \"/assets/images/demo-fancy/user-3.jpg\",\n    authorImgAlt: \"Image Description\",\n    author: \"Thomas Johnson\",\n    date: \"August 1\",\n    delay: \"0.2s\",\n  },\n];\n\nexport const blogs7 = [\n  {\n    id: 18,\n    imgSrc: \"/assets/images/demo-gradient/blog/post-prev-1.jpg\",\n    imgAlt: \"Add Image Description\",\n    postTitle: \"Elegant Branding Work for Red Circle by Oddone\",\n    postText:\n      \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImgSrc: \"/assets/images/demo-gradient/user-1.jpg\",\n    authorName: \"Thomas Johnson\",\n    postDate: \"August 3\",\n  },\n  {\n    id: 19,\n    imgSrc: \"/assets/images/demo-gradient/blog/post-prev-2.jpg\",\n    imgAlt: \"Add Image Description\",\n    postTitle: \"Random Explorations with Cinema 4D and Redshift\",\n    postText:\n      \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImgSrc: \"/assets/images/demo-gradient/user-2.jpg\",\n    authorName: \"Adam Smith\",\n    postDate: \"August 3\",\n  },\n];\n\nexport const blogs8 = [\n  {\n    id: 20,\n    imageSrc: \"/assets/images/demo-modern/blog/1.jpg\",\n    title: \"Natura Insects Series: Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n    rellaxY: \"\",\n    rellaxSpeed: -1,\n    rellaxPercentage: \"0.37\",\n  },\n  {\n    id: 21,\n    imageSrc: \"/assets/images/demo-modern/blog/2.jpg\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 2, 2023\",\n    rellaxY: \"\",\n    rellaxSpeed: 0,\n    rellaxPercentage: \"\",\n  },\n  {\n    id: 22,\n    imageSrc: \"/assets/images/demo-modern/blog/3.jpg\",\n    title: \"Stylish Newspaper Cover Illustration\",\n    date: \"November 29, 2023\",\n    rellaxY: \"\",\n    rellaxSpeed: 1,\n    rellaxPercentage: \"0.37\",\n  },\n];\n\nexport const blogs9 = [\n  {\n    id: 23,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-1.jpg\",\n    altText: \"Add Image Description\",\n    title: \"Elegant Branding Work for Red Circle by Oddone\",\n    intro:\n      \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImgSrc: \"/assets/images/demo-slick/user-1.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 24,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-2.jpg\",\n    altText: \"Add Image Description\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    intro:\n      \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImgSrc: \"/assets/images/demo-slick/user-2.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n];\n\nexport const blogs10 = [\n  {\n    id: 25,\n    imgSrc: \"/assets/images/demo-modern/blog/1.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Natura Insects Series: Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 26,\n    imgSrc: \"/assets/images/demo-modern/blog/2.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 27,\n    imgSrc: \"/assets/images/demo-modern/blog/3.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Stylish Newspaper Cover Illustration by New York Design Band\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 28,\n    imgSrc: \"/assets/images/demo-modern/blog/5.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Revitalizing Grenier brand identity and editorial design\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 29,\n    imgSrc: \"/assets/images/demo-modern/blog/6.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Athletics unveils vibrant branding project for MLS GO\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 30,\n    imgSrc: \"/assets/images/demo-modern/blog/4.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Manyway: a humanized global transaction experience\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 31,\n    imgSrc: \"/assets/images/demo-modern/blog/7.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Natura Insects Series: Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 32,\n    imgSrc: \"/assets/images/demo-modern/blog/8.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 33,\n    imgSrc: \"/assets/images/demo-modern/blog/9.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Stylish Newspaper Cover Illustration by New York Design Band\",\n    date: \"December 3, 2023\",\n  },\n];\n\nexport const blogs11 = [\n  {\n    id: 34,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-1.jpg\",\n    title: \"Elegant Branding Work for Red Circle by Oddone\",\n    text: \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 35,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-2.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 36,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-3.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 37,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-4.jpg\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 38,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-5.jpg\",\n    title: \"Strand man — an abstract 3D portrait series\",\n    text: \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 39,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-6.jpg\",\n    title: \"Design & 3D Motion Campaign for Tylko\",\n    text: \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 40,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-7.jpg\",\n    title: \"Elegant Branding Work for Red Circle by Oddone\",\n    text: \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 41,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-8.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n];\nexport const blogs12 = [\n  {\n    id: 42,\n    imgSrc: \"/assets/images/demo-modern/blog/1.jpg\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 43,\n    imgSrc: \"/assets/images/demo-modern/blog/2.jpg\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 44,\n    imgSrc: \"/assets/images/demo-modern/blog/3.jpg\",\n    title: \"Stylish Newspaper Cover Illustration by New York Design Band\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 45,\n    imgSrc: \"/assets/images/demo-modern/blog/5.jpg\",\n    title: \"Revitalizing Grenier brand identity and editorial design\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 46,\n    imgSrc: \"/assets/images/demo-modern/blog/6.jpg\",\n    title: \"Athletics unveils vibrant branding project for MLS GO\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 47,\n    imgSrc: \"/assets/images/demo-modern/blog/4.jpg\",\n    title: \"Manyway a humanized global transaction experience\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 48,\n    imgSrc: \"/assets/images/demo-modern/blog/7.jpg\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 49,\n    imgSrc: \"/assets/images/demo-modern/blog/8.jpg\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 50,\n    imgSrc: \"/assets/images/demo-modern/blog/9.jpg\",\n    title: \"Stylish Newspaper Cover Illustration by New York Design Band\",\n    date: \"December 3, 2023\",\n  },\n];\n\nexport const blogs13 = [\n  {\n    id: 51,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-1.jpg\",\n    title: \"Elegant Branding Work for Red Circle by Oddone\",\n    description:\n      \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 52,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-2.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    description:\n      \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 53,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-3.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    description:\n      \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 54,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-4.jpg\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    description:\n      \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 55,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-5.jpg\",\n    title: \"Strand man — an abstract 3D portrait series\",\n    description:\n      \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 56,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-6.jpg\",\n    title: \"Design & 3D Motion Campaign for Tylko\",\n    description:\n      \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 57,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-7.jpg\",\n    title: \"Elegant Branding Work for Red Circle by Oddone\",\n    description:\n      \"Oddone and brand strategist, Caren Williams, worked together with the San Francisco-based startup...\",\n    authorImg: \"/assets/images/demo-slick/user-1.jpg\",\n    author: \"Thomas Johnson\",\n    date: \"August 3\",\n  },\n  {\n    id: 58,\n    imgSrc: \"/assets/images/demo-slick/blog/post-prev-8.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    description:\n      \"Nidia Dias is a 3D designer based in Portugal with an incredible portfolio. From the professional...\",\n    authorImg: \"/assets/images/demo-slick/user-2.jpg\",\n    author: \"Adam Smith\",\n    date: \"August 3\",\n  },\n];\nexport const blogs14 = [\n  {\n    id: 59,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-1.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImg: \"/assets/images/demo-fancy/user-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 60,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-2.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImg: \"/assets/images/demo-fancy/user-2.jpg\",\n    authorName: \"Emma Kandel\",\n    date: \"August 2\",\n  },\n  {\n    id: 61,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-3.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImg: \"/assets/images/demo-fancy/user-3.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 1\",\n  },\n  {\n    id: 62,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-4.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Strand man — an abstract 3D portrait series\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImg: \"/assets/images/demo-fancy/user-3.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 1\",\n  },\n  {\n    id: 63,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-5.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Design & 3D Motion Campaign for Tylko\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImg: \"/assets/images/demo-fancy/user-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 64,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-6.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Unveiling the branding and visual Identity of Merkezsiz\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImg: \"/assets/images/demo-fancy/user-2.jpg\",\n    authorName: \"Emma Kandel\",\n    date: \"August 2\",\n  },\n  {\n    id: 65,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-7.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImg: \"/assets/images/demo-fancy/user-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 66,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-8.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImg: \"/assets/images/demo-fancy/user-2.jpg\",\n    authorName: \"Emma Kandel\",\n    date: \"August 2\",\n  },\n  {\n    id: 67,\n    imgSrc: \"/assets/images/demo-fancy/blog/post-prev-9.jpg\",\n    imgAlt: \"Add Image Description\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImg: \"/assets/images/demo-fancy/user-3.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 1\",\n  },\n];\nexport const blogs15 = [\n  {\n    id: 68,\n    imgSrc: \"/assets/images/demo-elegant/blog/1.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 69,\n    imgSrc: \"/assets/images/demo-elegant/blog/2.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 70,\n    imgSrc: \"/assets/images/demo-elegant/blog/3.jpg\",\n    title: \"Visually Identity & Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 71,\n    imgSrc: \"/assets/images/demo-elegant/blog/4.jpg\",\n    title: \"Design & 3D Motion Campaign for Tylko\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 72,\n    imgSrc: \"/assets/images/demo-elegant/blog/5.jpg\",\n    title: \"Unveiling the branding and visual Identity of Merkezsiz\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 73,\n    imgSrc: \"/assets/images/demo-elegant/blog/6.jpg\",\n    title: \"Strand man — an abstract 3D portrait series\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 74,\n    imgSrc: \"/assets/images/demo-elegant/blog/9.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 75,\n    imgSrc: \"/assets/images/demo-elegant/blog/7.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n  {\n    id: 76,\n    imgSrc: \"/assets/images/demo-elegant/blog/8.jpg\",\n    title: \"Visually Identity & Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    author: \"Thomas Johnson\",\n    date: \"August 5\",\n  },\n];\n\nexport const blogs16 = [\n  {\n    id: 77,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-1.jpg\",\n    title: \"Content Marketing Steps That Will Help You to Grow Your Business\",\n    date: \"February 13, 2022\",\n    category: \"Articles\",\n    colorClass: \"color-primary-1\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 78,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-2.jpg\",\n    title: \"Top Five Trends for Small Investment Companies Marketing\",\n    date: \"February 11, 2023\",\n    category: \"Tutorials\",\n    colorClass: \"color-primary-2\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 79,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-3.jpg\",\n    title: \"Powerful Content Marketing Strategies for Business Growth\",\n    date: \"February 11, 2023\",\n    category: \"Tutorials\",\n    colorClass: \"color-primary-3\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 80,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-4.jpg\",\n    title: \"Content Marketing Mastery Accelerating Your Business Success\",\n    date: \"February 13, 2022\",\n    category: \"Articles\",\n    colorClass: \"color-primary-4\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 81,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-5.jpg\",\n    title: \"Winning with Content Marketing Steps for Business Growth\",\n    date: \"February 11, 2023\",\n    category: \"Tutorials\",\n    colorClass: \"color-primary-1\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 82,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-6.jpg\",\n    title: \"From Content to Success Essential Business Growth Steps\",\n    date: \"February 13, 2022\",\n    category: \"Articles\",\n    colorClass: \"color-primary-2\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 83,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-7.jpg\",\n    title: \"Strategic Content Marketing for Expanding Your Business\",\n    date: \"February 11, 2023\",\n    category: \"Tutorials\",\n    colorClass: \"color-primary-3\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n  {\n    id: 84,\n    imgUrl: \"/assets/images/demo-corporate/blog/post-prev-8.jpg\",\n    title: \"Content Marketing Unleashed Your Path to Business Growth\",\n    date: \"February 13, 2022\",\n    category: \"Articles\",\n    colorClass: \"color-primary-4\",\n    desc: \"The macro-environment, over which a firm holds little control, consists of a variety of external factors that manifest on a large scale.\",\n  },\n];\n\nexport const widgetPosts = [\n  {\n    id: 85,\n    imgUrl: \"/assets/images/blog/previews/post-prev-1.jpg\",\n    title: \"Minimalistic Design Forever\",\n    author: \"John Doe\",\n  },\n  {\n    id: 86,\n    imgUrl: \"/assets/images/blog/previews/post-prev-2.jpg\",\n    title: \"Ipsum Dolor Sit Amet, Consectetur Adipiscing Elit\",\n    author: \"John Doe\",\n  },\n  {\n    id: 87,\n    imgUrl: \"/assets/images/blog/previews/post-prev-3.jpg\",\n    title: \"New Web Design Trends in 2023 Year\",\n    author: \"John Doe\",\n  },\n  {\n    id: 88,\n    imgUrl: \"/assets/images/blog/previews/post-prev-4.jpg\",\n    title: \"Hipster’s Style in Web Design and Logo\",\n    author: \"John Doe\",\n  },\n  {\n    id: 89,\n    imgUrl: \"/assets/images/blog/previews/post-prev-5.jpg\",\n    title: \"Duis Tristique Condimentum Nulla Bibendum Consectetu\",\n    author: \"John Doe\",\n  },\n];\n\nexport const blogs17 = [\n  {\n    id: 90,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/1.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n  },\n  {\n    id: 91,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/2.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n  },\n  {\n    id: 92,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/3.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Stylish Newspaper Cover Illustration by Jack Johnson\",\n  },\n  {\n    id: 93,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/6.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n  },\n  {\n    id: 94,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/5.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n  },\n  {\n    id: 95,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/4.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n  },\n  {\n    id: 96,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/7.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Stylish Newspaper Cover Illustration by Jack Johnson\",\n  },\n  {\n    id: 97,\n    imgSrc: \"/assets/images/demo-brutalist/blog/blog-2-col/8.jpg\",\n    imgAlt: \"Image Description\",\n    author: \"John Doe\",\n    date: \"6 June\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n  },\n];\nexport const blogs18 = [\n  {\n    id: 98,\n    imgSrc: \"/assets/images/demo-modern/blog/1.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 99,\n    imgSrc: \"/assets/images/demo-modern/blog/2.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 100,\n    imgSrc: \"/assets/images/demo-modern/blog/3.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Stylish Newspaper Cover Illustration by New York Design Band\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 101,\n    imgSrc: \"/assets/images/demo-modern/blog/5.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Revitalizing Grenier brand identity and editorial design\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 102,\n    imgSrc: \"/assets/images/demo-modern/blog/6.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Athletics unveils vibrant branding project for MLS GO\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 103,\n    imgSrc: \"/assets/images/demo-modern/blog/4.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Manyway a humanized global transaction experience\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 104,\n    imgSrc: \"/assets/images/demo-modern/blog/7.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Natura Insects Series Crafting Insects Made by Flowers\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 105,\n    imgSrc: \"/assets/images/demo-modern/blog/8.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Minimalistic Design Concept for Balmain Online Store\",\n    date: \"December 3, 2023\",\n  },\n  {\n    id: 106,\n    imgSrc: \"/assets/images/demo-modern/blog/9.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Stylish Newspaper Cover Illustration by New York Design Band\",\n    date: \"December 3, 2023\",\n  },\n];\n\nexport const blogs19 = [\n  {\n    id: 107,\n    imgSrc: \"/assets/images/blog/post-prev-1.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 108,\n    imgSrc: \"/assets/images/blog/post-prev-2.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImgSrc: \"/assets/images/blog/author/author-2.jpg\",\n    authorName: \"Emma Kandel\",\n    date: \"August 2\",\n  },\n  {\n    id: 109,\n    imgSrc: \"/assets/images/blog/post-prev-3.jpg\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImgSrc: \"/assets/images/blog/author/author-3.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 1\",\n  },\n  {\n    id: 110,\n    imgSrc: \"/assets/images/blog/post-prev-4.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 111,\n    imgSrc: \"/assets/images/blog/post-prev-5.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 112,\n    imgSrc: \"/assets/images/blog/post-prev-6.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImgSrc: \"/assets/images/blog/author/author-2.jpg\",\n    authorName: \"Emma Kandel\",\n    date: \"August 2\",\n  },\n  {\n    id: 113,\n    imgSrc: \"/assets/images/blog/post-prev-7.jpg\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImgSrc: \"/assets/images/blog/author/author-3.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 1\",\n  },\n  {\n    id: 114,\n    imgSrc: \"/assets/images/blog/post-prev-8.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 115,\n    imgSrc: \"/assets/images/blog/post-prev-9.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n  {\n    id: 116,\n    imgSrc: \"/assets/images/blog/post-prev-10.jpg\",\n    title: \"Random Explorations with Cinema 4D and Redshift\",\n    text: \"Nidia Dias is a 3D designer based in the Portugal with an incredible portfolio. From the professional work done with...\",\n    authorImgSrc: \"/assets/images/blog/author/author-2.jpg\",\n    authorName: \"Emma Kandel\",\n    date: \"August 2\",\n  },\n  {\n    id: 117,\n    imgSrc: \"/assets/images/blog/post-prev-11.jpg\",\n    title: \"Visually Identity and Branding for Mexican Restaurant\",\n    text: \"Anta Petrenco shared a beautiful visual identity, branding and packaging design project on their Behance profile...\",\n    authorImgSrc: \"/assets/images/blog/author/author-3.jpg\",\n    authorName: \"Thomas Johnson\",\n    date: \"August 1\",\n  },\n  {\n    id: 118,\n    imgSrc: \"/assets/images/blog/post-prev-12.jpg\",\n    title: \"Spotlight — Equinox Collection by Shane Griffin\",\n    text: \"Looking for inspiration to kick it off, I stumbled across the work of Shane Griffin, an artist and director based in New York...\",\n    authorImgSrc: \"/assets/images/blog/author/author-1.jpg\",\n    authorName: \"Adam Smith\",\n    date: \"August 3\",\n  },\n];\n\nexport const allBlogs = [\n  ...blogs1,\n  ...blogs2,\n  ...blogs3,\n  ...blogs4,\n  ...blogs5,\n  ...blogs6,\n  ...blogs7,\n  ...blogs8,\n  ...blogs9,\n  ...blogs10,\n  ...blogs11,\n  ...blogs12,\n  ...blogs13,\n  ...blogs14,\n  ...blogs15,\n  ...blogs16,\n  ...blogs17,\n  ...blogs18,\n  ...blogs19,\n  ...widgetPosts,\n];\n", "export const comments = [\n  {\n    id: 1,\n    author: \"<PERSON>\",\n    title: \"Lorem ipsum dolor sit amet\",\n  },\n  {\n    id: 2,\n    author: \"<PERSON>\",\n    title: \"Suspendisse accumsan interdum tellus\",\n  },\n  {\n    id: 3,\n    author: \"<PERSON>\",\n    title: \"Praesent ultricies ut ipsum\",\n  },\n  {\n    id: 4,\n    author: \"<PERSON>\",\n    title: \"Vivamus malesuada vel nulla vulputate\",\n  },\n  {\n    id: 5,\n    author: \"<PERSON>\",\n    title: \"Aenean auctor egestas auctor\",\n  },\n];\n", "import { archiveLinks } from \"@/data/archeve\";\nimport { widgetPosts } from \"@/data/blogs\";\nimport { categories } from \"@/data/categories\";\nimport { comments } from \"@/data/comments\";\nimport { tags } from \"@/data/tags\";\n\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\n\nexport default function Widget1({\n  searchInputClass = \"form-control input-md search-field input-circle\",\n}) {\n  return (\n    <>\n      <div className=\"widget\">\n        <form onSubmit={(e) => e.preventDefault()} className=\"form\">\n          <div className=\"search-wrap\">\n            <button\n              className=\"search-button animate\"\n              type=\"submit\"\n              title=\"Start Search\"\n            >\n              <i className=\"mi-search size-18\" />\n              <span className=\"visually-hidden\">Start search</span>\n            </button>\n            <input\n              type=\"text\"\n              className={searchInputClass}\n              placeholder=\"Search...\"\n              required\n            />\n          </div>\n        </form>\n      </div>\n      {/* End Search Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">Categories</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-menu\">\n            {categories.map((category) => (\n              <li key={category.id}>\n                <a href=\"#\" title=\"\">\n                  {category.name}\n                </a>\n                <small> - {category.count} </small>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">Tags</h3>\n        <div className=\"widget-body\">\n          <div className=\"tags\">\n            {tags.map((tag) => (\n              <a href=\"#\" key={tag.id}>\n                {tag.name}\n              </a>\n            ))}\n          </div>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">Latest posts</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-posts\">\n            {widgetPosts.map((post, index) => (\n              <li key={index} className=\"clearfix\">\n                <a href=\"#\">\n                  <img\n                    src={post.imgUrl}\n                    height={140}\n                    style={{ height: \"fit-content\" }}\n                    alt=\"\"\n                    width={100}\n                    className=\"widget-posts-img\"\n                  />\n                </a>\n                <div className=\"widget-posts-descr\">\n                  <a href=\"#\" title=\"\">\n                    {post.title}\n                  </a>\n                  <span>Posted by {post.author}</span>\n                </div>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">Comments</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-comments\">\n            {comments.map((comment, index) => (\n              <li key={index}>\n                {comment.author} on{\" \"}\n                <a href=\"#\" title=\"\">\n                  {comment.title}\n                </a>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">Text widget</h3>\n        <div className=\"widget-body\">\n          <div className=\"widget-text clearfix\">\n            <img\n              src=\"/assets/images/blog/previews/post-prev-6.jpg\"\n              height={140}\n              alt=\"Image Description\"\n              style={{ height: \"fit-content\" }}\n              width={100}\n              className=\"left img-left\"\n            />\n            Consectetur adipiscing elit. Quisque magna ante eleifend eleifend.\n            Purus ut dignissim consectetur, nulla erat ultrices purus, ut\n            consequat sem elit non sem. Quisque magna ante eleifend eleifend.\n          </div>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">Archive</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-menu\">\n            {archiveLinks.map((link) => (\n              <li key={link.id}>\n                <a href=\"#\" title=\"\">\n                  {link.date}\n                </a>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </>\n  );\n}\n\nWidget1.propTypes = {\n  searchInputClass: PropTypes.string,\n};\n", "import React from \"react\";\nimport { useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Map() {\n  const [mapOpen, setMapOpen] = useState(false);\n  const { t } = useTranslation();\n  return (\n    <>\n      <a href=\"#\" className={`map-section ${mapOpen ? \"js-active\" : \"\"}`}>\n        <div className=\"map-toggle wow fadeInUpShort\" aria-hidden=\"true\">\n          <div className=\"mt-icon\">\n            <i className=\"mi-location\"></i>\n          </div>\n          <div className=\"mt-text\">\n            <div onClick={() => setMapOpen((pre) => !pre)} className=\"mt-open\">\n              {t(\"contact.map.open\")} <i className=\"mt-open-icon\"></i>\n            </div>\n            <div onClick={() => setMapOpen((pre) => !pre)} className=\"mt-close\">\n              {t(\"contact.map.close\")} <i className=\"mt-close-icon\"></i>\n            </div>\n          </div>\n        </div>\n      </a>\n\n      <iframe\n        src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2028.7573128553793!2d24.7553!3d59.4372!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4692935c7d5dfa3b%3A0x4b0f5f5c5d9c1f0!2sTornim%C3%A4e%20tn%207%2C%2010145%20Tallinn!5e0!3m2!1sen!2see!4v1684450429598!5m2!1sen!2see\"\n        width={600}\n        height={450}\n        loading=\"lazy\"\n        style={{ border: 0 }}\n        allowFullScreen=\"\"\n        aria-hidden=\"false\"\n        tabIndex={0}\n      />\n    </>\n  );\n}\n", "import React, { useState, useRef, useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\n\nconst AdminLayout = ({ children, title }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);\n  const [userDropdownOpen, setUserDropdownOpen] = useState(false);\n  const dropdownRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setUserDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"adminToken\");\n    localStorage.removeItem(\"adminUser\");\n    navigate(\"/admin\");\n    setUserDropdownOpen(false);\n  };\n\n  const toggleUserDropdown = () => {\n    setUserDropdownOpen(!userDropdownOpen);\n  };\n\n  const isActive = (path) => {\n    return (\n      location.pathname === path || location.pathname.startsWith(path + \"/\")\n    );\n  };\n\n  const user = JSON.parse(localStorage.getItem(\"adminUser\") || \"{}\");\n\n  const menuItems = [\n    {\n      path: \"/admin/dashboard\",\n      icon: \"solar:widget-2-bold\",\n      label: \"Dashboard\",\n      exact: true,\n    },\n    {\n      path: \"/admin/posts\",\n      icon: \"solar:document-text-bold\",\n      label: \"Blog Posts\",\n      exact: false,\n    },\n    {\n      path: \"/admin/blog/new\",\n      icon: \"solar:add-circle-bold\",\n      label: \"New Post\",\n      exact: true,\n    },\n    {\n      path: \"/admin/categories\",\n      icon: \"solar:folder-bold\",\n      label: \"Categories\",\n      exact: true,\n    },\n    {\n      path: \"/admin/tags\",\n      icon: \"solar:tag-bold\",\n      label: \"Tags\",\n      exact: true,\n    },\n  ];\n\n  return (\n    <div className=\"admin-layout-wrapper\">\n      {/* Mobile Overlay */}\n      {mobileSidebarOpen && (\n        <div\n          className=\"admin-mobile-overlay\"\n          onClick={() => setMobileSidebarOpen(false)}\n        ></div>\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={`admin-sidebar ${sidebarCollapsed ? \"collapsed\" : \"\"} ${\n          mobileSidebarOpen ? \"mobile-open\" : \"\"\n        }`}\n      >\n        {/* Logo */}\n        <div className=\"admin-sidebar-header\">\n          <div className=\"admin-logo\">\n            {!sidebarCollapsed ? (\n              <span>\n                <span className=\"color-primary-1\">DevSkills</span> Admin\n              </span>\n            ) : (\n              <span className=\"color-primary-1\">DS</span>\n            )}\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"admin-sidebar-nav\">\n          <ul className=\"admin-nav-list\">\n            {menuItems.map((item) => (\n              <li\n                key={item.path}\n                className={`admin-nav-item ${\n                  isActive(item.path) ? \"active\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    navigate(item.path);\n                    setMobileSidebarOpen(false);\n                  }}\n                  className=\"admin-nav-link\"\n                  title={item.label}\n                >\n                  <iconify-icon\n                    icon={item.icon}\n                    className=\"admin-nav-icon\"\n                  ></iconify-icon>\n                  <span className=\"admin-nav-text\">{item.label}</span>\n                </a>\n              </li>\n            ))}\n\n            {/* Divider */}\n            <li className=\"admin-nav-divider\"></li>\n\n            {/* View Site */}\n            <li className=\"admin-nav-item\">\n              <a\n                href=\"/\"\n                target=\"_blank\"\n                className=\"admin-nav-link\"\n                title=\"View Site\"\n              >\n                <iconify-icon\n                  icon=\"solar:link-bold\"\n                  className=\"admin-nav-icon\"\n                ></iconify-icon>\n                <span className=\"admin-nav-text\">View Site</span>\n              </a>\n            </li>\n          </ul>\n        </nav>\n\n        {/* User Menu */}\n        <div className=\"admin-sidebar-footer\">\n          <div className=\"admin-user-menu\" ref={dropdownRef}>\n            <div\n              className=\"admin-user-info clickable\"\n              onClick={toggleUserDropdown}\n              title=\"User menu\"\n            >\n              <div className=\"admin-user-avatar\">\n                <iconify-icon icon=\"solar:user-bold\"></iconify-icon>\n              </div>\n              {!sidebarCollapsed && (\n                <div className=\"admin-user-details\">\n                  <div className=\"admin-user-name\">{user.name || \"Admin\"}</div>\n                  <div className=\"admin-user-email\">{user.email}</div>\n                </div>\n              )}\n              <div className=\"admin-user-dropdown-arrow\">\n                <iconify-icon\n                  icon={`solar:alt-arrow-${\n                    userDropdownOpen ? \"up\" : \"down\"\n                  }-bold`}\n                ></iconify-icon>\n              </div>\n            </div>\n\n            {/* Dropdown Menu */}\n            {userDropdownOpen && (\n              <div className=\"admin-user-dropdown\">\n                <div\n                  className=\"admin-dropdown-item\"\n                  onClick={() => {\n                    navigate(\"/admin/dashboard\");\n                    setUserDropdownOpen(false);\n                  }}\n                >\n                  <iconify-icon\n                    icon=\"solar:widget-2-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Dashboard\n                </div>\n                <div\n                  className=\"admin-dropdown-item\"\n                  onClick={() => {\n                    window.open(\"/\", \"_blank\");\n                    setUserDropdownOpen(false);\n                  }}\n                >\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  View Site\n                </div>\n                <div className=\"admin-dropdown-divider\"></div>\n                <div\n                  className=\"admin-dropdown-item logout\"\n                  onClick={handleLogout}\n                >\n                  <iconify-icon\n                    icon=\"solar:power-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Logout\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </aside>\n\n      {/* Main Content */}\n      <main\n        className={`admin-main-content ${\n          sidebarCollapsed ? \"sidebar-collapsed\" : \"\"\n        }`}\n      >\n        {/* Top Bar */}\n        <header className=\"admin-topbar\">\n          <div className=\"admin-topbar-left\">\n            <button\n              className=\"admin-sidebar-toggle\"\n              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n            >\n              <iconify-icon icon=\"solar:hamburger-menu-bold\"></iconify-icon>\n            </button>\n            <button\n              className=\"admin-mobile-toggle\"\n              onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}\n            >\n              <iconify-icon icon=\"solar:hamburger-menu-bold\"></iconify-icon>\n            </button>\n            {title && <h1 className=\"admin-page-title\">{title}</h1>}\n          </div>\n          <div className=\"admin-topbar-right\">\n            <span className=\"admin-welcome\">\n              Welcome, {user.name || user.email}\n            </span>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <div className=\"admin-content\">{children}</div>\n      </main>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "names": ["languages", "i18n", "Backend", "LanguageDetector", "initReactI18next", "value", "format", "lng", "ns", "key", "fallback<PERSON><PERSON><PERSON>", "GDPRConsent", "t", "useTranslation", "showBanner", "setShowBanner", "useState", "showDetails", "setShowDetails", "useEffect", "consent", "initializeGoogleAnalytics", "consentSettings", "handleAcceptAll", "handleRejectAll", "handleCustomize", "settings", "jsxs", "Fragment", "jsx", "GDPRDetailsModal", "onClose", "onSave", "setSettings", "handleSave", "e", "prev", "PropTypes", "trackPageView", "page_title", "page_location", "trackButtonClick", "button_name", "button_location", "additional_params", "trackFormSubmission", "form_name", "form_location", "success", "trackContactFormSubmission", "email", "message_length", "trackScrollDepth", "scroll_percentage", "trackTimeOnPage", "time_seconds", "ScrollTopBehaviour", "pathname", "useLocation", "LanguageSelector", "isOpen", "setIsOpen", "dropdownRef", "useRef", "isMobile", "setIsMobile", "handleResize", "handleClickOutside", "event", "toggleDropdown", "handleLanguageChange", "langCode", "currentLanguage", "langData", "AnimatedText", "text", "elm", "i", "React", "elm2", "i2", "ParallaxContainer", "props", "jarall<PERSON>", "SEO", "title", "description", "canonical", "image", "imageAlt", "imageWidth", "imageHeight", "type", "schema", "twitter<PERSON><PERSON>le", "publishedAt", "modifiedAt", "author", "keywords", "locale", "formattedTitle", "<PERSON><PERSON><PERSON>", "schemaItem", "index", "Error<PERSON>ou<PERSON><PERSON>", "error", "errorInfo", "MetaComponent", "meta", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "portfolioItems", "portfolios1", "portfolios2", "portfolios3", "portfolios4", "portfolios5", "portfolios6", "portfolios7", "portfolios8", "portfolios9", "portfolios10", "portfolios11", "portfolios12", "portfolios13", "allPortfolios", "categories", "tags", "archiveLinks", "Pagination", "className", "activePage", "setActivePage", "handlePageChange", "page", "MultilingualSEO", "slug", "alternateUrls", "baseUrl", "languageUrls", "currentUrl", "getLanguageSpecificData", "localeMap", "languageMap", "language", "lang", "url", "altLocale", "RelatedProjects", "Gallery", "item", "<PERSON><PERSON>", "ref", "open", "Link", "comments", "Comments", "comment", "reply", "Form", "widgetPosts", "Widget1", "searchInputClass", "category", "tag", "post", "link", "Map", "mapOpen", "setMapOpen", "pre", "AdminLayout", "children", "navigate", "useNavigate", "location", "sidebarCollapsed", "setSidebarCollapsed", "mobileSidebarOpen", "setMobileSidebarOpen", "userDropdownOpen", "setUserDropdownOpen", "handleLogout", "toggleUserDropdown", "isActive", "path", "user", "menuItems"], "mappings": "sMAMA,MAAMA,EAAY,CAChB,GAAI,CAAE,KAAM,UAAW,KAAM,MAAO,EACpC,GAAI,CAAE,KAAM,QAAS,KAAM,MAAO,EAClC,GAAI,CAAE,KAAM,QAAS,KAAM,MAAO,EAClC,GAAI,CAAE,KAAM,UAAW,KAAM,MAAO,EACpC,GAAI,CAAE,KAAM,UAAW,KAAM,MAAO,CACtC,EAGAC,EACG,IAAIC,CAAO,EACX,IAAIC,CAAgB,EACpB,IAAIC,CAAgB,EACpB,KAAK,CAEJ,IAAK,KACL,YAAa,KACb,cAAe,OAAO,KAAKJ,CAAS,EAGpC,GAAI,CAAC,aAAa,EAClB,UAAW,cAGX,QAAS,CACP,SAAU,8BACZ,EAGA,UAAW,CAET,MAAO,CACL,eACA,YACA,UACA,OACA,WACF,EAGA,OAAQ,CAAC,cAAc,EAGvB,mBAAoB,aAGpB,oBAAqB,EAGrB,eAAgB,EAClB,EAGA,cAAe,CACb,YAAa,GACb,gBAAiB,IACjB,OAAQ,CAACK,EAAOC,EAAQC,IAClBD,IAAW,YAAoBD,EAAM,YAAY,EACjDC,IAAW,YAAoBD,EAAM,YAAY,EACjDC,IAAW,aACND,EAAM,OAAO,CAAC,EAAE,cAAgBA,EAAM,MAAM,CAAC,EAC/CA,CAEX,EAGA,MAAO,CACL,YAAa,GACb,SAAU,kBACV,cAAe,GACf,oBAAqB,GACrB,2BAA4B,GAC5B,2BAA4B,CAAC,KAAM,SAAU,IAAK,KAAM,MAAM,CAChE,EAGA,MAAO,GAGP,KAAM,eACN,QAAS,OAAO,KAAKL,CAAS,EAG9B,UAAW,GAGX,aAAc,IACd,YAAa,IAGb,gBAAiB,IACjB,iBAAkB,IAGlB,cAAe,GACf,kBAAmB,GACnB,WAAY,GAGZ,WAAY,GAGZ,YAAa,GAGb,YAAa,GACb,cAAe,UAGf,kBAAmB,CAACO,EAAKC,EAAIC,EAAKC,IAAkB,CAIpD,EAGA,cAAe,GAGf,oBAAqB,EACvB,CAAC,EC1HH,MAAMC,GAAc,IAAM,CAClB,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAS,EAAK,EAC5C,CAACC,EAAaC,CAAc,EAAIF,EAAAA,SAAS,EAAK,EAEpDG,EAAAA,UAAU,IAAM,CAER,MAAAC,EAAU,aAAa,QAAQ,cAAc,EAC9CA,EAIuBC,EAAA,KAAK,MAAMD,CAAO,CAAC,EAH7CL,EAAc,EAAI,CAKtB,EAAG,EAAE,EAEC,MAAAM,EAA6BC,GAAoB,CAEjD,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,UAAW,SAAU,CAC/B,kBAAmBA,EAAgB,UAAY,UAAY,SAC3D,WAAYA,EAAgB,UAAY,UAAY,SACpD,aAAcA,EAAgB,UAAY,UAAY,SACtD,mBAAoBA,EAAgB,UAAY,UAAY,QAAA,CAC7D,CAEL,EAEMC,EAAkB,IAAM,CAC5B,MAAMH,EAAU,CACd,UAAW,GACX,UAAW,GACX,UAAW,GACX,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEA,aAAa,QAAQ,eAAgB,KAAK,UAAUA,CAAO,CAAC,EAC5DC,EAA0BD,CAAO,EACjCL,EAAc,EAAK,EAGf,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,kBAAmB,CACtC,eAAgB,OAChB,YAAa,YAAA,CACd,CAEL,EAEMS,EAAkB,IAAM,CAC5B,MAAMJ,EAAU,CACd,UAAW,GACX,UAAW,GACX,UAAW,GACX,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEA,aAAa,QAAQ,eAAgB,KAAK,UAAUA,CAAO,CAAC,EAC5DC,EAA0BD,CAAO,EACjCL,EAAc,EAAK,EAGf,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,iBAAkB,CACrC,eAAgB,OAChB,YAAa,YAAA,CACd,CAEL,EAEMU,EAAmBC,GAAa,CACpC,MAAMN,EAAU,CACd,UAAW,GACX,UAAWM,EAAS,UACpB,UAAWA,EAAS,UACpB,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEA,aAAa,QAAQ,eAAgB,KAAK,UAAUN,CAAO,CAAC,EAC5DC,EAA0BD,CAAO,EACjCL,EAAc,EAAK,EACnBG,EAAe,EAAK,EAGhB,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,qBAAsB,CACzC,eAAgB,OAChB,YAAa,cAAcQ,EAAS,SAAS,gBAAgBA,EAAS,SAAS,EAAA,CAChF,CAEL,EAEI,OAACZ,EAKDa,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,cACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAF,OAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,kBAAmB,SAAAjB,EAAE,YAAY,EAAE,QAChD,IAAE,CAAA,UAAU,wBAAyB,SAAAA,EAAE,kBAAkB,CAAE,CAAA,CAAA,CAAA,CAC9D,CACF,CAAA,QACC,MAAI,CAAA,UAAU,oBACb,SAACe,EAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,UAAU,yDACV,QAASN,EACT,mBAAiB,IAEjB,SAAAI,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAjB,EAAE,gBAAgB,EACrB,EACAiB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAjB,EAAE,gBAAgB,CACrB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAiB,EAAA,IAAC,SAAA,CACC,UAAU,gDACV,QAASL,EACT,mBAAiB,IAEjB,SAAAG,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAjB,EAAE,gBAAgB,EACrB,EACAiB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAjB,EAAE,gBAAgB,CACrB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAiB,EAAA,IAAC,SAAA,CACC,UAAU,gDACV,QAAS,IAAMX,EAAe,EAAI,EAClC,mBAAiB,IAEjB,SAAAS,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAjB,EAAE,gBAAgB,EACrB,EACAiB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAjB,EAAE,gBAAgB,CACrB,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGCK,GACCY,EAAA,IAACC,EAAA,CACC,QAAS,IAAMZ,EAAe,EAAK,EACnC,OAAQO,EACR,EAAAb,CAAA,CAAA,CACF,EAEJ,EAxEsB,IA0E1B,EAEMkB,EAAmB,CAAC,CAAE,QAAAC,EAAS,OAAAC,EAAQ,EAAApB,KAAQ,CACnD,KAAM,CAACc,EAAUO,CAAW,EAAIjB,WAAS,CACvC,UAAW,GACX,UAAW,EAAA,CACZ,EAEKkB,EAAa,IAAM,CACvBF,EAAON,CAAQ,CACjB,EAGE,OAAAG,EAAAA,IAAC,OAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,uBACb,SAAAF,OAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,0BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,wBACX,SAAAjB,EAAE,qBAAqB,EAC1B,EACAiB,EAAAA,IAAC,SAAO,CAAA,UAAU,aAAa,QAASE,EACtC,SAACF,EAAA,IAAA,IAAA,CAAE,UAAU,kBAAmB,CAAA,CAClC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,gCACX,SAAAjB,EAAE,gBAAgB,EACrB,QACC,OAAK,CAAA,UAAU,sBACb,SAAAA,EAAE,eAAe,CACpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,IAAE,CAAA,UAAU,qBACV,SAAAA,EAAE,oBAAoB,CACzB,CAAA,CAAA,EACF,EAEAe,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,2BACX,SAAAjB,EAAE,gBAAgB,EACrB,EACAe,EAAAA,KAAC,QAAM,CAAA,UAAU,cACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASH,EAAS,UAClB,SAAWS,GACTF,EAAaG,IAAU,CACrB,GAAGA,EACH,UAAWD,EAAE,OAAO,OAAA,EACpB,CAAA,CAEN,EACAN,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAc,CAAA,CAAA,CAChC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,IAAE,CAAA,UAAU,qBACV,SAAAjB,EAAE,oBAAoB,CACzB,CAAA,CAAA,EACF,EAEAe,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,2BACX,SAAAjB,EAAE,gBAAgB,EACrB,EACAe,EAAAA,KAAC,QAAM,CAAA,UAAU,cACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASH,EAAS,UAClB,SAAWS,GACTF,EAAaG,IAAU,CACrB,GAAGA,EACH,UAAWD,EAAE,OAAO,OAAA,EACpB,CAAA,CAEN,EACAN,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAc,CAAA,CAAA,CAChC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,IAAE,CAAA,UAAU,qBACV,SAAAjB,EAAE,oBAAoB,CACzB,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,oBACb,SAACe,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,UAAU,gDACV,QAASE,EACT,mBAAiB,IAEjB,SAAAJ,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAjB,EAAE,aAAa,EAClB,EACAiB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAjB,EAAE,aAAa,CAClB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAiB,EAAA,IAAC,SAAA,CACC,UAAU,yCACV,QAASK,EACT,mBAAiB,IAEjB,SAAAP,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAjB,EAAE,sBAAsB,EAC3B,EACAiB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAjB,EAAE,sBAAsB,CAC3B,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EAEAkB,EAAiB,UAAY,CAC3B,QAASO,EAAU,KAAK,WACxB,OAAQA,EAAU,KAAK,WACvB,EAAGA,EAAU,KAAK,UACpB,ECxTY,MAACC,GAAgB,CAACC,EAAYC,IAAkB,CACtD,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAAS,YAAa,CAChC,WAAYD,EACZ,cAAeC,EACf,QAAS,cACf,CAAK,CAEL,EAGaC,GAAmB,CAACC,EAAaC,EAAiBC,EAAoB,CAAA,IAAO,CACpF,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAAS,QAAS,CAC5B,eAAgB,SAChB,YAAaF,EACb,gBAAiBC,EACjB,GAAGC,EACH,QAAS,cACf,CAAK,CAEL,EAGaC,GAAsB,CAACC,EAAWC,EAAeC,EAAU,KAAS,CAC3E,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAASA,EAAU,cAAgB,aAAc,CAC3D,eAAgB,OAChB,YAAaF,EACb,cAAeC,EACf,QAASC,EACT,QAAS,cACf,CAAK,CAEL,EAGaC,GAA6B,CAACC,EAAOC,IAAmB,CAC/D,OAAO,OAAW,KAAe,OAAO,OAE1C,OAAO,KAAK,QAAS,aAAc,CACjC,QAAS,eACT,eAAgB,UAChB,YAAa,0BACb,MAAO,EACP,SAAU,MACV,WAAYD,EACZ,eAAgBC,CACtB,CAAK,EAGD,OAAO,KAAK,QAAS,gBAAiB,CACpC,QAAS,eACT,eAAgB,kBAChB,YAAa,eACb,MAAO,EACP,SAAU,KAChB,CAAK,EAEL,EA4BaC,GAAmB,CAACC,EAAmBb,IAAkB,CAChE,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAAS,SAAU,CAC7B,eAAgB,kBAChB,YAAa,GAAGa,CAAiB,aACjC,kBAAmBA,EACnB,cAAeb,EACf,QAAS,cACf,CAAK,CAEL,EAGac,GAAkB,CAACC,EAAcf,IAAkB,CAC1D,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAAS,kBAAmB,CACtC,KAAM,iBACN,MAAOe,EACP,eAAgB,kBAChB,YAAaf,EACb,QAAS,cACf,CAAK,CAEL,EC7GA,SAAwBgB,IAAqB,CACrC,KAAA,CAAE,SAAAC,CAAS,EAAIC,EAAY,EAEjCvC,OAAAA,EAAAA,UAAU,IAAM,CACP,OAAA,SAAS,EAAG,CAAC,CAAA,EACnB,CAACsC,CAAQ,CAAC,EAEJ5B,EAAA,IAAAD,EAAA,SAAA,EAAA,CACX,CCRA,SAAwB+B,IAAmB,CACnC,KAAA,CAAE,KAAA1D,CAAK,EAAIY,EAAe,EAC1B,CAAC+C,EAAQC,CAAS,EAAI7C,EAAAA,SAAS,EAAK,EACpC8C,EAAcC,SAAO,IAAI,EACzB,CAACC,EAAUC,CAAW,EAAIjD,EAAA,SAC9B,OAAO,OAAW,IAAc,OAAO,YAAc,KAAO,EAC9D,EAGAG,EAAAA,UAAU,IAAM,CACd,MAAM+C,EAAe,IAAM,CACbD,EAAA,OAAO,YAAc,IAAI,CACvC,EAEO,cAAA,iBAAiB,SAAUC,CAAY,EACvC,IAAM,CACJ,OAAA,oBAAoB,SAAUA,CAAY,CACnD,CACF,EAAG,EAAE,EAGL/C,EAAAA,UAAU,IAAM,CACd,SAASgD,EAAmBC,EAAO,CAC7BN,EAAY,SAAW,CAACA,EAAY,QAAQ,SAASM,EAAM,MAAM,GACnEP,EAAU,EAAK,CACjB,CAGO,gBAAA,iBAAiB,YAAaM,CAAkB,EAClD,IAAM,CACF,SAAA,oBAAoB,YAAaA,CAAkB,CAC9D,CACF,EAAG,EAAE,EAEL,MAAME,EAAiB,IAAM,CAC3BR,EAAU,CAACD,CAAM,CACnB,EAEMU,EAAwBC,GAAa,CACzCtE,EAAK,eAAesE,CAAQ,EAC5BV,EAAU,EAAK,EAGX,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,kBAAmB,CACtC,eAAgB,mBAChB,YAAa,GAAG5D,EAAK,QAAQ,OAAOsE,CAAQ,GAC5C,cAAetE,EAAK,SACpB,YAAasE,EACb,QAAS,cAAA,CACV,CAEL,EAEMC,EAAkBvE,EAAK,UAAY,KAGzC,OAAI+D,EAECnC,EAAAA,IAAA,MAAA,CAAI,UAAU,qDACZ,SAAO,OAAA,QAAQ7B,CAAS,EAAE,IAAI,CAAC,CAACuE,EAAUE,CAAQ,IACjD5C,EAAA,IAAC,SAAA,CAEC,UAAW,yBACT0C,IAAaC,EACT,aACA,uBACN,GACA,QAAS,IAAMF,EAAqBC,CAAQ,EAC5C,aAAY,aAAaE,EAAS,IAAI,GAEtC,SAAC5C,EAAA,IAAA,OAAA,CAAK,UAAU,gDACb,SACH0C,CAAA,CAAA,CAAA,EAXKA,CAaR,CAAA,EACH,EAMD5C,EAAAA,KAAA,MAAA,CAAI,UAAU,sCAAsC,IAAKmC,EACxD,SAAA,CAAAjC,EAAA,IAAC,SAAA,CACC,UAAU,6DACV,QAASwC,EACT,gBAAeT,EACf,gBAAc,OAEd,SAAC/B,EAAA,IAAA,OAAA,CAAK,UAAU,+BAAgC,SAAgB2C,CAAA,CAAA,CAAA,CAClE,EAECZ,GACC/B,EAAA,IAAC,MAAI,CAAA,UAAU,uEACZ,SAAO,OAAA,QAAQ7B,CAAS,EAAE,IAAI,CAAC,CAACuE,EAAUE,CAAQ,IACjD5C,EAAA,IAAC,SAAA,CAEC,UAAW,qDACT0C,IAAaC,EAAkB,SAAW,EAC5C,GACA,QAAS,IAAMF,EAAqBC,CAAQ,EAG5C,SAAC1C,EAAAA,IAAA,OAAA,CAAK,UAAU,gBAAiB,WAAS,IAAK,CAAA,CAAA,EAP1C0C,CAAA,CASR,CACH,CAAA,CAAA,EAEJ,CAEJ,CC/GA,SAAwBG,EAAa,CACnC,KAAAC,EAAO,wCACT,EAAG,CACD,OAEI9C,MAAAD,EAAAA,SAAA,CAAA,SAAAC,EAAA,IAAC,OAAA,CACC,UAAU,wCACV,iBAAe,QACf,cAAY,OACZ,MAAO,CACL,eAAgB8C,EAAK,MAAM,GAAG,EAAE,OAChC,eAAgBA,EAAK,MAAM,EAAE,EAAE,OAC/B,WAAY,SACd,EAEC,SACEA,EAAA,KACA,EAAA,MAAM,GAAG,EACT,IAAI,CAACC,EAAKC,IACRlD,EAAA,KAAAmD,EAAM,SAAN,CACC,SAAA,CAAAjD,EAAA,IAAC,OAAA,CACC,UAAU,OACV,YAAU,OACV,MAAO,CAAE,eAAgBgD,CAAE,EAE1B,WAAI,MAAM,EAAE,EAAE,IAAI,CAACE,EAAMC,IACxBnD,EAAA,IAAC,OAAA,CAEC,UAAU,OACV,YAAU,IACV,MAAO,CAAE,eAAgBgD,EAAIG,CAAG,EAE/B,SAAAD,CAAA,EALIC,CAOR,CAAA,CAAA,CACH,EACCnD,EAAA,IAAA,OAAA,CAAK,UAAU,aAAa,SAAC,GAAA,CAAA,CAAA,CAAA,EAjBXgD,CAkBrB,CACD,CAAA,CAAA,EAEP,CAEJ,CAEAH,EAAa,UAAY,CACvB,KAAMrC,EAAU,MAClB,EC5CA,SAAwB4C,EAAkBC,EAAO,CAC/C/D,OAAAA,EAAAA,UAAU,IAAM,CACLgE,EAAA,SAAS,iBAAiB,aAAa,EAAG,CACjD,MAAO,EAAA,CACR,CACH,EAAG,EAAE,EAEHtD,EAAA,IAAC,MAAA,CAEE,GAAGqD,EAEH,SAAMA,EAAA,QAAA,CACT,CAEJ,CAEAD,EAAkB,UAAY,CAC5B,SAAU5C,EAAU,IACtB,ECCA,MAAM+C,EAAM,CAAC,CACX,MAAAC,EAAQ,iDACR,YAAAC,EAAc,kIACd,UAAAC,EAAY,uBACZ,MAAAC,EAAQ,oCACR,SAAAC,EAAW,oBACX,WAAAC,EAAa,OACb,YAAAC,EAAc,MACd,KAAAC,EAAO,UACP,OAAAC,EAAS,KACT,cAAAC,EAAgB,eAChB,YAAAC,EAAc,GACd,WAAAC,EAAa,GACb,OAAAC,EAAS,oBACT,SAAAC,EAAW,CACT,6BACA,MACA,eACA,YACF,EACA,OAAAC,EAAS,OACX,IAAM,CAEE,MAAAC,EAAiB,GAAGf,CAAK,uBAE/B,cACGgB,EAEC,CAAA,SAAA,CAAAxE,EAAAA,IAAC,SAAO,SAAeuE,CAAA,CAAA,EACtBvE,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAASyD,EAAa,EAC9CzD,EAAA,IAAA,OAAA,CAAK,IAAI,YAAY,KAAM0D,EAAW,EACvC1D,MAAC,QAAK,KAAK,WAAW,QAASqE,EAAS,KAAK,IAAI,EAAG,EAGnDrE,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAASuE,EAAgB,EAClDvE,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAASyD,EAAa,EACrDzD,EAAA,IAAA,OAAA,CAAK,SAAS,UAAU,QAAS+D,EAAM,EACvC/D,EAAA,IAAA,OAAA,CAAK,SAAS,SAAS,QAAS0D,EAAW,EAC3C1D,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAAS2D,EAAO,EACzC3D,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAAS4D,EAAU,EAChD5D,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAAS6D,EAAY,EACpD7D,EAAA,IAAA,OAAA,CAAK,SAAS,kBAAkB,QAAS8D,EAAa,EACtD9D,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAAQ,oBAAoB,EACzDA,EAAA,IAAA,OAAA,CAAK,SAAS,YAAY,QAASsE,EAAQ,EAG3CP,IAAS,WAAaG,GACrBlE,MAAC,QAAK,SAAS,yBAAyB,QAASkE,EAAa,EAE/DH,IAAS,WAAaI,GACrBnE,MAAC,QAAK,SAAS,wBAAwB,QAASmE,EAAY,EAE7DJ,IAAS,WAAaK,GACrBpE,MAAC,QAAK,SAAS,iBAAiB,QAASoE,EAAQ,EAIlDpE,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,sBAAsB,EACvDA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAASiE,EAAe,EACjDjE,EAAA,IAAA,OAAA,CAAK,KAAK,kBAAkB,QAASiE,EAAe,EACpDjE,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAASuE,EAAgB,EACnDvE,EAAA,IAAA,OAAA,CAAK,KAAK,sBAAsB,QAASyD,EAAa,EACtDzD,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAAS2D,EAAO,EAC1C3D,EAAA,IAAA,OAAA,CAAK,KAAK,oBAAoB,QAAS4D,EAAU,EAGjD5D,EAAA,IAAA,OAAA,CAAK,KAAK,SAAS,QAAQ,yCAAyC,EACpEA,EAAA,IAAA,OAAA,CAAK,KAAK,YAAY,QAAQ,gBAAgB,EAC9CA,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,QAAQ,2BAA2B,EACjEA,EAAA,IAAA,OAAA,CAAK,KAAK,WAAW,QAAQ,UAAU,EAGxCA,EAAA,IAAC,OAAA,CACC,KAAK,WACL,QAAQ,0DAAA,CACV,EACCA,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAAQ,UAAU,EAC1CA,EAAA,IAAA,OAAA,CAAK,KAAK,+BAA+B,QAAQ,MAAM,EACxDA,EAAA,IAAC,OAAA,CACC,KAAK,wCACL,QAAQ,mBAAA,CACV,EAGCgE,GAAU,MAAM,QAAQA,CAAM,EAE7BA,EAAO,IAAI,CAACS,EAAYC,IACrB1E,EAAAA,IAAA,SAAA,CAAmB,KAAK,sBACtB,SAAK,KAAA,UAAUyE,CAAU,CAAA,EADfC,CAEb,CACD,EACCV,QAED,SAAO,CAAA,KAAK,sBAAuB,SAAK,KAAA,UAAUA,CAAM,CAAE,CAAA,EACzD,IAAA,EACN,CAEJ,EAEAT,EAAI,UAAY,CACd,MAAO/C,EAAU,OACjB,YAAaA,EAAU,OACvB,UAAWA,EAAU,OACrB,MAAOA,EAAU,OACjB,SAAUA,EAAU,OACpB,WAAYA,EAAU,OACtB,YAAaA,EAAU,OACvB,KAAMA,EAAU,OAChB,OAAQA,EAAU,UAAU,CAC1BA,EAAU,OACVA,EAAU,QAAQA,EAAU,MAAM,CAAA,CACnC,EACD,cAAeA,EAAU,OACzB,YAAaA,EAAU,OACvB,WAAYA,EAAU,OACtB,OAAQA,EAAU,OAClB,SAAUA,EAAU,QAAQA,EAAU,MAAM,EAC5C,OAAQA,EAAU,MACpB,EC3IA,MAAMmE,UAAsB1B,EAAM,SAAU,CAC1C,YAAYI,EAAO,CACjB,MAAMA,CAAK,EACX,KAAK,MAAQ,CAAE,SAAU,GAAO,MAAO,IAAK,CAAA,CAG9C,OAAO,yBAAyBuB,EAAO,CAE9B,MAAA,CAAE,SAAU,GAAM,MAAAA,CAAM,CAAA,CAGjC,kBAAkBA,EAAOC,EAAW,CAE1B,QAAA,MAAM,4BAA6BD,EAAOC,CAAS,CAAA,CAG7D,QAAS,CACH,OAAA,KAAK,MAAM,SAGX/E,EAAA,KAAC,MAAA,CACC,MAAO,CACL,QAAS,OACT,UAAW,SACX,MAAO,OACP,gBAAiB,UACjB,UAAW,QACX,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,cAAe,QACjB,EAEA,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAoB,sBAAA,CAAA,EACxBA,EAAAA,IAAC,KAAE,SAAyB,2BAAA,CAAA,EAC5BA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,OAAO,SAAS,OAAO,EACtC,MAAO,CACL,QAAS,YACT,gBAAiB,UACjB,MAAO,QACP,OAAQ,OACR,aAAc,MACd,OAAQ,UACR,UAAW,MACb,EACD,SAAA,aAAA,CAAA,CAED,CAAA,CACF,EAIG,KAAK,MAAM,QAAA,CAEtB,CAEA2E,EAAc,UAAY,CACxB,SAAUnE,EAAU,KAAK,UAC3B,EC1DwB,SAAAsE,EAAc,CAAE,KAAAC,GAAQ,CAE5C,OAAA/E,EAAAA,IAACgF,EACC,CAAA,SAAAlF,EAAAA,KAAC0E,EACC,CAAA,SAAA,CAACxE,EAAAA,IAAA,QAAA,CAAO,0BAAM,KAAM,CAAA,QACnB,OAAK,CAAA,KAAK,cAAc,QAAS+E,GAAA,YAAAA,EAAM,WAAa,CAAA,CAAA,CAAA,CACvD,CACF,CAAA,CAEJ,CAEAD,EAAc,UAAY,CACxB,KAAMtE,EAAU,MAAM,CACpB,MAAOA,EAAU,OACjB,YAAaA,EAAU,MACxB,CAAA,CACH,ECpBO,MAAMyE,EAAiB,CAC5B,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,iBACP,MAAO,wBACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,YACP,MAAO,2BACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,MAAO,kBACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,iBACP,MAAO,wBACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,YACP,MAAO,2BACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,MAAO,kBACR,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,EACJ,UAAW,0CACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,UACd,EACD,CACE,GAAI,EACJ,UAAW,gCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,iBACP,YAAa,eACd,EACD,CACE,GAAI,EACJ,UAAW,uCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,kBACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,uBACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,gCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,gBACP,YAAa,UACd,EACD,CACE,GAAI,GACJ,UAAW,2BACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,eACP,YACE,wHACF,KAAM,6BACN,WAAY,CAAC,aAAa,CAC3B,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,iBACP,YACE,mIACF,KAAM,6BACN,WAAY,CAAC,UAAU,CACxB,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,kBACP,YACE,sIACF,KAAM,6BACN,WAAY,CAAC,SAAU,aAAa,CACrC,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,YACP,YACE,wIACF,KAAM,6BACN,WAAY,CAAC,WAAY,QAAQ,CAClC,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,eACP,YACE,+IACF,KAAM,6BACN,WAAY,CAAC,SAAU,aAAa,CACrC,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,eACP,YACE,6HACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,iBACP,YACE,oIACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,kBACP,YACE,4HACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,iBACP,YACE,6HACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,YACP,YACE,+HACH,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,SAAU,wDACV,MAAO,4DACP,OAAQ,MACR,YAAa,uBACd,EACD,CACE,GAAI,GACJ,SAAU,wDACV,MACE,uEACF,OAAQ,KACR,YAAa,gCACd,EACD,CACE,GAAI,GACJ,SAAU,wDACV,MAAO,iEACP,OAAQ,OACR,YAAa,iCACd,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,eACP,KAAM,WACN,WAAY,CAAC,aAAa,CAC3B,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,iBACP,KAAM,gBACN,WAAY,CAAC,WAAY,QAAQ,CAClC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,kBACP,KAAM,gBACN,WAAY,CAAC,UAAU,CACxB,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,YACP,KAAM,gBACN,WAAY,CAAC,SAAU,aAAa,CACrC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,eACP,KAAM,gBACN,WAAY,CAAC,QAAQ,CACtB,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,gBACP,KAAM,WACN,WAAY,CAAC,SAAU,UAAU,CAClC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,iBACP,KAAM,gBACN,WAAY,CAAC,WAAY,QAAQ,CAClC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,eACP,KAAM,WACN,WAAY,CAAC,aAAa,CAC3B,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,WAAY,CAAC,aAAa,EAC1B,OAAQ,oDACR,MAAO,eACP,YAAa,WACb,SAAU,GACV,aAAc,yDACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,WAAY,QAAQ,EACjC,OAAQ,oDACR,MAAO,iBACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,UAAU,EACvB,OAAQ,oDACR,MAAO,kBACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,aAAa,EACpC,OAAQ,oDACR,MAAO,YACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,QAAQ,EACrB,OAAQ,oDACR,MAAO,eACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,UAAU,EACjC,OAAQ,oDACR,MAAO,gBACP,YAAa,WACb,SAAU,GACV,aAAc,yDACf,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,WAAY,CAAC,aAAa,EAC1B,OAAQ,uDACR,MAAO,eACP,YAAa,WACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,WAAY,QAAQ,EACjC,OAAQ,uDACR,MAAO,iBACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,UAAU,EACvB,OAAQ,uDACR,MAAO,kBACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,aAAa,EACpC,OAAQ,uDACR,MAAO,YACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,QAAQ,EACrB,OAAQ,uDACR,MAAO,eACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,UAAU,EACjC,OAAQ,uDACR,MAAO,gBACP,YAAa,WACb,aAAc,IACf,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,eACP,WAAY,mBACZ,MAAO,aACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,qBACP,WAAY,mBACZ,MAAO,UACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,kBACP,WAAY,gCACZ,MAAO,YACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,oBACP,WAAY,mBACZ,MAAO,UACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,eACP,WAAY,sBACZ,MAAO,aACR,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,aAAa,EACjC,OAAQ,oDACR,OAAQ,mBACR,MAAO,eACP,YAAa,WACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,QAAS,UAAW,MAAO,WAAY,QAAQ,EAC5D,OAAQ,oDACR,OAAQ,mBACR,MAAO,iBACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,UAAU,EAC9B,OAAQ,oDACR,OAAQ,mBACR,MAAO,kBACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,SAAU,aAAa,EAC3C,OAAQ,oDACR,OAAQ,mBACR,MAAO,YACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,QAAQ,EAC5B,OAAQ,oDACR,OAAQ,mBACR,MAAO,eACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,SAAU,UAAU,EACxC,OAAQ,oDACR,OAAQ,mBACR,MAAO,gBACP,YAAa,WACb,WAAY,EACb,CACH,EACaC,EAAe,CAC1B,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,iBACP,YAAa,wBACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,YACP,YAAa,2BACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,YAAa,kBACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,kBACP,YAAa,wBACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,YAAa,2BACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,gBACP,YAAa,kBACd,CACH,EAEaC,GAAe,CAC1B,CACE,GAAI,GACJ,MAAO,4DACP,SAAU,wDACV,OAAQ,MACR,YAAa,uBACd,EACD,CACE,GAAI,GACJ,MACE,uEACF,SAAU,wDACV,OAAQ,KACR,YAAa,gCACd,EACD,CACE,GAAI,GACJ,MAAO,iEACP,SAAU,wDACV,OAAQ,OACR,YAAa,iCACd,EACD,CACE,GAAI,GACJ,MACE,uEACF,SAAU,wDACV,OAAQ,KACR,YAAa,gCACd,EACD,CACE,GAAI,GACJ,MAAO,iEACP,SAAU,wDACV,OAAQ,OACR,YAAa,iCACd,EACD,CACE,GAAI,GACJ,MAAO,4DACP,SAAU,wDACV,OAAQ,MACR,YAAa,uBACd,CACH,EAEaC,GAAe,CAC1B,CACE,GAAI,GACJ,UAAW,4BACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,UACd,EACD,CACE,GAAI,GACJ,UAAW,gCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,iBACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,0BACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,kBACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,uBACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,gCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,gBACP,YAAa,UACd,EACD,CACE,GAAI,GACJ,UAAW,2BACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,CACH,EAEaC,GAAe,CAC1B,CACE,GAAI,GACJ,KAAM,WACN,IAAK,cACL,KAAM,8CACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,aACP,MAAO,UACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,kBACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,iBACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,WACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,eACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,qBACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,cACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,SACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,cACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,kBACL,KAAM,8CACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,WACP,MAAO,UACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,WACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,eACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,qBACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,cACP,MAAO,eACR,CACH,EAEaC,GAAgB,CAC3B,GAAGd,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,GACH,GAAGC,GACH,GAAGC,EACL,EC3yBaE,GAAa,CACxB,CAAE,GAAI,EAAG,KAAM,WAAY,MAAO,CAAG,EACrC,CAAE,GAAI,EAAG,KAAM,SAAU,MAAO,EAAI,EACpC,CAAE,GAAI,EAAG,KAAM,cAAe,MAAO,CAAG,EACxC,CAAE,GAAI,EAAG,KAAM,cAAe,MAAO,CAAG,EACxC,CAAE,GAAI,EAAG,KAAM,QAAS,MAAO,CAAG,CACpC,ECNaC,GAAO,CAClB,CAAE,GAAI,EAAG,KAAM,QAAU,EACzB,CAAE,GAAI,EAAG,KAAM,WAAa,EAC5B,CAAE,GAAI,EAAG,KAAM,SAAW,EAC1B,CAAE,GAAI,EAAG,KAAM,UAAY,EAC3B,CAAE,GAAI,EAAG,KAAM,OAAS,EACxB,CAAE,GAAI,EAAG,KAAM,OAAS,EACxB,CAAE,GAAI,EAAG,KAAM,SAAW,EAC1B,CAAE,GAAI,EAAG,KAAM,MAAQ,CACzB,ECTaC,GAAe,CAC1B,CAAE,GAAI,EAAG,KAAM,eAAiB,EAChC,CAAE,GAAI,EAAG,KAAM,cAAgB,EAC/B,CAAE,GAAI,EAAG,KAAM,eAAiB,CAClC,ECDwB,SAAAC,GAAW,CAAE,UAAAC,GAAa,CAChD,KAAM,CAACC,EAAYC,CAAa,EAAInH,EAAAA,SAAS,CAAC,EAGxCoH,EAAoBC,GAAS,CACjCF,EAAcE,CAAI,CACpB,EAGE,OAAA1G,EAAA,KAAC,MAAA,CACC,UAAWsG,GAAwB,oCAGnC,SAAA,CAAAtG,EAAA,KAAC,IAAA,CACC,QAAS,IAAMuG,EAAa,GAAKE,EAAiBF,EAAa,CAAC,EAChE,UAAWA,IAAe,EAAI,WAAa,GAE3C,SAAA,CAACrG,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAa,eAAA,CAAA,CAAA,CAAA,CACjD,EAGAA,EAAA,IAAC,IAAA,CACC,QAAS,IAAMuG,EAAiB,CAAC,EACjC,UAAWF,IAAe,EAAI,SAAW,GAC1C,SAAA,GAAA,CAED,EAGArG,EAAA,IAAC,IAAA,CACC,QAAS,IAAMuG,EAAiB,CAAC,EACjC,UAAWF,IAAe,EAAI,SAAW,GAC1C,SAAA,GAAA,CAED,EAGArG,EAAA,IAAC,IAAA,CACC,QAAS,IAAMuG,EAAiB,CAAC,EACjC,UAAWF,IAAe,EAAI,SAAW,GAC1C,SAAA,GAAA,CAED,EAECA,EAAa,GAAKA,EAAa,SAC7B,OAAK,CAAA,UAAU,YAAY,SAAG,KAAA,CAAA,EAGhCA,EAAa,GAAKA,EAAa,SAC7B,IAAE,CAAA,UAAW,SAAW,SAAWA,CAAA,CAAA,EAIrCrG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAG,MAAA,EAC9BqG,GAAc,GAAKrG,EAAAA,IAAC,IAAE,CAAA,UAAW,SAAW,SAAE,EAAA,EAE/CA,EAAA,IAAC,IAAA,CACC,QAAS,IAAMuG,EAAiB,CAAC,EACjC,UAAWF,IAAe,EAAI,SAAW,GAC1C,SAAA,GAAA,CAED,EAGAvG,EAAA,KAAC,IAAA,CACC,QAAS,IAAMuG,EAAa,GAAKE,EAAiBF,EAAa,CAAC,EAChE,UAAWA,IAAe,EAAI,WAAa,GAE3C,SAAA,CAACrG,EAAAA,IAAA,IAAA,CAAE,UAAU,kBAAmB,CAAA,EAC/BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,CAC7C,CAAA,CACF,CAEJ,CAEAmG,GAAW,UAAY,CACrB,UAAW3F,EAAU,MACvB,EC/DA,MAAMiG,GAAkB,CAAC,CACvB,MAAAjD,EACA,YAAAC,EACA,KAAAiD,EAAO,GACP,KAAA3C,EAAO,UACP,OAAAC,EAAS,KACT,SAAAK,EAAW,CAAC,EACZ,MAAAV,EAAQ,oCACR,cAAAgD,EAAgB,IAClB,IAAM,CACE,KAAA,CAAE,gBAAAhE,CAAgB,EAAI3D,EAAe,EAGrC4H,EAAU,uBAeVC,EAbAF,GAIS,CACX,GAAI,GAAGC,CAAO,GAAGF,EAAO,IAAIA,CAAI,GAAK,EAAE,GACvC,GAAI,GAAGE,CAAO,MAAMF,EAAO,IAAIA,CAAI,GAAK,EAAE,GAC1C,GAAI,GAAGE,CAAO,MAAMF,EAAO,IAAIA,CAAI,GAAK,EAAE,EAC5C,EAMII,EAAaD,EAAalE,CAAe,EAGzCoE,EAA0B,IAAM,CACpC,MAAMC,EAAY,CAChB,GAAI,QACJ,GAAI,QACJ,GAAI,OACN,EAEMC,EAAc,CAClB,GAAI,UACJ,GAAI,WACJ,GAAI,QACN,EAEO,MAAA,CACL,OAAQD,EAAUrE,CAAe,GAAK,QACtC,SAAUsE,EAAYtE,CAAe,GAAK,SAC5C,CACF,EAEM,CAAE,OAAA2B,EAAQ,SAAA4C,CAAS,EAAIH,EAAwB,EAG/CxC,EAAiB,GAAGf,CAAK,eAE/B,cACGgB,EAEC,CAAA,SAAA,CAAAxE,EAAAA,IAAC,SAAO,SAAeuE,CAAA,CAAA,EACtBvE,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAASyD,EAAa,EAC9CzD,EAAA,IAAA,OAAA,CAAK,IAAI,YAAY,KAAM8G,EAAY,EACxC9G,MAAC,QAAK,KAAK,WAAW,QAASqE,EAAS,KAAK,IAAI,EAAG,EACnDrE,EAAA,IAAA,OAAA,CAAK,KAAK,WAAW,QAASkH,EAAU,EAGxC,OAAO,QAAQL,CAAY,EAAE,IAAI,CAAC,CAACM,EAAMC,CAAG,IAC1CpH,EAAA,IAAA,OAAA,CAAgB,IAAI,YAAY,SAAUmH,EAAM,KAAMC,CAAA,EAA5CD,CAAiD,CAC7D,EAGDnH,MAAC,QAAK,IAAI,YAAY,SAAS,YAAY,KAAM6G,EAAa,GAAI,EAGjE7G,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAASuE,EAAgB,EAClDvE,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAASyD,EAAa,EACrDzD,EAAA,IAAA,OAAA,CAAK,SAAS,UAAU,QAAS+D,EAAM,EACvC/D,EAAA,IAAA,OAAA,CAAK,SAAS,SAAS,QAAS8G,EAAY,EAC5C9G,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAAS2D,EAAO,EACzC3D,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAASwD,EAAO,EAC7CxD,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAAQ,OAAO,EAC9CA,EAAA,IAAA,OAAA,CAAK,SAAS,kBAAkB,QAAQ,MAAM,EAC9CA,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAAQ,YAAY,EACjDA,EAAA,IAAA,OAAA,CAAK,SAAS,YAAY,QAASsE,EAAQ,EAG3C,OAAO,KAAKuC,CAAY,EACtB,OAAQM,GAASA,IAASxE,CAAe,EACzC,IAAKwE,GAAS,CACb,MAAME,EAAY,CAChB,GAAI,QACJ,GAAI,QACJ,GAAI,SACJF,CAAI,EAEJ,OAAAnH,EAAA,IAAC,OAAA,CAEC,SAAS,sBACT,QAASqH,CAAA,EAFJF,CAGP,CAAA,CAEH,EAGFnH,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,sBAAsB,EACvDA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,eAAe,EAChDA,EAAA,IAAA,OAAA,CAAK,KAAK,kBAAkB,QAAQ,eAAe,EACnDA,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAASuE,EAAgB,EACnDvE,EAAA,IAAA,OAAA,CAAK,KAAK,sBAAsB,QAASyD,EAAa,EACtDzD,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAAS2D,EAAO,EAC1C3D,EAAA,IAAA,OAAA,CAAK,KAAK,oBAAoB,QAASwD,EAAO,EAG9CxD,EAAA,IAAA,OAAA,CAAK,KAAK,SAAS,QAAQ,yCAAyC,EACpEA,EAAA,IAAA,OAAA,CAAK,KAAK,YAAY,QAAQ,gBAAgB,EAC9CA,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,QAAQ,2BAA2B,EACjEA,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,QAAS2C,EAAiB,EAG7D3C,EAAA,IAAC,OAAA,CACC,KAAK,WACL,QAAQ,0DAAA,CACV,EACCA,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAAQ,UAAU,EAC1CA,EAAA,IAAA,OAAA,CAAK,KAAK,+BAA+B,QAAQ,MAAM,EACxDA,EAAA,IAAC,OAAA,CACC,KAAK,wCACL,QAAQ,mBAAA,CACV,EAGCgE,GACEhE,EAAAA,IAAA,SAAA,CAAO,KAAK,sBACV,cAAK,UAAU,CACd,GAAGgE,EACH,WAAY,qBACZ,WAAYrB,EACZ,IAAKmE,CAAA,CACN,CACH,CAAA,CAAA,EAEJ,CAEJ,EAEAL,GAAgB,UAAY,CAC1B,MAAOjG,EAAU,OAAO,WACxB,YAAaA,EAAU,OAAO,WAC9B,KAAMA,EAAU,OAChB,KAAMA,EAAU,OAChB,OAAQA,EAAU,OAClB,SAAUA,EAAU,QAAQA,EAAU,MAAM,EAC5C,MAAOA,EAAU,OACjB,cAAeA,EAAU,MAC3B,ECtKA,SAAwB8G,IAAkB,CAEtC,OAAAxH,EAAA,KAAC,MAAI,CAAA,UAAU,qBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,6BACb,SAAAA,EAAA,IAAC,MAAG,UAAU,sBAAsB,4BAAgB,CACtD,CAAA,EAEAA,EAAA,IAAC,KAAA,CACC,UAAU,sDACV,GAAG,YAGH,gBAACuH,EAEE,CAAA,SAAA,CAAAjC,EAAY,MAAM,EAAG,CAAC,EAAE,IAAI,CAACkC,EAAM9C,IAClC1E,EAAA,IAAC,KAAA,CAEC,UAAW,iBAAiBwH,EAAK,WAAW,KAAK,GAAG,CAAC,GAEpD,SAAAA,EAAK,OAAS,WACbxH,EAAA,IAACyH,EAAA,CACC,SAAUD,EAAK,SACf,UAAWA,EAAK,SAChB,MAAO,IACP,OAAQ,IAEP,SAAA,CAAC,CAAE,IAAAE,EAAK,KAAAC,CAAK,WACX,IAAE,CAAA,QAASA,EAAM,UAAU,+BAC1B,SAAA,CAAC7H,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,CAAA,EAE5CA,EAAA,IAAC,MAAA,CACC,IAAKwH,EAAK,SACV,IAAAE,EACA,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACA5H,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAAwH,EAAK,MAAM,EACtCxH,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EAIJF,EAAA,KAAC8H,EAAA,CACC,GAAI,6BAA6BJ,EAAK,EAAE,GACxC,UAAU,gBAEV,SAAA,CAAC1H,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,aAAc,CAAA,EAC7BA,EAAA,IAAC,MAAA,CACC,IAAKwH,EAAK,SACV,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACA1H,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAAwH,EAAK,MAAM,EACtCxH,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,EAhDG0E,CAAA,CAmDR,EAAG,GAAA,CACN,CAAA,CAAA,CAAA,CAEF,EAEF,CAEJ,CC9EA,MAAMmD,GAAW,CACf,CACE,OAAQ,WACR,KAAM,uBACN,KAAM,uGACN,OAAQ,iCACR,QAAS,CACP,CACE,OAAQ,WACR,KAAM,uBACN,KAAM,uGACN,OAAQ,gCAAA,CACV,CAEJ,EACA,CACE,OAAQ,eACR,KAAM,uBACN,KAAM,uGACN,OAAQ,iCACR,QAAS,CAAA,CACX,EACA,CACE,OAAQ,WACR,KAAM,sBACN,KAAM,uGACN,OAAQ,iCACR,QAAS,CAAA,CAAC,CAEd,EAEA,SAAwBC,IAAW,CAE/B,OAAA9H,EAAA,IAAAD,EAAA,SAAA,CACG,YAAS,IAAI,CAACgI,EAAS/E,IACtBlD,EAAA,KAAC,KAAW,CAAA,UAAU,qBACpB,SAAA,CAAAE,EAAA,IAAC,IAAE,CAAA,UAAU,cAAc,KAAK,IAC9B,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,IAAK+H,EAAQ,OACb,IAAI,GACJ,MAAO,GACP,OAAQ,EAAA,CAAA,EAEZ,EACAjI,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC,KAAE,KAAK,IAAK,SAAQ+H,EAAA,MAAA,CAAO,CAC9B,CAAA,EACCA,EAAQ,KAAK,IAAE/H,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,IAAA,EAC5CF,EAAAA,KAAC,IAAE,CAAA,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAAE,QAAA,CAEjC,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,IAAG,CAAA,SAAA+H,EAAQ,IAAK,CAAA,EAChBA,EAAQ,SACPA,EAAQ,QAAQ,OAAS,GACzBA,EAAQ,QAAQ,IAAI,CAACC,EAAOtD,IACzB5E,EAAA,KAAA,MAAA,CAAgB,UAAU,qBACzB,SAAA,CAAAE,EAAA,IAAC,IAAE,CAAA,UAAU,cAAc,KAAK,IAC9B,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,IAAKgI,EAAM,OACX,IAAI,GACJ,MAAO,IACP,OAAQ,GAAA,CAAA,EAEZ,EACAlI,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC,KAAE,KAAK,IAAK,SAAMgI,EAAA,MAAA,CAAO,CAC5B,CAAA,EACCA,EAAM,KAAK,IAAEhI,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,IAAA,EAC1CF,EAAAA,KAAC,IAAE,CAAA,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAAE,QAAA,CAEjC,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,IAAG,CAAA,SAAAgI,EAAM,IAAK,CAAA,CAAA,CACjB,CAAA,CAAA,CAAA,EAtBQtD,CAuBV,CACD,CAAA,CACL,CAAA,CAAA,GAlDO1B,CAmDT,CACD,EACH,CAEJ,CC1FA,SAAwBiF,IAAO,CAE3B,OAAAnI,OAAC,QAAK,UAAU,OAAO,SAAWQ,GAAMA,EAAE,eAAA,EACxC,SAAA,CAACR,EAAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBAEb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,QAAQ,OAAO,SAAM,SAAA,EAC5BA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,OACL,GAAG,OACH,UAAU,8BACV,YAAY,kBACZ,UAAW,IACX,SAAQ,GACR,gBAAc,MAAA,CAAA,CAChB,EACF,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,WAEb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,QAAQ,QAAQ,SAAO,UAAA,EAC9BA,EAAA,IAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAY,mBACZ,UAAW,IACX,SAAQ,GACR,gBAAc,MAAA,CAAA,CAChB,CACF,CAAA,CAAA,EACF,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,QAAQ,UAAU,SAAO,UAAA,EAChCA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,UACL,GAAG,UACH,UAAU,8BACV,YAAY,qBACZ,UAAW,GAAA,CAAA,CACb,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,QAAQ,UAAU,SAAO,UAAA,EAChCA,EAAA,IAAC,WAAA,CACC,KAAK,UACL,GAAG,UACH,UAAU,8BACV,KAAM,EACN,YAAY,qBACZ,UAAW,IACX,aAAc,EAAA,CAAA,CAChB,EACF,EAEAF,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAU,wDACV,oBAAkB,IAElB,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oCAAoC,SAAA,CAAA,eACrC,IACbE,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACCF,EAAA,KAAA,OAAA,CAAK,UAAU,kCAAkC,cAAY,OAAO,SAAA,CAAA,eACtD,IACbE,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CACH,CAAA,CAAA,CAAA,CACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,wDAAwD,SAAA,CAAA,sEACD,IACnEE,EAAA,IAAA,IAAA,CAAE,KAAK,IAAI,SAAsB,qBAAA,EAAI,OAAK,IAC1CA,EAAA,IAAA,IAAA,CAAE,KAAK,IAAI,SAAc,iBAAA,EAAI,GAAA,CAChC,CAAA,CAAA,EACF,CAEJ,CC2pBO,MAAMkI,GAAc,CACzB,CACE,GAAI,GACJ,OAAQ,+CACR,MAAO,8BACP,OAAQ,UACT,EACD,CACE,GAAI,GACJ,OAAQ,+CACR,MAAO,oDACP,OAAQ,UACT,EACD,CACE,GAAI,GACJ,OAAQ,+CACR,MAAO,qCACP,OAAQ,UACT,EACD,CACE,GAAI,GACJ,OAAQ,+CACR,MAAO,yCACP,OAAQ,UACT,EACD,CACE,GAAI,GACJ,OAAQ,+CACR,MAAO,uDACP,OAAQ,UACT,CACH,ECnxBaL,GAAW,CACtB,CACE,GAAI,EACJ,OAAQ,WACR,MAAO,4BACR,EACD,CACE,GAAI,EACJ,OAAQ,eACR,MAAO,sCACR,EACD,CACE,GAAI,EACJ,OAAQ,WACR,MAAO,6BACR,EACD,CACE,GAAI,EACJ,OAAQ,YACR,MAAO,uCACR,EACD,CACE,GAAI,EACJ,OAAQ,WACR,MAAO,8BACR,CACH,ECjBA,SAAwBM,GAAQ,CAC9B,iBAAAC,EAAmB,iDACrB,EAAG,CACD,OAEItI,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,MAAC,OAAI,UAAU,SACb,SAACA,EAAAA,IAAA,OAAA,CAAK,SAAWM,GAAMA,EAAE,iBAAkB,UAAU,OACnD,SAACR,OAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,UAAU,wBACV,KAAK,SACL,MAAM,eAEN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,mBAAoB,CAAA,EAChCA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAY,cAAA,CAAA,CAAA,CAAA,CAChD,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAWoI,EACX,YAAY,YACZ,SAAQ,EAAA,CAAA,CACV,CACF,CAAA,CACF,CAAA,EACF,EAGAtI,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAU,aAAA,EACtCA,EAAA,IAAA,MAAA,CAAI,UAAU,cACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,wBACX,SAAWgG,GAAA,IAAKqC,UACd,KACC,CAAA,SAAA,CAAArI,MAAC,KAAE,KAAK,IAAI,MAAM,GACf,WAAS,KACZ,SACC,QAAM,CAAA,SAAA,CAAA,MAAIqI,EAAS,MAAM,GAAA,CAAC,CAAA,CAAA,CAAA,EAJpBA,EAAS,EAKlB,CACD,CACH,CAAA,CACF,CAAA,CAAA,EACF,EAGAvI,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAI,OAAA,EACjCA,EAAAA,IAAC,OAAI,UAAU,cACb,eAAC,MAAI,CAAA,UAAU,OACZ,SAAAiG,GAAK,IAAKqC,GACRtI,EAAA,IAAA,IAAA,CAAE,KAAK,IACL,SAAAsI,EAAI,MADUA,EAAI,EAErB,CACD,CACH,CAAA,CACF,CAAA,CAAA,EACF,EAGAxI,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAY,eAAA,QACxC,MAAI,CAAA,UAAU,cACb,SAAAA,MAAC,MAAG,UAAU,yBACX,SAAYkI,GAAA,IAAI,CAACK,EAAM7D,IACrB5E,OAAA,KAAA,CAAe,UAAU,WACxB,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,KAAK,IACN,SAAAA,EAAA,IAAC,MAAA,CACC,IAAKuI,EAAK,OACV,OAAQ,IACR,MAAO,CAAE,OAAQ,aAAc,EAC/B,IAAI,GACJ,MAAO,IACP,UAAU,kBAAA,CAAA,EAEd,EACAzI,EAAAA,KAAC,MAAI,CAAA,UAAU,qBACb,SAAA,CAAAE,MAAC,KAAE,KAAK,IAAI,MAAM,GACf,WAAK,MACR,SACC,OAAK,CAAA,SAAA,CAAA,aAAWuI,EAAK,MAAA,CAAO,CAAA,CAAA,CAC/B,CAAA,CAAA,GAhBO7D,CAiBT,CACD,CAAA,CACH,CACF,CAAA,CAAA,EACF,EAGA5E,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAQ,WAAA,EACpCA,MAAA,MAAA,CAAI,UAAU,cACb,eAAC,KAAG,CAAA,UAAU,4BACX,SAAA6H,GAAS,IAAI,CAACE,EAASrD,WACrB,KACE,CAAA,SAAA,CAAQqD,EAAA,OAAO,MAAI,UACnB,IAAE,CAAA,KAAK,IAAI,MAAM,GACf,WAAQ,KACX,CAAA,CAAA,GAJOrD,CAKT,CACD,CAAA,CACH,CACF,CAAA,CAAA,EACF,EAGA5E,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAW,cAAA,QACvC,MAAI,CAAA,UAAU,cACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,IAAI,+CACJ,OAAQ,IACR,IAAI,oBACJ,MAAO,CAAE,OAAQ,aAAc,EAC/B,MAAO,IACP,UAAU,eAAA,CACZ,EAAE,oMAAA,CAAA,CAIJ,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,eAAe,SAAO,UAAA,EACpCA,EAAA,IAAC,MAAI,CAAA,UAAU,cACb,SAAAA,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACX,SAAAkG,GAAa,IAAKsC,GACjBxI,MAAC,KACC,CAAA,SAAAA,EAAA,IAAC,IAAE,CAAA,KAAK,IAAI,MAAM,GACf,SAAAwI,EAAK,IACR,CAAA,CAAA,EAHOA,EAAK,EAId,CACD,CAAA,CACH,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,CAEAL,GAAQ,UAAY,CAClB,iBAAkB3H,EAAU,MAC9B,ECrJA,SAAwBiI,IAAM,CAC5B,KAAM,CAACC,EAASC,CAAU,EAAIxJ,EAAAA,SAAS,EAAK,EACtC,CAAE,EAAAJ,CAAE,EAAIC,EAAe,EAC7B,OAEIc,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,MAAC,IAAE,CAAA,KAAK,IAAI,UAAW,eAAe0I,EAAU,YAAc,EAAE,GAC9D,SAAC5I,EAAA,KAAA,MAAA,CAAI,UAAU,+BAA+B,cAAY,OACxD,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,UACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,UACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,QAAS,IAAM6I,EAAYC,GAAQ,CAACA,CAAG,EAAG,UAAU,UACtD,SAAA,CAAA7J,EAAE,kBAAkB,EAAE,IAACiB,EAAAA,IAAC,IAAE,CAAA,UAAU,cAAe,CAAA,CAAA,EACtD,EACAF,EAAAA,KAAC,MAAI,CAAA,QAAS,IAAM6I,EAAYC,GAAQ,CAACA,CAAG,EAAG,UAAU,WACtD,SAAA,CAAA7J,EAAE,mBAAmB,EAAE,IAACiB,EAAAA,IAAC,IAAE,CAAA,UAAU,eAAgB,CAAA,CAAA,CACxD,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAA,EAAA,IAAC,SAAA,CACC,IAAI,sRACJ,MAAO,IACP,OAAQ,IACR,QAAQ,OACR,MAAO,CAAE,OAAQ,CAAE,EACnB,gBAAgB,GAChB,cAAY,QACZ,SAAU,CAAA,CAAA,CACZ,EACF,CAEJ,CClCA,MAAM6I,GAAc,CAAC,CAAE,SAAAC,EAAU,MAAAtF,KAAY,CAC3C,MAAMuF,EAAWC,EAAY,EACvBC,EAAWpH,EAAY,EACvB,CAACqH,EAAkBC,CAAmB,EAAIhK,EAAAA,SAAS,EAAK,EACxD,CAACiK,EAAmBC,CAAoB,EAAIlK,EAAAA,SAAS,EAAK,EAC1D,CAACmK,EAAkBC,CAAmB,EAAIpK,EAAAA,SAAS,EAAK,EACxD8C,EAAcC,SAAO,IAAI,EAG/B5C,EAAAA,UAAU,IAAM,CACR,MAAAgD,EAAsBC,GAAU,CAChCN,EAAY,SAAW,CAACA,EAAY,QAAQ,SAASM,EAAM,MAAM,GACnEgH,EAAoB,EAAK,CAE7B,EAES,gBAAA,iBAAiB,YAAajH,CAAkB,EAClD,IAAM,CACF,SAAA,oBAAoB,YAAaA,CAAkB,CAC9D,CACF,EAAG,EAAE,EAEL,MAAMkH,EAAe,IAAM,CACzB,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnCT,EAAS,QAAQ,EACjBQ,EAAoB,EAAK,CAC3B,EAEME,EAAqB,IAAM,CAC/BF,EAAoB,CAACD,CAAgB,CACvC,EAEMI,EAAYC,GAEdV,EAAS,WAAaU,GAAQV,EAAS,SAAS,WAAWU,EAAO,GAAG,EAInEC,EAAO,KAAK,MAAM,aAAa,QAAQ,WAAW,GAAK,IAAI,EAE3DC,EAAY,CAChB,CACE,KAAM,mBACN,KAAM,sBACN,MAAO,YACP,MAAO,EACT,EACA,CACE,KAAM,eACN,KAAM,2BACN,MAAO,aACP,MAAO,EACT,EACA,CACE,KAAM,kBACN,KAAM,wBACN,MAAO,WACP,MAAO,EACT,EACA,CACE,KAAM,oBACN,KAAM,oBACN,MAAO,aACP,MAAO,EACT,EACA,CACE,KAAM,cACN,KAAM,iBACN,MAAO,OACP,MAAO,EAAA,CAEX,EAGE,OAAA/J,EAAA,KAAC,MAAI,CAAA,UAAU,uBAEZ,SAAA,CACCsJ,GAAApJ,EAAA,IAAC,MAAA,CACC,UAAU,uBACV,QAAS,IAAMqJ,EAAqB,EAAK,CAAA,CAC1C,EAIHvJ,EAAA,KAAC,QAAA,CACC,UAAW,iBAAiBoJ,EAAmB,YAAc,EAAE,IAC7DE,EAAoB,cAAgB,EACtC,GAGA,SAAA,CAACpJ,EAAA,IAAA,MAAA,CAAI,UAAU,uBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,aACZ,SAACkJ,EAKClJ,EAAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAA,IAAA,CAAE,EAJpCF,EAAAA,KAAC,OACC,CAAA,SAAA,CAACE,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,YAAA,EAAO,QAAA,CAAA,CACpD,CAIJ,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,oBACb,SAACF,EAAA,KAAA,KAAA,CAAG,UAAU,iBACX,SAAA,CAAU+J,EAAA,IAAKrC,GACdxH,EAAA,IAAC,KAAA,CAEC,UAAW,kBACT0J,EAASlC,EAAK,IAAI,EAAI,SAAW,EACnC,GAEA,SAAA1H,EAAA,KAAC,IAAA,CACC,KAAK,IACL,QAAUQ,GAAM,CACdA,EAAE,eAAe,EACjByI,EAASvB,EAAK,IAAI,EAClB6B,EAAqB,EAAK,CAC5B,EACA,UAAU,iBACV,MAAO7B,EAAK,MAEZ,SAAA,CAAAxH,EAAA,IAAC,eAAA,CACC,KAAMwH,EAAK,KACX,UAAU,gBAAA,CACX,EACAxH,EAAA,IAAA,OAAA,CAAK,UAAU,iBAAkB,WAAK,KAAM,CAAA,CAAA,CAAA,CAAA,CAC/C,EApBKwH,EAAK,IAAA,CAsBb,EAGDxH,EAAAA,IAAC,KAAG,CAAA,UAAU,mBAAoB,CAAA,EAGlCA,EAAAA,IAAC,KAAG,CAAA,UAAU,iBACZ,SAAAF,EAAA,KAAC,IAAA,CACC,KAAK,IACL,OAAO,SACP,UAAU,iBACV,MAAM,YAEN,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,gBAAA,CACX,EACAA,EAAA,IAAA,OAAA,CAAK,UAAU,iBAAiB,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,CAE9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAA,EAAAA,IAAC,OAAI,UAAU,uBACb,gBAAC,MAAI,CAAA,UAAU,kBAAkB,IAAKiC,EACpC,SAAA,CAAAnC,EAAA,KAAC,MAAA,CACC,UAAU,4BACV,QAAS2J,EACT,MAAM,YAEN,SAAA,CAAAzJ,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,kBAAkB,CACvC,CAAA,EACC,CAACkJ,GACCpJ,EAAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,kBAAmB,SAAA4J,EAAK,MAAQ,QAAQ,EACtD5J,EAAA,IAAA,MAAA,CAAI,UAAU,mBAAoB,WAAK,KAAM,CAAA,CAAA,EAChD,EAEFA,EAAAA,IAAC,MAAI,CAAA,UAAU,4BACb,SAAAA,EAAA,IAAC,eAAA,CACC,KAAM,mBACJsJ,EAAmB,KAAO,MAC5B,OAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,EAGCA,GACCxJ,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAU,sBACV,QAAS,IAAM,CACbiJ,EAAS,kBAAkB,EAC3BQ,EAAoB,EAAK,CAC3B,EAEA,SAAA,CAAAvJ,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,MAAA,CACX,EAAe,WAAA,CAAA,CAElB,EACAF,EAAA,KAAC,MAAA,CACC,UAAU,sBACV,QAAS,IAAM,CACN,OAAA,KAAK,IAAK,QAAQ,EACzByJ,EAAoB,EAAK,CAC3B,EAEA,SAAA,CAAAvJ,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,WAAA,CAAA,CAElB,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,wBAAyB,CAAA,EACxCF,EAAA,KAAC,MAAA,CACC,UAAU,6BACV,QAAS0J,EAET,SAAA,CAAAxJ,EAAA,IAAC,eAAA,CACC,KAAK,mBACL,UAAU,MAAA,CACX,EAAe,QAAA,CAAA,CAAA,CAElB,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,CAAA,CACF,EAGAF,EAAA,KAAC,OAAA,CACC,UAAW,sBACToJ,EAAmB,oBAAsB,EAC3C,GAGA,SAAA,CAACpJ,EAAAA,KAAA,SAAA,CAAO,UAAU,eAChB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,UAAU,uBACV,QAAS,IAAMmJ,EAAoB,CAACD,CAAgB,EAEpD,SAAAlJ,EAAAA,IAAC,eAAa,CAAA,KAAK,2BAA4B,CAAA,CAAA,CACjD,EACAA,EAAA,IAAC,SAAA,CACC,UAAU,sBACV,QAAS,IAAMqJ,EAAqB,CAACD,CAAiB,EAEtD,SAAApJ,EAAAA,IAAC,eAAa,CAAA,KAAK,2BAA4B,CAAA,CAAA,CACjD,EACCwD,GAASxD,EAAA,IAAC,KAAG,CAAA,UAAU,mBAAoB,SAAMwD,CAAA,CAAA,CAAA,EACpD,QACC,MAAI,CAAA,UAAU,qBACb,SAAC1D,EAAA,KAAA,OAAA,CAAK,UAAU,gBAAgB,SAAA,CAAA,YACpB8J,EAAK,MAAQA,EAAK,KAAA,CAAA,CAC9B,CACF,CAAA,CAAA,EACF,EAGC5J,EAAAA,IAAA,MAAA,CAAI,UAAU,gBAAiB,SAAA8I,CAAS,CAAA,CAAA,CAAA,CAAA,CAC3C,EACF,CAEJ"}