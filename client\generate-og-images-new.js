/*
 * This script generates Open Graph images for social media sharing
 * To use this script, you'll need to install the following packages:
 * npm install puppeteer sharp
 *
 * Then run: node generate-og-images-new.js
 */

import fs from "fs";
import path from "path";
import puppeteer from "puppeteer";
import { fileURLToPath } from "url";

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  template: path.join(__dirname, "public", "og-image-template-new.html"),
  outputDir: path.join(__dirname, "public", "og-images"),
  width: 1200,
  height: 630,
  pages: [
    {
      tag: "Business Management System",
      title: "Streamline Your Business Operations",
      description:
        "Boost productivity with our comprehensive solution designed for modern enterprises.",
      filename: "home.png",
    },
    {
      tag: "About Us",
      title: "The Team Behind DEVSKILLS",
      description:
        "Meet the innovators and experts driving our mission to transform business management.",
      filename: "about.png",
    },
    {
      tag: "Services",
      title: "Enterprise Solutions That Deliver",
      description:
        "Discover how our tailored services can transform your business operations and drive growth.",
      filename: "services.png",
    },
    {
      tag: "Products",
      title: "Innovative Business Solutions",
      description:
        "Explore our suite of powerful tools designed to revolutionize how you manage your business.",
      filename: "products.png",
    },
    {
      tag: "Ultimation Studio",
      title: "The Ultimate Business Management System",
      description:
        "An all-in-one platform that combines AI, automation, and modular design to transform your operations.",
      filename: "ultimation.png",
    },
    {
      tag: "Core Module",
      title: "The Foundation of Your Business",
      description:
        "Our Core Module provides the essential framework for managing your entire business ecosystem.",
      filename: "module-core.png",
    },
    {
      tag: "Accounting Module",
      title: "Financial Management Simplified",
      description:
        "Streamline your financial operations with our powerful and intuitive accounting tools.",
      filename: "module-accounting.png",
    },
    {
      tag: "Budget Module",
      title: "Strategic Financial Planning",
      description:
        "Take control of your finances with comprehensive budgeting and forecasting capabilities.",
      filename: "module-budget.png",
    },
    {
      tag: "HR Module",
      title: "Human Resources Excellence",
      description:
        "Manage your most valuable asset - your people - with our comprehensive HR solution.",
      filename: "module-hr.png",
    },
    {
      tag: "Recruiting Module",
      title: "Talent Acquisition Reimagined",
      description:
        "Find and hire the best talent with our AI-powered recruiting and applicant tracking system.",
      filename: "module-recruiting.png",
    },
    {
      tag: "Production Module",
      title: "Manufacturing & Operations",
      description:
        "Optimize your production processes and supply chain with real-time insights and automation.",
      filename: "module-production.png",
    },
    {
      tag: "Sales Module",
      title: "Revenue Growth Engine",
      description:
        "Accelerate your sales cycle and boost revenue with our integrated sales management platform.",
      filename: "module-sales.png",
    },
    {
      tag: "Quality Control",
      title: "Excellence in Every Detail",
      description:
        "Ensure consistent quality across all your products and services with our QC tools.",
      filename: "module-quality.png",
    },
    {
      tag: "Contact",
      title: "Let's Build Something Great Together",
      description:
        "Get in touch with our team to discuss how we can help your business succeed.",
      filename: "contact.png",
    },
    {
      tag: "404 Error",
      title: "Page Not Found",
      description:
        "The page you're looking for doesn't exist or has been moved.",
      filename: "404.png",
    },
  ],
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Read the template HTML
const templateHtml = fs.readFileSync(config.template, "utf8");

// Generate images
async function generateOgImages() {
  const browser = await puppeteer.launch({
    headless: "new",
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
  });

  try {
    for (const page of config.pages) {
      // Replace placeholders in the template
      let html = templateHtml
        .replace(
          '<div class="tag">Business Management System</div>',
          `<div class="tag">${page.tag}</div>`
        )
        .replace(
          '<h1 class="title">Streamline Your Business Operations</h1>',
          `<h1 class="title">${page.title}</h1>`
        )
        .replace(
          '<p class="description">Boost productivity with our comprehensive solution designed for modern enterprises.</p>',
          `<p class="description">${page.description}</p>`
        );

      // Create a new page
      const pup = await browser.newPage();
      await pup.setViewport({ width: config.width, height: config.height });

      // Set content and wait for rendering
      await pup.setContent(html, { waitUntil: "networkidle0" });

      // Take screenshot
      const outputPath = path.join(config.outputDir, page.filename);
      await pup.screenshot({ path: outputPath, type: "png" });

      console.log(`Generated: ${outputPath}`);
      await pup.close();
    }
  } catch (error) {
    console.error("Error generating OG images:", error);
  } finally {
    await browser.close();
  }
}

// Run the generator
generateOgImages()
  .then(() => {
    console.log("OG image generation complete!");
  })
  .catch((err) => {
    console.error("Failed to generate OG images:", err);
  });
