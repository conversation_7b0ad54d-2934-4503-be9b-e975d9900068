{"name": "@prisma/engines-version", "version": "6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "9b628578b3b7cae625e8c927178f15a170e74a9c"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.19.76", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}