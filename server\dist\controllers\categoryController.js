"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCategory = exports.updateCategory = exports.createCategory = exports.getCategories = void 0;
const client_1 = require("@prisma/client");
const joi_1 = __importDefault(require("joi"));
const slugify_1 = __importDefault(require("slugify"));
const prisma = new client_1.PrismaClient();
const createCategorySchema = joi_1.default.object({
    name: joi_1.default.string().min(1).max(100).required(),
    description: joi_1.default.string().max(500).optional().allow(''),
    color: joi_1.default.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
});
const updateCategorySchema = createCategorySchema.keys({
    id: joi_1.default.string().required(),
});
const getCategories = async (req, res) => {
    try {
        const categories = await prisma.category.findMany({
            include: {
                _count: {
                    select: { blogPosts: true },
                },
            },
            orderBy: { name: 'asc' },
        });
        res.json({
            success: true,
            data: categories,
        });
    }
    catch (error) {
        console.error('Get categories error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.getCategories = getCategories;
const createCategory = async (req, res) => {
    try {
        const { error, value } = createCategorySchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message,
            });
        }
        const { name, description, color } = value;
        const slug = (0, slugify_1.default)(name, { lower: true, strict: true });
        const existingCategory = await prisma.category.findUnique({
            where: { slug },
        });
        if (existingCategory) {
            return res.status(400).json({
                success: false,
                message: 'A category with this name already exists',
            });
        }
        const category = await prisma.category.create({
            data: {
                name,
                slug,
                description: description || null,
                color: color || null,
            },
        });
        res.status(201).json({
            success: true,
            data: category,
        });
    }
    catch (error) {
        console.error('Create category error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.createCategory = createCategory;
const updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { error, value } = updateCategorySchema.validate({ ...req.body, id });
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message,
            });
        }
        const { name, description, color } = value;
        const existingCategory = await prisma.category.findUnique({
            where: { id },
        });
        if (!existingCategory) {
            return res.status(404).json({
                success: false,
                message: 'Category not found',
            });
        }
        let slug = existingCategory.slug;
        if (name !== existingCategory.name) {
            slug = (0, slugify_1.default)(name, { lower: true, strict: true });
            const slugExists = await prisma.category.findUnique({
                where: { slug },
            });
            if (slugExists && slugExists.id !== id) {
                return res.status(400).json({
                    success: false,
                    message: 'A category with this name already exists',
                });
            }
        }
        const updatedCategory = await prisma.category.update({
            where: { id },
            data: {
                name,
                slug,
                description: description || null,
                color: color || null,
            },
        });
        res.json({
            success: true,
            data: updatedCategory,
        });
    }
    catch (error) {
        console.error('Update category error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.updateCategory = updateCategory;
const deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const category = await prisma.category.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { blogPosts: true },
                },
            },
        });
        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found',
            });
        }
        if (category._count.blogPosts > 0) {
            return res.status(400).json({
                success: false,
                message: `Cannot delete category. It is being used by ${category._count.blogPosts} blog post(s).`,
            });
        }
        await prisma.category.delete({
            where: { id },
        });
        res.json({
            success: true,
            message: 'Category deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete category error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
        });
    }
};
exports.deleteCategory = deleteCategory;
