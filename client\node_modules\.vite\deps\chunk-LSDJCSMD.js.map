{"version": 3, "sources": ["../../.pnpm/@tiptap+extension-list-item@2.22.3_@tiptap+core@2.22.3_@tiptap+pm@2.22.3_/node_modules/@tiptap/extension-list-item/src/list-item.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n"], "mappings": ";;;;;;AA6Ba,IAAA,WAAW,KAAK,OAAwB;EACnD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;MAChB,oBAAoB;MACpB,qBAAqB;;;EAIzB,SAAS;EAET,UAAU;EAEV,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG/E,uBAAoB;AAClB,WAAO;MACL,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;MACzD,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;MACtD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;;;AAGnE,CAAA;", "names": []}