{"version": 3, "sources": ["../../.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/xml.js"], "sourcesContent": ["/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common, web\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  const regex = hljs.regex;\n  // XML names can have the following additional letters: https://www.w3.org/TR/xml/#NT-NameChar\n  // OTHER_NAME_CHARS = /[:\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]/;\n  // Element names start with NAME_START_CHAR followed by optional other Unicode letters, ASCII digits, hyphens, underscores, and periods\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);;\n  // const XML_IDENT_RE = /[A-Z_a-z:\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]+/;\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);\n  // however, to cater for performance and more Unicode support rely simply on the Unicode letter class\n  const TAG_NAME_RE = regex.concat(/[\\p{L}_]/u, regex.optional(/[\\p{L}0-9_.-]*:/u), /[\\p{L}0-9_.-]*/u);\n  const XML_IDENT_RE = /[\\p{L}0-9._:-]+/u;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [\n      {\n        className: 'keyword',\n        begin: /#?[a-z_][a-z1-9_-]+/,\n        illegal: /\\n/\n      }\n    ]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, { className: 'string' });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: XML_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /=\\s*/,\n        relevance: 0,\n        contains: [\n          {\n            className: 'string',\n            endsParent: true,\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [ XML_ENTITIES ]\n              },\n              { begin: /[^\\s\"'=<>`]+/ }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: [\n      'html',\n      'xhtml',\n      'rss',\n      'atom',\n      'xjb',\n      'xsd',\n      'xsl',\n      'plist',\n      'wsf',\n      'svg'\n    ],\n    case_insensitive: true,\n    unicodeRegex: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: /<![a-z]/,\n        end: />/,\n        relevance: 10,\n        contains: [\n          XML_META_KEYWORDS,\n          QUOTE_META_STRING_MODE,\n          APOS_META_STRING_MODE,\n          XML_META_PAR_KEYWORDS,\n          {\n            begin: /\\[/,\n            end: /\\]/,\n            contains: [\n              {\n                className: 'meta',\n                begin: /<![a-z]/,\n                end: />/,\n                contains: [\n                  XML_META_KEYWORDS,\n                  XML_META_PAR_KEYWORDS,\n                  QUOTE_META_STRING_MODE,\n                  APOS_META_STRING_MODE\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      hljs.COMMENT(\n        /<!--/,\n        /-->/,\n        { relevance: 10 }\n      ),\n      {\n        begin: /<!\\[CDATA\\[/,\n        end: /\\]\\]>/,\n        relevance: 10\n      },\n      XML_ENTITIES,\n      // xml processing instructions\n      {\n        className: 'meta',\n        end: /\\?>/,\n        variants: [\n          {\n            begin: /<\\?xml/,\n            relevance: 10,\n            contains: [\n              QUOTE_META_STRING_MODE\n            ]\n          },\n          {\n            begin: /<\\?[a-z][a-z0-9]+/,\n          }\n        ]\n\n      },\n      {\n        className: 'tag',\n        /*\n        The lookahead pattern (?=...) ensures that 'begin' only matches\n        '<style' as a single word, followed by a whitespace or an\n        ending bracket.\n        */\n        begin: /<style(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'style' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/style>/,\n          returnEnd: true,\n          subLanguage: [\n            'css',\n            'xml'\n          ]\n        }\n      },\n      {\n        className: 'tag',\n        // See the comment in the <style tag about the lookahead pattern\n        begin: /<script(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'script' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/script>/,\n          returnEnd: true,\n          subLanguage: [\n            'javascript',\n            'handlebars',\n            'xml'\n          ]\n        }\n      },\n      // we need this for now for jSX\n      {\n        className: 'tag',\n        begin: /<>|<\\/>/\n      },\n      // open tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /</,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE,\n            // <tag/>\n            // <tag>\n            // <tag ...\n            regex.either(/\\/>/, />/, /\\s/)\n          ))\n        ),\n        end: /\\/?>/,\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0,\n            starts: TAG_INTERNALS\n          }\n        ]\n      },\n      // close tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /<\\//,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE, />/\n          ))\n        ),\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0\n          },\n          {\n            begin: />/,\n            relevance: 0,\n            endsParent: true\n          }\n        ]\n      }\n    ]\n  };\n}\n\nexport { xml as default };\n"], "mappings": ";AAQA,SAAS,IAAI,MAAM;AACjB,QAAM,QAAQ,KAAK;AAQnB,QAAM,cAAc,MAAM,OAAO,aAAa,MAAM,SAAS,kBAAkB,GAAG,iBAAiB;AACnG,QAAM,eAAe;AACrB,QAAM,eAAe;AAAA,IACnB,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AACA,QAAM,oBAAoB;AAAA,IACxB,OAAO;AAAA,IACP,UAAU;AAAA,MACR;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,QAAM,wBAAwB,KAAK,QAAQ,mBAAmB;AAAA,IAC5D,OAAO;AAAA,IACP,KAAK;AAAA,EACP,CAAC;AACD,QAAM,wBAAwB,KAAK,QAAQ,KAAK,kBAAkB,EAAE,WAAW,SAAS,CAAC;AACzF,QAAM,yBAAyB,KAAK,QAAQ,KAAK,mBAAmB,EAAE,WAAW,SAAS,CAAC;AAC3F,QAAM,gBAAgB;AAAA,IACpB,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,MACR;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU,CAAE,YAAa;AAAA,cAC3B;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU,CAAE,YAAa;AAAA,cAC3B;AAAA,cACA,EAAE,OAAO,eAAe;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,UAAU;AAAA,MACR;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA,EAAE,WAAW,GAAG;AAAA,MAClB;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,QACE,WAAW;AAAA,QACX,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA;AAAA,QACE,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,EAAE,MAAM,QAAQ;AAAA,QAC1B,UAAU,CAAE,aAAc;AAAA,QAC1B,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,aAAa;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW;AAAA;AAAA,QAEX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,EAAE,MAAM,SAAS;AAAA,QAC3B,UAAU,CAAE,aAAc;AAAA,QAC1B,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,aAAa;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAAA;AAAA,MAEA;AAAA,QACE,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,UACX;AAAA,UACA,MAAM,UAAU,MAAM;AAAA,YACpB;AAAA;AAAA;AAAA;AAAA,YAIA,MAAM,OAAO,OAAO,KAAK,IAAI;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,QACA,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,YACX,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,UACX;AAAA,UACA,MAAM,UAAU,MAAM;AAAA,YACpB;AAAA,YAAa;AAAA,UACf,CAAC;AAAA,QACH;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}