"use strict";var j=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var D=Object.getOwnPropertyNames;var M=Object.prototype.hasOwnProperty;var U=(t,e)=>{for(var n in e)j(t,n,{get:e[n],enumerable:!0})},B=(t,e,n,_)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of D(e))!M.call(t,o)&&o!==n&&j(t,o,{get:()=>e[o],enumerable:!(_=R(e,o))||_.enumerable});return t};var N=t=>B(j({},"__esModule",{value:!0}),t);var Oe={};U(Oe,{QueryEngine:()=>G,__wbg_String_88810dfeb4021902:()=>Et,__wbg_buffer_344d9b41efe96da7:()=>Rt,__wbg_call_53fc3abd42e24ec8:()=>ie,__wbg_call_669127b9d730c650:()=>Qt,__wbg_crypto_58f13aa23ffcb166:()=>Ct,__wbg_done_bc26bf4ada718266:()=>Xt,__wbg_entries_6d727b73ee02b7ce:()=>pe,__wbg_getRandomValues_504510b5564925af:()=>Bt,__wbg_getTime_ed6ee333b702f8fc:()=>ct,__wbg_get_2aff440840bb6202:()=>te,__wbg_get_4a9aa5157afeb382:()=>Ht,__wbg_get_94990005bd6ca07c:()=>vt,__wbg_getwithrefkey_5e6d9547403deab8:()=>qt,__wbg_globalThis_17eff828815f7d84:()=>re,__wbg_global_46f939f6541643c5:()=>_e,__wbg_has_cdf8b85f6e903c80:()=>rt,__wbg_instanceof_ArrayBuffer_c7cc317e5c29cc0d:()=>we,__wbg_instanceof_Promise_cfbcc42300367513:()=>st,__wbg_instanceof_Uint8Array_19e6f142a5e7e1e1:()=>le,__wbg_isArray_38525be7442aa21e:()=>ce,__wbg_isSafeInteger_c38b0a16d0c7cef7:()=>ue,__wbg_iterator_7ee1a391d310f8e4:()=>bt,__wbg_length_a5587d6cd79ab197:()=>be,__wbg_length_cace2e0b3ddc0502:()=>at,__wbg_msCrypto_abcb1295e768d1f2:()=>Pt,__wbg_new0_ad75dd38f92424e2:()=>ot,__wbg_new_08236689f0afb357:()=>ht,__wbg_new_1b94180eeb48f2a2:()=>It,__wbg_new_c728d68b8b34487e:()=>St,__wbg_new_d8a000788389a31e:()=>Mt,__wbg_new_feb65b865d980ae2:()=>Y,__wbg_newnoargs_ccdcae30fd002262:()=>oe,__wbg_newwithbyteoffsetandlength_2dc04d99088b15e3:()=>Dt,__wbg_newwithlength_13b5319ab422dcf6:()=>Wt,__wbg_next_15da6a3df9290720:()=>Zt,__wbg_next_1989a20442400aaa:()=>Kt,__wbg_node_523d7bd03ef69fba:()=>zt,__wbg_now_28a6b413aca4a96a:()=>ge,__wbg_now_4579335d3581594c:()=>ut,__wbg_now_8ed1a4454e40ecd1:()=>it,__wbg_parse_3f0cb48976ca4123:()=>_t,__wbg_process_5b786e71d465a513:()=>$t,__wbg_push_fd3233d09cf81821:()=>kt,__wbg_randomFillSync_a0d98aa11c81fe89:()=>Nt,__wbg_require_2784e593a4674877:()=>Lt,__wbg_resolve_a3252b2860f0a09e:()=>Ae,__wbg_self_3fad056edded10bd:()=>ee,__wbg_setTimeout_631fe61f31fa2fad:()=>Z,__wbg_set_0ac78a2bc07da03c:()=>Tt,__wbg_set_3355b9f2d3092e3b:()=>At,__wbg_set_40f7786a25a9cc7e:()=>se,__wbg_set_841ac57cff3d672b:()=>Ot,__wbg_set_dcfd613a3420f908:()=>ae,__wbg_set_wasm:()=>C,__wbg_stringify_4039297315a25b00:()=>fe,__wbg_subarray_6ca5cfa7fbb9abbe:()=>Ut,__wbg_then_1bbc9edafd859b06:()=>Se,__wbg_then_89e1c559530b85cf:()=>Ie,__wbg_valueOf_ff4b62641803432a:()=>Gt,__wbg_value_0570714ff7d75f35:()=>Yt,__wbg_versions_c2ab80650590b6a2:()=>Vt,__wbg_window_a4f46c98a61d4089:()=>ne,__wbindgen_bigint_from_i64:()=>wt,__wbindgen_bigint_from_u64:()=>xt,__wbindgen_bigint_get_as_i64:()=>ye,__wbindgen_boolean_get:()=>gt,__wbindgen_cb_drop:()=>he,__wbindgen_closure_wrapper6700:()=>je,__wbindgen_debug_string:()=>me,__wbindgen_error_new:()=>X,__wbindgen_in:()=>pt,__wbindgen_is_bigint:()=>dt,__wbindgen_is_function:()=>Jt,__wbindgen_is_object:()=>ft,__wbindgen_is_string:()=>jt,__wbindgen_is_undefined:()=>nt,__wbindgen_jsval_eq:()=>yt,__wbindgen_jsval_loose_eq:()=>de,__wbindgen_memory:()=>Ft,__wbindgen_number_get:()=>lt,__wbindgen_number_new:()=>mt,__wbindgen_object_clone_ref:()=>et,__wbindgen_object_drop_ref:()=>Te,__wbindgen_string_get:()=>K,__wbindgen_string_new:()=>tt,__wbindgen_throw:()=>xe,debug_panic:()=>Q,getBuildTimeInfo:()=>J});module.exports=N(Oe);var T=()=>{};T.prototype=T;let c;function C(t){c=t}const w=new Array(128).fill(void 0);w.push(void 0,null,!0,!1);function r(t){return w[t]}let a=0,I=null;function S(){return(I===null||I.byteLength===0)&&(I=new Uint8Array(c.memory.buffer)),I}const $=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let A=new $("utf-8");const V=typeof A.encodeInto=="function"?function(t,e){return A.encodeInto(t,e)}:function(t,e){const n=A.encode(t);return e.set(n),{read:t.length,written:n.length}};function d(t,e,n){if(n===void 0){const s=A.encode(t),p=e(s.length,1)>>>0;return S().subarray(p,p+s.length).set(s),a=s.length,p}let _=t.length,o=e(_,1)>>>0;const f=S();let u=0;for(;u<_;u++){const s=t.charCodeAt(u);if(s>127)break;f[o+u]=s}if(u!==_){u!==0&&(t=t.slice(u)),o=n(o,_,_=u+t.length*3,1)>>>0;const s=S().subarray(o+u,o+_),p=V(t,s);u+=p.written,o=n(o,_,u,1)>>>0}return a=u,o}function x(t){return t==null}let y=null;function l(){return(y===null||y.buffer.detached===!0||y.buffer.detached===void 0&&y.buffer!==c.memory.buffer)&&(y=new DataView(c.memory.buffer)),y}const z=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let q=new z("utf-8",{ignoreBOM:!0,fatal:!0});q.decode();function m(t,e){return t=t>>>0,q.decode(S().subarray(t,t+e))}let h=w.length;function i(t){h===w.length&&w.push(w.length+1);const e=h;return h=w[e],w[e]=t,e}function O(t){const e=typeof t;if(e=="number"||e=="boolean"||t==null)return`${t}`;if(e=="string")return`"${t}"`;if(e=="symbol"){const o=t.description;return o==null?"Symbol":`Symbol(${o})`}if(e=="function"){const o=t.name;return typeof o=="string"&&o.length>0?`Function(${o})`:"Function"}if(Array.isArray(t)){const o=t.length;let f="[";o>0&&(f+=O(t[0]));for(let u=1;u<o;u++)f+=", "+O(t[u]);return f+="]",f}const n=/\[object ([^\]]+)\]/.exec(toString.call(t));let _;if(n.length>1)_=n[1];else return toString.call(t);if(_=="Object")try{return"Object("+JSON.stringify(t)+")"}catch{return"Object"}return t instanceof Error?`${t.name}: ${t.message}
${t.stack}`:_}function L(t){t<132||(w[t]=h,h=t)}function b(t){const e=r(t);return L(t),e}const k=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(t=>{c.__wbindgen_export_2.get(t.dtor)(t.a,t.b)});function P(t,e,n,_){const o={a:t,b:e,cnt:1,dtor:n},f=(...u)=>{o.cnt++;const s=o.a;o.a=0;try{return _(s,o.b,...u)}finally{--o.cnt===0?(c.__wbindgen_export_2.get(o.dtor)(s,o.b),k.unregister(o)):o.a=s}};return f.original=o,k.register(f,o,o),f}function W(t,e,n){c._dyn_core__ops__function__FnMut__A____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h9eef02caf99553a1(t,e,i(n))}function J(){const t=c.getBuildTimeInfo();return b(t)}function Q(t){try{const f=c.__wbindgen_add_to_stack_pointer(-16);var e=x(t)?0:d(t,c.__wbindgen_malloc,c.__wbindgen_realloc),n=a;c.debug_panic(f,e,n);var _=l().getInt32(f+4*0,!0),o=l().getInt32(f+4*1,!0);if(o)throw b(_)}finally{c.__wbindgen_add_to_stack_pointer(16)}}function g(t,e){try{return t.apply(this,e)}catch(n){c.__wbindgen_exn_store(i(n))}}function H(t,e,n,_){c.wasm_bindgen__convert__closures__invoke2_mut__h174c8485536aed69(t,e,i(n),i(_))}const v=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(t=>c.__wbg_queryengine_free(t>>>0,1));class G{__destroy_into_raw(){const e=this.__wbg_ptr;return this.__wbg_ptr=0,v.unregister(this),e}free(){const e=this.__destroy_into_raw();c.__wbg_queryengine_free(e,0)}constructor(e,n,_){try{const s=c.__wbindgen_add_to_stack_pointer(-16);c.queryengine_new(s,i(e),i(n),i(_));var o=l().getInt32(s+4*0,!0),f=l().getInt32(s+4*1,!0),u=l().getInt32(s+4*2,!0);if(u)throw b(f);return this.__wbg_ptr=o>>>0,v.register(this,this.__wbg_ptr,this),this}finally{c.__wbindgen_add_to_stack_pointer(16)}}connect(e){const n=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a,o=c.queryengine_connect(this.__wbg_ptr,n,_);return b(o)}disconnect(e){const n=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a,o=c.queryengine_disconnect(this.__wbg_ptr,n,_);return b(o)}query(e,n,_){const o=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),f=a,u=d(n,c.__wbindgen_malloc,c.__wbindgen_realloc),s=a;var p=x(_)?0:d(_,c.__wbindgen_malloc,c.__wbindgen_realloc),E=a;const F=c.queryengine_query(this.__wbg_ptr,o,f,u,s,p,E);return b(F)}startTransaction(e,n){const _=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a,f=d(n,c.__wbindgen_malloc,c.__wbindgen_realloc),u=a,s=c.queryengine_startTransaction(this.__wbg_ptr,_,o,f,u);return b(s)}commitTransaction(e,n){const _=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a,f=d(n,c.__wbindgen_malloc,c.__wbindgen_realloc),u=a,s=c.queryengine_commitTransaction(this.__wbg_ptr,_,o,f,u);return b(s)}rollbackTransaction(e,n){const _=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a,f=d(n,c.__wbindgen_malloc,c.__wbindgen_realloc),u=a,s=c.queryengine_rollbackTransaction(this.__wbg_ptr,_,o,f,u);return b(s)}metrics(e){const n=d(e,c.__wbindgen_malloc,c.__wbindgen_realloc),_=a,o=c.queryengine_metrics(this.__wbg_ptr,n,_);return b(o)}}function K(t,e){const n=r(e),_=typeof n=="string"?n:void 0;var o=x(_)?0:d(_,c.__wbindgen_malloc,c.__wbindgen_realloc),f=a;l().setInt32(t+4*1,f,!0),l().setInt32(t+4*0,o,!0)}function X(t,e){const n=new Error(m(t,e));return i(n)}function Y(t,e){try{var n={a:t,b:e},_=(f,u)=>{const s=n.a;n.a=0;try{return H(s,n.b,f,u)}finally{n.a=s}};const o=new Promise(_);return i(o)}finally{n.a=n.b=0}}function Z(t,e){return setTimeout(r(t),e>>>0)}function tt(t,e){const n=m(t,e);return i(n)}function et(t){const e=r(t);return i(e)}function nt(t){return r(t)===void 0}function rt(){return g(function(t,e){return Reflect.has(r(t),r(e))},arguments)}function _t(){return g(function(t,e){const n=JSON.parse(m(t,e));return i(n)},arguments)}function ot(){return i(new Date)}function ct(t){return r(t).getTime()}function it(t){return r(t).now()}function ut(){return Date.now()}function st(t){let e;try{e=r(t)instanceof Promise}catch{e=!1}return e}function ft(t){const e=r(t);return typeof e=="object"&&e!==null}function at(t){return r(t).length}function bt(){return i(Symbol.iterator)}function gt(t){const e=r(t);return typeof e=="boolean"?e?1:0:2}function dt(t){return typeof r(t)=="bigint"}function lt(t,e){const n=r(e),_=typeof n=="number"?n:void 0;l().setFloat64(t+8*1,x(_)?0:_,!0),l().setInt32(t+4*0,!x(_),!0)}function wt(t){return i(t)}function pt(t,e){return r(t)in r(e)}function xt(t){const e=BigInt.asUintN(64,t);return i(e)}function yt(t,e){return r(t)===r(e)}function mt(t){return i(t)}function ht(){const t=new Array;return i(t)}function Tt(t,e,n){r(t)[e>>>0]=b(n)}function It(){return i(new Map)}function St(){const t=new Object;return i(t)}function At(t,e,n){const _=r(t).set(r(e),r(n));return i(_)}function jt(t){return typeof r(t)=="string"}function Ot(t,e,n){r(t)[b(e)]=b(n)}function qt(t,e){const n=r(t)[r(e)];return i(n)}function kt(t,e){return r(t).push(r(e))}function vt(){return g(function(t,e){const n=r(t)[b(e)];return i(n)},arguments)}function Et(t,e){const n=String(r(e)),_=d(n,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a;l().setInt32(t+4*1,o,!0),l().setInt32(t+4*0,_,!0)}function Ft(){const t=c.memory;return i(t)}function Rt(t){const e=r(t).buffer;return i(e)}function Dt(t,e,n){const _=new Uint8Array(r(t),e>>>0,n>>>0);return i(_)}function Mt(t){const e=new Uint8Array(r(t));return i(e)}function Ut(t,e,n){const _=r(t).subarray(e>>>0,n>>>0);return i(_)}function Bt(){return g(function(t,e){r(t).getRandomValues(r(e))},arguments)}function Nt(){return g(function(t,e){r(t).randomFillSync(b(e))},arguments)}function Ct(t){const e=r(t).crypto;return i(e)}function $t(t){const e=r(t).process;return i(e)}function Vt(t){const e=r(t).versions;return i(e)}function zt(t){const e=r(t).node;return i(e)}function Lt(){return g(function(){const t=module.require;return i(t)},arguments)}function Pt(t){const e=r(t).msCrypto;return i(e)}function Wt(t){const e=new Uint8Array(t>>>0);return i(e)}function Jt(t){return typeof r(t)=="function"}function Qt(){return g(function(t,e){const n=r(t).call(r(e));return i(n)},arguments)}function Ht(t,e){const n=r(t)[e>>>0];return i(n)}function Gt(t){return r(t).valueOf()}function Kt(){return g(function(t){const e=r(t).next();return i(e)},arguments)}function Xt(t){return r(t).done}function Yt(t){const e=r(t).value;return i(e)}function Zt(t){const e=r(t).next;return i(e)}function te(){return g(function(t,e){const n=Reflect.get(r(t),r(e));return i(n)},arguments)}function ee(){return g(function(){const t=self.self;return i(t)},arguments)}function ne(){return g(function(){const t=window.window;return i(t)},arguments)}function re(){return g(function(){const t=globalThis.globalThis;return i(t)},arguments)}function _e(){return g(function(){const t=global.global;return i(t)},arguments)}function oe(t,e){const n=new T(m(t,e));return i(n)}function ce(t){return Array.isArray(r(t))}function ie(){return g(function(t,e,n){const _=r(t).call(r(e),r(n));return i(_)},arguments)}function ue(t){return Number.isSafeInteger(r(t))}function se(){return g(function(t,e,n){return Reflect.set(r(t),r(e),r(n))},arguments)}function fe(){return g(function(t){const e=JSON.stringify(r(t));return i(e)},arguments)}function ae(t,e,n){r(t).set(r(e),n>>>0)}function be(t){return r(t).length}function ge(){return g(function(){return Date.now()},arguments)}function de(t,e){return r(t)==r(e)}function le(t){let e;try{e=r(t)instanceof Uint8Array}catch{e=!1}return e}function we(t){let e;try{e=r(t)instanceof ArrayBuffer}catch{e=!1}return e}function pe(t){const e=Object.entries(r(t));return i(e)}function xe(t,e){throw new Error(m(t,e))}function ye(t,e){const n=r(e),_=typeof n=="bigint"?n:void 0;l().setBigInt64(t+8*1,x(_)?BigInt(0):_,!0),l().setInt32(t+4*0,!x(_),!0)}function me(t,e){const n=O(r(e)),_=d(n,c.__wbindgen_malloc,c.__wbindgen_realloc),o=a;l().setInt32(t+4*1,o,!0),l().setInt32(t+4*0,_,!0)}function he(t){const e=b(t).original;return e.cnt--==1?(e.a=0,!0):!1}function Te(t){b(t)}function Ie(t,e){const n=r(t).then(r(e));return i(n)}function Se(t,e,n){const _=r(t).then(r(e),r(n));return i(_)}function Ae(t){const e=Promise.resolve(r(t));return i(e)}function je(t,e,n){const _=P(t,e,530,W);return i(_)}0&&(module.exports={QueryEngine,__wbg_String_88810dfeb4021902,__wbg_buffer_344d9b41efe96da7,__wbg_call_53fc3abd42e24ec8,__wbg_call_669127b9d730c650,__wbg_crypto_58f13aa23ffcb166,__wbg_done_bc26bf4ada718266,__wbg_entries_6d727b73ee02b7ce,__wbg_getRandomValues_504510b5564925af,__wbg_getTime_ed6ee333b702f8fc,__wbg_get_2aff440840bb6202,__wbg_get_4a9aa5157afeb382,__wbg_get_94990005bd6ca07c,__wbg_getwithrefkey_5e6d9547403deab8,__wbg_globalThis_17eff828815f7d84,__wbg_global_46f939f6541643c5,__wbg_has_cdf8b85f6e903c80,__wbg_instanceof_ArrayBuffer_c7cc317e5c29cc0d,__wbg_instanceof_Promise_cfbcc42300367513,__wbg_instanceof_Uint8Array_19e6f142a5e7e1e1,__wbg_isArray_38525be7442aa21e,__wbg_isSafeInteger_c38b0a16d0c7cef7,__wbg_iterator_7ee1a391d310f8e4,__wbg_length_a5587d6cd79ab197,__wbg_length_cace2e0b3ddc0502,__wbg_msCrypto_abcb1295e768d1f2,__wbg_new0_ad75dd38f92424e2,__wbg_new_08236689f0afb357,__wbg_new_1b94180eeb48f2a2,__wbg_new_c728d68b8b34487e,__wbg_new_d8a000788389a31e,__wbg_new_feb65b865d980ae2,__wbg_newnoargs_ccdcae30fd002262,__wbg_newwithbyteoffsetandlength_2dc04d99088b15e3,__wbg_newwithlength_13b5319ab422dcf6,__wbg_next_15da6a3df9290720,__wbg_next_1989a20442400aaa,__wbg_node_523d7bd03ef69fba,__wbg_now_28a6b413aca4a96a,__wbg_now_4579335d3581594c,__wbg_now_8ed1a4454e40ecd1,__wbg_parse_3f0cb48976ca4123,__wbg_process_5b786e71d465a513,__wbg_push_fd3233d09cf81821,__wbg_randomFillSync_a0d98aa11c81fe89,__wbg_require_2784e593a4674877,__wbg_resolve_a3252b2860f0a09e,__wbg_self_3fad056edded10bd,__wbg_setTimeout_631fe61f31fa2fad,__wbg_set_0ac78a2bc07da03c,__wbg_set_3355b9f2d3092e3b,__wbg_set_40f7786a25a9cc7e,__wbg_set_841ac57cff3d672b,__wbg_set_dcfd613a3420f908,__wbg_set_wasm,__wbg_stringify_4039297315a25b00,__wbg_subarray_6ca5cfa7fbb9abbe,__wbg_then_1bbc9edafd859b06,__wbg_then_89e1c559530b85cf,__wbg_valueOf_ff4b62641803432a,__wbg_value_0570714ff7d75f35,__wbg_versions_c2ab80650590b6a2,__wbg_window_a4f46c98a61d4089,__wbindgen_bigint_from_i64,__wbindgen_bigint_from_u64,__wbindgen_bigint_get_as_i64,__wbindgen_boolean_get,__wbindgen_cb_drop,__wbindgen_closure_wrapper6700,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_is_bigint,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_eq,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_number_new,__wbindgen_object_clone_ref,__wbindgen_object_drop_ref,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw,debug_panic,getBuildTimeInfo});
