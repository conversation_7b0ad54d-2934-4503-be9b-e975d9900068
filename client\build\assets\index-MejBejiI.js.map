{"version": 3, "mappings": ";8pCAEO,MAAMA,EAAwB,IAAM,CACzC,SAAS,iBAAiB,2BAA2B,EAAE,QAASC,GAAU,CACxEA,EAAM,iBAAiB,YAAa,SAAUC,EAAG,CAC/C,MAAMC,EAAI,OAAO,WACXC,EAAI,OAAO,YACXC,EAAU,IAAOH,EAAE,MAAQ,KAAK,YAAcC,EAC9CG,EAAU,IAAOJ,EAAE,MAAQ,KAAK,WAAaE,EAEnD,KAAK,iBAAiB,qBAAqB,EAAE,QAASG,GAAO,CAC3D,MAAMC,EAAS,SAASD,EAAG,aAAa,aAAa,CAAC,EAChDE,EAAY,eAAe,KAAK,MACpCJ,EAAUG,CACpB,CAAS,OAAO,KAAK,MAAMF,EAAUE,CAAM,CAAC,WACpCD,EAAG,MAAM,UAAYE,CAC7B,CAAO,EAED,IAAIC,EAAeR,EAAE,MAAQ,KAAK,WACfA,EAAE,MAAQ,KAAK,UAElC,KAAK,iBAAiB,4BAA4B,EAAE,QAASK,GAAO,CAClEA,EAAG,MAAM,KAAO,GAAGG,CAAY,KAC/BH,EAAG,MAAM,IAAM,MACvB,CAAO,CAKP,CAAK,EAEDN,EAAM,iBAAiB,aAAc,SAAUC,EAAG,CAChD,KAAK,iBAAiB,4BAA4B,EAAE,QAASK,GAAO,CAClE,WAAW,IAAM,CACfA,EAAG,MAAM,WAAa,iCACtBA,EAAG,MAAM,WAAa,WACvB,EAAE,EAAE,CACb,CAAO,CACP,CAAK,EAEDN,EAAM,iBAAiB,WAAY,SAAUC,EAAG,CAC9C,KAAK,iBAAiB,4BAA4B,EAAE,QAASK,GAAO,CAClEA,EAAG,MAAM,WAAa,MAC9B,CAAO,CACP,CAAK,CACL,CAAG,CACH,EAEO,SAASI,GAAiB,CAG/B,GAAI,SAAS,iBAAiB,iBAAiB,EAAE,QAC3C,OAAO,YAAc,KAAqB,CAM5C,IAASC,EAAT,UAA6B,CAC3B,SAAS,iBAAiB,iBAAiB,EAAE,QAASC,GAAY,CAE9DA,EAAQ,sBAAqB,EAAG,IAAM,OAAO,aAC7CA,EAAQ,sBAAuB,EAAC,OAAS,EAEpCA,EAAQ,UAAU,SAAS,gBAAgB,IAC9CA,EAAQ,UAAU,IAAI,gBAAgB,EACtCC,EAAS,QAAS,GAGhBD,EAAQ,UAAU,SAAS,gBAAgB,GAC7CA,EAAQ,UAAU,OAAO,gBAAgB,CAGvD,CAAS,CACT,EArBM,MAAMC,EAAW,IAAIC,EAAO,kBAAmB,CAC7C,SAAU,GACV,WAAY,EACpB,CAAO,EAoBD,OAAO,iBAAiB,SAAUH,CAAiB,CAGzD,CAGE,GAAI,SAAS,iBAAiB,iBAAiB,EAAE,QAC3C,OAAO,YAAc,KAAqB,CAI5C,IAASI,EAAT,UAAwB,CACtB,SAAS,iBAAiB,iBAAiB,EAAE,QAASH,GAAY,CAE9DA,EAAQ,sBAAqB,EAAG,IAAM,OAAO,aAC7CA,EAAQ,sBAAuB,EAAC,OAAS,EAEpCA,EAAQ,UAAU,SAAS,gBAAgB,IAC9CA,EAAQ,UAAU,IAAI,gBAAgB,EACtCI,EAAS,QAAS,GAGhBJ,EAAQ,UAAU,SAAS,gBAAgB,GAC7CA,EAAQ,UAAU,OAAO,gBAAgB,CAGvD,CAAS,CACT,EAnBM,MAAMI,EAAW,IAAIF,EAAO,kBAAmB,CAC7C,WAAY,EACpB,CAAO,EAkBD,OAAO,iBAAiB,SAAUC,CAAY,CAEpD,CAEA,CC3GO,SAASE,GAAW,CAEzB,WAAW,IAAM,CACf,GAAI,CAEE,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACnD,SACG,iBAAiB,MAAM,EACvB,QAASX,GAAOA,EAAG,UAAU,IAAI,YAAY,CAAC,EAGnD,IAAIY,EAAM,IAAIC,EAAI,CAChB,SAAU,MACV,aAAc,WACd,OAAQ,IACR,OAAQ,GACR,KAAM,GACN,SAAU,SAAUC,EAAK,CAEvBA,EAAI,UAAU,IAAI,UAAU,EAC5BA,EAAI,MAAM,QAAU,IACpBA,EAAI,MAAM,WAAa,SACxB,CACT,CAAO,EAGI,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACpD,SAAS,KAAK,UAAU,IAAI,gBAAgB,EAI9CF,EAAI,KAAM,EAGV,WAAW,IAAM,CACf,SAAS,iBAAiB,MAAM,EAAE,QAASZ,GAAO,CAC3CA,EAAG,UAAU,SAAS,UAAU,IACnCA,EAAG,MAAM,QAAU,IACnBA,EAAG,MAAM,WAAa,UACtBA,EAAG,UAAU,IAAI,UAAU,EAEvC,CAAS,CACF,EAAE,GAAI,EAGH,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACnD,SACG,iBAAiB,QAAQ,EACzB,QAASA,GAAOA,EAAG,UAAU,IAAI,YAAY,CAAC,EAEnD,IAAIe,EAAQ,IAAIF,EAAI,CAClB,SAAU,QACV,aAAc,WACd,OAAQ,IACR,OAAQ,GACR,KAAM,GACN,SAAU,SAAUC,EAAK,CACvBA,EAAI,UAAU,IAAI,UAAU,EAC5BA,EAAI,MAAM,QAAU,IACpBA,EAAI,MAAM,WAAa,SACxB,CACT,CAAO,EAEG,SAAS,KAAK,UAAU,SAAS,gBAAgB,EACnDC,EAAM,KAAM,EAEZ,SACG,iBAAiB,QAAQ,EACzB,QAASf,GAAQA,EAAG,MAAM,QAAU,GAAI,EAK3C,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACjD,OAAO,YAAc,MACrB,SAAS,gBAAgB,UAAU,SAAS,WAAW,EAEvD,SAAS,iBAAiB,cAAc,EAAE,QAASA,GAAO,CACxDA,EAAG,UAAU,IAAI,aAAc,aAAc,UAAU,EACvD,YAAY,IAAM,CAChBA,EAAG,UAAU,OAAO,YAAY,CACjC,EAAE,IAAI,CACjB,CAAS,EAED,SACG,iBAAiB,cAAc,EAC/B,QAASA,GAAQA,EAAG,MAAM,QAAU,GAAI,CAE9C,OAAQgB,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,EAEjD,SAAS,iBAAiB,4BAA4B,EAAE,QAAShB,GAAO,CACtEA,EAAG,MAAM,QAAU,IACnBA,EAAG,UAAU,IAAI,UAAU,CACnC,CAAO,CACP,CACG,EAAE,GAAG,CACR,CClGO,MAAMiB,EAAuB,IAAM,CACxC,IAAIC,EAAU,SAAS,cAAc,WAAW,EAC5CC,EAAkB,SAAS,cAAc,sBAAsB,EAC/DC,EAAmB,SAAS,cAAc,qBAAqB,EAG9DF,IAID,OAAO,QAAU,GACnBA,EAAQ,UAAU,OAAO,aAAa,EACtCA,EAAQ,UAAU,IAAI,eAAgB,eAAe,EACjDC,GAAiBA,EAAgB,UAAU,IAAI,cAAc,EAC7DC,GAAkBA,EAAiB,UAAU,OAAO,MAAM,GACrD,OAAO,UAAY,IAC5BF,EAAQ,UAAU,IAAI,aAAa,EACnCA,EAAQ,UAAU,OAAO,eAAgB,eAAe,EACpDC,GAAiBA,EAAgB,UAAU,OAAO,cAAc,EAChEC,GAAkBA,EAAiB,UAAU,IAAI,MAAM,GAE/D,ECCMC,EAAkBC,EAAA,KAAK,IAAAC,EAAA,IAC3B,OAAO,8BAAoC,OAAAC,KAAA,6CAC7C,EACMC,EAAgBH,OAAK,UAAM,OAAO,8BAAkC,OAAAE,KAAA,6CAAC,EACrEE,EAAiBJ,OAAK,UAAM,OAAO,8BAAgC,OAAAE,KAAA,6CAAC,EACpEG,EAAuBL,EAAA,KAAK,IAAAC,EAAA,IAChC,OAAO,8BAAsC,OAAAC,KAAA,6CAC/C,EACMI,EAAmBN,OAAK,UAAM,OAAO,8BAAkC,OAAAE,KAAA,6CAAC,EACxEK,EAAeP,OAAK,UAAM,OAAO,8BAA8B,OAAAE,KAAA,6CAAC,EAChEM,EAAwBR,EAAA,KAAK,IAAAC,EAAA,IACjC,OAAO,8BAAuC,OAAAC,KAAA,6CAChD,EACMO,EAAuBT,EAAA,KAAK,IAAAC,EAAA,IAChC,OAAO,8BAAsC,OAAAC,KAAA,6CAC/C,EACMQ,EAAkBV,OAAK,UAAM,OAAO,8BAAiC,OAAAE,KAAA,6CAAC,EACtES,EAA2BX,EAAA,KAAK,IAAAC,EAAA,IACpC,OAAO,8BAAmC,OAAAC,KAAA,6CAC5C,EACMU,EAA0BZ,EAAA,KAAK,IAAAC,EAAA,IACnC,OAAO,8BAAyC,OAAAC,KAAA,6CAClD,EACMW,EAAsBb,EAAA,KAAK,IAAAC,EAAA,IAC/B,OAAO,8BAAqC,OAAAC,KAAA,6CAC9C,EACMY,EAA+Bd,EAAA,KAAK,IAAAC,EAAA,IACxC,OAAO,8BAA2C,OAAAC,KAAA,6CACpD,EAEMa,EAAuBf,OAAK,IAAMC,EAAA,WAAO,4BAAoB,OAAAC,KAAA,GAAC,0CAC9Dc,EAA0BhB,OAAK,IAAMC,EAAA,WAAO,4BAAuB,OAAAC,KAAA,GAAC,0CACpEe,EAA2BjB,OAAK,IAAMC,EAAA,WAAO,2BAAwB,OAAAC,KAAA,GAAC,0CACtEgB,EAAsBlB,OAAK,IAAMC,EAAA,WAAO,2BAAoB,OAAAC,KAAA,GAAC,0CAC7DiB,EAAiCnB,EAAA,KAAK,IAAAC,EAAA,IAC1C,OAAO,2BAA+B,OAAAC,KAAA,2CACxC,EACMkB,EAA4BpB,EAAA,KAAK,IAAAC,EAAA,IACrC,OAAO,2BAA0B,OAAAC,KAAA,2CACnC,EACMmB,EAAyBrB,OAAK,IAAMC,EAAA,WAAO,4BAAsB,OAAAC,KAAA,GAAC,0CAClEoB,GAAmBtB,OAAK,IAAMC,EAAA,WAAO,2BAAyB,OAAAC,KAAA,GAAC,0CAG/DqB,GAAavB,OAAK,UAAM,OAAO,2BAAoB,OAAAE,KAAA,GAAC,0CACpDsB,GAAiBxB,OAAK,UAAM,OAAO,2BAAwB,OAAAE,KAAA,GAAC,0CAC5DuB,GAAiBzB,OAAK,UAAM,OAAO,2BAAwB,OAAAE,KAAA,GAAC,0CAC5DwB,EAAkB1B,OAAK,UAAM,OAAO,2BAAyB,OAAAE,KAAA,GAAC,0CAC9DyB,GAAkB3B,OAAK,UAAM,OAAO,2BAAyB,OAAAE,KAAA,GAAC,0CAC9D0B,GAAY5B,OAAK,UAAM,OAAO,2BAAmB,OAAAE,KAAA,GAAC,0CAGlD2B,GAAa,IACjBC,EAAA,KAAC,OACC,UAAU,cACV,MAAO,CACL,SAAU,QACV,IAAK,EACL,KAAM,EACN,MAAO,OACP,OAAQ,OACR,gBAAiB,UACjB,QAAS,OACT,eAAgB,SAChB,WAAY,SACZ,OAAQ,IACV,EAEA,UAAAC,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,OAAQ,iBACR,UAAW,iBACX,aAAc,MACd,UAAW,0BACb,CACD,QACA,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,KAKN,IACJ,EAGF,SAASC,IAAM,CACP,MAAE,SAAAC,CAAS,EAAIC,EAAY,EACjCC,mBAAU,IAAM,OACL9C,EAAA,EACalB,EAAA,EAClB,IAAAyB,EAAU,SAAS,cAAc,WAAW,EAC5CA,GAAA,MAAAA,EAAS,UAAU,SAAS,eACtBA,EAAA,UAAU,IAAI,gBAAgB,GAC5BwC,EAAAxC,GAAA,YAAAA,EAAS,YAAT,MAAAwC,EAAoB,SAAS,SAC9BxC,GAAA,MAAAA,EAAA,UAAU,IAAI,2BAGlB,wBAAiB,SAAUD,CAAoB,EACvCb,EAAA,EAGT,MAAAuD,EAAY,SAAS,OAAS,YAC9BC,EAAe,OAAO,SAAS,KACrC,OAAAC,EAAcF,EAAWC,CAAY,EAE9B,IAAM,CACJ,2BAAoB,SAAU3C,CAAoB,CAC3D,GACC,CAACsC,CAAQ,CAAC,EACbE,YAAU,IAAM,CACV,OAAO,OAAW,KAEblC,EAAA,oCAAiC,eAAE,+BAAK,IAAM,CAEnD,QAAQ,IAAI,kBAAkB,EAC/B,CAEL,EAAG,EAAE,EAGD6B,EAAA,KAAAU,WAAA,WAACT,EAAA,IAAAU,EAAA,UAAS,SAAUV,EAAA,IAACF,GAAW,IAC9B,eAACa,EACC,UAAAZ,EAAA,KAACa,EAAM,MAAK,IACV,UAAAZ,MAACY,GAAM,MAAK,GAAC,QAASZ,MAACa,GAA2B,GAAI,QACrDD,EAAM,MAAK,QAAQ,QAASZ,MAAChB,GAAqB,GAAI,EACvDe,OAACa,EAAM,MAAK,WACV,UAAAZ,MAACY,GAAM,KAAK,MAAM,QAASZ,MAAChC,GAAgB,GAAI,QAC/C4C,EAAM,MAAK,WAAW,QAASZ,MAAC3B,GAAe,GAAI,QACnDuC,EAAM,MAAK,iBAAiB,QAASZ,MAAC1B,GAAqB,GAAI,QAC/DsC,EAAM,MAAK,aAAa,QAASZ,MAACzB,GAAiB,GAAI,QACvDqC,EAAM,MAAK,SAAS,QAASZ,MAACxB,GAAa,GAAI,EAChDwB,EAAA,IAACY,EAAA,CACC,KAAK,kBACL,cAAUnC,EAAsB,IAClC,QACCmC,EAAM,MAAK,iBAAiB,QAASZ,MAACtB,GAAqB,GAAI,QAC/DkC,EAAM,MAAK,YAAY,QAASZ,MAACrB,GAAgB,GAAI,EACtDqB,EAAA,IAACY,EAAA,CACC,KAAK,cACL,cAAUhC,EAAyB,IACrC,EACAoB,EAAA,IAACY,EAAA,CACC,KAAK,oBACL,cAAU/B,EAAwB,IACpC,QACC+B,EAAM,MAAK,gBAAgB,QAASZ,MAAClB,GAAoB,GAAI,QAC7D8B,EAAM,MAAK,gBAAgB,QAASZ,MAAC5B,GAAc,GAAI,EACxD4B,EAAA,IAACY,EAAA,CACC,KAAK,sBACL,cAAU7B,EAA6B,KACzC,EACF,QACC6B,EAAM,MAAK,WAAW,QAASZ,MAACf,GAAwB,GAAI,QAC5D2B,EAAM,MAAK,YAAY,QAASZ,MAACd,GAAyB,GAAI,EAC/Dc,EAAA,IAACY,EAAA,CACC,KAAK,uBACL,cAAUxB,EAA+B,IAC3C,QACCwB,EAAM,MAAK,OAAO,QAASZ,MAACb,GAAoB,GAAI,EACrDa,EAAA,IAACY,EAAA,CACC,KAAK,kBACL,cAAUvB,EAA0B,IACtC,QACCuB,EAAM,MAAK,UAAU,QAASZ,MAACV,GAAuB,GAAI,QAG1DsB,EAAM,MAAK,QAAQ,QAASZ,MAACR,IAAW,GAAI,QAC5CoB,EAAM,MAAK,kBAAkB,QAASZ,MAACP,IAAe,GAAI,QAC1DmB,EAAM,MAAK,cAAc,QAASZ,MAACN,IAAe,GAAI,QACtDkB,EAAM,MAAK,iBAAiB,QAASZ,MAACL,GAAgB,GAAI,QAC1DiB,EAAM,MAAK,sBAAsB,QAASZ,MAACL,GAAgB,GAAI,QAC/DiB,EAAM,MAAK,mBAAmB,QAASZ,MAACJ,IAAgB,GAAI,QAC5DgB,EAAM,MAAK,aAAa,QAASZ,MAACH,IAAU,GAAI,QAEhDe,EAAM,MAAK,IAAI,QAASZ,EAAA,IAACT,KAAiB,CAAI,GACjD,EACF,GACF,QACCuB,EAAmB,UACnBC,EAAY,KAGf,CAEJ,CCtMA,MAAMC,EAAc,SAAS,eAAe,MAAM,EAG5CC,QACHC,EAAM,WAAN,CACC,SAAClB,MAAAmB,EAAA,CACC,eAACC,EACC,UAAApB,EAAA,IAACqB,EAAA,CACC,OAAQ,CACN,mBAAoB,GACpB,qBAAsB,EACxB,EAEA,eAACpB,GAAI,IACP,CACF,EACF,GACF,EAIIqB,GAAiBN,GAAeA,EAAY,cAAc,EAGhE,GAAI,CACEM,GAEFC,EAAA,YAAYP,EAAaC,CAAgB,EAG5BO,aAAWR,CAAW,EAC9B,OAAOC,CAAgB,CAEhC,OAAStD,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAE9B6D,aAAWR,CAAW,EAC9B,OAAOC,CAAgB,CAC9B", "names": ["parallaxMouseMovement", "scene", "e", "w", "h", "offsetX", "offsetY", "el", "offset", "translate", "sceneOffsetX", "parallaxScroll", "addScrollParallax", "element", "rellax_y", "Rellax", "addParallaxX", "rellax_x", "init_wow", "wow", "WOW", "box", "wow_p", "error", "headerChangeOnScroll", "mainNav", "navLogoWrapLogo", "lightAfterScroll", "BMSOverviewPage", "lazy", "__vitePreload", "n", "BMSModulePage", "CoreModulePage", "AccountingModulePage", "BudgetModulePage", "HRModulePage", "RecruitmentModulePage", "ProductionModulePage", "SalesModulePage", "QualityControlModulePage", "CommunicationModulePage", "CompaniesModulePage", "UltimationStudioOverviewPage", "ElegantAboutPageDark", "ElegantServicesPageDark", "ElegantPortfolioPageDark", "ElegantBlogPageDark", "ElegantPortfolioSinglePageDark", "ElegantBlogSinglePageDark", "ElegantContactPageDark", "MainPageNotFound", "AdminLogin", "AdminDashboard", "AdminBlogPosts", "AdminBlogEditor", "AdminCategories", "AdminTags", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "jsx", "App", "pathname", "useLocation", "useEffect", "_a", "pageTitle", "pageLocation", "trackPageView", "Fragment", "Suspense", "Routes", "Route", "Home5MainDemoMultiPageDark", "ScrollTopBehaviour", "GDPRConsent", "rootElement", "AppWithProviders", "React", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>ed", "hydrateRoot", "createRoot"], "ignoreList": [], "sources": ["../../src/utils/parallax.js", "../../src/utils/initWowjs.js", "../../src/utils/changeHeaderOnScroll.js", "../../src/App.jsx", "../../src/main.jsx"], "sourcesContent": ["import Rellax from \"rellax\";\n\nexport const parallaxMouseMovement = () => {\n  document.querySelectorAll(\".parallax-mousemove-scene\").forEach((scene) => {\n    scene.addEventListener(\"mousemove\", function (e) {\n      const w = window.innerWidth;\n      const h = window.innerHeight;\n      const offsetX = 0.5 - (e.pageX - this.offsetLeft) / w;\n      const offsetY = 0.5 - (e.pageY - this.offsetTop) / h;\n\n      this.querySelectorAll(\".parallax-mousemove\").forEach((el) => {\n        const offset = parseInt(el.getAttribute(\"data-offset\"));\n        const translate = `translate3d(${Math.round(\n          offsetX * offset\n        )}px, ${Math.round(offsetY * offset)}px, 0px)`;\n        el.style.transform = translate;\n      });\n\n      let sceneOffsetX = e.pageX - this.offsetLeft;\n      let sceneOffsetY = e.pageY - this.offsetTop;\n\n      this.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n        el.style.left = `${sceneOffsetX}px`;\n        el.style.top = `${31}px`;\n      });\n\n      // document.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n      //   el.style.left = `${sceneOffsetX}px`;\n      // });\n    });\n\n    scene.addEventListener(\"mouseenter\", function (e) {\n      this.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n        setTimeout(() => {\n          el.style.transition = \"all .27s var(--ease-out-short)\";\n          el.style.willChange = \"transform\";\n        }, 27);\n      });\n    });\n\n    scene.addEventListener(\"mouseout\", function (e) {\n      this.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n        el.style.transition = \"none\";\n      });\n    });\n  });\n};\n\nexport function parallaxScroll() {\n  const mobileTest = false; // Assuming mobileTest is defined elsewhere\n\n  if (document.querySelectorAll(\"[data-rellax-y]\").length) {\n    if (window.innerWidth >= 1280 && !mobileTest) {\n      const rellax_y = new Rellax(\"[data-rellax-y]\", {\n        vertical: true,\n        horizontal: false,\n      });\n\n      function addScrollParallax() {\n        document.querySelectorAll(\"[data-rellax-y]\").forEach((element) => {\n          if (\n            element.getBoundingClientRect().top < window.innerHeight &&\n            element.getBoundingClientRect().bottom > 0\n          ) {\n            if (!element.classList.contains(\"js-in-viewport\")) {\n              element.classList.add(\"js-in-viewport\");\n              rellax_y.refresh();\n            }\n          } else {\n            if (element.classList.contains(\"js-in-viewport\")) {\n              element.classList.remove(\"js-in-viewport\");\n            }\n          }\n        });\n      }\n\n      window.addEventListener(\"scroll\", addScrollParallax);\n      // window.removeEventListener(\"scroll\", addScrollParallax);\n      // rellax_y.destroy();\n    }\n  }\n\n  if (document.querySelectorAll(\"[data-rellax-x]\").length) {\n    if (window.innerWidth >= 1280 && !mobileTest) {\n      const rellax_x = new Rellax(\"[data-rellax-x]\", {\n        horizontal: true,\n      });\n      function addParallaxX() {\n        document.querySelectorAll(\"[data-rellax-x]\").forEach((element) => {\n          if (\n            element.getBoundingClientRect().top < window.innerHeight &&\n            element.getBoundingClientRect().bottom > 0\n          ) {\n            if (!element.classList.contains(\"js-in-viewport\")) {\n              element.classList.add(\"js-in-viewport\");\n              rellax_x.refresh();\n            }\n          } else {\n            if (element.classList.contains(\"js-in-viewport\")) {\n              element.classList.remove(\"js-in-viewport\");\n            }\n          }\n        });\n      }\n      window.addEventListener(\"scroll\", addParallaxX);\n      // window.removeEventListener(\"scroll\", addParallaxX);\n    }\n  }\n}\n", "import WOW from \"wow.js\";\nexport function init_wow() {\n  // Add error handling and better timing\n  setTimeout(() => {\n    try {\n      /* Wow init */\n      if (document.body.classList.contains(\"appear-animate\")) {\n        document\n          .querySelectorAll(\".wow\")\n          .forEach((el) => el.classList.add(\"no-animate\"));\n      }\n\n      var wow = new WOW({\n        boxClass: \"wow\",\n        animateClass: \"animated\",\n        offset: 100,\n        mobile: true,\n        live: false, // Disable live detection to prevent issues\n        callback: function (box) {\n          // Ensure element stays visible after animation\n          box.classList.add(\"animated\");\n          box.style.opacity = \"1\";\n          box.style.visibility = \"visible\";\n        },\n      });\n\n      // Always ensure appear-animate class exists\n      if (!document.body.classList.contains(\"appear-animate\")) {\n        document.body.classList.add(\"appear-animate\");\n      }\n\n      // Initialize WOW\n      wow.init();\n\n      // Fallback: ensure all wow elements are visible\n      setTimeout(() => {\n        document.querySelectorAll(\".wow\").forEach((el) => {\n          if (!el.classList.contains(\"animated\")) {\n            el.style.opacity = \"1\";\n            el.style.visibility = \"visible\";\n            el.classList.add(\"animated\");\n          }\n        });\n      }, 2000);\n\n      /* Wow for portfolio init */\n      if (document.body.classList.contains(\"appear-animate\")) {\n        document\n          .querySelectorAll(\".wow-p\")\n          .forEach((el) => el.classList.add(\"no-animate\"));\n      }\n      var wow_p = new WOW({\n        boxClass: \"wow-p\",\n        animateClass: \"animated\",\n        offset: 100,\n        mobile: true,\n        live: false,\n        callback: function (box) {\n          box.classList.add(\"animated\");\n          box.style.opacity = \"1\";\n          box.style.visibility = \"visible\";\n        },\n      });\n\n      if (document.body.classList.contains(\"appear-animate\")) {\n        wow_p.init();\n      } else {\n        document\n          .querySelectorAll(\".wow-p\")\n          .forEach((el) => (el.style.opacity = \"1\"));\n      }\n\n      /* Wow for menu bar init */\n      if (\n        document.body.classList.contains(\"appear-animate\") &&\n        window.innerWidth >= 1024 &&\n        document.documentElement.classList.contains(\"no-mobile\")\n      ) {\n        document.querySelectorAll(\".wow-menubar\").forEach((el) => {\n          el.classList.add(\"no-animate\", \"fadeInDown\", \"animated\");\n          setInterval(() => {\n            el.classList.remove(\"no-animate\");\n          }, 1500);\n        });\n      } else {\n        document\n          .querySelectorAll(\".wow-menubar\")\n          .forEach((el) => (el.style.opacity = \"1\"));\n      }\n    } catch (error) {\n      console.error(\"Error initializing WOW.js:\", error);\n      // Fallback: make all elements visible\n      document.querySelectorAll(\".wow, .wow-p, .wow-menubar\").forEach((el) => {\n        el.style.opacity = \"1\";\n        el.classList.add(\"animated\");\n      });\n    }\n  }, 100);\n}\n", "export const headerChangeOnScroll = () => {\n  var mainNav = document.querySelector(\".main-nav\");\n  var navLogoWrapLogo = document.querySelector(\".nav-logo-wrap .logo\");\n  var lightAfterScroll = document.querySelector(\".light-after-scroll\");\n\n  // Exit early if main navigation doesn't exist (e.g., on admin pages)\n  if (!mainNav) {\n    return;\n  }\n\n  if (window.scrollY > 0) {\n    mainNav.classList.remove(\"transparent\");\n    mainNav.classList.add(\"small-height\", \"body-scrolled\");\n    if (navLogoWrapLogo) navLogoWrapLogo.classList.add(\"small-height\");\n    if (lightAfterScroll) lightAfterScroll.classList.remove(\"dark\");\n  } else if (window.scrollY === 0) {\n    mainNav.classList.add(\"transparent\");\n    mainNav.classList.remove(\"small-height\", \"body-scrolled\");\n    if (navLogoWrapLogo) navLogoWrapLogo.classList.remove(\"small-height\");\n    if (lightAfterScroll) lightAfterScroll.classList.add(\"dark\");\n  }\n};\n", "import React, { useEffect, Suspense, lazy } from \"react\";\nimport \"./styles/styles.css\";\nimport \"./styles/module-buttons.css\";\nimport \"./styles/grayscale-effect.css\";\nimport \"./styles/languageSelector.css\";\nimport \"./styles/gdpr.css\";\nimport \"./i18n\"; // Initialize i18n\nimport GDPRConsent from \"./components/common/GDPRConsent\";\nimport AnalyticsDebug from \"./components/common/AnalyticsDebug\";\nimport { parallaxMouseMovement, parallaxScroll } from \"@/utils/parallax\";\n\nimport { init_wow } from \"@/utils/initWowjs\";\nimport { headerChangeOnScroll } from \"@/utils/changeHeaderOnScroll\";\nimport { Route, Routes, useLocation } from \"react-router-dom\";\nimport { trackPageView } from \"@/utils/analytics\";\n\nimport ScrollTopBehaviour from \"./components/common/ScrollTopBehaviour\";\n\n// Eager load the home page for better initial performance\nimport Home5MainDemoMultiPageDark from \"@/pages/home/<USER>\";\n\n// Lazy load all other pages to reduce initial bundle size\nconst BMSOverviewPage = lazy(() =>\n  import(\"@/pages/products/bms/overview/page\")\n);\nconst BMSModulePage = lazy(() => import(\"@/pages/products/bms/module/page\"));\nconst CoreModulePage = lazy(() => import(\"@/pages/products/bms/core/page\"));\nconst AccountingModulePage = lazy(() =>\n  import(\"@/pages/products/bms/accounting/page\")\n);\nconst BudgetModulePage = lazy(() => import(\"@/pages/products/bms/budget/page\"));\nconst HRModulePage = lazy(() => import(\"@/pages/products/bms/hr/page\"));\nconst RecruitmentModulePage = lazy(() =>\n  import(\"@/pages/products/bms/recruitment/page\")\n);\nconst ProductionModulePage = lazy(() =>\n  import(\"@/pages/products/bms/production/page\")\n);\nconst SalesModulePage = lazy(() => import(\"@/pages/products/bms/sales/page\"));\nconst QualityControlModulePage = lazy(() =>\n  import(\"@/pages/products/bms/quality/page\")\n);\nconst CommunicationModulePage = lazy(() =>\n  import(\"@/pages/products/bms/communication/page\")\n);\nconst CompaniesModulePage = lazy(() =>\n  import(\"@/pages/products/bms/companies/page\")\n);\nconst UltimationStudioOverviewPage = lazy(() =>\n  import(\"@/pages/products/ultimation/overview/page\")\n);\n\nconst ElegantAboutPageDark = lazy(() => import(\"./pages/about/page\"));\nconst ElegantServicesPageDark = lazy(() => import(\"./pages/services/page\"));\nconst ElegantPortfolioPageDark = lazy(() => import(\"./pages/portfolio/page\"));\nconst ElegantBlogPageDark = lazy(() => import(\"./pages/blogs/page\"));\nconst ElegantPortfolioSinglePageDark = lazy(() =>\n  import(\"./pages/portfolio-single/page\")\n);\nconst ElegantBlogSinglePageDark = lazy(() =>\n  import(\"./pages/blog-single/page\")\n);\nconst ElegantContactPageDark = lazy(() => import(\"./pages/contact/page\"));\nconst MainPageNotFound = lazy(() => import(\"./pages/otherPages/page\"));\n\n// Admin pages\nconst AdminLogin = lazy(() => import(\"./pages/AdminLogin\"));\nconst AdminDashboard = lazy(() => import(\"./pages/AdminDashboard\"));\nconst AdminBlogPosts = lazy(() => import(\"./pages/AdminBlogPosts\"));\nconst AdminBlogEditor = lazy(() => import(\"./pages/AdminBlogEditor\"));\nconst AdminCategories = lazy(() => import(\"./pages/AdminCategories\"));\nconst AdminTags = lazy(() => import(\"./pages/AdminTags\"));\n\n// Loading component for lazy-loaded routes\nconst PageLoader = () => (\n  <div\n    className=\"page-loader\"\n    style={{\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      width: \"100%\",\n      height: \"100%\",\n      backgroundColor: \"#1a1a1a\",\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      zIndex: 9999,\n    }}\n  >\n    <div\n      className=\"loader\"\n      style={{\n        width: \"40px\",\n        height: \"40px\",\n        border: \"4px solid #333\",\n        borderTop: \"4px solid #fff\",\n        borderRadius: \"50%\",\n        animation: \"spin 1s linear infinite\",\n      }}\n    ></div>\n    <style>{`\n      @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n    `}</style>\n  </div>\n);\n\nfunction App() {\n  const { pathname } = useLocation();\n  useEffect(() => {\n    init_wow();\n    parallaxMouseMovement();\n    var mainNav = document.querySelector(\".main-nav\");\n    if (mainNav?.classList.contains(\"transparent\")) {\n      mainNav.classList.add(\"js-transparent\");\n    } else if (!mainNav?.classList?.contains(\"dark\")) {\n      mainNav?.classList.add(\"js-no-transparent-white\");\n    }\n\n    window.addEventListener(\"scroll\", headerChangeOnScroll);\n    parallaxScroll();\n\n    // Track page view on route change\n    const pageTitle = document.title || \"DevSkills\";\n    const pageLocation = window.location.href;\n    trackPageView(pageTitle, pageLocation);\n\n    return () => {\n      window.removeEventListener(\"scroll\", headerChangeOnScroll);\n    };\n  }, [pathname]);\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      // Import the script only on the client side\n      import(\"bootstrap/dist/js/bootstrap.esm\").then(() => {\n        // Bootstrap is now loaded and available\n        console.log(\"Bootstrap loaded\");\n      });\n    }\n  }, []);\n  return (\n    <>\n      <Suspense fallback={<PageLoader />}>\n        <Routes>\n          <Route path=\"/\">\n            <Route index element={<Home5MainDemoMultiPageDark />} />\n            <Route path=\"about\" element={<ElegantAboutPageDark />} />\n            <Route path=\"products\">\n              <Route path=\"bms\" element={<BMSOverviewPage />} />\n              <Route path=\"bms/core\" element={<CoreModulePage />} />\n              <Route path=\"bms/accounting\" element={<AccountingModulePage />} />\n              <Route path=\"bms/budget\" element={<BudgetModulePage />} />\n              <Route path=\"bms/hr\" element={<HRModulePage />} />\n              <Route\n                path=\"bms/recruitment\"\n                element={<RecruitmentModulePage />}\n              />\n              <Route path=\"bms/production\" element={<ProductionModulePage />} />\n              <Route path=\"bms/sales\" element={<SalesModulePage />} />\n              <Route\n                path=\"bms/quality\"\n                element={<QualityControlModulePage />}\n              />\n              <Route\n                path=\"bms/communication\"\n                element={<CommunicationModulePage />}\n              />\n              <Route path=\"bms/companies\" element={<CompaniesModulePage />} />\n              <Route path=\"bms/:moduleId\" element={<BMSModulePage />} />\n              <Route\n                path=\"ultimation/overview\"\n                element={<UltimationStudioOverviewPage />}\n              />\n            </Route>\n            <Route path=\"services\" element={<ElegantServicesPageDark />} />\n            <Route path=\"portfolio\" element={<ElegantPortfolioPageDark />} />\n            <Route\n              path=\"portfolio-single/:id\"\n              element={<ElegantPortfolioSinglePageDark />}\n            />\n            <Route path=\"blog\" element={<ElegantBlogPageDark />} />\n            <Route\n              path=\"blog-single/:id\"\n              element={<ElegantBlogSinglePageDark />}\n            />\n            <Route path=\"contact\" element={<ElegantContactPageDark />} />\n\n            {/* Admin routes */}\n            <Route path=\"admin\" element={<AdminLogin />} />\n            <Route path=\"admin/dashboard\" element={<AdminDashboard />} />\n            <Route path=\"admin/posts\" element={<AdminBlogPosts />} />\n            <Route path=\"admin/blog/new\" element={<AdminBlogEditor />} />\n            <Route path=\"admin/blog/edit/:id\" element={<AdminBlogEditor />} />\n            <Route path=\"admin/categories\" element={<AdminCategories />} />\n            <Route path=\"admin/tags\" element={<AdminTags />} />\n\n            <Route path=\"*\" element={<MainPageNotFound />} />\n          </Route>\n        </Routes>\n      </Suspense>\n      <ScrollTopBehaviour />\n      <GDPRConsent />\n      {/* Analytics Debug Panel - Only show in development */}\n      {/* {import.meta.env.DEV && <AnalyticsDebug />} */}\n    </>\n  );\n}\n\nexport default App;\n", "import React from \"react\";\nimport { createRoot, hydrateRoot } from \"react-dom/client\";\nimport App from \"./App.jsx\";\nimport { B<PERSON>erRouter } from \"react-router-dom\";\nimport { HelmetProvider } from \"react-helmet-async\";\nimport ErrorBoundary from \"./components/common/ErrorBoundary.jsx\";\nimport \"./styles/languageSelector.css\";\n\n// Ready for Google Analytics and Google Tag Manager implementation\n\n// For react-snap compatibility\nconst rootElement = document.getElementById(\"root\");\n\n// Define the app content once to avoid duplication\nconst AppWithProviders = (\n  <React.StrictMode>\n    <ErrorBoundary>\n      <HelmetProvider>\n        <BrowserRouter\n          future={{\n            v7_startTransition: true,\n            v7_relativeSplatPath: true,\n          }}\n        >\n          <App />\n        </BrowserRouter>\n      </HelmetProvider>\n    </ErrorBoundary>\n  </React.StrictMode>\n);\n\n// Check if the document has been prerendered by react-snap\nconst hasPrerendered = rootElement && rootElement.hasChildNodes();\n\n// Use the appropriate rendering method\ntry {\n  if (hasPrerendered) {\n    // For prerendered content, use React 18 hydrateRoot\n    hydrateRoot(rootElement, AppWithProviders);\n  } else {\n    // For fresh loads, use standard rendering\n    const root = createRoot(rootElement);\n    root.render(AppWithProviders);\n  }\n} catch (error) {\n  console.error(\"Error rendering app:\", error);\n  // Fallback rendering\n  const root = createRoot(rootElement);\n  root.render(AppWithProviders);\n}\n"], "file": "assets/index-MejBejiI.js"}