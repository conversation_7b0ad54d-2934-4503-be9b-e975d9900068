"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const upload_1 = require("../middleware/upload");
const categoryController_1 = require("../controllers/categoryController");
const tagController_1 = require("../controllers/tagController");
const router = express_1.default.Router();
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)("ADMIN"));
router.get("/dashboard", async (req, res) => {
    try {
        const { PrismaClient } = await Promise.resolve().then(() => __importStar(require("@prisma/client")));
        const prisma = new PrismaClient();
        const [totalPosts, publishedPosts, draftPosts, totalCategories, totalTags, totalComments, approvedComments,] = await Promise.all([
            prisma.blogPost.count(),
            prisma.blogPost.count({ where: { published: true } }),
            prisma.blogPost.count({ where: { published: false } }),
            prisma.category.count(),
            prisma.tag.count(),
            prisma.comment.count(),
            prisma.comment.count({ where: { approved: true } }),
        ]);
        res.json({
            success: true,
            data: {
                posts: {
                    total: totalPosts,
                    published: publishedPosts,
                    drafts: draftPosts,
                },
                categories: totalCategories,
                tags: totalTags,
                comments: {
                    total: totalComments,
                    approved: approvedComments,
                    pending: totalComments - approvedComments,
                },
            },
        });
    }
    catch (error) {
        console.error("Dashboard stats error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
});
router.get("/posts", async (req, res) => {
    try {
        const { PrismaClient } = await Promise.resolve().then(() => __importStar(require("@prisma/client")));
        const prisma = new PrismaClient();
        const { page = 1, limit = 10, status, search } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const where = {};
        if (status === "published") {
            where.published = true;
        }
        else if (status === "draft") {
            where.published = false;
        }
        if (search) {
            where.translations = {
                some: {
                    OR: [
                        { title: { contains: search, mode: "insensitive" } },
                        { content: { contains: search, mode: "insensitive" } },
                    ],
                },
            };
        }
        const [posts, total] = await Promise.all([
            prisma.blogPost.findMany({
                where,
                include: {
                    author: {
                        select: { id: true, name: true, email: true },
                    },
                    translations: true,
                    categories: {
                        include: { category: true },
                    },
                    tags: {
                        include: { tag: true },
                    },
                    _count: {
                        select: { comments: true },
                    },
                },
                orderBy: { createdAt: "desc" },
                skip,
                take: Number(limit),
            }),
            prisma.blogPost.count({ where }),
        ]);
        res.json({
            success: true,
            data: {
                posts,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total,
                    pages: Math.ceil(total / Number(limit)),
                },
            },
        });
    }
    catch (error) {
        console.error("Get admin posts error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
});
router.post("/upload-image", upload_1.uploadBlogImage.single("image"), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: "No image file provided",
            });
        }
        const { getFileUrl } = require("../middleware/upload");
        const imageUrl = getFileUrl(req.file.filename);
        res.json({
            success: true,
            data: {
                filename: req.file.filename,
                url: imageUrl,
                size: req.file.size,
                mimetype: req.file.mimetype,
            },
        });
    }
    catch (error) {
        console.error("Upload image error:", error);
        res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
});
router.get("/categories", categoryController_1.getCategories);
router.post("/categories", categoryController_1.createCategory);
router.put("/categories/:id", categoryController_1.updateCategory);
router.delete("/categories/:id", categoryController_1.deleteCategory);
router.get("/tags", tagController_1.getTags);
router.post("/tags", tagController_1.createTag);
router.put("/tags/:id", tagController_1.updateTag);
router.delete("/tags/:id", tagController_1.deleteTag);
exports.default = router;
