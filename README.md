# DevSkills - Business Management System & Blog Platform

A comprehensive business management system with multilingual blog capabilities, built with React, TypeScript, Node.js, and PostgreSQL.

## 🌟 Features

### Frontend (React)

- **Multilingual Support**: 5 languages (English, Estonian, Finnish, Swedish, German)
- **Modern UI**: Dark theme with responsive design
- **SEO Optimized**: Proper meta tags, hreflang implementation
- **Blog System**: Dynamic blog with categories, tags, and comments
- **Admin Panel**: Complete content management system

### Backend (TypeScript + Node.js)

- **TypeScript**: Fully typed backend with route/controller architecture
- **Authentication**: Secure JWT-based authentication
- **Database**: PostgreSQL with Prisma ORM
- **File Upload**: Image upload with multer and Docker volume mapping
- **Blog Management**: Full CRUD operations with multilingual content
- **Scheduling**: Blog post scheduling with date/time selection

### Blog Features

- **Multilingual Content**: Create content in all supported languages
- **Rich Text Support**: Paste formatted content from Word documents
- **Image Management**: Upload and manage blog images
- **Scheduling**: Schedule posts for future publication
- **Categories & Tags**: Organize content with categories and tags
- **Comments System**: User comments with moderation
- **SEO Optimization**: Meta tags, structured data, social media optimization

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- PostgreSQL database
- Docker (for production deployment)

### Backend Setup

1. **Navigate to server directory**

   ```bash
   cd server
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   # Edit .env with your database credentials
   DATABASE_URL="postgresql://root:Onamission%23007@37.27.84.251:5432/devskills"
   JWT_SECRET="your-super-secret-jwt-key"
   ```

4. **Set up database**

   ```bash
   pnpm db:generate  # Generate Prisma client
   pnpm db:push      # Create database tables
   pnpm db:seed      # Seed with initial data
   ```

5. **Start development server**
   ```bash
   pnpm dev
   ```

### Frontend Setup

1. **Navigate to client directory**

   ```bash
   cd client
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Start development server**
   ```bash
   pnpm dev
   ```

## 📝 Admin Panel

### Access

- URL: `http://localhost:3000/admin`
- Default credentials:
  - Email: `<EMAIL>`
  - Password: `Valgehunt#1405`

### Features

- **Dashboard**: Overview of posts, categories, tags, and comments
- **Blog Management**: Create, edit, delete, and schedule blog posts
- **Multilingual Content**: Manage content in all supported languages
- **Image Upload**: Upload and manage blog images
- **Categories & Tags**: Organize blog content
- **User Management**: Manage admin users

### Production Deployment

```bash
# Deploy via GitHub Actions (automatic)
git push origin main

# Manual deployment
./deploy.sh
```

## 📧 Contact Form

- **Frontend**: React contact form with validation
- **Backend**: Express.js API with Gmail SMTP
- **Email**: Sent via <NAME_EMAIL>
- **Reply-to**: <EMAIL>

## 🔧 Configuration

### Environment Variables (server/.env)

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>
```

## 🏗️ Architecture

- **Frontend**: React + Vite (port 8082/8083)
- **Backend**: Express.js (port 4005)
- **Deployment**: Blue/Green with Docker + Nginx
- **Email**: Gmail SMTP for reliability

## 📋 Scripts

- `deploy.sh` - Deploy to production
- `rollback.sh` - Rollback deployment

## 🌐 URLs

- **Production**: https://devskills.ee
- **API**: https://devskills.ee/api/v1/communication/public/contact-form
