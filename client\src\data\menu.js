export const menuItems = [
  { href: "/", text: "Home" },
  { href: "/about", text: "About" },
  { href: "/products", text: "Products" },
  { href: "/services", text: "Services" },
  // { href: "/portfolio", text: "Portfolio" },
  { href: "/blog", text: "Blog" },
  { href: "/contact", text: "Contact" },
];

export const productsSubmenu = {
  bms: {
    title: "products.bms.title", // Using translation key instead of hardcoded text
    href: "/products/ultimation/overview",
    modules: [
      { href: "/products/bms/core", text: "bms.module.core" },
      { href: "/products/bms/accounting", text: "bms.module.accounting" },
      { href: "/products/bms/budget", text: "bms.module.budget" },
      { href: "/products/bms/hr", text: "bms.module.hr" },
      { href: "/products/bms/recruitment", text: "bms.module.recruiting" },
      { href: "/products/bms/production", text: "bms.module.production" },
      { href: "/products/bms/sales", text: "bms.module.sales" },
      { href: "/products/bms/quality", text: "bms.module.quality" },
      { href: "/products/bms/communication", text: "bms.module.communication" },
      { href: "/products/bms/companies", text: "bms.module.companies" },
    ],
  },
};
