{"version": 3, "sources": ["../../.pnpm/@tiptap+extension-superscript@2.22.3_@tiptap+core@2.22.3_@tiptap+pm@2.22.3_/node_modules/@tiptap/extension-superscript/src/superscript.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\nimport type { StyleParseRule } from '@tiptap/pm/model'\n\nexport interface SuperscriptExtensionOptions {\n  /**\n   * HTML attributes to add to the superscript element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    superscript: {\n      /**\n       * Set a superscript mark\n       * @example editor.commands.setSuperscript()\n       */\n      setSuperscript: () => ReturnType,\n      /**\n       * Toggle a superscript mark\n       * @example editor.commands.toggleSuperscript()\n       */\n      toggleSuperscript: () => ReturnType,\n      /**\n       * Unset a superscript mark\n       *  @example editor.commands.unsetSuperscript()\n       */\n      unsetSuperscript: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create superscript text.\n * @see https://www.tiptap.dev/api/marks/superscript\n */\nexport const Superscript = Mark.create<SuperscriptExtensionOptions>({\n  name: 'superscript',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'sup',\n      },\n      {\n        style: 'vertical-align',\n        getAttrs(value) {\n          // Don’t match this rule if the vertical align isn’t super.\n          if (value !== 'super') {\n            return false\n          }\n\n          // If it falls through we’ll match, and this mark will be applied.\n          return null\n        },\n      } as StyleParseRule,\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['sup', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setSuperscript: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleSuperscript: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetSuperscript: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-.': () => this.editor.commands.toggleSuperscript(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;AAsCa,IAAA,cAAc,KAAK,OAAoC;EAClE,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,OAAO;QACP,SAAS,OAAK;AAEZ,cAAI,UAAU,SAAS;AACrB,mBAAO;;AAIT,iBAAO;;MAEQ;;;EAIvB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,OAAO,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGhF,cAAW;AACT,WAAO;MACL,gBAAgB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACrC,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,mBAAmB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACxC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,kBAAkB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACvC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,kBAAiB;;;AAG1D,CAAA;", "names": []}