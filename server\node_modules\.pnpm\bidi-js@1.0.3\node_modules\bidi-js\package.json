{"name": "bidi-js", "version": "1.0.3", "description": "A JavaScript implementation of the Unicode Bidirectional Algorithm", "main": "dist/bidi.js", "module": "dist/bidi.mjs", "repository": {"type": "git", "url": "https://github.com/lojjic/bidi-js.git"}, "scripts": {"build": "rollup -c rollup.config.js", "test": "npx babel-node --plugins @babel/plugin-transform-modules-commonjs test/runTestsOnSrc.js", "test-build": "node test/runTestsOnBuild.js"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/cli": "^7.13.16", "@babel/core": "^7.14.0", "@babel/node": "^7.13.13", "@babel/plugin-transform-modules-commonjs": "^7.13.8", "@babel/preset-env": "^7.14.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-buble": "^0.21.3", "node-fetch": "^2.6.1", "rollup": "^2.45.1", "rollup-plugin-terser": "^7.0.2"}, "files": ["/dist", "/src", "/LICENSE.txt", "/README.md"], "dependencies": {"require-from-string": "^2.0.2"}}