import React, { useEffect, Suspense, lazy } from "react";
import "./styles/styles.css";
import "./styles/module-buttons.css";
import "./styles/grayscale-effect.css";
import "./styles/languageSelector.css";
import "./styles/gdpr.css";
import "./i18n"; // Initialize i18n
import GDPRConsent from "./components/common/GDPRConsent";
import AnalyticsDebug from "./components/common/AnalyticsDebug";
import { parallaxMouseMovement, parallaxScroll } from "@/utils/parallax";

import { init_wow } from "@/utils/initWowjs";
import { headerChangeOnScroll } from "@/utils/changeHeaderOnScroll";
import { Route, Routes, useLocation } from "react-router-dom";
import { trackPageView } from "@/utils/analytics";

import ScrollTopBehaviour from "./components/common/ScrollTopBehaviour";

// Eager load the home page for better initial performance
import Home5MainDemoMultiPageDark from "@/pages/home/<USER>";

// Lazy load all other pages to reduce initial bundle size
const BMSOverviewPage = lazy(() =>
  import("@/pages/products/bms/overview/page")
);
const BMSModulePage = lazy(() => import("@/pages/products/bms/module/page"));
const CoreModulePage = lazy(() => import("@/pages/products/bms/core/page"));
const AccountingModulePage = lazy(() =>
  import("@/pages/products/bms/accounting/page")
);
const BudgetModulePage = lazy(() => import("@/pages/products/bms/budget/page"));
const HRModulePage = lazy(() => import("@/pages/products/bms/hr/page"));
const RecruitmentModulePage = lazy(() =>
  import("@/pages/products/bms/recruitment/page")
);
const ProductionModulePage = lazy(() =>
  import("@/pages/products/bms/production/page")
);
const SalesModulePage = lazy(() => import("@/pages/products/bms/sales/page"));
const QualityControlModulePage = lazy(() =>
  import("@/pages/products/bms/quality/page")
);
const CommunicationModulePage = lazy(() =>
  import("@/pages/products/bms/communication/page")
);
const CompaniesModulePage = lazy(() =>
  import("@/pages/products/bms/companies/page")
);
const UltimationStudioOverviewPage = lazy(() =>
  import("@/pages/products/ultimation/overview/page")
);

const ElegantAboutPageDark = lazy(() => import("./pages/about/page"));
const ElegantServicesPageDark = lazy(() => import("./pages/services/page"));
const ElegantPortfolioPageDark = lazy(() => import("./pages/portfolio/page"));
const ElegantBlogPageDark = lazy(() => import("./pages/blogs/page"));
const ElegantPortfolioSinglePageDark = lazy(() =>
  import("./pages/portfolio-single/page")
);
const ElegantBlogSinglePageDark = lazy(() =>
  import("./pages/blog-single/page")
);
const ElegantContactPageDark = lazy(() => import("./pages/contact/page"));
const MainPageNotFound = lazy(() => import("./pages/otherPages/page"));

// Admin pages
const AdminLogin = lazy(() => import("./pages/AdminLogin"));
const AdminDashboard = lazy(() => import("./pages/AdminDashboard"));
const AdminBlogPosts = lazy(() => import("./pages/AdminBlogPosts"));
const AdminBlogEditor = lazy(() => import("./pages/AdminBlogEditor"));
const AdminCategories = lazy(() => import("./pages/AdminCategories"));
const AdminTags = lazy(() => import("./pages/AdminTags"));
const HighlightTestPage = lazy(() => import("./pages/highlight-test/page"));

// Loading component for lazy-loaded routes
const PageLoader = () => (
  <div
    className="page-loader"
    style={{
      position: "fixed",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "#1a1a1a",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 9999,
    }}
  >
    <div
      className="loader"
      style={{
        width: "40px",
        height: "40px",
        border: "4px solid #333",
        borderTop: "4px solid #fff",
        borderRadius: "50%",
        animation: "spin 1s linear infinite",
      }}
    ></div>
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

function App() {
  const { pathname } = useLocation();
  useEffect(() => {
    init_wow();
    parallaxMouseMovement();
    var mainNav = document.querySelector(".main-nav");
    if (mainNav?.classList.contains("transparent")) {
      mainNav.classList.add("js-transparent");
    } else if (!mainNav?.classList?.contains("dark")) {
      mainNav?.classList.add("js-no-transparent-white");
    }

    window.addEventListener("scroll", headerChangeOnScroll);
    parallaxScroll();

    // Track page view on route change
    const pageTitle = document.title || "DevSkills";
    const pageLocation = window.location.href;
    trackPageView(pageTitle, pageLocation);

    return () => {
      window.removeEventListener("scroll", headerChangeOnScroll);
    };
  }, [pathname]);
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Import the script only on the client side
      import("bootstrap/dist/js/bootstrap.esm").then(() => {
        // Bootstrap is now loaded and available
        console.log("Bootstrap loaded");
      });
    }
  }, []);
  return (
    <>
      <Suspense fallback={<PageLoader />}>
        <Routes>
          <Route path="/">
            <Route index element={<Home5MainDemoMultiPageDark />} />
            <Route path="about" element={<ElegantAboutPageDark />} />
            <Route path="products">
              <Route path="bms" element={<BMSOverviewPage />} />
              <Route path="bms/core" element={<CoreModulePage />} />
              <Route path="bms/accounting" element={<AccountingModulePage />} />
              <Route path="bms/budget" element={<BudgetModulePage />} />
              <Route path="bms/hr" element={<HRModulePage />} />
              <Route
                path="bms/recruitment"
                element={<RecruitmentModulePage />}
              />
              <Route path="bms/production" element={<ProductionModulePage />} />
              <Route path="bms/sales" element={<SalesModulePage />} />
              <Route
                path="bms/quality"
                element={<QualityControlModulePage />}
              />
              <Route
                path="bms/communication"
                element={<CommunicationModulePage />}
              />
              <Route path="bms/companies" element={<CompaniesModulePage />} />
              <Route path="bms/:moduleId" element={<BMSModulePage />} />
              <Route
                path="ultimation/overview"
                element={<UltimationStudioOverviewPage />}
              />
            </Route>
            <Route path="services" element={<ElegantServicesPageDark />} />
            <Route path="portfolio" element={<ElegantPortfolioPageDark />} />
            <Route
              path="portfolio-single/:id"
              element={<ElegantPortfolioSinglePageDark />}
            />
            <Route path="blog" element={<ElegantBlogPageDark />} />
            <Route
              path="blog-single/:id"
              element={<ElegantBlogSinglePageDark />}
            />
            <Route path="contact" element={<ElegantContactPageDark />} />

            {/* Admin routes */}
            <Route path="admin" element={<AdminLogin />} />
            <Route path="admin/dashboard" element={<AdminDashboard />} />
            <Route path="admin/posts" element={<AdminBlogPosts />} />
            <Route path="admin/blog/new" element={<AdminBlogEditor />} />
            <Route path="admin/blog/edit/:id" element={<AdminBlogEditor />} />
            <Route path="admin/categories" element={<AdminCategories />} />
            <Route path="admin/tags" element={<AdminTags />} />

            {/* Test route for highlight.js debugging */}
            <Route path="highlight-test" element={<HighlightTestPage />} />

            <Route path="*" element={<MainPageNotFound />} />
          </Route>
        </Routes>
      </Suspense>
      <ScrollTopBehaviour />
      <GDPRConsent />
      {/* Analytics Debug Panel - Only show in development */}
      {/* {import.meta.env.DEV && <AnalyticsDebug />} */}
    </>
  );
}

export default App;
