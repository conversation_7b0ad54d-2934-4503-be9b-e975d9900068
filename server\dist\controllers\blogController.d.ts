import { Request, Response } from "express";
import { AuthRequest } from "../middleware/auth";
export declare const getBlogPosts: (req: Request, res: Response) => Promise<void>;
export declare const getBlogPost: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createBlogPost: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteBlogPost: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const toggleBlogPostVisibility: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateBlogPost: (req: AuthRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=blogController.d.ts.map