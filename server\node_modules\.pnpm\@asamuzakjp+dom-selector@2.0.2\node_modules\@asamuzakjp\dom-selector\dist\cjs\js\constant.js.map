{"version": 3, "sources": ["../../../src/js/constant.js"], "sourcesContent": ["/**\n * constant.js\n */\n\n/* string */\nexport const ALPHA_NUM = '[A-Z\\\\d]+';\nexport const AN_PLUS_B = 'AnPlusB';\nexport const COMBINATOR = 'Combinator';\nexport const IDENTIFIER = 'Identifier';\nexport const NOT_SUPPORTED_ERR = 'NotSupportedError';\nexport const NTH = 'Nth';\nexport const RAW = 'Raw';\nexport const SELECTOR = 'Selector';\nexport const SELECTOR_ATTR = 'AttributeSelector';\nexport const SELECTOR_CLASS = 'ClassSelector';\nexport const SELECTOR_ID = 'IdSelector';\nexport const SELECTOR_LIST = 'SelectorList';\nexport const SELECTOR_PSEUDO_CLASS = 'PseudoClassSelector';\nexport const SELECTOR_PSEUDO_ELEMENT = 'PseudoElementSelector';\nexport const SELECTOR_TYPE = 'TypeSelector';\nexport const STRING = 'String';\nexport const SYNTAX_ERR = 'SyntaxError';\nexport const U_FFFD = '\\uFFFD';\n\n/* numeric */\nexport const BIT_01 = 1;\nexport const BIT_02 = 2;\nexport const BIT_04 = 4;\nexport const BIT_08 = 8;\nexport const BIT_16 = 0x10;\nexport const BIT_32 = 0x20;\nexport const BIT_HYPHEN = 0x2D;\nexport const DUO = 2;\nexport const HEX = 16;\nexport const MAX_BIT_16 = 0xFFFF;\nexport const TYPE_FROM = 8;\nexport const TYPE_TO = -1;\n\n/* Node */\nexport const ELEMENT_NODE = 1;\nexport const TEXT_NODE = 3;\nexport const DOCUMENT_NODE = 9;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\nexport const DOCUMENT_POSITION_PRECEDING = 2;\nexport const DOCUMENT_POSITION_CONTAINS = 8;\nexport const DOCUMENT_POSITION_CONTAINED_BY = 0x10;\n\n/* NodeFilter */\nexport const SHOW_ALL = 0xffffffff;\nexport const SHOW_DOCUMENT = 0x100;\nexport const SHOW_DOCUMENT_FRAGMENT = 0x400;\nexport const SHOW_ELEMENT = 1;\n\n/* regexp */\nexport const REG_LOGICAL_PSEUDO = /^(?:(?:ha|i)s|not|where)$/;\nexport const REG_SHADOW_HOST = /^host(?:-context)?$/;\nexport const REG_SHADOW_MODE = /^(?:close|open)$/;\nexport const REG_SHADOW_PSEUDO = /^part|slotted$/;\n"], "mappings": "4ZAAA,IAAAA,GAAA,GAAAC,EAAAD,GAAA,eAAAE,EAAA,cAAAC,EAAA,WAAAC,EAAA,WAAAC,EAAA,WAAAC,EAAA,WAAAC,EAAA,WAAAC,EAAA,WAAAC,EAAA,eAAAC,EAAA,eAAAC,EAAA,2BAAAC,EAAA,kBAAAC,EAAA,mCAAAC,EAAA,+BAAAC,EAAA,gCAAAC,EAAA,QAAAC,EAAA,iBAAAC,EAAA,QAAAC,EAAA,eAAAC,EAAA,eAAAC,EAAA,sBAAAC,EAAA,QAAAC,EAAA,QAAAC,EAAA,uBAAAC,EAAA,oBAAAC,GAAA,oBAAAC,GAAA,sBAAAC,GAAA,aAAAC,EAAA,kBAAAC,EAAA,mBAAAC,EAAA,gBAAAC,EAAA,kBAAAC,EAAA,0BAAAC,EAAA,4BAAAC,EAAA,kBAAAC,EAAA,aAAAC,EAAA,kBAAAC,EAAA,2BAAAC,EAAA,iBAAAC,EAAA,WAAAC,EAAA,eAAAC,EAAA,cAAAC,EAAA,cAAAC,EAAA,YAAAC,EAAA,WAAAC,IAAA,eAAAC,EAAA/C,IAKO,MAAME,EAAY,YACZC,EAAY,UACZQ,EAAa,aACbS,EAAa,aACbE,EAAoB,oBACpBC,EAAM,MACNC,EAAM,MACNK,EAAW,WACXC,EAAgB,oBAChBC,EAAiB,gBACjBC,EAAc,aACdC,EAAgB,eAChBC,EAAwB,sBACxBC,EAA0B,wBAC1BC,EAAgB,eAChBK,EAAS,SACTC,EAAa,cACbI,EAAS,SAGT1C,EAAS,EACTC,EAAS,EACTC,EAAS,EACTC,EAAS,EACTC,EAAS,GACTC,EAAS,GACTC,EAAa,GACbO,EAAM,EACNE,EAAM,GACNE,EAAa,MACbuB,EAAY,EACZC,EAAU,GAGV3B,EAAe,EACfyB,EAAY,EACZ9B,EAAgB,EAChBD,EAAyB,GACzBI,EAA8B,EAC9BD,EAA6B,EAC7BD,EAAiC,GAGjCuB,EAAW,WACXC,EAAgB,IAChBC,EAAyB,KACzBC,EAAe,EAGff,EAAqB,4BACrBC,GAAkB,sBAClBC,GAAkB,mBAClBC,GAAoB", "names": ["constant_exports", "__export", "ALPHA_NUM", "AN_PLUS_B", "BIT_01", "BIT_02", "BIT_04", "BIT_08", "BIT_16", "BIT_32", "BIT_HYPHEN", "COMBINATOR", "DOCUMENT_FRAGMENT_NODE", "DOCUMENT_NODE", "DOCUMENT_POSITION_CONTAINED_BY", "DOCUMENT_POSITION_CONTAINS", "DOCUMENT_POSITION_PRECEDING", "DUO", "ELEMENT_NODE", "HEX", "IDENTIFIER", "MAX_BIT_16", "NOT_SUPPORTED_ERR", "NTH", "RAW", "REG_LOGICAL_PSEUDO", "REG_SHADOW_HOST", "REG_SHADOW_MODE", "REG_SHADOW_PSEUDO", "SELECTOR", "SELECTOR_ATTR", "SELECTOR_CLASS", "SELECTOR_ID", "SELECTOR_LIST", "SELECTOR_PSEUDO_CLASS", "SELECTOR_PSEUDO_ELEMENT", "SELECTOR_TYPE", "SHOW_ALL", "SHOW_DOCUMENT", "SHOW_DOCUMENT_FRAGMENT", "SHOW_ELEMENT", "STRING", "SYNTAX_ERR", "TEXT_NODE", "TYPE_FROM", "TYPE_TO", "U_FFFD", "__toCommonJS"]}