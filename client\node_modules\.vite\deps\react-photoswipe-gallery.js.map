{"version": 3, "sources": ["../../.pnpm/photoswipe@5.4.4/src/js/util/util.js", "../../.pnpm/photoswipe@5.4.4/src/js/util/dom-events.js", "../../.pnpm/photoswipe@5.4.4/src/js/util/viewport-size.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/pan-bounds.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/zoom-level.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/slide.js", "../../.pnpm/photoswipe@5.4.4/src/js/gestures/drag-handler.js", "../../.pnpm/photoswipe@5.4.4/src/js/gestures/zoom-handler.js", "../../.pnpm/photoswipe@5.4.4/src/js/gestures/tap-handler.js", "../../.pnpm/photoswipe@5.4.4/src/js/gestures/gestures.js", "../../.pnpm/photoswipe@5.4.4/src/js/main-scroll.js", "../../.pnpm/photoswipe@5.4.4/src/js/keyboard.js", "../../.pnpm/photoswipe@5.4.4/src/js/util/css-animation.js", "../../.pnpm/photoswipe@5.4.4/src/js/util/spring-easer.js", "../../.pnpm/photoswipe@5.4.4/src/js/util/spring-animation.js", "../../.pnpm/photoswipe@5.4.4/src/js/util/animations.js", "../../.pnpm/photoswipe@5.4.4/src/js/scroll-wheel.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/ui-element.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/button-arrow.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/button-close.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/button-zoom.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/loading-indicator.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/counter-indicator.js", "../../.pnpm/photoswipe@5.4.4/src/js/ui/ui.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/get-thumb-bounds.js", "../../.pnpm/photoswipe@5.4.4/src/js/core/eventable.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/placeholder.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/content.js", "../../.pnpm/photoswipe@5.4.4/src/js/slide/loader.js", "../../.pnpm/photoswipe@5.4.4/src/js/core/base.js", "../../.pnpm/photoswipe@5.4.4/src/js/opener.js", "../../.pnpm/photoswipe@5.4.4/src/js/photoswipe.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/gallery.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/sort-nodes.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/object-to-hash.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/hash-to-object.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/get-hash-without-gid-and-pid.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/get-hash-value.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/get-base-url.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/hash-includes-navigation-query-params.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/get-initial-active-slide-index.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/no-ref-error.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/entry-item-ref-is-element.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/helpers/ensure-ref-passed.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/context.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/lightbox-stub.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/item.js", "../../.pnpm/react-photoswipe-gallery@3.0.2_photoswipe@5.4.4_prop-types@15.8.1_react@19.0.0/node_modules/react-photoswipe-gallery/dist/hooks.js"], "sourcesContent": ["/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/**\r\n * @template {keyof HTMLElementTagNameMap} T\r\n * @param {string} className\r\n * @param {T} tagName\r\n * @param {Node} [appendToEl]\r\n * @returns {HTMLElementTagNameMap[T]}\r\n */\r\nexport function createElement(className, tagName, appendToEl) {\r\n  const el = document.createElement(tagName);\r\n  if (className) {\r\n    el.className = className;\r\n  }\r\n  if (appendToEl) {\r\n    appendToEl.appendChild(el);\r\n  }\r\n  return el;\r\n}\r\n\r\n/**\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\r\nexport function equalizePoints(p1, p2) {\r\n  p1.x = p2.x;\r\n  p1.y = p2.y;\r\n  if (p2.id !== undefined) {\r\n    p1.id = p2.id;\r\n  }\r\n  return p1;\r\n}\r\n\r\n/**\r\n * @param {Point} p\r\n */\r\nexport function roundPoint(p) {\r\n  p.x = Math.round(p.x);\r\n  p.y = Math.round(p.y);\r\n}\r\n\r\n/**\r\n * Returns distance between two points.\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {number}\r\n */\r\nexport function getDistanceBetween(p1, p2) {\r\n  const x = Math.abs(p1.x - p2.x);\r\n  const y = Math.abs(p1.y - p2.y);\r\n  return Math.sqrt((x * x) + (y * y));\r\n}\r\n\r\n/**\r\n * Whether X and Y positions of points are equal\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {boolean}\r\n */\r\nexport function pointsEqual(p1, p2) {\r\n  return p1.x === p2.x && p1.y === p2.y;\r\n}\r\n\r\n/**\r\n * The float result between the min and max values.\r\n *\r\n * @param {number} val\r\n * @param {number} min\r\n * @param {number} max\r\n * @returns {number}\r\n */\r\nexport function clamp(val, min, max) {\r\n  return Math.min(Math.max(val, min), max);\r\n}\r\n\r\n/**\r\n * Get transform string\r\n *\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n * @returns {string}\r\n */\r\nexport function toTransformString(x, y, scale) {\r\n  let propValue = `translate3d(${x}px,${y || 0}px,0)`;\r\n\r\n  if (scale !== undefined) {\r\n    propValue += ` scale3d(${scale},${scale},1)`;\r\n  }\r\n\r\n  return propValue;\r\n}\r\n\r\n/**\r\n * Apply transform:translate(x, y) scale(scale) to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n */\r\nexport function setTransform(el, x, y, scale) {\r\n  el.style.transform = toTransformString(x, y, scale);\r\n}\r\n\r\nconst defaultCSSEasing = 'cubic-bezier(.4,0,.22,1)';\r\n\r\n/**\r\n * Apply CSS transition to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string} [prop] CSS property to animate\r\n * @param {number} [duration] in ms\r\n * @param {string} [ease] CSS easing function\r\n */\r\nexport function setTransitionStyle(el, prop, duration, ease) {\r\n  // inOut: 'cubic-bezier(.4, 0, .22, 1)', // for \"toggle state\" transitions\r\n  // out: 'cubic-bezier(0, 0, .22, 1)', // for \"show\" transitions\r\n  // in: 'cubic-bezier(.4, 0, 1, 1)'// for \"hide\" transitions\r\n  el.style.transition = prop\r\n    ? `${prop} ${duration}ms ${ease || defaultCSSEasing}`\r\n    : 'none';\r\n}\r\n\r\n/**\r\n * Apply width and height CSS properties to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string | number} w\r\n * @param {string | number} h\r\n */\r\nexport function setWidthHeight(el, w, h) {\r\n  el.style.width = (typeof w === 'number') ? `${w}px` : w;\r\n  el.style.height = (typeof h === 'number') ? `${h}px` : h;\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n */\r\nexport function removeTransitionStyle(el) {\r\n  setTransitionStyle(el);\r\n}\r\n\r\n/**\r\n * @param {HTMLImageElement} img\r\n * @returns {Promise<HTMLImageElement | void>}\r\n */\r\nexport function decodeImage(img) {\r\n  if ('decode' in img) {\r\n    return img.decode().catch(() => {});\r\n  }\r\n\r\n  if (img.complete) {\r\n    return Promise.resolve(img);\r\n  }\r\n\r\n  return new Promise((resolve, reject) => {\r\n    img.onload = () => resolve(img);\r\n    img.onerror = reject;\r\n  });\r\n}\r\n\r\n/** @typedef {LOAD_STATE[keyof LOAD_STATE]} LoadState */\r\n/** @type {{ IDLE: 'idle'; LOADING: 'loading'; LOADED: 'loaded'; ERROR: 'error' }} */\r\nexport const LOAD_STATE = {\r\n  IDLE: 'idle',\r\n  LOADING: 'loading',\r\n  LOADED: 'loaded',\r\n  ERROR: 'error',\r\n};\r\n\r\n\r\n/**\r\n * Check if click or keydown event was dispatched\r\n * with a special key or via mouse wheel.\r\n *\r\n * @param {MouseEvent | KeyboardEvent} e\r\n * @returns {boolean}\r\n */\r\nexport function specialKeyUsed(e) {\r\n  return ('button' in e && e.button === 1) || e.ctrlKey || e.metaKey || e.altKey || e.shiftKey;\r\n}\r\n\r\n/**\r\n * Parse `gallery` or `children` options.\r\n *\r\n * @param {import('../photoswipe.js').ElementProvider} [option]\r\n * @param {string} [legacySelector]\r\n * @param {HTMLElement | Document} [parent]\r\n * @returns HTMLElement[]\r\n */\r\nexport function getElementsFromOption(option, legacySelector, parent = document) {\r\n  /** @type {HTMLElement[]} */\r\n  let elements = [];\r\n\r\n  if (option instanceof Element) {\r\n    elements = [option];\r\n  } else if (option instanceof NodeList || Array.isArray(option)) {\r\n    elements = Array.from(option);\r\n  } else {\r\n    const selector = typeof option === 'string' ? option : legacySelector;\r\n    if (selector) {\r\n      elements = Array.from(parent.querySelectorAll(selector));\r\n    }\r\n  }\r\n\r\n  return elements;\r\n}\r\n\r\n/**\r\n * Check if variable is PhotoSwipe class\r\n *\r\n * @param {any} fn\r\n * @returns {boolean}\r\n */\r\nexport function isPswpClass(fn) {\r\n  return typeof fn === 'function'\r\n    && fn.prototype\r\n    && fn.prototype.goTo;\r\n}\r\n\r\n/**\r\n * Check if browser is Safari\r\n *\r\n * @returns {boolean}\r\n */\r\nexport function isSafari() {\r\n  return !!(navigator.vendor && navigator.vendor.match(/apple/i));\r\n}\r\n\r\n", "// Detect passive event listener support\r\nlet supportsPassive = false;\r\n/* eslint-disable */\r\ntry {\r\n  /* @ts-ignore */\r\n  window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\r\n    get: () => {\r\n      supportsPassive = true;\r\n    }\r\n  }));\r\n} catch (e) {}\r\n/* eslint-enable */\r\n\r\n/**\r\n * @typedef {Object} PoolItem\r\n * @prop {HTMLElement | Window | Document | undefined | null} target\r\n * @prop {string} type\r\n * @prop {EventListenerOrEventListenerObject} listener\r\n * @prop {boolean} [passive]\r\n */\r\n\r\nclass DOMEvents {\r\n  constructor() {\r\n    /**\r\n     * @type {PoolItem[]}\r\n     * @private\r\n     */\r\n    this._pool = [];\r\n  }\r\n\r\n  /**\r\n   * Adds event listeners\r\n   *\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type Can be multiple, separated by space.\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   */\r\n  add(target, type, listener, passive) {\r\n    this._toggleListener(target, type, listener, passive);\r\n  }\r\n\r\n  /**\r\n   * Removes event listeners\r\n   *\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   */\r\n  remove(target, type, listener, passive) {\r\n    this._toggleListener(target, type, listener, passive, true);\r\n  }\r\n\r\n  /**\r\n   * Removes all bound events\r\n   */\r\n  removeAll() {\r\n    this._pool.forEach((poolItem) => {\r\n      this._toggleListener(\r\n        poolItem.target,\r\n        poolItem.type,\r\n        poolItem.listener,\r\n        poolItem.passive,\r\n        true,\r\n        true\r\n      );\r\n    });\r\n    this._pool = [];\r\n  }\r\n\r\n  /**\r\n   * Adds or removes event\r\n   *\r\n   * @private\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   * @param {boolean} [unbind] Whether the event should be added or removed\r\n   * @param {boolean} [skipPool] Whether events pool should be skipped\r\n   */\r\n  _toggleListener(target, type, listener, passive, unbind, skipPool) {\r\n    if (!target) {\r\n      return;\r\n    }\r\n\r\n    const methodName = unbind ? 'removeEventListener' : 'addEventListener';\r\n    const types = type.split(' ');\r\n    types.forEach((eType) => {\r\n      if (eType) {\r\n        // Events pool is used to easily unbind all events when PhotoSwipe is closed,\r\n        // so developer doesn't need to do this manually\r\n        if (!skipPool) {\r\n          if (unbind) {\r\n            // Remove from the events pool\r\n            this._pool = this._pool.filter((poolItem) => {\r\n              return poolItem.type !== eType\r\n                || poolItem.listener !== listener\r\n                || poolItem.target !== target;\r\n            });\r\n          } else {\r\n            // Add to the events pool\r\n            this._pool.push({\r\n              target,\r\n              type: eType,\r\n              listener,\r\n              passive\r\n            });\r\n          }\r\n        }\r\n\r\n        // most PhotoSwipe events call preventDefault,\r\n        // and we do not need browser to scroll the page\r\n        const eventOptions = supportsPassive ? { passive: (passive || false) } : false;\r\n\r\n        target[methodName](\r\n          eType,\r\n          listener,\r\n          eventOptions\r\n        );\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport default DOMEvents;\r\n", "/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n\r\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {PhotoSwipeBase} pswp\r\n * @returns {Point}\r\n */\r\nexport function getViewportSize(options, pswp) {\r\n  if (options.getViewportSizeFn) {\r\n    const newViewportSize = options.getViewportSizeFn(options, pswp);\r\n    if (newViewportSize) {\r\n      return newViewportSize;\r\n    }\r\n  }\r\n\r\n  return {\r\n    x: document.documentElement.clientWidth,\r\n\r\n    // TODO: height on mobile is very incosistent due to toolbar\r\n    // find a way to improve this\r\n    //\r\n    // document.documentElement.clientHeight - doesn't seem to work well\r\n    y: window.innerHeight\r\n  };\r\n}\r\n\r\n/**\r\n * Parses padding option.\r\n * Supported formats:\r\n *\r\n * // Object\r\n * padding: {\r\n *  top: 0,\r\n *  bottom: 0,\r\n *  left: 0,\r\n *  right: 0\r\n * }\r\n *\r\n * // A function that returns the object\r\n * paddingFn: (viewportSize, itemData, index) => {\r\n *  return {\r\n *    top: 0,\r\n *    bottom: 0,\r\n *    left: 0,\r\n *    right: 0\r\n *  };\r\n * }\r\n *\r\n * // Legacy variant\r\n * paddingLeft: 0,\r\n * paddingRight: 0,\r\n * paddingTop: 0,\r\n * paddingBottom: 0,\r\n *\r\n * @param {'left' | 'top' | 'bottom' | 'right'} prop\r\n * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n * @param {Point} viewportSize PhotoSwipe viewport size, for example: { x:800, y:600 }\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {number} index Slide index\r\n * @returns {number}\r\n */\r\nexport function parsePaddingOption(prop, options, viewportSize, itemData, index) {\r\n  let paddingValue = 0;\r\n\r\n  if (options.paddingFn) {\r\n    paddingValue = options.paddingFn(viewportSize, itemData, index)[prop];\r\n  } else if (options.padding) {\r\n    paddingValue = options.padding[prop];\r\n  } else {\r\n    const legacyPropName = 'padding' + prop[0].toUpperCase() + prop.slice(1);\r\n    // @ts-expect-error\r\n    if (options[legacyPropName]) {\r\n      // @ts-expect-error\r\n      paddingValue = options[legacyPropName];\r\n    }\r\n  }\r\n\r\n  return Number(paddingValue) || 0;\r\n}\r\n\r\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {Point} viewportSize\r\n * @param {SlideData} itemData\r\n * @param {number} index\r\n * @returns {Point}\r\n */\r\nexport function getPanAreaSize(options, viewportSize, itemData, index) {\r\n  return {\r\n    x: viewportSize.x\r\n      - parsePaddingOption('left', options, viewportSize, itemData, index)\r\n      - parsePaddingOption('right', options, viewportSize, itemData, index),\r\n    y: viewportSize.y\r\n      - parsePaddingOption('top', options, viewportSize, itemData, index)\r\n      - parsePaddingOption('bottom', options, viewportSize, itemData, index)\r\n  };\r\n}\r\n", "import { clamp } from '../util/util.js';\r\nimport { parsePaddingOption } from '../util/viewport-size.js';\r\n\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {Record<Axis, number>} Point */\r\n/** @typedef {'x' | 'y'} Axis */\r\n\r\n/**\r\n * Calculates minimum, maximum and initial (center) bounds of a slide\r\n */\r\nclass PanBounds {\r\n  /**\r\n   * @param {Slide} slide\r\n   */\r\n  constructor(slide) {\r\n    this.slide = slide;\r\n    this.currZoomLevel = 1;\r\n    this.center = /** @type {Point} */ { x: 0, y: 0 };\r\n    this.max = /** @type {Point} */ { x: 0, y: 0 };\r\n    this.min = /** @type {Point} */ { x: 0, y: 0 };\r\n  }\r\n\r\n  /**\r\n   * _getItemBounds\r\n   *\r\n   * @param {number} currZoomLevel\r\n   */\r\n  update(currZoomLevel) {\r\n    this.currZoomLevel = currZoomLevel;\r\n\r\n    if (!this.slide.width) {\r\n      this.reset();\r\n    } else {\r\n      this._updateAxis('x');\r\n      this._updateAxis('y');\r\n      this.slide.pswp.dispatch('calcBounds', { slide: this.slide });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * _calculateItemBoundsForAxis\r\n   *\r\n   * @param {Axis} axis\r\n   */\r\n  _updateAxis(axis) {\r\n    const { pswp } = this.slide;\r\n    const elSize = this.slide[axis === 'x' ? 'width' : 'height'] * this.currZoomLevel;\r\n    const paddingProp = axis === 'x' ? 'left' : 'top';\r\n    const padding = parsePaddingOption(\r\n      paddingProp,\r\n      pswp.options,\r\n      pswp.viewportSize,\r\n      this.slide.data,\r\n      this.slide.index\r\n    );\r\n\r\n    const panAreaSize = this.slide.panAreaSize[axis];\r\n\r\n    // Default position of element.\r\n    // By default, it is center of viewport:\r\n    this.center[axis] = Math.round((panAreaSize - elSize) / 2) + padding;\r\n\r\n    // maximum pan position\r\n    this.max[axis] = (elSize > panAreaSize)\r\n      ? Math.round(panAreaSize - elSize) + padding\r\n      : this.center[axis];\r\n\r\n    // minimum pan position\r\n    this.min[axis] = (elSize > panAreaSize)\r\n      ? padding\r\n      : this.center[axis];\r\n  }\r\n\r\n  // _getZeroBounds\r\n  reset() {\r\n    this.center.x = 0;\r\n    this.center.y = 0;\r\n    this.max.x = 0;\r\n    this.max.y = 0;\r\n    this.min.x = 0;\r\n    this.min.y = 0;\r\n  }\r\n\r\n  /**\r\n   * Correct pan position if it's beyond the bounds\r\n   *\r\n   * @param {Axis} axis x or y\r\n   * @param {number} panOffset\r\n   * @returns {number}\r\n   */\r\n  correctPan(axis, panOffset) { // checkPanBounds\r\n    return clamp(panOffset, this.max[axis], this.min[axis]);\r\n  }\r\n}\r\n\r\nexport default PanBounds;\r\n", "const MAX_IMAGE_WIDTH = 4000;\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n\r\n/** @typedef {'fit' | 'fill' | number | ((zoomLevelObject: ZoomLevel) => number)} ZoomLevelOption */\r\n\r\n/**\r\n * Calculates zoom levels for specific slide.\r\n * Depends on viewport size and image size.\r\n */\r\nclass ZoomLevel {\r\n  /**\r\n   * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {number} index Slide index\r\n   * @param {PhotoSwipe} [pswp] PhotoSwipe instance, can be undefined if not initialized yet\r\n   */\r\n  constructor(options, itemData, index, pswp) {\r\n    this.pswp = pswp;\r\n    this.options = options;\r\n    this.itemData = itemData;\r\n    this.index = index;\r\n    /** @type { Point | null } */\r\n    this.panAreaSize = null;\r\n    /** @type { Point | null } */\r\n    this.elementSize = null;\r\n    this.fit = 1;\r\n    this.fill = 1;\r\n    this.vFill = 1;\r\n    this.initial = 1;\r\n    this.secondary = 1;\r\n    this.max = 1;\r\n    this.min = 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate initial, secondary and maximum zoom level for the specified slide.\r\n   *\r\n   * It should be called when either image or viewport size changes.\r\n   *\r\n   * @param {number} maxWidth\r\n   * @param {number} maxHeight\r\n   * @param {Point} panAreaSize\r\n   */\r\n  update(maxWidth, maxHeight, panAreaSize) {\r\n    /** @type {Point} */\r\n    const elementSize = { x: maxWidth, y: maxHeight };\r\n    this.elementSize = elementSize;\r\n    this.panAreaSize = panAreaSize;\r\n\r\n    const hRatio = panAreaSize.x / elementSize.x;\r\n    const vRatio = panAreaSize.y / elementSize.y;\r\n\r\n    this.fit = Math.min(1, hRatio < vRatio ? hRatio : vRatio);\r\n    this.fill = Math.min(1, hRatio > vRatio ? hRatio : vRatio);\r\n\r\n    // zoom.vFill defines zoom level of the image\r\n    // when it has 100% of viewport vertical space (height)\r\n    this.vFill = Math.min(1, vRatio);\r\n\r\n    this.initial = this._getInitial();\r\n    this.secondary = this._getSecondary();\r\n    this.max = Math.max(\r\n      this.initial,\r\n      this.secondary,\r\n      this._getMax()\r\n    );\r\n\r\n    this.min = Math.min(\r\n      this.fit,\r\n      this.initial,\r\n      this.secondary\r\n    );\r\n\r\n    if (this.pswp) {\r\n      this.pswp.dispatch('zoomLevelsUpdate', { zoomLevels: this, slideData: this.itemData });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses user-defined zoom option.\r\n   *\r\n   * @private\r\n   * @param {'initial' | 'secondary' | 'max'} optionPrefix Zoom level option prefix (initial, secondary, max)\r\n   * @returns { number | undefined }\r\n   */\r\n  _parseZoomLevelOption(optionPrefix) {\r\n    const optionName = /** @type {'initialZoomLevel' | 'secondaryZoomLevel' | 'maxZoomLevel'} */ (\r\n      optionPrefix + 'ZoomLevel'\r\n    );\r\n    const optionValue = this.options[optionName];\r\n\r\n    if (!optionValue) {\r\n      return;\r\n    }\r\n\r\n    if (typeof optionValue === 'function') {\r\n      return optionValue(this);\r\n    }\r\n\r\n    if (optionValue === 'fill') {\r\n      return this.fill;\r\n    }\r\n\r\n    if (optionValue === 'fit') {\r\n      return this.fit;\r\n    }\r\n\r\n    return Number(optionValue);\r\n  }\r\n\r\n  /**\r\n   * Get zoom level to which image will be zoomed after double-tap gesture,\r\n   * or when user clicks on zoom icon,\r\n   * or mouse-click on image itself.\r\n   * If you return 1 image will be zoomed to its original size.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getSecondary() {\r\n    let currZoomLevel = this._parseZoomLevelOption('secondary');\r\n\r\n    if (currZoomLevel) {\r\n      return currZoomLevel;\r\n    }\r\n\r\n    // 3x of \"fit\" state, but not larger than original\r\n    currZoomLevel = Math.min(1, this.fit * 3);\r\n\r\n    if (this.elementSize && currZoomLevel * this.elementSize.x > MAX_IMAGE_WIDTH) {\r\n      currZoomLevel = MAX_IMAGE_WIDTH / this.elementSize.x;\r\n    }\r\n\r\n    return currZoomLevel;\r\n  }\r\n\r\n  /**\r\n   * Get initial image zoom level.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getInitial() {\r\n    return this._parseZoomLevelOption('initial') || this.fit;\r\n  }\r\n\r\n  /**\r\n   * Maximum zoom level when user zooms\r\n   * via zoom/pinch gesture,\r\n   * via cmd/ctrl-wheel or via trackpad.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getMax() {\r\n    // max zoom level is x4 from \"fit state\",\r\n    // used for zoom gesture and ctrl/trackpad zoom\r\n    return this._parseZoomLevelOption('max') || Math.max(1, this.fit * 4);\r\n  }\r\n}\r\n\r\nexport default ZoomLevel;\r\n", "/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/**\r\n * @typedef {_SlideData & Record<string, any>} SlideData\r\n * @typedef {Object} _SlideData\r\n * @prop {HTMLElement} [element] thumbnail element\r\n * @prop {string} [src] image URL\r\n * @prop {string} [srcset] image srcset\r\n * @prop {number} [w] image width (deprecated)\r\n * @prop {number} [h] image height (deprecated)\r\n * @prop {number} [width] image width\r\n * @prop {number} [height] image height\r\n * @prop {string} [msrc] placeholder image URL that's displayed before large image is loaded\r\n * @prop {string} [alt] image alt text\r\n * @prop {boolean} [thumbCropped] whether thumbnail is cropped client-side or not\r\n * @prop {string} [html] html content of a slide\r\n * @prop {'image' | 'html' | string} [type] slide type\r\n */\r\n\r\nimport {\r\n  createElement,\r\n  setTransform,\r\n  equalizePoints,\r\n  roundPoint,\r\n  toTransformString,\r\n  clamp,\r\n} from '../util/util.js';\r\n\r\nimport PanBounds from './pan-bounds.js';\r\nimport ZoomLevel from './zoom-level.js';\r\nimport { getPanAreaSize } from '../util/viewport-size.js';\r\n\r\n/**\r\n * Renders and allows to control a single slide\r\n */\r\nclass Slide {\r\n  /**\r\n   * @param {SlideData} data\r\n   * @param {number} index\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(data, index, pswp) {\r\n    this.data = data;\r\n    this.index = index;\r\n    this.pswp = pswp;\r\n    this.isActive = (index === pswp.currIndex);\r\n    this.currentResolution = 0;\r\n    /** @type {Point} */\r\n    this.panAreaSize = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.pan = { x: 0, y: 0 };\r\n\r\n    this.isFirstSlide = (this.isActive && !pswp.opener.isOpen);\r\n\r\n    this.zoomLevels = new ZoomLevel(pswp.options, data, index, pswp);\r\n\r\n    this.pswp.dispatch('gettingData', {\r\n      slide: this,\r\n      data: this.data,\r\n      index\r\n    });\r\n\r\n    this.content = this.pswp.contentLoader.getContentBySlide(this);\r\n    this.container = createElement('pswp__zoom-wrap', 'div');\r\n    /** @type {HTMLElement | null} */\r\n    this.holderElement = null;\r\n\r\n    this.currZoomLevel = 1;\r\n    /** @type {number} */\r\n    this.width = this.content.width;\r\n    /** @type {number} */\r\n    this.height = this.content.height;\r\n    this.heavyAppended = false;\r\n    this.bounds = new PanBounds(this);\r\n\r\n    this.prevDisplayedWidth = -1;\r\n    this.prevDisplayedHeight = -1;\r\n\r\n    this.pswp.dispatch('slideInit', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * If this slide is active/current/visible\r\n   *\r\n   * @param {boolean} isActive\r\n   */\r\n  setIsActive(isActive) {\r\n    if (isActive && !this.isActive) {\r\n      // slide just became active\r\n      this.activate();\r\n    } else if (!isActive && this.isActive) {\r\n      // slide just became non-active\r\n      this.deactivate();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Appends slide content to DOM\r\n   *\r\n   * @param {HTMLElement} holderElement\r\n   */\r\n  append(holderElement) {\r\n    this.holderElement = holderElement;\r\n\r\n    this.container.style.transformOrigin = '0 0';\r\n\r\n    // Slide appended to DOM\r\n    if (!this.data) {\r\n      return;\r\n    }\r\n\r\n    this.calculateSize();\r\n\r\n    this.load();\r\n    this.updateContentSize();\r\n    this.appendHeavy();\r\n\r\n    this.holderElement.appendChild(this.container);\r\n\r\n    this.zoomAndPanToInitial();\r\n\r\n    this.pswp.dispatch('firstZoomPan', { slide: this });\r\n\r\n    this.applyCurrentZoomPan();\r\n\r\n    this.pswp.dispatch('afterSetContent', { slide: this });\r\n\r\n    if (this.isActive) {\r\n      this.activate();\r\n    }\r\n  }\r\n\r\n  load() {\r\n    this.content.load(false);\r\n    this.pswp.dispatch('slideLoad', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Append \"heavy\" DOM elements\r\n   *\r\n   * This may depend on a type of slide,\r\n   * but generally these are large images.\r\n   */\r\n  appendHeavy() {\r\n    const { pswp } = this;\r\n    const appendHeavyNearby = true; // todo\r\n\r\n    // Avoid appending heavy elements during animations\r\n    if (this.heavyAppended\r\n        || !pswp.opener.isOpen\r\n        || pswp.mainScroll.isShifted()\r\n        || (!this.isActive && !appendHeavyNearby)) {\r\n      return;\r\n    }\r\n\r\n    if (this.pswp.dispatch('appendHeavy', { slide: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.heavyAppended = true;\r\n\r\n    this.content.append();\r\n\r\n    this.pswp.dispatch('appendHeavyContent', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Triggered when this slide is active (selected).\r\n   *\r\n   * If it's part of opening/closing transition -\r\n   * activate() will trigger after the transition is ended.\r\n   */\r\n  activate() {\r\n    this.isActive = true;\r\n    this.appendHeavy();\r\n    this.content.activate();\r\n    this.pswp.dispatch('slideActivate', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Triggered when this slide becomes inactive.\r\n   *\r\n   * Slide can become inactive only after it was active.\r\n   */\r\n  deactivate() {\r\n    this.isActive = false;\r\n    this.content.deactivate();\r\n\r\n    if (this.currZoomLevel !== this.zoomLevels.initial) {\r\n      // allow filtering\r\n      this.calculateSize();\r\n    }\r\n\r\n    // reset zoom level\r\n    this.currentResolution = 0;\r\n    this.zoomAndPanToInitial();\r\n    this.applyCurrentZoomPan();\r\n    this.updateContentSize();\r\n\r\n    this.pswp.dispatch('slideDeactivate', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * The slide should destroy itself, it will never be used again.\r\n   * (unbind all events and destroy internal components)\r\n   */\r\n  destroy() {\r\n    this.content.hasSlide = false;\r\n    this.content.remove();\r\n    this.container.remove();\r\n    this.pswp.dispatch('slideDestroy', { slide: this });\r\n  }\r\n\r\n  resize() {\r\n    if (this.currZoomLevel === this.zoomLevels.initial || !this.isActive) {\r\n      // Keep initial zoom level if it was before the resize,\r\n      // as well as when this slide is not active\r\n\r\n      // Reset position and scale to original state\r\n      this.calculateSize();\r\n      this.currentResolution = 0;\r\n      this.zoomAndPanToInitial();\r\n      this.applyCurrentZoomPan();\r\n      this.updateContentSize();\r\n    } else {\r\n      // readjust pan position if it's beyond the bounds\r\n      this.calculateSize();\r\n      this.bounds.update(this.currZoomLevel);\r\n      this.panTo(this.pan.x, this.pan.y);\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Apply size to current slide content,\r\n   * based on the current resolution and scale.\r\n   *\r\n   * @param {boolean} [force] if size should be updated even if dimensions weren't changed\r\n   */\r\n  updateContentSize(force) {\r\n    // Use initial zoom level\r\n    // if resolution is not defined (user didn't zoom yet)\r\n    const scaleMultiplier = this.currentResolution || this.zoomLevels.initial;\r\n\r\n    if (!scaleMultiplier) {\r\n      return;\r\n    }\r\n\r\n    const width = Math.round(this.width * scaleMultiplier) || this.pswp.viewportSize.x;\r\n    const height = Math.round(this.height * scaleMultiplier) || this.pswp.viewportSize.y;\r\n\r\n    if (!this.sizeChanged(width, height) && !force) {\r\n      return;\r\n    }\r\n    this.content.setDisplayedSize(width, height);\r\n  }\r\n\r\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\r\n  sizeChanged(width, height) {\r\n    if (width !== this.prevDisplayedWidth\r\n        || height !== this.prevDisplayedHeight) {\r\n      this.prevDisplayedWidth = width;\r\n      this.prevDisplayedHeight = height;\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /** @returns {HTMLImageElement | HTMLDivElement | null | undefined} */\r\n  getPlaceholderElement() {\r\n    return this.content.placeholder?.element;\r\n  }\r\n\r\n  /**\r\n   * Zoom current slide image to...\r\n   *\r\n   * @param {number} destZoomLevel Destination zoom level.\r\n   * @param {Point} [centerPoint]\r\n   * Transform origin center point, or false if viewport center should be used.\r\n   * @param {number | false} [transitionDuration] Transition duration, may be set to 0.\r\n   * @param {boolean} [ignoreBounds] Minimum and maximum zoom levels will be ignored.\r\n   */\r\n  zoomTo(destZoomLevel, centerPoint, transitionDuration, ignoreBounds) {\r\n    const { pswp } = this;\r\n    if (!this.isZoomable()\r\n        || pswp.mainScroll.isShifted()) {\r\n      return;\r\n    }\r\n\r\n    pswp.dispatch('beforeZoomTo', {\r\n      destZoomLevel, centerPoint, transitionDuration\r\n    });\r\n\r\n    // stop all pan and zoom transitions\r\n    pswp.animations.stopAllPan();\r\n\r\n    // if (!centerPoint) {\r\n    //   centerPoint = pswp.getViewportCenterPoint();\r\n    // }\r\n\r\n    const prevZoomLevel = this.currZoomLevel;\r\n\r\n    if (!ignoreBounds) {\r\n      destZoomLevel = clamp(destZoomLevel, this.zoomLevels.min, this.zoomLevels.max);\r\n    }\r\n\r\n    // if (transitionDuration === undefined) {\r\n    //   transitionDuration = this.pswp.options.zoomAnimationDuration;\r\n    // }\r\n\r\n    this.setZoomLevel(destZoomLevel);\r\n    this.pan.x = this.calculateZoomToPanOffset('x', centerPoint, prevZoomLevel);\r\n    this.pan.y = this.calculateZoomToPanOffset('y', centerPoint, prevZoomLevel);\r\n    roundPoint(this.pan);\r\n\r\n    const finishTransition = () => {\r\n      this._setResolution(destZoomLevel);\r\n      this.applyCurrentZoomPan();\r\n    };\r\n\r\n    if (!transitionDuration) {\r\n      finishTransition();\r\n    } else {\r\n      pswp.animations.startTransition({\r\n        isPan: true,\r\n        name: 'zoomTo',\r\n        target: this.container,\r\n        transform: this.getCurrentTransform(),\r\n        onComplete: finishTransition,\r\n        duration: transitionDuration,\r\n        easing: pswp.options.easing\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Point} [centerPoint]\r\n   */\r\n  toggleZoom(centerPoint) {\r\n    this.zoomTo(\r\n      this.currZoomLevel === this.zoomLevels.initial\r\n        ? this.zoomLevels.secondary : this.zoomLevels.initial,\r\n      centerPoint,\r\n      this.pswp.options.zoomAnimationDuration\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Updates zoom level property and recalculates new pan bounds,\r\n   * unlike zoomTo it does not apply transform (use applyCurrentZoomPan)\r\n   *\r\n   * @param {number} currZoomLevel\r\n   */\r\n  setZoomLevel(currZoomLevel) {\r\n    this.currZoomLevel = currZoomLevel;\r\n    this.bounds.update(this.currZoomLevel);\r\n  }\r\n\r\n  /**\r\n   * Get pan position after zoom at a given `point`.\r\n   *\r\n   * Always call setZoomLevel(newZoomLevel) beforehand to recalculate\r\n   * pan bounds according to the new zoom level.\r\n   *\r\n   * @param {'x' | 'y'} axis\r\n   * @param {Point} [point]\r\n   * point based on which zoom is performed, usually refers to the current mouse position,\r\n   * if false - viewport center will be used.\r\n   * @param {number} [prevZoomLevel] Zoom level before new zoom was applied.\r\n   * @returns {number}\r\n   */\r\n  calculateZoomToPanOffset(axis, point, prevZoomLevel) {\r\n    const totalPanDistance = this.bounds.max[axis] - this.bounds.min[axis];\r\n    if (totalPanDistance === 0) {\r\n      return this.bounds.center[axis];\r\n    }\r\n\r\n    if (!point) {\r\n      point = this.pswp.getViewportCenterPoint();\r\n    }\r\n\r\n    if (!prevZoomLevel) {\r\n      prevZoomLevel = this.zoomLevels.initial;\r\n    }\r\n\r\n    const zoomFactor = this.currZoomLevel / prevZoomLevel;\r\n    return this.bounds.correctPan(\r\n      axis,\r\n      (this.pan[axis] - point[axis]) * zoomFactor + point[axis]\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Apply pan and keep it within bounds.\r\n   *\r\n   * @param {number} panX\r\n   * @param {number} panY\r\n   */\r\n  panTo(panX, panY) {\r\n    this.pan.x = this.bounds.correctPan('x', panX);\r\n    this.pan.y = this.bounds.correctPan('y', panY);\r\n    this.applyCurrentZoomPan();\r\n  }\r\n\r\n  /**\r\n   * If the slide in the current state can be panned by the user\r\n   * @returns {boolean}\r\n   */\r\n  isPannable() {\r\n    return Boolean(this.width) && (this.currZoomLevel > this.zoomLevels.fit);\r\n  }\r\n\r\n  /**\r\n   * If the slide can be zoomed\r\n   * @returns {boolean}\r\n   */\r\n  isZoomable() {\r\n    return Boolean(this.width) && this.content.isZoomable();\r\n  }\r\n\r\n  /**\r\n   * Apply transform and scale based on\r\n   * the current pan position (this.pan) and zoom level (this.currZoomLevel)\r\n   */\r\n  applyCurrentZoomPan() {\r\n    this._applyZoomTransform(this.pan.x, this.pan.y, this.currZoomLevel);\r\n    if (this === this.pswp.currSlide) {\r\n      this.pswp.dispatch('zoomPanUpdate', { slide: this });\r\n    }\r\n  }\r\n\r\n  zoomAndPanToInitial() {\r\n    this.currZoomLevel = this.zoomLevels.initial;\r\n\r\n    // pan according to the zoom level\r\n    this.bounds.update(this.currZoomLevel);\r\n    equalizePoints(this.pan, this.bounds.center);\r\n    this.pswp.dispatch('initialZoomPan', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Set translate and scale based on current resolution\r\n   *\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} zoom\r\n   * @private\r\n   */\r\n  _applyZoomTransform(x, y, zoom) {\r\n    zoom /= this.currentResolution || this.zoomLevels.initial;\r\n    setTransform(this.container, x, y, zoom);\r\n  }\r\n\r\n  calculateSize() {\r\n    const { pswp } = this;\r\n\r\n    equalizePoints(\r\n      this.panAreaSize,\r\n      getPanAreaSize(pswp.options, pswp.viewportSize, this.data, this.index)\r\n    );\r\n\r\n    this.zoomLevels.update(this.width, this.height, this.panAreaSize);\r\n\r\n    pswp.dispatch('calcSlideSize', {\r\n      slide: this\r\n    });\r\n  }\r\n\r\n  /** @returns {string} */\r\n  getCurrentTransform() {\r\n    const scale = this.currZoomLevel / (this.currentResolution || this.zoomLevels.initial);\r\n    return toTransformString(this.pan.x, this.pan.y, scale);\r\n  }\r\n\r\n  /**\r\n   * Set resolution and re-render the image.\r\n   *\r\n   * For example, if the real image size is 2000x1500,\r\n   * and resolution is 0.5 - it will be rendered as 1000x750.\r\n   *\r\n   * Image with zoom level 2 and resolution 0.5 is\r\n   * the same as image with zoom level 1 and resolution 1.\r\n   *\r\n   * Used to optimize animations and make\r\n   * sure that browser renders image in the highest quality.\r\n   * Also used by responsive images to load the correct one.\r\n   *\r\n   * @param {number} newResolution\r\n   */\r\n  _setResolution(newResolution) {\r\n    if (newResolution === this.currentResolution) {\r\n      return;\r\n    }\r\n\r\n    this.currentResolution = newResolution;\r\n    this.updateContentSize();\r\n\r\n    this.pswp.dispatch('resolutionChanged');\r\n  }\r\n}\r\n\r\nexport default Slide;\r\n", "import {\r\n  equalizePoints, roundPoint, clamp\r\n} from '../util/util.js';\r\n\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('./gestures.js').default} Gestures */\r\n\r\nconst PAN_END_FRICTION = 0.35;\r\nconst VERTICAL_DRAG_FRICTION = 0.6;\r\n\r\n// 1 corresponds to the third of viewport height\r\nconst MIN_RATIO_TO_CLOSE = 0.4;\r\n\r\n// Minimum speed required to navigate\r\n// to next or previous slide\r\nconst MIN_NEXT_SLIDE_SPEED = 0.5;\r\n\r\n/**\r\n * @param {number} initialVelocity\r\n * @param {number} decelerationRate\r\n * @returns {number}\r\n */\r\nfunction project(initialVelocity, decelerationRate) {\r\n  return initialVelocity * decelerationRate / (1 - decelerationRate);\r\n}\r\n\r\n/**\r\n * Handles single pointer dragging\r\n */\r\nclass DragHandler {\r\n  /**\r\n   * @param {Gestures} gestures\r\n   */\r\n  constructor(gestures) {\r\n    this.gestures = gestures;\r\n    this.pswp = gestures.pswp;\r\n    /** @type {Point} */\r\n    this.startPan = { x: 0, y: 0 };\r\n  }\r\n\r\n  start() {\r\n    if (this.pswp.currSlide) {\r\n      equalizePoints(this.startPan, this.pswp.currSlide.pan);\r\n    }\r\n    this.pswp.animations.stopAll();\r\n  }\r\n\r\n  change() {\r\n    const { p1, prevP1, dragAxis } = this.gestures;\r\n    const { currSlide } = this.pswp;\r\n\r\n    if (dragAxis === 'y'\r\n        && this.pswp.options.closeOnVerticalDrag\r\n        && (currSlide && currSlide.currZoomLevel <= currSlide.zoomLevels.fit)\r\n        && !this.gestures.isMultitouch) {\r\n      // Handle vertical drag to close\r\n      const panY = currSlide.pan.y + (p1.y - prevP1.y);\r\n      if (!this.pswp.dispatch('verticalDrag', { panY }).defaultPrevented) {\r\n        this._setPanWithFriction('y', panY, VERTICAL_DRAG_FRICTION);\r\n        const bgOpacity = 1 - Math.abs(this._getVerticalDragRatio(currSlide.pan.y));\r\n        this.pswp.applyBgOpacity(bgOpacity);\r\n        currSlide.applyCurrentZoomPan();\r\n      }\r\n    } else {\r\n      const mainScrollChanged = this._panOrMoveMainScroll('x');\r\n      if (!mainScrollChanged) {\r\n        this._panOrMoveMainScroll('y');\r\n\r\n        if (currSlide) {\r\n          roundPoint(currSlide.pan);\r\n          currSlide.applyCurrentZoomPan();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  end() {\r\n    const { velocity } = this.gestures;\r\n    const { mainScroll, currSlide } = this.pswp;\r\n    let indexDiff = 0;\r\n\r\n    this.pswp.animations.stopAll();\r\n\r\n    // Handle main scroll if it's shifted\r\n    if (mainScroll.isShifted()) {\r\n      // Position of the main scroll relative to the viewport\r\n      const mainScrollShiftDiff = mainScroll.x - mainScroll.getCurrSlideX();\r\n\r\n      // Ratio between 0 and 1:\r\n      // 0 - slide is not visible at all,\r\n      // 0.5 - half of the slide is visible\r\n      // 1 - slide is fully visible\r\n      const currentSlideVisibilityRatio = (mainScrollShiftDiff / this.pswp.viewportSize.x);\r\n\r\n      // Go next slide.\r\n      //\r\n      // - if velocity and its direction is matched,\r\n      //   and we see at least tiny part of the next slide\r\n      //\r\n      // - or if we see less than 50% of the current slide\r\n      //   and velocity is close to 0\r\n      //\r\n      if ((velocity.x < -MIN_NEXT_SLIDE_SPEED && currentSlideVisibilityRatio < 0)\r\n          || (velocity.x < 0.1 && currentSlideVisibilityRatio < -0.5)) {\r\n        // Go to next slide\r\n        indexDiff = 1;\r\n        velocity.x = Math.min(velocity.x, 0);\r\n      } else if ((velocity.x > MIN_NEXT_SLIDE_SPEED && currentSlideVisibilityRatio > 0)\r\n          || (velocity.x > -0.1 && currentSlideVisibilityRatio > 0.5)) {\r\n        // Go to prev slide\r\n        indexDiff = -1;\r\n        velocity.x = Math.max(velocity.x, 0);\r\n      }\r\n\r\n      mainScroll.moveIndexBy(indexDiff, true, velocity.x);\r\n    }\r\n\r\n    // Restore zoom level\r\n    if ((currSlide && currSlide.currZoomLevel > currSlide.zoomLevels.max)\r\n        || this.gestures.isMultitouch) {\r\n      this.gestures.zoomLevels.correctZoomPan(true);\r\n    } else {\r\n      // we run two animations instead of one,\r\n      // as each axis has own pan boundaries and thus different spring function\r\n      // (correctZoomPan does not have this functionality,\r\n      //  it animates all properties with single timing function)\r\n      this._finishPanGestureForAxis('x');\r\n      this._finishPanGestureForAxis('y');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   */\r\n  _finishPanGestureForAxis(axis) {\r\n    const { velocity } = this.gestures;\r\n    const { currSlide } = this.pswp;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    const { pan, bounds } = currSlide;\r\n    const panPos = pan[axis];\r\n    const restoreBgOpacity = (this.pswp.bgOpacity < 1 && axis === 'y');\r\n\r\n    // 0.995 means - scroll view loses 0.5% of its velocity per millisecond\r\n    // Increasing this number will reduce travel distance\r\n    const decelerationRate = 0.995; // 0.99\r\n\r\n    // Pan position if there is no bounds\r\n    const projectedPosition = panPos + project(velocity[axis], decelerationRate);\r\n\r\n    if (restoreBgOpacity) {\r\n      const vDragRatio = this._getVerticalDragRatio(panPos);\r\n      const projectedVDragRatio = this._getVerticalDragRatio(projectedPosition);\r\n\r\n      // If we are above and moving upwards,\r\n      // or if we are below and moving downwards\r\n      if ((vDragRatio < 0 && projectedVDragRatio < -MIN_RATIO_TO_CLOSE)\r\n          || (vDragRatio > 0 && projectedVDragRatio > MIN_RATIO_TO_CLOSE)) {\r\n        this.pswp.close();\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Pan position with corrected bounds\r\n    const correctedPanPosition = bounds.correctPan(axis, projectedPosition);\r\n\r\n    // Exit if pan position should not be changed\r\n    // or if speed it too low\r\n    if (panPos === correctedPanPosition) {\r\n      return;\r\n    }\r\n\r\n    // Overshoot if the final position is out of pan bounds\r\n    const dampingRatio = (correctedPanPosition === projectedPosition) ? 1 : 0.82;\r\n\r\n    const initialBgOpacity = this.pswp.bgOpacity;\r\n    const totalPanDist = correctedPanPosition - panPos;\r\n\r\n    this.pswp.animations.startSpring({\r\n      name: 'panGesture' + axis,\r\n      isPan: true,\r\n      start: panPos,\r\n      end: correctedPanPosition,\r\n      velocity: velocity[axis],\r\n      dampingRatio,\r\n      onUpdate: (pos) => {\r\n        // Animate opacity of background relative to Y pan position of an image\r\n        if (restoreBgOpacity && this.pswp.bgOpacity < 1) {\r\n          // 0 - start of animation, 1 - end of animation\r\n          const animationProgressRatio = 1 - (correctedPanPosition - pos) / totalPanDist;\r\n\r\n          // We clamp opacity to keep it between 0 and 1.\r\n          // As progress ratio can be larger than 1 due to overshoot,\r\n          // and we do not want to bounce opacity.\r\n          this.pswp.applyBgOpacity(clamp(\r\n            initialBgOpacity + (1 - initialBgOpacity) * animationProgressRatio,\r\n            0,\r\n            1\r\n          ));\r\n        }\r\n\r\n        pan[axis] = Math.floor(pos);\r\n        currSlide.applyCurrentZoomPan();\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update position of the main scroll,\r\n   * or/and update pan position of the current slide.\r\n   *\r\n   * Should return true if it changes (or can change) main scroll.\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @returns {boolean}\r\n   */\r\n  _panOrMoveMainScroll(axis) {\r\n    const { p1, dragAxis, prevP1, isMultitouch } = this.gestures;\r\n    const { currSlide, mainScroll } = this.pswp;\r\n    const delta = (p1[axis] - prevP1[axis]);\r\n    const newMainScrollX = mainScroll.x + delta;\r\n\r\n    if (!delta || !currSlide) {\r\n      return false;\r\n    }\r\n\r\n    // Always move main scroll if image can not be panned\r\n    if (axis === 'x' && !currSlide.isPannable() && !isMultitouch) {\r\n      mainScroll.moveTo(newMainScrollX, true);\r\n      return true; // changed main scroll\r\n    }\r\n\r\n    const { bounds } = currSlide;\r\n    const newPan = currSlide.pan[axis] + delta;\r\n\r\n    if (this.pswp.options.allowPanToNext\r\n        && dragAxis === 'x'\r\n        && axis === 'x'\r\n        && !isMultitouch) {\r\n      const currSlideMainScrollX = mainScroll.getCurrSlideX();\r\n\r\n      // Position of the main scroll relative to the viewport\r\n      const mainScrollShiftDiff = mainScroll.x - currSlideMainScrollX;\r\n\r\n      const isLeftToRight = delta > 0;\r\n      const isRightToLeft = !isLeftToRight;\r\n\r\n      if (newPan > bounds.min[axis] && isLeftToRight) {\r\n        // Panning from left to right, beyond the left edge\r\n\r\n        // Wether the image was at minimum pan position (or less)\r\n        // when this drag gesture started.\r\n        // Minimum pan position refers to the left edge of the image.\r\n        const wasAtMinPanPosition = (bounds.min[axis] <= this.startPan[axis]);\r\n\r\n        if (wasAtMinPanPosition) {\r\n          mainScroll.moveTo(newMainScrollX, true);\r\n          return true;\r\n        } else {\r\n          this._setPanWithFriction(axis, newPan);\r\n          //currSlide.pan[axis] = newPan;\r\n        }\r\n      } else if (newPan < bounds.max[axis] && isRightToLeft) {\r\n        // Paning from right to left, beyond the right edge\r\n\r\n        // Maximum pan position refers to the right edge of the image.\r\n        const wasAtMaxPanPosition = (this.startPan[axis] <= bounds.max[axis]);\r\n\r\n        if (wasAtMaxPanPosition) {\r\n          mainScroll.moveTo(newMainScrollX, true);\r\n          return true;\r\n        } else {\r\n          this._setPanWithFriction(axis, newPan);\r\n          //currSlide.pan[axis] = newPan;\r\n        }\r\n      } else {\r\n        // If main scroll is shifted\r\n        if (mainScrollShiftDiff !== 0) {\r\n          // If main scroll is shifted right\r\n          if (mainScrollShiftDiff > 0 /*&& isRightToLeft*/) {\r\n            mainScroll.moveTo(Math.max(newMainScrollX, currSlideMainScrollX), true);\r\n            return true;\r\n          } else if (mainScrollShiftDiff < 0 /*&& isLeftToRight*/) {\r\n            // Main scroll is shifted left (Position is less than 0 comparing to the viewport 0)\r\n            mainScroll.moveTo(Math.min(newMainScrollX, currSlideMainScrollX), true);\r\n            return true;\r\n          }\r\n        } else {\r\n          // We are within pan bounds, so just pan\r\n          this._setPanWithFriction(axis, newPan);\r\n        }\r\n      }\r\n    } else {\r\n      if (axis === 'y') {\r\n        // Do not pan vertically if main scroll is shifted o\r\n        if (!mainScroll.isShifted() && bounds.min.y !== bounds.max.y) {\r\n          this._setPanWithFriction(axis, newPan);\r\n        }\r\n      } else {\r\n        this._setPanWithFriction(axis, newPan);\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  // If we move above - the ratio is negative\r\n  // If we move below the ratio is positive\r\n\r\n  /**\r\n   * Relation between pan Y position and third of viewport height.\r\n   *\r\n   * When we are at initial position (center bounds) - the ratio is 0,\r\n   * if position is shifted upwards - the ratio is negative,\r\n   * if position is shifted downwards - the ratio is positive.\r\n   *\r\n   * @private\r\n   * @param {number} panY The current pan Y position.\r\n   * @returns {number}\r\n   */\r\n  _getVerticalDragRatio(panY) {\r\n    return (panY - (this.pswp.currSlide?.bounds.center.y ?? 0)) / (this.pswp.viewportSize.y / 3);\r\n  }\r\n\r\n  /**\r\n   * Set pan position of the current slide.\r\n   * Apply friction if the position is beyond the pan bounds,\r\n   * or if custom friction is defined.\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} potentialPan\r\n   * @param {number} [customFriction] (0.1 - 1)\r\n   */\r\n  _setPanWithFriction(axis, potentialPan, customFriction) {\r\n    const { currSlide } = this.pswp;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    const { pan, bounds } = currSlide;\r\n    const correctedPan = bounds.correctPan(axis, potentialPan);\r\n    // If we are out of pan bounds\r\n    if (correctedPan !== potentialPan || customFriction) {\r\n      const delta = Math.round(potentialPan - pan[axis]);\r\n      pan[axis] += delta * (customFriction || PAN_END_FRICTION);\r\n    } else {\r\n      pan[axis] = potentialPan;\r\n    }\r\n  }\r\n}\r\n\r\nexport default DragHandler;\r\n", "import {\r\n  equalizePoints, getDistanceBetween, clamp, pointsEqual\r\n} from '../util/util.js';\r\n\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('./gestures.js').default} Gestures */\r\n\r\nconst UPPER_ZOOM_FRICTION = 0.05;\r\nconst LOWER_ZOOM_FRICTION = 0.15;\r\n\r\n\r\n/**\r\n * Get center point between two points\r\n *\r\n * @param {Point} p\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\r\nfunction getZoomPointsCenter(p, p1, p2) {\r\n  p.x = (p1.x + p2.x) / 2;\r\n  p.y = (p1.y + p2.y) / 2;\r\n  return p;\r\n}\r\n\r\nclass ZoomHandler {\r\n  /**\r\n   * @param {Gestures} gestures\r\n   */\r\n  constructor(gestures) {\r\n    this.gestures = gestures;\r\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\r\n    this._startPan = { x: 0, y: 0 };\r\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\r\n    this._startZoomPoint = { x: 0, y: 0 };\r\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\r\n    this._zoomPoint = { x: 0, y: 0 };\r\n    /** @private */\r\n    this._wasOverFitZoomLevel = false;\r\n    /** @private */\r\n    this._startZoomLevel = 1;\r\n  }\r\n\r\n  start() {\r\n    const { currSlide } = this.gestures.pswp;\r\n    if (currSlide) {\r\n      this._startZoomLevel = currSlide.currZoomLevel;\r\n      equalizePoints(this._startPan, currSlide.pan);\r\n    }\r\n\r\n    this.gestures.pswp.animations.stopAllPan();\r\n    this._wasOverFitZoomLevel = false;\r\n  }\r\n\r\n  change() {\r\n    const { p1, startP1, p2, startP2, pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    const minZoomLevel = currSlide.zoomLevels.min;\r\n    const maxZoomLevel = currSlide.zoomLevels.max;\r\n\r\n    if (!currSlide.isZoomable() || pswp.mainScroll.isShifted()) {\r\n      return;\r\n    }\r\n\r\n    getZoomPointsCenter(this._startZoomPoint, startP1, startP2);\r\n    getZoomPointsCenter(this._zoomPoint, p1, p2);\r\n\r\n    let currZoomLevel = (1 / getDistanceBetween(startP1, startP2))\r\n                      * getDistanceBetween(p1, p2)\r\n                      * this._startZoomLevel;\r\n\r\n    // slightly over the zoom.fit\r\n    if (currZoomLevel > currSlide.zoomLevels.initial + (currSlide.zoomLevels.initial / 15)) {\r\n      this._wasOverFitZoomLevel = true;\r\n    }\r\n\r\n    if (currZoomLevel < minZoomLevel) {\r\n      if (pswp.options.pinchToClose\r\n          && !this._wasOverFitZoomLevel\r\n          && this._startZoomLevel <= currSlide.zoomLevels.initial) {\r\n        // fade out background if zooming out\r\n        const bgOpacity = 1 - ((minZoomLevel - currZoomLevel) / (minZoomLevel / 1.2));\r\n        if (!pswp.dispatch('pinchClose', { bgOpacity }).defaultPrevented) {\r\n          pswp.applyBgOpacity(bgOpacity);\r\n        }\r\n      } else {\r\n        // Apply the friction if zoom level is below the min\r\n        currZoomLevel = minZoomLevel - (minZoomLevel - currZoomLevel) * LOWER_ZOOM_FRICTION;\r\n      }\r\n    } else if (currZoomLevel > maxZoomLevel) {\r\n      // Apply the friction if zoom level is above the max\r\n      currZoomLevel = maxZoomLevel + (currZoomLevel - maxZoomLevel) * UPPER_ZOOM_FRICTION;\r\n    }\r\n\r\n    currSlide.pan.x = this._calculatePanForZoomLevel('x', currZoomLevel);\r\n    currSlide.pan.y = this._calculatePanForZoomLevel('y', currZoomLevel);\r\n\r\n    currSlide.setZoomLevel(currZoomLevel);\r\n    currSlide.applyCurrentZoomPan();\r\n  }\r\n\r\n  end() {\r\n    const { pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n    if ((!currSlide || currSlide.currZoomLevel < currSlide.zoomLevels.initial)\r\n        && !this._wasOverFitZoomLevel\r\n        && pswp.options.pinchToClose) {\r\n      pswp.close();\r\n    } else {\r\n      this.correctZoomPan();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} currZoomLevel\r\n   * @returns {number}\r\n   */\r\n  _calculatePanForZoomLevel(axis, currZoomLevel) {\r\n    const zoomFactor = currZoomLevel / this._startZoomLevel;\r\n    return this._zoomPoint[axis]\r\n            - ((this._startZoomPoint[axis] - this._startPan[axis]) * zoomFactor);\r\n  }\r\n\r\n  /**\r\n   * Correct currZoomLevel and pan if they are\r\n   * beyond minimum or maximum values.\r\n   * With animation.\r\n   *\r\n   * @param {boolean} [ignoreGesture]\r\n   * Wether gesture coordinates should be ignored when calculating destination pan position.\r\n   */\r\n  correctZoomPan(ignoreGesture) {\r\n    const { pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n\r\n    if (!currSlide?.isZoomable()) {\r\n      return;\r\n    }\r\n\r\n    if (this._zoomPoint.x === 0) {\r\n      ignoreGesture = true;\r\n    }\r\n\r\n    const prevZoomLevel = currSlide.currZoomLevel;\r\n\r\n    /** @type {number} */\r\n    let destinationZoomLevel;\r\n    let currZoomLevelNeedsChange = true;\r\n\r\n    if (prevZoomLevel < currSlide.zoomLevels.initial) {\r\n      destinationZoomLevel = currSlide.zoomLevels.initial;\r\n      // zoom to min\r\n    } else if (prevZoomLevel > currSlide.zoomLevels.max) {\r\n      destinationZoomLevel = currSlide.zoomLevels.max;\r\n      // zoom to max\r\n    } else {\r\n      currZoomLevelNeedsChange = false;\r\n      destinationZoomLevel = prevZoomLevel;\r\n    }\r\n\r\n    const initialBgOpacity = pswp.bgOpacity;\r\n    const restoreBgOpacity = pswp.bgOpacity < 1;\r\n\r\n    const initialPan = equalizePoints({ x: 0, y: 0 }, currSlide.pan);\r\n    let destinationPan = equalizePoints({ x: 0, y: 0 }, initialPan);\r\n\r\n    if (ignoreGesture) {\r\n      this._zoomPoint.x = 0;\r\n      this._zoomPoint.y = 0;\r\n      this._startZoomPoint.x = 0;\r\n      this._startZoomPoint.y = 0;\r\n      this._startZoomLevel = prevZoomLevel;\r\n      equalizePoints(this._startPan, initialPan);\r\n    }\r\n\r\n    if (currZoomLevelNeedsChange) {\r\n      destinationPan = {\r\n        x: this._calculatePanForZoomLevel('x', destinationZoomLevel),\r\n        y: this._calculatePanForZoomLevel('y', destinationZoomLevel)\r\n      };\r\n    }\r\n\r\n    // set zoom level, so pan bounds are updated according to it\r\n    currSlide.setZoomLevel(destinationZoomLevel);\r\n\r\n    destinationPan = {\r\n      x: currSlide.bounds.correctPan('x', destinationPan.x),\r\n      y: currSlide.bounds.correctPan('y', destinationPan.y)\r\n    };\r\n\r\n    // return zoom level and its bounds to initial\r\n    currSlide.setZoomLevel(prevZoomLevel);\r\n\r\n    const panNeedsChange = !pointsEqual(destinationPan, initialPan);\r\n\r\n    if (!panNeedsChange && !currZoomLevelNeedsChange && !restoreBgOpacity) {\r\n      // update resolution after gesture\r\n      currSlide._setResolution(destinationZoomLevel);\r\n      currSlide.applyCurrentZoomPan();\r\n\r\n      // nothing to animate\r\n      return;\r\n    }\r\n\r\n    pswp.animations.stopAllPan();\r\n\r\n    pswp.animations.startSpring({\r\n      isPan: true,\r\n      start: 0,\r\n      end: 1000,\r\n      velocity: 0,\r\n      dampingRatio: 1,\r\n      naturalFrequency: 40,\r\n      onUpdate: (now) => {\r\n        now /= 1000; // 0 - start, 1 - end\r\n\r\n        if (panNeedsChange || currZoomLevelNeedsChange) {\r\n          if (panNeedsChange) {\r\n            currSlide.pan.x = initialPan.x + (destinationPan.x - initialPan.x) * now;\r\n            currSlide.pan.y = initialPan.y + (destinationPan.y - initialPan.y) * now;\r\n          }\r\n\r\n          if (currZoomLevelNeedsChange) {\r\n            const newZoomLevel = prevZoomLevel\r\n                        + (destinationZoomLevel - prevZoomLevel) * now;\r\n            currSlide.setZoomLevel(newZoomLevel);\r\n          }\r\n\r\n          currSlide.applyCurrentZoomPan();\r\n        }\r\n\r\n        // Restore background opacity\r\n        if (restoreBgOpacity && pswp.bgOpacity < 1) {\r\n          // We clamp opacity to keep it between 0 and 1.\r\n          // As progress ratio can be larger than 1 due to overshoot,\r\n          // and we do not want to bounce opacity.\r\n          pswp.applyBgOpacity(clamp(\r\n            initialBgOpacity + (1 - initialBgOpacity) * now, 0, 1\r\n          ));\r\n        }\r\n      },\r\n      onComplete: () => {\r\n        // update resolution after transition ends\r\n        currSlide._setResolution(destinationZoomLevel);\r\n        currSlide.applyCurrentZoomPan();\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport default ZoomHandler;\r\n", "/**\r\n * @template {string} T\r\n * @template {string} P\r\n * @typedef {import('../types.js').AddPostfix<T, P>} AddPostfix<T, P>\r\n */\r\n\r\n/** @typedef {import('./gestures.js').default} Gestures */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/** @typedef {'imageClick' | 'bgClick' | 'tap' | 'doubleTap'} Actions */\r\n\r\n/**\r\n * Whether the tap was performed on the main slide\r\n * (rather than controls or caption).\r\n *\r\n * @param {PointerEvent} event\r\n * @returns {boolean}\r\n */\r\nfunction didTapOnMainContent(event) {\r\n  return !!(/** @type {HTMLElement} */ (event.target).closest('.pswp__container'));\r\n}\r\n\r\n/**\r\n * Tap, double-tap handler.\r\n */\r\nclass TapHandler {\r\n  /**\r\n   * @param {Gestures} gestures\r\n   */\r\n  constructor(gestures) {\r\n    this.gestures = gestures;\r\n  }\r\n\r\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  click(point, originalEvent) {\r\n    const targetClassList = /** @type {HTMLElement} */ (originalEvent.target).classList;\r\n    const isImageClick = targetClassList.contains('pswp__img');\r\n    const isBackgroundClick = targetClassList.contains('pswp__item')\r\n                              || targetClassList.contains('pswp__zoom-wrap');\r\n\r\n    if (isImageClick) {\r\n      this._doClickOrTapAction('imageClick', point, originalEvent);\r\n    } else if (isBackgroundClick) {\r\n      this._doClickOrTapAction('bgClick', point, originalEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  tap(point, originalEvent) {\r\n    if (didTapOnMainContent(originalEvent)) {\r\n      this._doClickOrTapAction('tap', point, originalEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  doubleTap(point, originalEvent) {\r\n    if (didTapOnMainContent(originalEvent)) {\r\n      this._doClickOrTapAction('doubleTap', point, originalEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {Actions} actionName\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  _doClickOrTapAction(actionName, point, originalEvent) {\r\n    const { pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n    const actionFullName = /** @type {AddPostfix<Actions, 'Action'>} */ (actionName + 'Action');\r\n    const optionValue = pswp.options[actionFullName];\r\n\r\n    if (pswp.dispatch(actionFullName, { point, originalEvent }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (typeof optionValue === 'function') {\r\n      optionValue.call(pswp, point, originalEvent);\r\n      return;\r\n    }\r\n\r\n    switch (optionValue) {\r\n      case 'close':\r\n      case 'next':\r\n        pswp[optionValue]();\r\n        break;\r\n      case 'zoom':\r\n        currSlide?.toggleZoom(point);\r\n        break;\r\n      case 'zoom-or-close':\r\n        // by default click zooms current image,\r\n        // if it can not be zoomed - gallery will be closed\r\n        if (currSlide?.isZoomable()\r\n            && currSlide.zoomLevels.secondary !== currSlide.zoomLevels.initial) {\r\n          currSlide.toggleZoom(point);\r\n        } else if (pswp.options.clickToCloseNonZoomable) {\r\n          pswp.close();\r\n        }\r\n        break;\r\n      case 'toggle-controls':\r\n        this.gestures.pswp.element?.classList.toggle('pswp--ui-visible');\r\n        // if (_controlsVisible) {\r\n        //   _ui.hideControls();\r\n        // } else {\r\n        //   _ui.showControls();\r\n        // }\r\n        break;\r\n    }\r\n  }\r\n}\r\n\r\nexport default TapHandler;\r\n", "import {\r\n  equalizePoints, pointsEqual, getDistanceBetween\r\n} from '../util/util.js';\r\n\r\nimport <PERSON><PERSON><PERSON><PERSON><PERSON> from './drag-handler.js';\r\nimport ZoomHandler from './zoom-handler.js';\r\nimport <PERSON><PERSON><PERSON>andler from './tap-handler.js';\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n// How far should user should drag\r\n// until we can determine that the gesture is swipe and its direction\r\nconst AXIS_SWIPE_HYSTERISIS = 10;\r\n//const PAN_END_FRICTION = 0.35;\r\n\r\nconst DOUBLE_TAP_DELAY = 300; // ms\r\nconst MIN_TAP_DISTANCE = 25; // px\r\n\r\n/**\r\n * Gestures class bind touch, pointer or mouse events\r\n * and emits drag to drag-handler and zoom events zoom-handler.\r\n *\r\n * Drag and zoom events are emited in requestAnimationFrame,\r\n * and only when one of pointers was actually changed.\r\n */\r\nclass Gestures {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n\r\n    /** @type {'x' | 'y' | null} */\r\n    this.dragAxis = null;\r\n\r\n    // point objects are defined once and reused\r\n    // PhotoSwipe keeps track only of two pointers, others are ignored\r\n    /** @type {Point} */\r\n    this.p1 = { x: 0, y: 0 }; // the first pressed pointer\r\n    /** @type {Point} */\r\n    this.p2 = { x: 0, y: 0 }; // the second pressed pointer\r\n    /** @type {Point} */\r\n    this.prevP1 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.prevP2 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.startP1 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.startP2 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.velocity = { x: 0, y: 0 };\r\n\r\n    /** @type {Point}\r\n     * @private\r\n     */\r\n    this._lastStartP1 = { x: 0, y: 0 };\r\n    /** @type {Point}\r\n     * @private\r\n     */\r\n    this._intervalP1 = { x: 0, y: 0 };\r\n    /** @private */\r\n    this._numActivePoints = 0;\r\n    /** @type {Point[]}\r\n     * @private\r\n     */\r\n    this._ongoingPointers = [];\r\n    /** @private */\r\n    this._touchEventEnabled = 'ontouchstart' in window;\r\n    /** @private */\r\n    this._pointerEventEnabled = !!(window.PointerEvent);\r\n    this.supportsTouch = this._touchEventEnabled\r\n                          || (this._pointerEventEnabled && navigator.maxTouchPoints > 1);\r\n    /** @private */\r\n    this._numActivePoints = 0;\r\n    /** @private */\r\n    this._intervalTime = 0;\r\n    /** @private */\r\n    this._velocityCalculated = false;\r\n    this.isMultitouch = false;\r\n    this.isDragging = false;\r\n    this.isZooming = false;\r\n    /** @type {number | null} */\r\n    this.raf = null;\r\n    /** @type {NodeJS.Timeout | null}\r\n     * @private\r\n     */\r\n    this._tapTimer = null;\r\n\r\n    if (!this.supportsTouch) {\r\n      // disable pan to next slide for non-touch devices\r\n      pswp.options.allowPanToNext = false;\r\n    }\r\n\r\n    this.drag = new DragHandler(this);\r\n    this.zoomLevels = new ZoomHandler(this);\r\n    this.tapHandler = new TapHandler(this);\r\n\r\n    pswp.on('bindEvents', () => {\r\n      pswp.events.add(\r\n        pswp.scrollWrap,\r\n        'click',\r\n        /** @type EventListener */(this._onClick.bind(this))\r\n      );\r\n\r\n      if (this._pointerEventEnabled) {\r\n        this._bindEvents('pointer', 'down', 'up', 'cancel');\r\n      } else if (this._touchEventEnabled) {\r\n        this._bindEvents('touch', 'start', 'end', 'cancel');\r\n\r\n        // In previous versions we also bound mouse event here,\r\n        // in case device supports both touch and mouse events,\r\n        // but newer versions of browsers now support PointerEvent.\r\n\r\n        // on iOS10 if you bind touchmove/end after touchstart,\r\n        // and you don't preventDefault touchstart (which PhotoSwipe does),\r\n        // preventDefault will have no effect on touchmove and touchend.\r\n        // Unless you bind it previously.\r\n        if (pswp.scrollWrap) {\r\n          pswp.scrollWrap.ontouchmove = () => {};\r\n          pswp.scrollWrap.ontouchend = () => {};\r\n        }\r\n      } else {\r\n        this._bindEvents('mouse', 'down', 'up');\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {'mouse' | 'touch' | 'pointer'} pref\r\n   * @param {'down' | 'start'} down\r\n   * @param {'up' | 'end'} up\r\n   * @param {'cancel'} [cancel]\r\n   */\r\n  _bindEvents(pref, down, up, cancel) {\r\n    const { pswp } = this;\r\n    const { events } = pswp;\r\n\r\n    const cancelEvent = cancel ? pref + cancel : '';\r\n\r\n    events.add(\r\n      pswp.scrollWrap,\r\n      pref + down,\r\n      /** @type EventListener */(this.onPointerDown.bind(this))\r\n    );\r\n    events.add(window, pref + 'move', /** @type EventListener */(this.onPointerMove.bind(this)));\r\n    events.add(window, pref + up, /** @type EventListener */(this.onPointerUp.bind(this)));\r\n    if (cancelEvent) {\r\n      events.add(\r\n        pswp.scrollWrap,\r\n        cancelEvent,\r\n        /** @type EventListener */(this.onPointerUp.bind(this))\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {PointerEvent} e\r\n   */\r\n  onPointerDown(e) {\r\n    // We do not call preventDefault for touch events\r\n    // to allow browser to show native dialog on longpress\r\n    // (the one that allows to save image or open it in new tab).\r\n    //\r\n    // Desktop Safari allows to drag images when preventDefault isn't called on mousedown,\r\n    // even though preventDefault IS called on mousemove. That's why we preventDefault mousedown.\r\n    const isMousePointer = e.type === 'mousedown' || e.pointerType === 'mouse';\r\n\r\n    // Allow dragging only via left mouse button.\r\n    // http://www.quirksmode.org/js/events_properties.html\r\n    // https://developer.mozilla.org/en-US/docs/Web/API/event.button\r\n    if (isMousePointer && e.button > 0) {\r\n      return;\r\n    }\r\n\r\n    const { pswp } = this;\r\n\r\n    // if PhotoSwipe is opening or closing\r\n    if (!pswp.opener.isOpen) {\r\n      e.preventDefault();\r\n      return;\r\n    }\r\n\r\n    if (pswp.dispatch('pointerDown', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (isMousePointer) {\r\n      pswp.mouseDetected();\r\n\r\n      // preventDefault mouse event to prevent\r\n      // browser image drag feature\r\n      this._preventPointerEventBehaviour(e, 'down');\r\n    }\r\n\r\n    pswp.animations.stopAll();\r\n\r\n    this._updatePoints(e, 'down');\r\n\r\n    if (this._numActivePoints === 1) {\r\n      this.dragAxis = null;\r\n      // we need to store initial point to determine the main axis,\r\n      // drag is activated only after the axis is determined\r\n      equalizePoints(this.startP1, this.p1);\r\n    }\r\n\r\n    if (this._numActivePoints > 1) {\r\n      // Tap or double tap should not trigger if more than one pointer\r\n      this._clearTapTimer();\r\n      this.isMultitouch = true;\r\n    } else {\r\n      this.isMultitouch = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {PointerEvent} e\r\n   */\r\n  onPointerMove(e) {\r\n    this._preventPointerEventBehaviour(e, 'move');\r\n\r\n    if (!this._numActivePoints) {\r\n      return;\r\n    }\r\n\r\n    this._updatePoints(e, 'move');\r\n\r\n    if (this.pswp.dispatch('pointerMove', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this._numActivePoints === 1 && !this.isDragging) {\r\n      if (!this.dragAxis) {\r\n        this._calculateDragDirection();\r\n      }\r\n\r\n      // Drag axis was detected, emit drag.start\r\n      if (this.dragAxis && !this.isDragging) {\r\n        if (this.isZooming) {\r\n          this.isZooming = false;\r\n          this.zoomLevels.end();\r\n        }\r\n\r\n        this.isDragging = true;\r\n        this._clearTapTimer(); // Tap can not trigger after drag\r\n\r\n        // Adjust starting point\r\n        this._updateStartPoints();\r\n        this._intervalTime = Date.now();\r\n        //this._startTime = this._intervalTime;\r\n        this._velocityCalculated = false;\r\n        equalizePoints(this._intervalP1, this.p1);\r\n        this.velocity.x = 0;\r\n        this.velocity.y = 0;\r\n        this.drag.start();\r\n\r\n        this._rafStopLoop();\r\n        this._rafRenderLoop();\r\n      }\r\n    } else if (this._numActivePoints > 1 && !this.isZooming) {\r\n      this._finishDrag();\r\n\r\n      this.isZooming = true;\r\n\r\n      // Adjust starting points\r\n      this._updateStartPoints();\r\n\r\n      this.zoomLevels.start();\r\n\r\n      this._rafStopLoop();\r\n      this._rafRenderLoop();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _finishDrag() {\r\n    if (this.isDragging) {\r\n      this.isDragging = false;\r\n\r\n      // Try to calculate velocity,\r\n      // if it wasn't calculated yet in drag.change\r\n      if (!this._velocityCalculated) {\r\n        this._updateVelocity(true);\r\n      }\r\n\r\n      this.drag.end();\r\n      this.dragAxis = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {PointerEvent} e\r\n   */\r\n  onPointerUp(e) {\r\n    if (!this._numActivePoints) {\r\n      return;\r\n    }\r\n\r\n    this._updatePoints(e, 'up');\r\n\r\n    if (this.pswp.dispatch('pointerUp', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this._numActivePoints === 0) {\r\n      this._rafStopLoop();\r\n\r\n      if (this.isDragging) {\r\n        this._finishDrag();\r\n      } else if (!this.isZooming && !this.isMultitouch) {\r\n        //this.zoomLevels.correctZoomPan();\r\n        this._finishTap(e);\r\n      }\r\n    }\r\n\r\n    if (this._numActivePoints < 2 && this.isZooming) {\r\n      this.isZooming = false;\r\n      this.zoomLevels.end();\r\n\r\n      if (this._numActivePoints === 1) {\r\n        // Since we have 1 point left, we need to reinitiate drag\r\n        this.dragAxis = null;\r\n        this._updateStartPoints();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _rafRenderLoop() {\r\n    if (this.isDragging || this.isZooming) {\r\n      this._updateVelocity();\r\n\r\n      if (this.isDragging) {\r\n        // make sure that pointer moved since the last update\r\n        if (!pointsEqual(this.p1, this.prevP1)) {\r\n          this.drag.change();\r\n        }\r\n      } else /* if (this.isZooming) */ {\r\n        if (!pointsEqual(this.p1, this.prevP1)\r\n            || !pointsEqual(this.p2, this.prevP2)) {\r\n          this.zoomLevels.change();\r\n        }\r\n      }\r\n\r\n      this._updatePrevPoints();\r\n      this.raf = requestAnimationFrame(this._rafRenderLoop.bind(this));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update velocity at 50ms interval\r\n   *\r\n   * @private\r\n   * @param {boolean} [force]\r\n   */\r\n  _updateVelocity(force) {\r\n    const time = Date.now();\r\n    const duration = time - this._intervalTime;\r\n\r\n    if (duration < 50 && !force) {\r\n      return;\r\n    }\r\n\r\n\r\n    this.velocity.x = this._getVelocity('x', duration);\r\n    this.velocity.y = this._getVelocity('y', duration);\r\n\r\n    this._intervalTime = time;\r\n    equalizePoints(this._intervalP1, this.p1);\r\n    this._velocityCalculated = true;\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   */\r\n  _finishTap(e) {\r\n    const { mainScroll } = this.pswp;\r\n\r\n    // Do not trigger tap events if main scroll is shifted\r\n    if (mainScroll.isShifted()) {\r\n      // restore main scroll position\r\n      // (usually happens if stopped in the middle of animation)\r\n      mainScroll.moveIndexBy(0, true);\r\n      return;\r\n    }\r\n\r\n    // Do not trigger tap for touchcancel or pointercancel\r\n    if (e.type.indexOf('cancel') > 0) {\r\n      return;\r\n    }\r\n\r\n    // Trigger click instead of tap for mouse events\r\n    if (e.type === 'mouseup' || e.pointerType === 'mouse') {\r\n      this.tapHandler.click(this.startP1, e);\r\n      return;\r\n    }\r\n\r\n    // Disable delay if there is no doubleTapAction\r\n    const tapDelay = this.pswp.options.doubleTapAction ? DOUBLE_TAP_DELAY : 0;\r\n\r\n    // If tapTimer is defined - we tapped recently,\r\n    // check if the current tap is close to the previous one,\r\n    // if yes - trigger double tap\r\n    if (this._tapTimer) {\r\n      this._clearTapTimer();\r\n      // Check if two taps were more or less on the same place\r\n      if (getDistanceBetween(this._lastStartP1, this.startP1) < MIN_TAP_DISTANCE) {\r\n        this.tapHandler.doubleTap(this.startP1, e);\r\n      }\r\n    } else {\r\n      equalizePoints(this._lastStartP1, this.startP1);\r\n      this._tapTimer = setTimeout(() => {\r\n        this.tapHandler.tap(this.startP1, e);\r\n        this._clearTapTimer();\r\n      }, tapDelay);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _clearTapTimer() {\r\n    if (this._tapTimer) {\r\n      clearTimeout(this._tapTimer);\r\n      this._tapTimer = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get velocity for axis\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} duration\r\n   * @returns {number}\r\n   */\r\n  _getVelocity(axis, duration) {\r\n    // displacement is like distance, but can be negative.\r\n    const displacement = this.p1[axis] - this._intervalP1[axis];\r\n\r\n    if (Math.abs(displacement) > 1 && duration > 5) {\r\n      return displacement / duration;\r\n    }\r\n\r\n    return 0;\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _rafStopLoop() {\r\n    if (this.raf) {\r\n      cancelAnimationFrame(this.raf);\r\n      this.raf = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   * @param {'up' | 'down' | 'move'} pointerType Normalized pointer type\r\n   */\r\n  _preventPointerEventBehaviour(e, pointerType) {\r\n    const preventPointerEvent = this.pswp.applyFilters('preventPointerEvent', true, e, pointerType);\r\n    if (preventPointerEvent) {\r\n      e.preventDefault();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses and normalizes points from the touch, mouse or pointer event.\r\n   * Updates p1 and p2.\r\n   *\r\n   * @private\r\n   * @param {PointerEvent | TouchEvent} e\r\n   * @param {'up' | 'down' | 'move'} pointerType Normalized pointer type\r\n   */\r\n  _updatePoints(e, pointerType) {\r\n    if (this._pointerEventEnabled) {\r\n      const pointerEvent = /** @type {PointerEvent} */ (e);\r\n      // Try to find the current pointer in ongoing pointers by its ID\r\n      const pointerIndex = this._ongoingPointers.findIndex((ongoingPointer) => {\r\n        return ongoingPointer.id === pointerEvent.pointerId;\r\n      });\r\n\r\n      if (pointerType === 'up' && pointerIndex > -1) {\r\n        // release the pointer - remove it from ongoing\r\n        this._ongoingPointers.splice(pointerIndex, 1);\r\n      } else if (pointerType === 'down' && pointerIndex === -1) {\r\n        // add new pointer\r\n        this._ongoingPointers.push(this._convertEventPosToPoint(pointerEvent, { x: 0, y: 0 }));\r\n      } else if (pointerIndex > -1) {\r\n        // update existing pointer\r\n        this._convertEventPosToPoint(pointerEvent, this._ongoingPointers[pointerIndex]);\r\n      }\r\n\r\n      this._numActivePoints = this._ongoingPointers.length;\r\n\r\n      // update points that PhotoSwipe uses\r\n      // to calculate position and scale\r\n      if (this._numActivePoints > 0) {\r\n        equalizePoints(this.p1, this._ongoingPointers[0]);\r\n      }\r\n\r\n      if (this._numActivePoints > 1) {\r\n        equalizePoints(this.p2, this._ongoingPointers[1]);\r\n      }\r\n    } else {\r\n      const touchEvent = /** @type {TouchEvent} */ (e);\r\n\r\n      this._numActivePoints = 0;\r\n      if (touchEvent.type.indexOf('touch') > -1) {\r\n        // Touch Event\r\n        // https://developer.mozilla.org/en-US/docs/Web/API/TouchEvent\r\n        if (touchEvent.touches && touchEvent.touches.length > 0) {\r\n          this._convertEventPosToPoint(touchEvent.touches[0], this.p1);\r\n          this._numActivePoints++;\r\n          if (touchEvent.touches.length > 1) {\r\n            this._convertEventPosToPoint(touchEvent.touches[1], this.p2);\r\n            this._numActivePoints++;\r\n          }\r\n        }\r\n      } else {\r\n        // Mouse Event\r\n        this._convertEventPosToPoint(/** @type {PointerEvent} */ (e), this.p1);\r\n        if (pointerType === 'up') {\r\n          // clear all points on mouseup\r\n          this._numActivePoints = 0;\r\n        } else {\r\n          this._numActivePoints++;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /** update points that were used during previous rAF tick\r\n   * @private\r\n   */\r\n  _updatePrevPoints() {\r\n    equalizePoints(this.prevP1, this.p1);\r\n    equalizePoints(this.prevP2, this.p2);\r\n  }\r\n\r\n  /** update points at the start of gesture\r\n   * @private\r\n   */\r\n  _updateStartPoints() {\r\n    equalizePoints(this.startP1, this.p1);\r\n    equalizePoints(this.startP2, this.p2);\r\n    this._updatePrevPoints();\r\n  }\r\n\r\n  /** @private */\r\n  _calculateDragDirection() {\r\n    if (this.pswp.mainScroll.isShifted()) {\r\n      // if main scroll position is shifted – direction is always horizontal\r\n      this.dragAxis = 'x';\r\n    } else {\r\n      // calculate delta of the last touchmove tick\r\n      const diff = Math.abs(this.p1.x - this.startP1.x) - Math.abs(this.p1.y - this.startP1.y);\r\n\r\n      if (diff !== 0) {\r\n        // check if pointer was shifted horizontally or vertically\r\n        const axisToCheck = diff > 0 ? 'x' : 'y';\r\n\r\n        if (Math.abs(this.p1[axisToCheck] - this.startP1[axisToCheck]) >= AXIS_SWIPE_HYSTERISIS) {\r\n          this.dragAxis = axisToCheck;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Converts touch, pointer or mouse event\r\n   * to PhotoSwipe point.\r\n   *\r\n   * @private\r\n   * @param {Touch | PointerEvent} e\r\n   * @param {Point} p\r\n   * @returns {Point}\r\n   */\r\n  _convertEventPosToPoint(e, p) {\r\n    p.x = e.pageX - this.pswp.offset.x;\r\n    p.y = e.pageY - this.pswp.offset.y;\r\n\r\n    if ('pointerId' in e) {\r\n      p.id = e.pointerId;\r\n    } else if (e.identifier !== undefined) {\r\n      p.id = e.identifier;\r\n    }\r\n\r\n    return p;\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   */\r\n  _onClick(e) {\r\n    // Do not allow click event to pass through after drag\r\n    if (this.pswp.mainScroll.isShifted()) {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Gestures;\r\n", "import {\r\n  setTransform,\r\n  createElement,\r\n} from './util/util.js';\r\n\r\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('./slide/slide.js').default} Slide */\r\n\r\n/** @typedef {{ el: HTMLDivElement; slide?: Slide }} ItemHolder */\r\n\r\nconst MAIN_SCROLL_END_FRICTION = 0.35;\r\n\r\n\r\n// const MIN_SWIPE_TRANSITION_DURATION = 250;\r\n// const MAX_SWIPE_TRABSITION_DURATION = 500;\r\n// const DEFAULT_SWIPE_TRANSITION_DURATION = 333;\r\n\r\n/**\r\n * Handles movement of the main scrolling container\r\n * (for example, it repositions when user swipes left or right).\r\n *\r\n * Also stores its state.\r\n */\r\nclass MainScroll {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    this.x = 0;\r\n    this.slideWidth = 0;\r\n    /** @private */\r\n    this._currPositionIndex = 0;\r\n    /** @private */\r\n    this._prevPositionIndex = 0;\r\n    /** @private */\r\n    this._containerShiftIndex = -1;\r\n\r\n    /** @type {ItemHolder[]} */\r\n    this.itemHolders = [];\r\n  }\r\n\r\n  /**\r\n   * Position the scroller and slide containers\r\n   * according to viewport size.\r\n   *\r\n   * @param {boolean} [resizeSlides] Whether slides content should resized\r\n   */\r\n  resize(resizeSlides) {\r\n    const { pswp } = this;\r\n    const newSlideWidth = Math.round(\r\n      pswp.viewportSize.x + pswp.viewportSize.x * pswp.options.spacing\r\n    );\r\n    // Mobile browsers might trigger a resize event during a gesture.\r\n    // (due to toolbar appearing or hiding).\r\n    // Avoid re-adjusting main scroll position if width wasn't changed\r\n    const slideWidthChanged = (newSlideWidth !== this.slideWidth);\r\n\r\n    if (slideWidthChanged) {\r\n      this.slideWidth = newSlideWidth;\r\n      this.moveTo(this.getCurrSlideX());\r\n    }\r\n\r\n    this.itemHolders.forEach((itemHolder, index) => {\r\n      if (slideWidthChanged) {\r\n        setTransform(itemHolder.el, (index + this._containerShiftIndex)\r\n                                    * this.slideWidth);\r\n      }\r\n\r\n      if (resizeSlides && itemHolder.slide) {\r\n        itemHolder.slide.resize();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Reset X position of the main scroller to zero\r\n   */\r\n  resetPosition() {\r\n    // Position on the main scroller (offset)\r\n    // it is independent from slide index\r\n    this._currPositionIndex = 0;\r\n    this._prevPositionIndex = 0;\r\n\r\n    // This will force recalculation of size on next resize()\r\n    this.slideWidth = 0;\r\n\r\n    // _containerShiftIndex*viewportSize will give you amount of transform of the current slide\r\n    this._containerShiftIndex = -1;\r\n  }\r\n\r\n  /**\r\n   * Create and append array of three items\r\n   * that hold data about slides in DOM\r\n   */\r\n  appendHolders() {\r\n    this.itemHolders = [];\r\n\r\n    // append our three slide holders -\r\n    // previous, current, and next\r\n    for (let i = 0; i < 3; i++) {\r\n      const el = createElement('pswp__item', 'div', this.pswp.container);\r\n      el.setAttribute('role', 'group');\r\n      el.setAttribute('aria-roledescription', 'slide');\r\n      el.setAttribute('aria-hidden', 'true');\r\n\r\n      // hide nearby item holders until initial zoom animation finishes (to avoid extra Paints)\r\n      el.style.display = (i === 1) ? 'block' : 'none';\r\n\r\n      this.itemHolders.push({\r\n        el,\r\n        //index: -1\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Whether the main scroll can be horizontally swiped to the next or previous slide.\r\n   * @returns {boolean}\r\n   */\r\n  canBeSwiped() {\r\n    return this.pswp.getNumItems() > 1;\r\n  }\r\n\r\n  /**\r\n   * Move main scroll by X amount of slides.\r\n   * For example:\r\n   *   `-1` will move to the previous slide,\r\n   *    `0` will reset the scroll position of the current slide,\r\n   *    `3` will move three slides forward\r\n   *\r\n   * If loop option is enabled - index will be automatically looped too,\r\n   * (for example `-1` will move to the last slide of the gallery).\r\n   *\r\n   * @param {number} diff\r\n   * @param {boolean} [animate]\r\n   * @param {number} [velocityX]\r\n   * @returns {boolean} whether index was changed or not\r\n   */\r\n  moveIndexBy(diff, animate, velocityX) {\r\n    const { pswp } = this;\r\n    let newIndex = pswp.potentialIndex + diff;\r\n    const numSlides = pswp.getNumItems();\r\n\r\n    if (pswp.canLoop()) {\r\n      newIndex = pswp.getLoopedIndex(newIndex);\r\n      const distance = (diff + numSlides) % numSlides;\r\n      if (distance <= numSlides / 2) {\r\n        // go forward\r\n        diff = distance;\r\n      } else {\r\n        // go backwards\r\n        diff = distance - numSlides;\r\n      }\r\n    } else {\r\n      if (newIndex < 0) {\r\n        newIndex = 0;\r\n      } else if (newIndex >= numSlides) {\r\n        newIndex = numSlides - 1;\r\n      }\r\n      diff = newIndex - pswp.potentialIndex;\r\n    }\r\n\r\n    pswp.potentialIndex = newIndex;\r\n    this._currPositionIndex -= diff;\r\n\r\n    pswp.animations.stopMainScroll();\r\n\r\n    const destinationX = this.getCurrSlideX();\r\n    if (!animate) {\r\n      this.moveTo(destinationX);\r\n      this.updateCurrItem();\r\n    } else {\r\n      pswp.animations.startSpring({\r\n        isMainScroll: true,\r\n        start: this.x,\r\n        end: destinationX,\r\n        velocity: velocityX || 0,\r\n        naturalFrequency: 30,\r\n        dampingRatio: 1, //0.7,\r\n        onUpdate: (x) => {\r\n          this.moveTo(x);\r\n        },\r\n        onComplete: () => {\r\n          this.updateCurrItem();\r\n          pswp.appendHeavy();\r\n        }\r\n      });\r\n\r\n      let currDiff = pswp.potentialIndex - pswp.currIndex;\r\n      if (pswp.canLoop()) {\r\n        const currDistance = (currDiff + numSlides) % numSlides;\r\n        if (currDistance <= numSlides / 2) {\r\n          // go forward\r\n          currDiff = currDistance;\r\n        } else {\r\n          // go backwards\r\n          currDiff = currDistance - numSlides;\r\n        }\r\n      }\r\n\r\n      // Force-append new slides during transition\r\n      // if difference between slides is more than 1\r\n      if (Math.abs(currDiff) > 1) {\r\n        this.updateCurrItem();\r\n      }\r\n    }\r\n\r\n    return Boolean(diff);\r\n  }\r\n\r\n  /**\r\n   * X position of the main scroll for the current slide\r\n   * (ignores position during dragging)\r\n   * @returns {number}\r\n   */\r\n  getCurrSlideX() {\r\n    return this.slideWidth * this._currPositionIndex;\r\n  }\r\n\r\n  /**\r\n   * Whether scroll position is shifted.\r\n   * For example, it will return true if the scroll is being dragged or animated.\r\n   * @returns {boolean}\r\n   */\r\n  isShifted() {\r\n    return this.x !== this.getCurrSlideX();\r\n  }\r\n\r\n  /**\r\n   * Update slides X positions and set their content\r\n   */\r\n  updateCurrItem() {\r\n    const { pswp } = this;\r\n    const positionDifference = this._prevPositionIndex - this._currPositionIndex;\r\n\r\n    if (!positionDifference) {\r\n      return;\r\n    }\r\n\r\n    this._prevPositionIndex = this._currPositionIndex;\r\n\r\n    pswp.currIndex = pswp.potentialIndex;\r\n\r\n    let diffAbs = Math.abs(positionDifference);\r\n    /** @type {ItemHolder | undefined} */\r\n    let tempHolder;\r\n\r\n    if (diffAbs >= 3) {\r\n      this._containerShiftIndex += positionDifference + (positionDifference > 0 ? -3 : 3);\r\n      diffAbs = 3;\r\n\r\n      // If slides are changed by 3 screens or more - clean up previous slides\r\n      this.itemHolders.forEach((itemHolder) => {\r\n        itemHolder.slide?.destroy();\r\n        itemHolder.slide = undefined;\r\n      });\r\n    }\r\n\r\n    for (let i = 0; i < diffAbs; i++) {\r\n      if (positionDifference > 0) {\r\n        tempHolder = this.itemHolders.shift();\r\n        if (tempHolder) {\r\n          this.itemHolders[2] = tempHolder; // move first to last\r\n\r\n          this._containerShiftIndex++;\r\n\r\n          setTransform(tempHolder.el, (this._containerShiftIndex + 2) * this.slideWidth);\r\n\r\n          pswp.setContent(tempHolder, (pswp.currIndex - diffAbs) + i + 2);\r\n        }\r\n      } else {\r\n        tempHolder = this.itemHolders.pop();\r\n        if (tempHolder) {\r\n          this.itemHolders.unshift(tempHolder); // move last to first\r\n\r\n          this._containerShiftIndex--;\r\n\r\n          setTransform(tempHolder.el, this._containerShiftIndex * this.slideWidth);\r\n\r\n          pswp.setContent(tempHolder, (pswp.currIndex + diffAbs) - i - 2);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Reset transfrom every 50ish navigations in one direction.\r\n    //\r\n    // Otherwise transform will keep growing indefinitely,\r\n    // which might cause issues as browsers have a maximum transform limit.\r\n    // I wasn't able to reach it, but just to be safe.\r\n    // This should not cause noticable lag.\r\n    if (Math.abs(this._containerShiftIndex) > 50 && !this.isShifted()) {\r\n      this.resetPosition();\r\n      this.resize();\r\n    }\r\n\r\n    // Pan transition might be running (and consntantly updating pan position)\r\n    pswp.animations.stopAllPan();\r\n\r\n    this.itemHolders.forEach((itemHolder, i) => {\r\n      if (itemHolder.slide) {\r\n        // Slide in the 2nd holder is always active\r\n        itemHolder.slide.setIsActive(i === 1);\r\n      }\r\n    });\r\n\r\n    pswp.currSlide = this.itemHolders[1]?.slide;\r\n    pswp.contentLoader.updateLazy(positionDifference);\r\n\r\n    if (pswp.currSlide) {\r\n      pswp.currSlide.applyCurrentZoomPan();\r\n    }\r\n\r\n    pswp.dispatch('change');\r\n  }\r\n\r\n  /**\r\n   * Move the X position of the main scroll container\r\n   *\r\n   * @param {number} x\r\n   * @param {boolean} [dragging]\r\n   */\r\n  moveTo(x, dragging) {\r\n    if (!this.pswp.canLoop() && dragging) {\r\n      // Apply friction\r\n      let newSlideIndexOffset = ((this.slideWidth * this._currPositionIndex) - x) / this.slideWidth;\r\n      newSlideIndexOffset += this.pswp.currIndex;\r\n      const delta = Math.round(x - this.x);\r\n\r\n      if ((newSlideIndexOffset < 0 && delta > 0)\r\n          || (newSlideIndexOffset >= this.pswp.getNumItems() - 1 && delta < 0)) {\r\n        x = this.x + (delta * MAIN_SCROLL_END_FRICTION);\r\n      }\r\n    }\r\n\r\n    this.x = x;\r\n\r\n    if (this.pswp.container) {\r\n      setTransform(this.pswp.container, x);\r\n    }\r\n\r\n    this.pswp.dispatch('moveMainScroll', { x, dragging: dragging ?? false });\r\n  }\r\n}\r\n\r\nexport default MainScroll;\r\n", "import { specialKeyUsed } from './util/util.js';\r\n\r\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('./types.js').Methods<T>} Methods<T>\r\n */\r\n\r\nconst KeyboardKeyCodesMap = {\r\n  Escape: 27,\r\n  z: 90,\r\n  ArrowLeft: 37,\r\n  ArrowUp: 38,\r\n  ArrowRight: 39,\r\n  ArrowDown: 40,\r\n  Tab: 9,\r\n};\r\n\r\n/**\r\n * @template {keyof KeyboardKeyCodesMap} T\r\n * @param {T} key\r\n * @param {boolean} isKeySupported\r\n * @returns {T | number | undefined}\r\n */\r\nconst getKeyboardEventKey = (key, isKeySupported) => {\r\n  return isKeySupported ? key : KeyboardKeyCodesMap[key];\r\n};\r\n\r\n/**\r\n * - Manages keyboard shortcuts.\r\n * - Helps trap focus within photoswipe.\r\n */\r\nclass Keyboard {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    /** @private */\r\n    this._wasFocused = false;\r\n\r\n    pswp.on('bindEvents', () => {\r\n      if (pswp.options.trapFocus) {\r\n        // Dialog was likely opened by keyboard if initial point is not defined\r\n        if (!pswp.options.initialPointerPos) {\r\n          // focus causes layout,\r\n          // which causes lag during the animation,\r\n          // that's why we delay it until the opener transition ends\r\n          this._focusRoot();\r\n        }\r\n\r\n        pswp.events.add(\r\n          document,\r\n          'focusin',\r\n          /** @type EventListener */(this._onFocusIn.bind(this))\r\n        );\r\n      }\r\n\r\n      pswp.events.add(document, 'keydown', /** @type EventListener */(this._onKeyDown.bind(this)));\r\n    });\r\n\r\n    const lastActiveElement = /** @type {HTMLElement} */ (document.activeElement);\r\n    pswp.on('destroy', () => {\r\n      if (pswp.options.returnFocus\r\n          && lastActiveElement\r\n          && this._wasFocused) {\r\n        lastActiveElement.focus();\r\n      }\r\n    });\r\n  }\r\n\r\n  /** @private */\r\n  _focusRoot() {\r\n    if (!this._wasFocused && this.pswp.element) {\r\n      this.pswp.element.focus();\r\n      this._wasFocused = true;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {KeyboardEvent} e\r\n   */\r\n  _onKeyDown(e) {\r\n    const { pswp } = this;\r\n\r\n    if (pswp.dispatch('keydown', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (specialKeyUsed(e)) {\r\n      // don't do anything if special key pressed\r\n      // to prevent from overriding default browser actions\r\n      // for example, in Chrome on Mac cmd+arrow-left returns to previous page\r\n      return;\r\n    }\r\n\r\n    /** @type {Methods<PhotoSwipe> | undefined} */\r\n    let keydownAction;\r\n    /** @type {'x' | 'y' | undefined} */\r\n    let axis;\r\n    let isForward = false;\r\n    const isKeySupported = 'key' in e;\r\n\r\n    switch (isKeySupported ? e.key : e.keyCode) {\r\n      case getKeyboardEventKey('Escape', isKeySupported):\r\n        if (pswp.options.escKey) {\r\n          keydownAction = 'close';\r\n        }\r\n        break;\r\n      case getKeyboardEventKey('z', isKeySupported):\r\n        keydownAction = 'toggleZoom';\r\n        break;\r\n      case getKeyboardEventKey('ArrowLeft', isKeySupported):\r\n        axis = 'x';\r\n        break;\r\n      case getKeyboardEventKey('ArrowUp', isKeySupported):\r\n        axis = 'y';\r\n        break;\r\n      case getKeyboardEventKey('ArrowRight', isKeySupported):\r\n        axis = 'x';\r\n        isForward = true;\r\n        break;\r\n      case getKeyboardEventKey('ArrowDown', isKeySupported):\r\n        isForward = true;\r\n        axis = 'y';\r\n        break;\r\n      case getKeyboardEventKey('Tab', isKeySupported):\r\n        this._focusRoot();\r\n        break;\r\n      default:\r\n    }\r\n\r\n    // if left/right/top/bottom key\r\n    if (axis) {\r\n      // prevent page scroll\r\n      e.preventDefault();\r\n\r\n      const { currSlide } = pswp;\r\n\r\n      if (pswp.options.arrowKeys\r\n          && axis === 'x'\r\n          && pswp.getNumItems() > 1) {\r\n        keydownAction = isForward ? 'next' : 'prev';\r\n      } else if (currSlide && currSlide.currZoomLevel > currSlide.zoomLevels.fit) {\r\n        // up/down arrow keys pan the image vertically\r\n        // left/right arrow keys pan horizontally.\r\n        // Unless there is only one image,\r\n        // or arrowKeys option is disabled\r\n        currSlide.pan[axis] += isForward ? -80 : 80;\r\n        currSlide.panTo(currSlide.pan.x, currSlide.pan.y);\r\n      }\r\n    }\r\n\r\n    if (keydownAction) {\r\n      e.preventDefault();\r\n      // @ts-ignore\r\n      pswp[keydownAction]();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Trap focus inside photoswipe\r\n   *\r\n   * @private\r\n   * @param {FocusEvent} e\r\n   */\r\n  _onFocusIn(e) {\r\n    const { template } = this.pswp;\r\n    if (template\r\n        && document !== e.target\r\n        && template !== e.target\r\n        && !template.contains(/** @type {Node} */ (e.target))) {\r\n      // focus root element\r\n      template.focus();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Keyboard;\r\n", "import { setTransitionStyle, removeTransitionStyle } from './util.js';\r\n\r\nconst DEFAULT_EASING = 'cubic-bezier(.4,0,.22,1)';\r\n\r\n/** @typedef {import('./animations.js').SharedAnimationProps} SharedAnimationProps */\r\n\r\n/** @typedef {Object} DefaultCssAnimationProps\r\n *\r\n * @prop {HTMLElement} target\r\n * @prop {number} [duration]\r\n * @prop {string} [easing]\r\n * @prop {string} [transform]\r\n * @prop {string} [opacity]\r\n * */\r\n\r\n/** @typedef {SharedAnimationProps & DefaultCssAnimationProps} CssAnimationProps */\r\n\r\n/**\r\n * Runs CSS transition.\r\n */\r\nclass CSSAnimation {\r\n  /**\r\n   * onComplete can be unpredictable, be careful about current state\r\n   *\r\n   * @param {CssAnimationProps} props\r\n   */\r\n  constructor(props) {\r\n    this.props = props;\r\n    const {\r\n      target,\r\n      onComplete,\r\n      transform,\r\n      onFinish = () => {},\r\n      duration = 333,\r\n      easing = DEFAULT_EASING,\r\n    } = props;\r\n\r\n    this.onFinish = onFinish;\r\n\r\n    // support only transform and opacity\r\n    const prop = transform ? 'transform' : 'opacity';\r\n    const propValue = props[prop] ?? '';\r\n\r\n    /** @private */\r\n    this._target = target;\r\n    /** @private */\r\n    this._onComplete = onComplete;\r\n    /** @private */\r\n    this._finished = false;\r\n\r\n    /** @private */\r\n    this._onTransitionEnd = this._onTransitionEnd.bind(this);\r\n\r\n    // Using timeout hack to make sure that animation\r\n    // starts even if the animated property was changed recently,\r\n    // otherwise transitionend might not fire or transition won't start.\r\n    // https://drafts.csswg.org/css-transitions/#starting\r\n    //\r\n    // ¯\\_(ツ)_/¯\r\n    /** @private */\r\n    this._helperTimeout = setTimeout(() => {\r\n      setTransitionStyle(target, prop, duration, easing);\r\n      this._helperTimeout = setTimeout(() => {\r\n        target.addEventListener('transitionend', this._onTransitionEnd, false);\r\n        target.addEventListener('transitioncancel', this._onTransitionEnd, false);\r\n\r\n        // Safari occasionally does not emit transitionend event\r\n        // if element property was modified during the transition,\r\n        // which may be caused by resize or third party component,\r\n        // using timeout as a safety fallback\r\n        this._helperTimeout = setTimeout(() => {\r\n          this._finalizeAnimation();\r\n        }, duration + 500);\r\n        target.style[prop] = propValue;\r\n      }, 30); // Do not reduce this number\r\n    }, 0);\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {TransitionEvent} e\r\n   */\r\n  _onTransitionEnd(e) {\r\n    if (e.target === this._target) {\r\n      this._finalizeAnimation();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _finalizeAnimation() {\r\n    if (!this._finished) {\r\n      this._finished = true;\r\n      this.onFinish();\r\n      if (this._onComplete) {\r\n        this._onComplete();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Destroy is called automatically onFinish\r\n  destroy() {\r\n    if (this._helperTimeout) {\r\n      clearTimeout(this._helperTimeout);\r\n    }\r\n    removeTransitionStyle(this._target);\r\n    this._target.removeEventListener('transitionend', this._onTransitionEnd, false);\r\n    this._target.removeEventListener('transitioncancel', this._onTransitionEnd, false);\r\n    if (!this._finished) {\r\n      this._finalizeAnimation();\r\n    }\r\n  }\r\n}\r\n\r\nexport default CSSAnimation;\r\n", "const DEFAULT_NATURAL_FREQUENCY = 12;\r\nconst DEFAULT_DAMPING_RATIO = 0.75;\r\n\r\n/**\r\n * Spring easing helper\r\n */\r\nclass SpringEaser {\r\n  /**\r\n   * @param {number} initialVelocity Initial velocity, px per ms.\r\n   *\r\n   * @param {number} [dampingRatio]\r\n   * Determines how bouncy animation will be.\r\n   * From 0 to 1, 0 - always overshoot, 1 - do not overshoot.\r\n   * \"overshoot\" refers to part of animation that\r\n   * goes beyond the final value.\r\n   *\r\n   * @param {number} [naturalFrequency]\r\n   * Determines how fast animation will slow down.\r\n   * The higher value - the stiffer the transition will be,\r\n   * and the faster it will slow down.\r\n   * Recommended value from 10 to 50\r\n   */\r\n  constructor(initialVelocity, dampingRatio, naturalFrequency) {\r\n    this.velocity = initialVelocity * 1000; // convert to \"pixels per second\"\r\n\r\n    // https://en.wikipedia.org/wiki/Damping_ratio\r\n    this._dampingRatio = dampingRatio || DEFAULT_DAMPING_RATIO;\r\n\r\n    // https://en.wikipedia.org/wiki/Natural_frequency\r\n    this._naturalFrequency = naturalFrequency || DEFAULT_NATURAL_FREQUENCY;\r\n\r\n    this._dampedFrequency = this._naturalFrequency;\r\n\r\n    if (this._dampingRatio < 1) {\r\n      this._dampedFrequency *= Math.sqrt(1 - this._dampingRatio * this._dampingRatio);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} deltaPosition Difference between current and end position of the animation\r\n   * @param {number} deltaTime Frame duration in milliseconds\r\n   *\r\n   * @returns {number} Displacement, relative to the end position.\r\n   */\r\n  easeFrame(deltaPosition, deltaTime) {\r\n    // Inspired by Apple Webkit and Android spring function implementation\r\n    // https://en.wikipedia.org/wiki/Oscillation\r\n    // https://en.wikipedia.org/wiki/Damping_ratio\r\n    // we ignore mass (assume that it's 1kg)\r\n\r\n    let displacement = 0;\r\n    let coeff;\r\n\r\n    deltaTime /= 1000;\r\n\r\n    const naturalDumpingPow = Math.E ** (-this._dampingRatio * this._naturalFrequency * deltaTime);\r\n\r\n    if (this._dampingRatio === 1) {\r\n      coeff = this.velocity + this._naturalFrequency * deltaPosition;\r\n\r\n      displacement = (deltaPosition + coeff * deltaTime) * naturalDumpingPow;\r\n\r\n      this.velocity = displacement\r\n                        * (-this._naturalFrequency) + coeff\r\n                        * naturalDumpingPow;\r\n    } else if (this._dampingRatio < 1) {\r\n      coeff = (1 / this._dampedFrequency)\r\n                * (this._dampingRatio * this._naturalFrequency * deltaPosition + this.velocity);\r\n\r\n      const dumpedFCos = Math.cos(this._dampedFrequency * deltaTime);\r\n      const dumpedFSin = Math.sin(this._dampedFrequency * deltaTime);\r\n\r\n      displacement = naturalDumpingPow\r\n                       * (deltaPosition * dumpedFCos + coeff * dumpedFSin);\r\n\r\n      this.velocity = displacement\r\n                        * (-this._naturalFrequency)\r\n                        * this._dampingRatio\r\n                        + naturalDumpingPow\r\n                        * (-this._dampedFrequency * deltaPosition * dumpedFSin\r\n                        + this._dampedFrequency * coeff * dumpedFCos);\r\n    }\r\n\r\n    // Overdamped (>1) damping ratio is not supported\r\n\r\n    return displacement;\r\n  }\r\n}\r\n\r\nexport default SpringEaser;\r\n", "import SpringEaser from './spring-easer.js';\r\n\r\n/** @typedef {import('./animations.js').SharedAnimationProps} SharedAnimationProps */\r\n\r\n/**\r\n * @typedef {Object} DefaultSpringAnimationProps\r\n *\r\n * @prop {number} start\r\n * @prop {number} end\r\n * @prop {number} velocity\r\n * @prop {number} [dampingRatio]\r\n * @prop {number} [naturalFrequency]\r\n * @prop {(end: number) => void} onUpdate\r\n */\r\n\r\n/** @typedef {SharedAnimationProps & DefaultSpringAnimationProps} SpringAnimationProps */\r\n\r\nclass SpringAnimation {\r\n  /**\r\n   * @param {SpringAnimationProps} props\r\n   */\r\n  constructor(props) {\r\n    this.props = props;\r\n    this._raf = 0;\r\n\r\n    const {\r\n      start,\r\n      end,\r\n      velocity,\r\n      onUpdate,\r\n      onComplete,\r\n      onFinish = () => {},\r\n      dampingRatio,\r\n      naturalFrequency\r\n    } = props;\r\n\r\n    this.onFinish = onFinish;\r\n\r\n    const easer = new SpringEaser(velocity, dampingRatio, naturalFrequency);\r\n    let prevTime = Date.now();\r\n    let deltaPosition = start - end;\r\n\r\n    const animationLoop = () => {\r\n      if (this._raf) {\r\n        deltaPosition = easer.easeFrame(deltaPosition, Date.now() - prevTime);\r\n\r\n        // Stop the animation if velocity is low and position is close to end\r\n        if (Math.abs(deltaPosition) < 1 && Math.abs(easer.velocity) < 50) {\r\n          // Finalize the animation\r\n          onUpdate(end);\r\n          if (onComplete) {\r\n            onComplete();\r\n          }\r\n          this.onFinish();\r\n        } else {\r\n          prevTime = Date.now();\r\n          onUpdate(deltaPosition + end);\r\n          this._raf = requestAnimationFrame(animationLoop);\r\n        }\r\n      }\r\n    };\r\n\r\n    this._raf = requestAnimationFrame(animationLoop);\r\n  }\r\n\r\n  // Destroy is called automatically onFinish\r\n  destroy() {\r\n    if (this._raf >= 0) {\r\n      cancelAnimationFrame(this._raf);\r\n    }\r\n    this._raf = 0;\r\n  }\r\n}\r\n\r\nexport default SpringAnimation;\r\n", "import CSSAnimation from './css-animation.js';\r\nimport SpringAnimation from './spring-animation.js';\r\n\r\n/** @typedef {import('./css-animation.js').CssAnimationProps} CssAnimationProps */\r\n/** @typedef {import('./spring-animation.js').SpringAnimationProps} SpringAnimationProps */\r\n\r\n/** @typedef {Object} SharedAnimationProps\r\n * @prop {string} [name]\r\n * @prop {boolean} [isPan]\r\n * @prop {boolean} [isMainScroll]\r\n * @prop {VoidFunction} [onComplete]\r\n * @prop {VoidFunction} [onFinish]\r\n */\r\n\r\n/** @typedef {SpringAnimation | CSSAnimation} Animation */\r\n/** @typedef {SpringAnimationProps | CssAnimationProps} AnimationProps */\r\n\r\n/**\r\n * Manages animations\r\n */\r\nclass Animations {\r\n  constructor() {\r\n    /** @type {Animation[]} */\r\n    this.activeAnimations = [];\r\n  }\r\n\r\n  /**\r\n   * @param {SpringAnimationProps} props\r\n   */\r\n  startSpring(props) {\r\n    this._start(props, true);\r\n  }\r\n\r\n  /**\r\n   * @param {CssAnimationProps} props\r\n   */\r\n  startTransition(props) {\r\n    this._start(props);\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {AnimationProps} props\r\n   * @param {boolean} [isSpring]\r\n   * @returns {Animation}\r\n   */\r\n  _start(props, isSpring) {\r\n    const animation = isSpring\r\n      ? new SpringAnimation(/** @type SpringAnimationProps */ (props))\r\n      : new CSSAnimation(/** @type CssAnimationProps */ (props));\r\n\r\n    this.activeAnimations.push(animation);\r\n    animation.onFinish = () => this.stop(animation);\r\n\r\n    return animation;\r\n  }\r\n\r\n  /**\r\n   * @param {Animation} animation\r\n   */\r\n  stop(animation) {\r\n    animation.destroy();\r\n    const index = this.activeAnimations.indexOf(animation);\r\n    if (index > -1) {\r\n      this.activeAnimations.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  stopAll() { // _stopAllAnimations\r\n    this.activeAnimations.forEach((animation) => {\r\n      animation.destroy();\r\n    });\r\n    this.activeAnimations = [];\r\n  }\r\n\r\n  /**\r\n   * Stop all pan or zoom transitions\r\n   */\r\n  stopAllPan() {\r\n    this.activeAnimations = this.activeAnimations.filter((animation) => {\r\n      if (animation.props.isPan) {\r\n        animation.destroy();\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }\r\n\r\n  stopMainScroll() {\r\n    this.activeAnimations = this.activeAnimations.filter((animation) => {\r\n      if (animation.props.isMainScroll) {\r\n        animation.destroy();\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns true if main scroll transition is running\r\n   */\r\n  // isMainScrollRunning() {\r\n  //   return this.activeAnimations.some((animation) => {\r\n  //     return animation.props.isMainScroll;\r\n  //   });\r\n  // }\r\n\r\n  /**\r\n   * Returns true if any pan or zoom transition is running\r\n   */\r\n  isPanRunning() {\r\n    return this.activeAnimations.some((animation) => {\r\n      return animation.props.isPan;\r\n    });\r\n  }\r\n}\r\n\r\nexport default Animations;\r\n", "/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n * <PERSON><PERSON> scroll wheel.\r\n * Can pan and zoom current slide image.\r\n */\r\nclass ScrollWheel {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    pswp.events.add(pswp.element, 'wheel', /** @type EventListener */(this._onWheel.bind(this)));\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {WheelEvent} e\r\n   */\r\n  _onWheel(e) {\r\n    e.preventDefault();\r\n    const { currSlide } = this.pswp;\r\n    let { deltaX, deltaY } = e;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    if (this.pswp.dispatch('wheel', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (e.ctrlKey || this.pswp.options.wheelToZoom) {\r\n      // zoom\r\n      if (currSlide.isZoomable()) {\r\n        let zoomFactor = -deltaY;\r\n        if (e.deltaMode === 1 /* DOM_DELTA_LINE */) {\r\n          zoomFactor *= 0.05;\r\n        } else {\r\n          zoomFactor *= e.deltaMode ? 1 : 0.002;\r\n        }\r\n        zoomFactor = 2 ** zoomFactor;\r\n\r\n        const destZoomLevel = currSlide.currZoomLevel * zoomFactor;\r\n        currSlide.zoomTo(destZoomLevel, {\r\n          x: e.clientX,\r\n          y: e.clientY\r\n        });\r\n      }\r\n    } else {\r\n      // pan\r\n      if (currSlide.isPannable()) {\r\n        if (e.deltaMode === 1 /* DOM_DELTA_LINE */) {\r\n          // 18 - average line height\r\n          deltaX *= 18;\r\n          deltaY *= 18;\r\n        }\r\n\r\n        currSlide.panTo(\r\n          currSlide.pan.x - deltaX,\r\n          currSlide.pan.y - deltaY\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nexport default ScrollWheel;\r\n", "import { createElement } from '../util/util.js';\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('../types.js').Methods<T>} Methods<T>\r\n */\r\n\r\n/**\r\n * @typedef {Object} UIElementMarkupProps\r\n * @prop {boolean} [isCustomSVG]\r\n * @prop {string} inner\r\n * @prop {string} [outlineID]\r\n * @prop {number | string} [size]\r\n */\r\n\r\n/**\r\n * @typedef {Object} UIElementData\r\n * @prop {DefaultUIElements | string} [name]\r\n * @prop {string} [className]\r\n * @prop {UIElementMarkup} [html]\r\n * @prop {boolean} [isButton]\r\n * @prop {keyof HTMLElementTagNameMap} [tagName]\r\n * @prop {string} [title]\r\n * @prop {string} [ariaLabel]\r\n * @prop {(element: HTMLElement, pswp: PhotoSwipe) => void} [onInit]\r\n * @prop {Methods<PhotoSwipe> | ((e: MouseEvent, element: HTMLElement, pswp: PhotoSwipe) => void)} [onClick]\r\n * @prop {'bar' | 'wrapper' | 'root'} [appendTo]\r\n * @prop {number} [order]\r\n */\r\n\r\n/** @typedef {'arrowPrev' | 'arrowNext' | 'close' | 'zoom' | 'counter'} DefaultUIElements */\r\n\r\n/** @typedef {string | UIElementMarkupProps} UIElementMarkup */\r\n\r\n/**\r\n * @param {UIElementMarkup} [htmlData]\r\n * @returns {string}\r\n */\r\nfunction addElementHTML(htmlData) {\r\n  if (typeof htmlData === 'string') {\r\n    // Allow developers to provide full svg,\r\n    // For example:\r\n    // <svg viewBox=\"0 0 32 32\" width=\"32\" height=\"32\" aria-hidden=\"true\" class=\"pswp__icn\">\r\n    //   <path d=\"...\" />\r\n    //   <circle ... />\r\n    // </svg>\r\n    // Can also be any HTML string.\r\n    return htmlData;\r\n  }\r\n\r\n  if (!htmlData || !htmlData.isCustomSVG) {\r\n    return '';\r\n  }\r\n\r\n  const svgData = htmlData;\r\n  let out = '<svg aria-hidden=\"true\" class=\"pswp__icn\" viewBox=\"0 0 %d %d\" width=\"%d\" height=\"%d\">';\r\n  // replace all %d with size\r\n  out = out.split('%d').join(/** @type {string} */ (svgData.size || 32));\r\n\r\n  // Icons may contain outline/shadow,\r\n  // to make it we \"clone\" base icon shape and add border to it.\r\n  // Icon itself and border are styled via CSS.\r\n  //\r\n  // Property shadowID defines ID of element that should be cloned.\r\n  if (svgData.outlineID) {\r\n    out += '<use class=\"pswp__icn-shadow\" xlink:href=\"#' + svgData.outlineID + '\"/>';\r\n  }\r\n\r\n  out += svgData.inner;\r\n\r\n  out += '</svg>';\r\n\r\n  return out;\r\n}\r\n\r\nclass UIElement {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   * @param {UIElementData} data\r\n   */\r\n  constructor(pswp, data) {\r\n    const name = data.name || data.className;\r\n    let elementHTML = data.html;\r\n\r\n    // @ts-expect-error lookup only by `data.name` maybe?\r\n    if (pswp.options[name] === false) {\r\n      // exit if element is disabled from options\r\n      return;\r\n    }\r\n\r\n    // Allow to override SVG icons from options\r\n    // @ts-expect-error lookup only by `data.name` maybe?\r\n    if (typeof pswp.options[name + 'SVG'] === 'string') {\r\n      // arrowPrevSVG\r\n      // arrowNextSVG\r\n      // closeSVG\r\n      // zoomSVG\r\n      // @ts-expect-error lookup only by `data.name` maybe?\r\n      elementHTML = pswp.options[name + 'SVG'];\r\n    }\r\n\r\n    pswp.dispatch('uiElementCreate', { data });\r\n\r\n    let className = '';\r\n    if (data.isButton) {\r\n      className += 'pswp__button ';\r\n      className += (data.className || `pswp__button--${data.name}`);\r\n    } else {\r\n      className += (data.className || `pswp__${data.name}`);\r\n    }\r\n\r\n    let tagName = data.isButton ? (data.tagName || 'button') : (data.tagName || 'div');\r\n    tagName = /** @type {keyof HTMLElementTagNameMap} */ (tagName.toLowerCase());\r\n    /** @type {HTMLElement} */\r\n    const element = createElement(className, tagName);\r\n\r\n    if (data.isButton) {\r\n      if (tagName === 'button') {\r\n        /** @type {HTMLButtonElement} */ (element).type = 'button';\r\n      }\r\n\r\n      let { title } = data;\r\n      const { ariaLabel } = data;\r\n\r\n      // @ts-expect-error lookup only by `data.name` maybe?\r\n      if (typeof pswp.options[name + 'Title'] === 'string') {\r\n        // @ts-expect-error lookup only by `data.name` maybe?\r\n        title = pswp.options[name + 'Title'];\r\n      }\r\n\r\n      if (title) {\r\n        element.title = title;\r\n      }\r\n\r\n      const ariaText = ariaLabel || title;\r\n      if (ariaText) {\r\n        element.setAttribute('aria-label', ariaText);\r\n      }\r\n    }\r\n\r\n    element.innerHTML = addElementHTML(elementHTML);\r\n\r\n    if (data.onInit) {\r\n      data.onInit(element, pswp);\r\n    }\r\n\r\n    if (data.onClick) {\r\n      element.onclick = (e) => {\r\n        if (typeof data.onClick === 'string') {\r\n          // @ts-ignore\r\n          pswp[data.onClick]();\r\n        } else if (typeof data.onClick === 'function') {\r\n          data.onClick(e, element, pswp);\r\n        }\r\n      };\r\n    }\r\n\r\n    // Top bar is default position\r\n    const appendTo = data.appendTo || 'bar';\r\n    /** @type {HTMLElement | undefined} root element by default */\r\n    let container = pswp.element;\r\n    if (appendTo === 'bar') {\r\n      if (!pswp.topBar) {\r\n        pswp.topBar = createElement('pswp__top-bar pswp__hide-on-close', 'div', pswp.scrollWrap);\r\n      }\r\n      container = pswp.topBar;\r\n    } else {\r\n      // element outside of top bar gets a secondary class\r\n      // that makes element fade out on close\r\n      element.classList.add('pswp__hide-on-close');\r\n\r\n      if (appendTo === 'wrapper') {\r\n        container = pswp.scrollWrap;\r\n      }\r\n    }\r\n\r\n    container?.appendChild(pswp.applyFilters('uiElement', element, data));\r\n  }\r\n}\r\n\r\nexport default UIElement;\r\n", "/*\r\n  Backward and forward arrow buttons\r\n */\r\n\r\n/** @typedef {import('./ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n *\r\n * @param {HTMLElement} element\r\n * @param {PhotoSwipe} pswp\r\n * @param {boolean} [isNextButton]\r\n */\r\nfunction initArrowButton(element, pswp, isNextButton) {\r\n  element.classList.add('pswp__button--arrow');\r\n  // TODO: this should point to a unique id for this instance\r\n  element.setAttribute('aria-controls', 'pswp__items');\r\n  pswp.on('change', () => {\r\n    if (!pswp.options.loop) {\r\n      if (isNextButton) {\r\n        /** @type {HTMLButtonElement} */\r\n        (element).disabled = !(pswp.currIndex < pswp.getNumItems() - 1);\r\n      } else {\r\n        /** @type {HTMLButtonElement} */\r\n        (element).disabled = !(pswp.currIndex > 0);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n/** @type {UIElementData} */\r\nexport const arrowPrev = {\r\n  name: 'arrowPrev',\r\n  className: 'pswp__button--arrow--prev',\r\n  title: 'Previous',\r\n  order: 10,\r\n  isButton: true,\r\n  appendTo: 'wrapper',\r\n  html: {\r\n    isCustomSVG: true,\r\n    size: 60,\r\n    inner: '<path d=\"M29 43l-3 3-16-16 16-16 3 3-13 13 13 13z\" id=\"pswp__icn-arrow\"/>',\r\n    outlineID: 'pswp__icn-arrow'\r\n  },\r\n  onClick: 'prev',\r\n  onInit: initArrowButton\r\n};\r\n\r\n/** @type {UIElementData} */\r\nexport const arrowNext = {\r\n  name: 'arrowNext',\r\n  className: 'pswp__button--arrow--next',\r\n  title: 'Next',\r\n  order: 11,\r\n  isButton: true,\r\n  appendTo: 'wrapper',\r\n  html: {\r\n    isCustomSVG: true,\r\n    size: 60,\r\n    inner: '<use xlink:href=\"#pswp__icn-arrow\"/>',\r\n    outlineID: 'pswp__icn-arrow'\r\n  },\r\n  onClick: 'next',\r\n  onInit: (el, pswp) => {\r\n    initArrowButton(el, pswp, true);\r\n  }\r\n};\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nconst closeButton = {\r\n  name: 'close',\r\n  title: 'Close',\r\n  order: 20,\r\n  isButton: true,\r\n  html: {\r\n    isCustomSVG: true,\r\n    inner: '<path d=\"M24 10l-2-2-6 6-6-6-2 2 6 6-6 6 2 2 6-6 6 6 2-2-6-6z\" id=\"pswp__icn-close\"/>',\r\n    outlineID: 'pswp__icn-close'\r\n  },\r\n  onClick: 'close'\r\n};\r\n\r\nexport default closeButton;\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nconst zoomButton = {\r\n  name: 'zoom',\r\n  title: 'Zoom',\r\n  order: 10,\r\n  isButton: true,\r\n  html: {\r\n    isCustomSVG: true,\r\n    // eslint-disable-next-line max-len\r\n    inner: '<path d=\"M17.426 19.926a6 6 0 1 1 1.5-1.5L23 22.5 21.5 24l-4.074-4.074z\" id=\"pswp__icn-zoom\"/>'\r\n          + '<path fill=\"currentColor\" class=\"pswp__zoom-icn-bar-h\" d=\"M11 16v-2h6v2z\"/>'\r\n          + '<path fill=\"currentColor\" class=\"pswp__zoom-icn-bar-v\" d=\"M13 12h2v6h-2z\"/>',\r\n    outlineID: 'pswp__icn-zoom'\r\n  },\r\n  onClick: 'toggleZoom'\r\n};\r\n\r\nexport default zoomButton;\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nexport const loadingIndicator = {\r\n  name: 'preloader',\r\n  appendTo: 'bar',\r\n  order: 7,\r\n  html: {\r\n    isCustomSVG: true,\r\n    // eslint-disable-next-line max-len\r\n    inner: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M21.2 16a5.2 5.2 0 1 1-5.2-5.2V8a8 8 0 1 0 8 8h-2.8Z\" id=\"pswp__icn-loading\"/>',\r\n    outlineID: 'pswp__icn-loading'\r\n  },\r\n  onInit: (indicatorElement, pswp) => {\r\n    /** @type {boolean | undefined} */\r\n    let isVisible;\r\n    /** @type {NodeJS.Timeout | null} */\r\n    let delayTimeout = null;\r\n\r\n    /**\r\n     * @param {string} className\r\n     * @param {boolean} add\r\n     */\r\n    const toggleIndicatorClass = (className, add) => {\r\n      indicatorElement.classList.toggle('pswp__preloader--' + className, add);\r\n    };\r\n\r\n    /**\r\n     * @param {boolean} visible\r\n     */\r\n    const setIndicatorVisibility = (visible) => {\r\n      if (isVisible !== visible) {\r\n        isVisible = visible;\r\n        toggleIndicatorClass('active', visible);\r\n      }\r\n    };\r\n\r\n    const updatePreloaderVisibility = () => {\r\n      if (!pswp.currSlide?.content.isLoading()) {\r\n        setIndicatorVisibility(false);\r\n        if (delayTimeout) {\r\n          clearTimeout(delayTimeout);\r\n          delayTimeout = null;\r\n        }\r\n        return;\r\n      }\r\n\r\n      if (!delayTimeout) {\r\n        // display loading indicator with delay\r\n        delayTimeout = setTimeout(() => {\r\n          setIndicatorVisibility(Boolean(pswp.currSlide?.content.isLoading()));\r\n          delayTimeout = null;\r\n        }, pswp.options.preloaderDelay);\r\n      }\r\n    };\r\n\r\n    pswp.on('change', updatePreloaderVisibility);\r\n\r\n    pswp.on('loadComplete', (e) => {\r\n      if (pswp.currSlide === e.slide) {\r\n        updatePreloaderVisibility();\r\n      }\r\n    });\r\n\r\n    // expose the method\r\n    if (pswp.ui) {\r\n      pswp.ui.updatePreloaderVisibility = updatePreloaderVisibility;\r\n    }\r\n  }\r\n};\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nexport const counterIndicator = {\r\n  name: 'counter',\r\n  order: 5,\r\n  onInit: (counterElement, pswp) => {\r\n    pswp.on('change', () => {\r\n      counterElement.innerText = (pswp.currIndex + 1)\r\n                                  + pswp.options.indexIndicatorSep\r\n                                  + pswp.getNumItems();\r\n    });\r\n  }\r\n};\r\n", "import UIElement from './ui-element.js';\r\nimport { arrowPrev, arrowNext } from './button-arrow.js';\r\nimport closeButton from './button-close.js';\r\nimport zoomButton from './button-zoom.js';\r\nimport { loadingIndicator } from './loading-indicator.js';\r\nimport { counterIndicator } from './counter-indicator.js';\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('./ui-element.js').UIElementData} UIElementData */\r\n\r\n/**\r\n * Set special class on element when image is zoomed.\r\n *\r\n * By default, it is used to adjust\r\n * zoom icon and zoom cursor via CSS.\r\n *\r\n * @param {HTMLElement} el\r\n * @param {boolean} isZoomedIn\r\n */\r\nfunction setZoomedIn(el, isZoomedIn) {\r\n  el.classList.toggle('pswp--zoomed-in', isZoomedIn);\r\n}\r\n\r\nclass UI {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    this.isRegistered = false;\r\n    /** @type {UIElementData[]} */\r\n    this.uiElementsData = [];\r\n    /** @type {(UIElement | UIElementData)[]} */\r\n    this.items = [];\r\n    /** @type {() => void} */\r\n    this.updatePreloaderVisibility = () => {};\r\n\r\n    /**\r\n     * @private\r\n     * @type {number | undefined}\r\n     */\r\n    this._lastUpdatedZoomLevel = undefined;\r\n  }\r\n\r\n  init() {\r\n    const { pswp } = this;\r\n    this.isRegistered = false;\r\n    this.uiElementsData = [\r\n      closeButton,\r\n      arrowPrev,\r\n      arrowNext,\r\n      zoomButton,\r\n      loadingIndicator,\r\n      counterIndicator\r\n    ];\r\n\r\n    pswp.dispatch('uiRegister');\r\n\r\n    // sort by order\r\n    this.uiElementsData.sort((a, b) => {\r\n      // default order is 0\r\n      return (a.order || 0) - (b.order || 0);\r\n    });\r\n\r\n    this.items = [];\r\n\r\n    this.isRegistered = true;\r\n    this.uiElementsData.forEach((uiElementData) => {\r\n      this.registerElement(uiElementData);\r\n    });\r\n\r\n    pswp.on('change', () => {\r\n      pswp.element?.classList.toggle('pswp--one-slide', pswp.getNumItems() === 1);\r\n    });\r\n\r\n    pswp.on('zoomPanUpdate', () => this._onZoomPanUpdate());\r\n  }\r\n\r\n  /**\r\n   * @param {UIElementData} elementData\r\n   */\r\n  registerElement(elementData) {\r\n    if (this.isRegistered) {\r\n      this.items.push(\r\n        new UIElement(this.pswp, elementData)\r\n      );\r\n    } else {\r\n      this.uiElementsData.push(elementData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fired each time zoom or pan position is changed.\r\n   * Update classes that control visibility of zoom button and cursor icon.\r\n   *\r\n   * @private\r\n   */\r\n  _onZoomPanUpdate() {\r\n    const { template, currSlide, options } = this.pswp;\r\n\r\n    if (this.pswp.opener.isClosing || !template || !currSlide) {\r\n      return;\r\n    }\r\n\r\n    let { currZoomLevel } = currSlide;\r\n\r\n    // if not open yet - check against initial zoom level\r\n    if (!this.pswp.opener.isOpen) {\r\n      currZoomLevel = currSlide.zoomLevels.initial;\r\n    }\r\n\r\n    if (currZoomLevel === this._lastUpdatedZoomLevel) {\r\n      return;\r\n    }\r\n    this._lastUpdatedZoomLevel = currZoomLevel;\r\n\r\n    const currZoomLevelDiff = currSlide.zoomLevels.initial - currSlide.zoomLevels.secondary;\r\n\r\n    // Initial and secondary zoom levels are almost equal\r\n    if (Math.abs(currZoomLevelDiff) < 0.01 || !currSlide.isZoomable()) {\r\n      // disable zoom\r\n      setZoomedIn(template, false);\r\n      template.classList.remove('pswp--zoom-allowed');\r\n      return;\r\n    }\r\n\r\n    template.classList.add('pswp--zoom-allowed');\r\n\r\n    const potentialZoomLevel = currZoomLevel === currSlide.zoomLevels.initial\r\n      ? currSlide.zoomLevels.secondary : currSlide.zoomLevels.initial;\r\n\r\n    setZoomedIn(template, potentialZoomLevel <= currZoomLevel);\r\n\r\n    if (options.imageClickAction === 'zoom'\r\n        || options.imageClickAction === 'zoom-or-close') {\r\n      template.classList.add('pswp--click-to-zoom');\r\n    }\r\n  }\r\n}\r\n\r\nexport default UI;\r\n", "/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\n/** @typedef {{ x: number; y: number; w: number; innerRect?: { w: number; h: number; x: number; y: number } }} Bounds */\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n * @returns Bounds\r\n */\r\nfunction getBoundsByElement(el) {\r\n  const thumbAreaRect = el.getBoundingClientRect();\r\n  return {\r\n    x: thumbAreaRect.left,\r\n    y: thumbAreaRect.top,\r\n    w: thumbAreaRect.width\r\n  };\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n * @param {number} imageWidth\r\n * @param {number} imageHeight\r\n * @returns Bounds\r\n */\r\nfunction getCroppedBoundsByElement(el, imageWidth, imageHeight) {\r\n  const thumbAreaRect = el.getBoundingClientRect();\r\n\r\n  // fill image into the area\r\n  // (do they same as object-fit:cover does to retrieve coordinates)\r\n  const hRatio = thumbAreaRect.width / imageWidth;\r\n  const vRatio = thumbAreaRect.height / imageHeight;\r\n  const fillZoomLevel = hRatio > vRatio ? hRatio : vRatio;\r\n\r\n  const offsetX = (thumbAreaRect.width - imageWidth * fillZoomLevel) / 2;\r\n  const offsetY = (thumbAreaRect.height - imageHeight * fillZoomLevel) / 2;\r\n\r\n  /**\r\n   * Coordinates of the image,\r\n   * as if it was not cropped,\r\n   * height is calculated automatically\r\n   *\r\n   * @type {Bounds}\r\n   */\r\n  const bounds = {\r\n    x: thumbAreaRect.left + offsetX,\r\n    y: thumbAreaRect.top + offsetY,\r\n    w: imageWidth * fillZoomLevel\r\n  };\r\n\r\n  // Coordinates of inner crop area\r\n  // relative to the image\r\n  bounds.innerRect = {\r\n    w: thumbAreaRect.width,\r\n    h: thumbAreaRect.height,\r\n    x: offsetX,\r\n    y: offsetY\r\n  };\r\n\r\n  return bounds;\r\n}\r\n\r\n/**\r\n * Get dimensions of thumbnail image\r\n * (click on which opens photoswipe or closes photoswipe to)\r\n *\r\n * @param {number} index\r\n * @param {SlideData} itemData\r\n * @param {PhotoSwipe} instance PhotoSwipe instance\r\n * @returns {Bounds | undefined}\r\n */\r\nexport function getThumbBounds(index, itemData, instance) {\r\n  // legacy event, before filters were introduced\r\n  const event = instance.dispatch('thumbBounds', {\r\n    index,\r\n    itemData,\r\n    instance\r\n  });\r\n  // @ts-expect-error\r\n  if (event.thumbBounds) {\r\n    // @ts-expect-error\r\n    return event.thumbBounds;\r\n  }\r\n\r\n  const { element } = itemData;\r\n  /** @type {Bounds | undefined} */\r\n  let thumbBounds;\r\n  /** @type {HTMLElement | null | undefined} */\r\n  let thumbnail;\r\n\r\n  if (element && instance.options.thumbSelector !== false) {\r\n    const thumbSelector = instance.options.thumbSelector || 'img';\r\n    thumbnail = element.matches(thumbSelector)\r\n      ? element : /** @type {HTMLElement | null} */ (element.querySelector(thumbSelector));\r\n  }\r\n\r\n  thumbnail = instance.applyFilters('thumbEl', thumbnail, itemData, index);\r\n\r\n  if (thumbnail) {\r\n    if (!itemData.thumbCropped) {\r\n      thumbBounds = getBoundsByElement(thumbnail);\r\n    } else {\r\n      thumbBounds = getCroppedBoundsByElement(\r\n        thumbnail,\r\n        itemData.width || itemData.w || 0,\r\n        itemData.height || itemData.h || 0\r\n      );\r\n    }\r\n  }\r\n\r\n  return instance.applyFilters('thumbBounds', thumbBounds, itemData, index);\r\n}\r\n", "/** @typedef {import('../lightbox/lightbox.js').default} PhotoSwipeLightbox */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').DataSource} DataSource */\r\n/** @typedef {import('../ui/ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('../slide/content.js').default} ContentDefault */\r\n/** @typedef {import('../slide/slide.js').default} Slide */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n/** @typedef {import('../slide/zoom-level.js').default} ZoomLevel */\r\n/** @typedef {import('../slide/get-thumb-bounds.js').Bounds} Bounds */\r\n\r\n/**\r\n * Allow adding an arbitrary props to the Content\r\n * https://photoswipe.com/custom-content/#using-webp-image-format\r\n * @typedef {ContentDefault & Record<string, any>} Content\r\n */\r\n/** @typedef {{ x?: number; y?: number }} Point */\r\n\r\n/**\r\n * @typedef {Object} PhotoSwipeEventsMap https://photoswipe.com/events/\r\n *\r\n *\r\n * https://photoswipe.com/adding-ui-elements/\r\n *\r\n * @prop {undefined} uiRegister\r\n * @prop {{ data: UIElementData }} uiElementCreate\r\n *\r\n *\r\n * https://photoswipe.com/events/#initialization-events\r\n *\r\n * @prop {undefined} beforeOpen\r\n * @prop {undefined} firstUpdate\r\n * @prop {undefined} initialLayout\r\n * @prop {undefined} change\r\n * @prop {undefined} afterInit\r\n * @prop {undefined} bindEvents\r\n *\r\n *\r\n * https://photoswipe.com/events/#opening-or-closing-transition-events\r\n *\r\n * @prop {undefined} openingAnimationStart\r\n * @prop {undefined} openingAnimationEnd\r\n * @prop {undefined} closingAnimationStart\r\n * @prop {undefined} closingAnimationEnd\r\n *\r\n *\r\n * https://photoswipe.com/events/#closing-events\r\n *\r\n * @prop {undefined} close\r\n * @prop {undefined} destroy\r\n *\r\n *\r\n * https://photoswipe.com/events/#pointer-and-gesture-events\r\n *\r\n * @prop {{ originalEvent: PointerEvent }} pointerDown\r\n * @prop {{ originalEvent: PointerEvent }} pointerMove\r\n * @prop {{ originalEvent: PointerEvent }} pointerUp\r\n * @prop {{ bgOpacity: number }} pinchClose can be default prevented\r\n * @prop {{ panY: number }} verticalDrag can be default prevented\r\n *\r\n *\r\n * https://photoswipe.com/events/#slide-content-events\r\n *\r\n * @prop {{ content: Content }} contentInit\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoad can be default prevented\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoadImage can be default prevented\r\n * @prop {{ content: Content; slide: Slide; isError?: boolean }} loadComplete\r\n * @prop {{ content: Content; slide: Slide }} loadError\r\n * @prop {{ content: Content; width: number; height: number }} contentResize can be default prevented\r\n * @prop {{ content: Content; width: number; height: number; slide: Slide }} imageSizeChange\r\n * @prop {{ content: Content }} contentLazyLoad can be default prevented\r\n * @prop {{ content: Content }} contentAppend can be default prevented\r\n * @prop {{ content: Content }} contentActivate can be default prevented\r\n * @prop {{ content: Content }} contentDeactivate can be default prevented\r\n * @prop {{ content: Content }} contentRemove can be default prevented\r\n * @prop {{ content: Content }} contentDestroy can be default prevented\r\n *\r\n *\r\n * undocumented\r\n *\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} imageClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} bgClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} tapAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} doubleTapAction can be default prevented\r\n *\r\n * @prop {{ originalEvent: KeyboardEvent }} keydown can be default prevented\r\n * @prop {{ x: number; dragging: boolean }} moveMainScroll\r\n * @prop {{ slide: Slide }} firstZoomPan\r\n * @prop {{ slide: Slide | undefined, data: SlideData, index: number }} gettingData\r\n * @prop {undefined} beforeResize\r\n * @prop {undefined} resize\r\n * @prop {undefined} viewportSize\r\n * @prop {undefined} updateScrollOffset\r\n * @prop {{ slide: Slide }} slideInit\r\n * @prop {{ slide: Slide }} afterSetContent\r\n * @prop {{ slide: Slide }} slideLoad\r\n * @prop {{ slide: Slide }} appendHeavy can be default prevented\r\n * @prop {{ slide: Slide }} appendHeavyContent\r\n * @prop {{ slide: Slide }} slideActivate\r\n * @prop {{ slide: Slide }} slideDeactivate\r\n * @prop {{ slide: Slide }} slideDestroy\r\n * @prop {{ destZoomLevel: number, centerPoint: Point | undefined, transitionDuration: number | false | undefined }} beforeZoomTo\r\n * @prop {{ slide: Slide }} zoomPanUpdate\r\n * @prop {{ slide: Slide }} initialZoomPan\r\n * @prop {{ slide: Slide }} calcSlideSize\r\n * @prop {undefined} resolutionChanged\r\n * @prop {{ originalEvent: WheelEvent }} wheel can be default prevented\r\n * @prop {{ content: Content }} contentAppendImage can be default prevented\r\n * @prop {{ index: number; itemData: SlideData }} lazyLoadSlide can be default prevented\r\n * @prop {undefined} lazyLoad\r\n * @prop {{ slide: Slide }} calcBounds\r\n * @prop {{ zoomLevels: ZoomLevel, slideData: SlideData }} zoomLevelsUpdate\r\n *\r\n *\r\n * legacy\r\n *\r\n * @prop {undefined} init\r\n * @prop {undefined} initialZoomIn\r\n * @prop {undefined} initialZoomOut\r\n * @prop {undefined} initialZoomInEnd\r\n * @prop {undefined} initialZoomOutEnd\r\n * @prop {{ dataSource: DataSource | undefined, numItems: number }} numItems\r\n * @prop {{ itemData: SlideData; index: number }} itemData\r\n * @prop {{ index: number, itemData: SlideData, instance: PhotoSwipe }} thumbBounds\r\n */\r\n\r\n/**\r\n * @typedef {Object} PhotoSwipeFiltersMap https://photoswipe.com/filters/\r\n *\r\n * @prop {(numItems: number, dataSource: DataSource | undefined) => number} numItems\r\n * Modify the total amount of slides. Example on Data sources page.\r\n * https://photoswipe.com/filters/#numitems\r\n *\r\n * @prop {(itemData: SlideData, index: number) => SlideData} itemData\r\n * Modify slide item data. Example on Data sources page.\r\n * https://photoswipe.com/filters/#itemdata\r\n *\r\n * @prop {(itemData: SlideData, element: HTMLElement, linkEl: HTMLAnchorElement) => SlideData} domItemData\r\n * Modify item data when it's parsed from DOM element. Example on Data sources page.\r\n * https://photoswipe.com/filters/#domitemdata\r\n *\r\n * @prop {(clickedIndex: number, e: MouseEvent, instance: PhotoSwipeLightbox) => number} clickedIndex\r\n * Modify clicked gallery item index.\r\n * https://photoswipe.com/filters/#clickedindex\r\n *\r\n * @prop {(placeholderSrc: string | false, content: Content) => string | false} placeholderSrc\r\n * Modify placeholder image source.\r\n * https://photoswipe.com/filters/#placeholdersrc\r\n *\r\n * @prop {(isContentLoading: boolean, content: Content) => boolean} isContentLoading\r\n * Modify if the content is currently loading.\r\n * https://photoswipe.com/filters/#iscontentloading\r\n *\r\n * @prop {(isContentZoomable: boolean, content: Content) => boolean} isContentZoomable\r\n * Modify if the content can be zoomed.\r\n * https://photoswipe.com/filters/#iscontentzoomable\r\n *\r\n * @prop {(useContentPlaceholder: boolean, content: Content) => boolean} useContentPlaceholder\r\n * Modify if the placeholder should be used for the content.\r\n * https://photoswipe.com/filters/#usecontentplaceholder\r\n *\r\n * @prop {(isKeepingPlaceholder: boolean, content: Content) => boolean} isKeepingPlaceholder\r\n * Modify if the placeholder should be kept after the content is loaded.\r\n * https://photoswipe.com/filters/#iskeepingplaceholder\r\n *\r\n *\r\n * @prop {(contentErrorElement: HTMLElement, content: Content) => HTMLElement} contentErrorElement\r\n * Modify an element when the content has error state (for example, if image cannot be loaded).\r\n * https://photoswipe.com/filters/#contenterrorelement\r\n *\r\n * @prop {(element: HTMLElement, data: UIElementData) => HTMLElement} uiElement\r\n * Modify a UI element that's being created.\r\n * https://photoswipe.com/filters/#uielement\r\n *\r\n * @prop {(thumbnail: HTMLElement | null | undefined, itemData: SlideData, index: number) => HTMLElement} thumbEl\r\n * Modify the thumbnail element from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbel\r\n *\r\n * @prop {(thumbBounds: Bounds | undefined, itemData: SlideData, index: number) => Bounds} thumbBounds\r\n * Modify the thumbnail bounds from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbbounds\r\n *\r\n * @prop {(srcsetSizesWidth: number, content: Content) => number} srcsetSizesWidth\r\n *\r\n * @prop {(preventPointerEvent: boolean, event: PointerEvent, pointerType: string) => boolean} preventPointerEvent\r\n *\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeFiltersMap} T\r\n * @typedef {{ fn: PhotoSwipeFiltersMap[T], priority: number }} Filter\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {PhotoSwipeEventsMap[T] extends undefined ? PhotoSwipeEvent<T> : PhotoSwipeEvent<T> & PhotoSwipeEventsMap[T]} AugmentedEvent\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {(event: AugmentedEvent<T>) => void} EventCallback\r\n */\r\n\r\n/**\r\n * Base PhotoSwipe event object\r\n *\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n */\r\nclass PhotoSwipeEvent {\r\n  /**\r\n   * @param {T} type\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   */\r\n  constructor(type, details) {\r\n    this.type = type;\r\n    this.defaultPrevented = false;\r\n    if (details) {\r\n      Object.assign(this, details);\r\n    }\r\n  }\r\n\r\n  preventDefault() {\r\n    this.defaultPrevented = true;\r\n  }\r\n}\r\n\r\n/**\r\n * PhotoSwipe base class that can listen and dispatch for events.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox, extended by base.js\r\n */\r\nclass Eventable {\r\n  constructor() {\r\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeEventsMap]?: ((event: AugmentedEvent<T>) => void)[] }}\r\n     */\r\n    this._listeners = {};\r\n\r\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeFiltersMap]?: Filter<T>[] }}\r\n     */\r\n    this._filters = {};\r\n\r\n    /** @type {PhotoSwipe | undefined} */\r\n    this.pswp = undefined;\r\n\r\n    /** @type {PhotoSwipeOptions | undefined} */\r\n    this.options = undefined;\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   * @param {number} priority\r\n   */\r\n  addFilter(name, fn, priority = 100) {\r\n    if (!this._filters[name]) {\r\n      this._filters[name] = [];\r\n    }\r\n\r\n    this._filters[name]?.push({ fn, priority });\r\n    this._filters[name]?.sort((f1, f2) => f1.priority - f2.priority);\r\n\r\n    this.pswp?.addFilter(name, fn, priority);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   */\r\n  removeFilter(name, fn) {\r\n    if (this._filters[name]) {\r\n      // @ts-expect-error\r\n      this._filters[name] = this._filters[name].filter(filter => (filter.fn !== fn));\r\n    }\r\n\r\n    if (this.pswp) {\r\n      this.pswp.removeFilter(name, fn);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {Parameters<PhotoSwipeFiltersMap[T]>} args\r\n   * @returns {Parameters<PhotoSwipeFiltersMap[T]>[0]}\r\n   */\r\n  applyFilters(name, ...args) {\r\n    this._filters[name]?.forEach((filter) => {\r\n      // @ts-expect-error\r\n      args[0] = filter.fn.apply(this, args);\r\n    });\r\n    return args[0];\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\r\n  on(name, fn) {\r\n    if (!this._listeners[name]) {\r\n      this._listeners[name] = [];\r\n    }\r\n    this._listeners[name]?.push(fn);\r\n\r\n    // When binding events to lightbox,\r\n    // also bind events to PhotoSwipe Core,\r\n    // if it's open.\r\n    this.pswp?.on(name, fn);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\r\n  off(name, fn) {\r\n    if (this._listeners[name]) {\r\n      // @ts-expect-error\r\n      this._listeners[name] = this._listeners[name].filter(listener => (fn !== listener));\r\n    }\r\n\r\n    this.pswp?.off(name, fn);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   * @returns {AugmentedEvent<T>}\r\n   */\r\n  dispatch(name, details) {\r\n    if (this.pswp) {\r\n      return this.pswp.dispatch(name, details);\r\n    }\r\n\r\n    const event = /** @type {AugmentedEvent<T>} */ (new PhotoSwipeEvent(name, details));\r\n\r\n    this._listeners[name]?.forEach((listener) => {\r\n      listener.call(this, event);\r\n    });\r\n\r\n    return event;\r\n  }\r\n}\r\n\r\nexport default Eventable;\r\n", "import { createElement, setWidthHeight, toTransformString } from '../util/util.js';\r\n\r\nclass Placeholder {\r\n  /**\r\n   * @param {string | false} imageSrc\r\n   * @param {HTMLElement} container\r\n   */\r\n  constructor(imageSrc, container) {\r\n    // Create placeholder\r\n    // (stretched thumbnail or simple div behind the main image)\r\n    /** @type {HTMLImageElement | HTMLDivElement | null} */\r\n    this.element = createElement(\r\n      'pswp__img pswp__img--placeholder',\r\n      imageSrc ? 'img' : 'div',\r\n      container\r\n    );\r\n\r\n    if (imageSrc) {\r\n      const imgEl = /** @type {HTMLImageElement} */ (this.element);\r\n      imgEl.decoding = 'async';\r\n      imgEl.alt = '';\r\n      imgEl.src = imageSrc;\r\n      imgEl.setAttribute('role', 'presentation');\r\n    }\r\n\r\n    this.element.setAttribute('aria-hidden', 'true');\r\n  }\r\n\r\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\r\n  setDisplayedSize(width, height) {\r\n    if (!this.element) {\r\n      return;\r\n    }\r\n\r\n    if (this.element.tagName === 'IMG') {\r\n      // Use transform scale() to modify img placeholder size\r\n      // (instead of changing width/height directly).\r\n      // This helps with performance, specifically in iOS15 Safari.\r\n      setWidthHeight(this.element, 250, 'auto');\r\n      this.element.style.transformOrigin = '0 0';\r\n      this.element.style.transform = toTransformString(0, 0, width / 250);\r\n    } else {\r\n      setWidthHeight(this.element, width, height);\r\n    }\r\n  }\r\n\r\n  destroy() {\r\n    if (this.element?.parentNode) {\r\n      this.element.remove();\r\n    }\r\n    this.element = null;\r\n  }\r\n}\r\n\r\nexport default Placeholder;\r\n", "import { createElement, is<PERSON><PERSON><PERSON>, <PERSON>OAD_STATE, setWidthHeight } from '../util/util.js';\r\nimport Placeholder from './placeholder.js';\r\n\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../util/util.js').LoadState} LoadState */\r\n\r\nclass Content {\r\n  /**\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n   * @param {number} index\r\n   */\r\n  constructor(itemData, instance, index) {\r\n    this.instance = instance;\r\n    this.data = itemData;\r\n    this.index = index;\r\n\r\n    /** @type {HTMLImageElement | HTMLDivElement | undefined} */\r\n    this.element = undefined;\r\n    /** @type {Placeholder | undefined} */\r\n    this.placeholder = undefined;\r\n    /** @type {Slide | undefined} */\r\n    this.slide = undefined;\r\n\r\n    this.displayedImageWidth = 0;\r\n    this.displayedImageHeight = 0;\r\n\r\n    this.width = Number(this.data.w) || Number(this.data.width) || 0;\r\n    this.height = Number(this.data.h) || Number(this.data.height) || 0;\r\n\r\n    this.isAttached = false;\r\n    this.hasSlide = false;\r\n    this.isDecoding = false;\r\n    /** @type {LoadState} */\r\n    this.state = LOAD_STATE.IDLE;\r\n\r\n    if (this.data.type) {\r\n      this.type = this.data.type;\r\n    } else if (this.data.src) {\r\n      this.type = 'image';\r\n    } else {\r\n      this.type = 'html';\r\n    }\r\n\r\n    this.instance.dispatch('contentInit', { content: this });\r\n  }\r\n\r\n  removePlaceholder() {\r\n    if (this.placeholder && !this.keepPlaceholder()) {\r\n      // With delay, as image might be loaded, but not rendered\r\n      setTimeout(() => {\r\n        if (this.placeholder) {\r\n          this.placeholder.destroy();\r\n          this.placeholder = undefined;\r\n        }\r\n      }, 1000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Preload content\r\n   *\r\n   * @param {boolean} isLazy\r\n   * @param {boolean} [reload]\r\n   */\r\n  load(isLazy, reload) {\r\n    if (this.slide && this.usePlaceholder()) {\r\n      if (!this.placeholder) {\r\n        const placeholderSrc = this.instance.applyFilters(\r\n          'placeholderSrc',\r\n          // use  image-based placeholder only for the first slide,\r\n          // as rendering (even small stretched thumbnail) is an expensive operation\r\n          (this.data.msrc && this.slide.isFirstSlide) ? this.data.msrc : false,\r\n          this\r\n        );\r\n        this.placeholder = new Placeholder(\r\n          placeholderSrc,\r\n          this.slide.container\r\n        );\r\n      } else {\r\n        const placeholderEl = this.placeholder.element;\r\n        // Add placeholder to DOM if it was already created\r\n        if (placeholderEl && !placeholderEl.parentElement) {\r\n          this.slide.container.prepend(placeholderEl);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.element && !reload) {\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentLoad', { content: this, isLazy }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this.isImageContent()) {\r\n      this.element = createElement('pswp__img', 'img');\r\n      // Start loading only after width is defined, as sizes might depend on it.\r\n      // Due to Safari feature, we must define sizes before srcset.\r\n      if (this.displayedImageWidth) {\r\n        this.loadImage(isLazy);\r\n      }\r\n    } else {\r\n      this.element = createElement('pswp__content', 'div');\r\n      this.element.innerHTML = this.data.html || '';\r\n    }\r\n\r\n    if (reload && this.slide) {\r\n      this.slide.updateContentSize(true);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Preload image\r\n   *\r\n   * @param {boolean} isLazy\r\n   */\r\n  loadImage(isLazy) {\r\n    if (!this.isImageContent()\r\n      || !this.element\r\n      || this.instance.dispatch('contentLoadImage', { content: this, isLazy }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const imageElement = /** @type HTMLImageElement */ (this.element);\r\n\r\n    this.updateSrcsetSizes();\r\n\r\n    if (this.data.srcset) {\r\n      imageElement.srcset = this.data.srcset;\r\n    }\r\n\r\n    imageElement.src = this.data.src ?? '';\r\n    imageElement.alt = this.data.alt ?? '';\r\n\r\n    this.state = LOAD_STATE.LOADING;\r\n\r\n    if (imageElement.complete) {\r\n      this.onLoaded();\r\n    } else {\r\n      imageElement.onload = () => {\r\n        this.onLoaded();\r\n      };\r\n\r\n      imageElement.onerror = () => {\r\n        this.onError();\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assign slide to content\r\n   *\r\n   * @param {Slide} slide\r\n   */\r\n  setSlide(slide) {\r\n    this.slide = slide;\r\n    this.hasSlide = true;\r\n    this.instance = slide.pswp;\r\n\r\n    // todo: do we need to unset slide?\r\n  }\r\n\r\n  /**\r\n   * Content load success handler\r\n   */\r\n  onLoaded() {\r\n    this.state = LOAD_STATE.LOADED;\r\n\r\n    if (this.slide && this.element) {\r\n      this.instance.dispatch('loadComplete', { slide: this.slide, content: this });\r\n\r\n      // if content is reloaded\r\n      if (this.slide.isActive\r\n          && this.slide.heavyAppended\r\n          && !this.element.parentNode) {\r\n        this.append();\r\n        this.slide.updateContentSize(true);\r\n      }\r\n\r\n      if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\r\n        this.removePlaceholder();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Content load error handler\r\n   */\r\n  onError() {\r\n    this.state = LOAD_STATE.ERROR;\r\n\r\n    if (this.slide) {\r\n      this.displayError();\r\n      this.instance.dispatch('loadComplete', { slide: this.slide, isError: true, content: this });\r\n      this.instance.dispatch('loadError', { slide: this.slide, content: this });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {Boolean} If the content is currently loading\r\n   */\r\n  isLoading() {\r\n    return this.instance.applyFilters(\r\n      'isContentLoading',\r\n      this.state === LOAD_STATE.LOADING,\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * @returns {Boolean} If the content is in error state\r\n   */\r\n  isError() {\r\n    return this.state === LOAD_STATE.ERROR;\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If the content is image\r\n   */\r\n  isImageContent() {\r\n    return this.type === 'image';\r\n  }\r\n\r\n  /**\r\n   * Update content size\r\n   *\r\n   * @param {Number} width\r\n   * @param {Number} height\r\n   */\r\n  setDisplayedSize(width, height) {\r\n    if (!this.element) {\r\n      return;\r\n    }\r\n\r\n    if (this.placeholder) {\r\n      this.placeholder.setDisplayedSize(width, height);\r\n    }\r\n\r\n    if (this.instance.dispatch(\r\n      'contentResize',\r\n      { content: this, width, height }).defaultPrevented\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    setWidthHeight(this.element, width, height);\r\n\r\n    if (this.isImageContent() && !this.isError()) {\r\n      const isInitialSizeUpdate = (!this.displayedImageWidth && width);\r\n\r\n      this.displayedImageWidth = width;\r\n      this.displayedImageHeight = height;\r\n\r\n      if (isInitialSizeUpdate) {\r\n        this.loadImage(false);\r\n      } else {\r\n        this.updateSrcsetSizes();\r\n      }\r\n\r\n      if (this.slide) {\r\n        this.instance.dispatch(\r\n          'imageSizeChange',\r\n          { slide: this.slide, width, height, content: this }\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If the content can be zoomed\r\n   */\r\n  isZoomable() {\r\n    return this.instance.applyFilters(\r\n      'isContentZoomable',\r\n      this.isImageContent() && (this.state !== LOAD_STATE.ERROR),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update image srcset sizes attribute based on width and height\r\n   */\r\n  updateSrcsetSizes() {\r\n    // Handle srcset sizes attribute.\r\n    //\r\n    // Never lower quality, if it was increased previously.\r\n    // Chrome does this automatically, Firefox and Safari do not,\r\n    // so we store largest used size in dataset.\r\n    if (!this.isImageContent() || !this.element || !this.data.srcset) {\r\n      return;\r\n    }\r\n\r\n    const image = /** @type HTMLImageElement */ (this.element);\r\n    const sizesWidth = this.instance.applyFilters(\r\n      'srcsetSizesWidth',\r\n      this.displayedImageWidth,\r\n      this\r\n    );\r\n\r\n    if (\r\n      !image.dataset.largestUsedSize\r\n      || sizesWidth > parseInt(image.dataset.largestUsedSize, 10)\r\n    ) {\r\n      image.sizes = sizesWidth + 'px';\r\n      image.dataset.largestUsedSize = String(sizesWidth);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If content should use a placeholder (from msrc by default)\r\n   */\r\n  usePlaceholder() {\r\n    return this.instance.applyFilters(\r\n      'useContentPlaceholder',\r\n      this.isImageContent(),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Preload content with lazy-loading param\r\n   */\r\n  lazyLoad() {\r\n    if (this.instance.dispatch('contentLazyLoad', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.load(true);\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If placeholder should be kept after content is loaded\r\n   */\r\n  keepPlaceholder() {\r\n    return this.instance.applyFilters(\r\n      'isKeepingPlaceholder',\r\n      this.isLoading(),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Destroy the content\r\n   */\r\n  destroy() {\r\n    this.hasSlide = false;\r\n    this.slide = undefined;\r\n\r\n    if (this.instance.dispatch('contentDestroy', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.remove();\r\n\r\n    if (this.placeholder) {\r\n      this.placeholder.destroy();\r\n      this.placeholder = undefined;\r\n    }\r\n\r\n    if (this.isImageContent() && this.element) {\r\n      this.element.onload = null;\r\n      this.element.onerror = null;\r\n      this.element = undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Display error message\r\n   */\r\n  displayError() {\r\n    if (this.slide) {\r\n      let errorMsgEl = createElement('pswp__error-msg', 'div');\r\n      errorMsgEl.innerText = this.instance.options?.errorMsg ?? '';\r\n      errorMsgEl = /** @type {HTMLDivElement} */ (this.instance.applyFilters(\r\n        'contentErrorElement',\r\n        errorMsgEl,\r\n        this\r\n      ));\r\n      this.element = createElement('pswp__content pswp__error-msg-container', 'div');\r\n      this.element.appendChild(errorMsgEl);\r\n      this.slide.container.innerText = '';\r\n      this.slide.container.appendChild(this.element);\r\n      this.slide.updateContentSize(true);\r\n      this.removePlaceholder();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Append the content\r\n   */\r\n  append() {\r\n    if (this.isAttached || !this.element) {\r\n      return;\r\n    }\r\n\r\n    this.isAttached = true;\r\n\r\n    if (this.state === LOAD_STATE.ERROR) {\r\n      this.displayError();\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentAppend', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const supportsDecode = ('decode' in this.element);\r\n\r\n    if (this.isImageContent()) {\r\n      // Use decode() on nearby slides\r\n      //\r\n      // Nearby slide images are in DOM and not hidden via display:none.\r\n      // However, they are placed offscreen (to the left and right side).\r\n      //\r\n      // Some browsers do not composite the image until it's actually visible,\r\n      // using decode() helps.\r\n      //\r\n      // You might ask \"why dont you just decode() and then append all images\",\r\n      // that's because I want to show image before it's fully loaded,\r\n      // as browser can render parts of image while it is loading.\r\n      // We do not do this in Safari due to partial loading bug.\r\n      if (supportsDecode && this.slide && (!this.slide.isActive || isSafari())) {\r\n        this.isDecoding = true;\r\n        // purposefully using finally instead of then,\r\n        // as if srcset sizes changes dynamically - it may cause decode error\r\n        /** @type {HTMLImageElement} */\r\n        (this.element).decode().catch(() => {}).finally(() => {\r\n          this.isDecoding = false;\r\n          this.appendImage();\r\n        });\r\n      } else {\r\n        this.appendImage();\r\n      }\r\n    } else if (this.slide && !this.element.parentNode) {\r\n      this.slide.container.appendChild(this.element);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Activate the slide,\r\n   * active slide is generally the current one,\r\n   * meaning the user can see it.\r\n   */\r\n  activate() {\r\n    if (this.instance.dispatch('contentActivate', { content: this }).defaultPrevented\r\n      || !this.slide) {\r\n      return;\r\n    }\r\n\r\n    if (this.isImageContent() && this.isDecoding && !isSafari()) {\r\n      // add image to slide when it becomes active,\r\n      // even if it's not finished decoding\r\n      this.appendImage();\r\n    } else if (this.isError()) {\r\n      this.load(false, true); // try to reload\r\n    }\r\n\r\n    if (this.slide.holderElement) {\r\n      this.slide.holderElement.setAttribute('aria-hidden', 'false');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deactivate the content\r\n   */\r\n  deactivate() {\r\n    this.instance.dispatch('contentDeactivate', { content: this });\r\n    if (this.slide && this.slide.holderElement) {\r\n      this.slide.holderElement.setAttribute('aria-hidden', 'true');\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Remove the content from DOM\r\n   */\r\n  remove() {\r\n    this.isAttached = false;\r\n\r\n    if (this.instance.dispatch('contentRemove', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this.element && this.element.parentNode) {\r\n      this.element.remove();\r\n    }\r\n\r\n    if (this.placeholder && this.placeholder.element) {\r\n      this.placeholder.element.remove();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Append the image content to slide container\r\n   */\r\n  appendImage() {\r\n    if (!this.isAttached) {\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentAppendImage', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    // ensure that element exists and is not already appended\r\n    if (this.slide && this.element && !this.element.parentNode) {\r\n      this.slide.container.appendChild(this.element);\r\n    }\r\n\r\n    if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\r\n      this.removePlaceholder();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Content;\r\n", "import { getViewportSize, getPanAreaSize } from '../util/viewport-size.js';\r\nimport ZoomLevel from './zoom-level.js';\r\n\r\n/** @typedef {import('./content.js').default} Content */\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\nconst MIN_SLIDES_TO_CACHE = 5;\r\n\r\n/**\r\n * Lazy-load an image\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n * @param {number} index\r\n * @returns {Content} Image that is being decoded or false.\r\n */\r\nexport function lazyLoadData(itemData, instance, index) {\r\n  const content = instance.createContentFromData(itemData, index);\r\n  /** @type {ZoomLevel | undefined} */\r\n  let zoomLevel;\r\n\r\n  const { options } = instance;\r\n\r\n  // We need to know dimensions of the image to preload it,\r\n  // as it might use srcset, and we need to define sizes\r\n  if (options) {\r\n    zoomLevel = new ZoomLevel(options, itemData, -1);\r\n\r\n    let viewportSize;\r\n    if (instance.pswp) {\r\n      viewportSize = instance.pswp.viewportSize;\r\n    } else {\r\n      viewportSize = getViewportSize(options, instance);\r\n    }\r\n\r\n    const panAreaSize = getPanAreaSize(options, viewportSize, itemData, index);\r\n    zoomLevel.update(content.width, content.height, panAreaSize);\r\n  }\r\n\r\n  content.lazyLoad();\r\n\r\n  if (zoomLevel) {\r\n    content.setDisplayedSize(\r\n      Math.ceil(content.width * zoomLevel.initial),\r\n      Math.ceil(content.height * zoomLevel.initial)\r\n    );\r\n  }\r\n\r\n  return content;\r\n}\r\n\r\n\r\n/**\r\n * Lazy-loads specific slide.\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * By default, it loads image based on viewport size and initial zoom level.\r\n *\r\n * @param {number} index Slide index\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox eventable instance\r\n * @returns {Content | undefined}\r\n */\r\nexport function lazyLoadSlide(index, instance) {\r\n  const itemData = instance.getItemData(index);\r\n\r\n  if (instance.dispatch('lazyLoadSlide', { index, itemData }).defaultPrevented) {\r\n    return;\r\n  }\r\n\r\n  return lazyLoadData(itemData, instance, index);\r\n}\r\n\r\nclass ContentLoader {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    // Total amount of cached images\r\n    this.limit = Math.max(\r\n      pswp.options.preload[0] + pswp.options.preload[1] + 1,\r\n      MIN_SLIDES_TO_CACHE\r\n    );\r\n    /** @type {Content[]} */\r\n    this._cachedItems = [];\r\n  }\r\n\r\n  /**\r\n   * Lazy load nearby slides based on `preload` option.\r\n   *\r\n   * @param {number} [diff] Difference between slide indexes that was changed recently, or 0.\r\n   */\r\n  updateLazy(diff) {\r\n    const { pswp } = this;\r\n\r\n    if (pswp.dispatch('lazyLoad').defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const { preload } = pswp.options;\r\n    const isForward = diff === undefined ? true : (diff >= 0);\r\n    let i;\r\n\r\n    // preload[1] - num items to preload in forward direction\r\n    for (i = 0; i <= preload[1]; i++) {\r\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? i : (-i)));\r\n    }\r\n\r\n    // preload[0] - num items to preload in backward direction\r\n    for (i = 1; i <= preload[0]; i++) {\r\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? (-i) : i));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} initialIndex\r\n   */\r\n  loadSlideByIndex(initialIndex) {\r\n    const index = this.pswp.getLoopedIndex(initialIndex);\r\n    // try to get cached content\r\n    let content = this.getContentByIndex(index);\r\n    if (!content) {\r\n      // no cached content, so try to load from scratch:\r\n      content = lazyLoadSlide(index, this.pswp);\r\n      // if content can be loaded, add it to cache:\r\n      if (content) {\r\n        this.addToCache(content);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Slide} slide\r\n   * @returns {Content}\r\n   */\r\n  getContentBySlide(slide) {\r\n    let content = this.getContentByIndex(slide.index);\r\n    if (!content) {\r\n      // create content if not found in cache\r\n      content = this.pswp.createContentFromData(slide.data, slide.index);\r\n      this.addToCache(content);\r\n    }\r\n\r\n    // assign slide to content\r\n    content.setSlide(slide);\r\n\r\n    return content;\r\n  }\r\n\r\n  /**\r\n   * @param {Content} content\r\n   */\r\n  addToCache(content) {\r\n    // move to the end of array\r\n    this.removeByIndex(content.index);\r\n    this._cachedItems.push(content);\r\n\r\n    if (this._cachedItems.length > this.limit) {\r\n      // Destroy the first content that's not attached\r\n      const indexToRemove = this._cachedItems.findIndex((item) => {\r\n        return !item.isAttached && !item.hasSlide;\r\n      });\r\n      if (indexToRemove !== -1) {\r\n        const removedItem = this._cachedItems.splice(indexToRemove, 1)[0];\r\n        removedItem.destroy();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes an image from cache, does not destroy() it, just removes.\r\n   *\r\n   * @param {number} index\r\n   */\r\n  removeByIndex(index) {\r\n    const indexToRemove = this._cachedItems.findIndex(item => item.index === index);\r\n    if (indexToRemove !== -1) {\r\n      this._cachedItems.splice(indexToRemove, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} index\r\n   * @returns {Content | undefined}\r\n   */\r\n  getContentByIndex(index) {\r\n    return this._cachedItems.find(content => content.index === index);\r\n  }\r\n\r\n  destroy() {\r\n    this._cachedItems.forEach(content => content.destroy());\r\n    this._cachedItems = [];\r\n  }\r\n}\r\n\r\nexport default ContentLoader;\r\n", "import Eventable from './eventable.js';\r\nimport { getElementsFromOption } from '../util/util.js';\r\nimport Content from '../slide/content.js';\r\nimport { lazyLoadData } from '../slide/loader.js';\r\n\r\n/** @typedef {import(\"../photoswipe.js\").default} PhotoSwipe */\r\n/** @typedef {import(\"../slide/slide.js\").SlideData} SlideData */\r\n\r\n/**\r\n * PhotoSwipe base class that can retrieve data about every slide.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox\r\n */\r\nclass PhotoSwipeBase extends Eventable {\r\n  /**\r\n   * Get total number of slides\r\n   *\r\n   * @returns {number}\r\n   */\r\n  getNumItems() {\r\n    let numItems = 0;\r\n    const dataSource = this.options?.dataSource;\r\n\r\n    if (dataSource && 'length' in dataSource) {\r\n      // may be an array or just object with length property\r\n      numItems = dataSource.length;\r\n    } else if (dataSource && 'gallery' in dataSource) {\r\n      // query DOM elements\r\n      if (!dataSource.items) {\r\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\r\n      }\r\n\r\n      if (dataSource.items) {\r\n        numItems = dataSource.items.length;\r\n      }\r\n    }\r\n\r\n    // legacy event, before filters were introduced\r\n    const event = this.dispatch('numItems', {\r\n      dataSource,\r\n      numItems\r\n    });\r\n    return this.applyFilters('numItems', event.numItems, dataSource);\r\n  }\r\n\r\n  /**\r\n   * @param {SlideData} slideData\r\n   * @param {number} index\r\n   * @returns {Content}\r\n   */\r\n  createContentFromData(slideData, index) {\r\n    return new Content(slideData, this, index);\r\n  }\r\n\r\n  /**\r\n   * Get item data by index.\r\n   *\r\n   * \"item data\" should contain normalized information that PhotoSwipe needs to generate a slide.\r\n   * For example, it may contain properties like\r\n   * `src`, `srcset`, `w`, `h`, which will be used to generate a slide with image.\r\n   *\r\n   * @param {number} index\r\n   * @returns {SlideData}\r\n   */\r\n  getItemData(index) {\r\n    const dataSource = this.options?.dataSource;\r\n    /** @type {SlideData | HTMLElement} */\r\n    let dataSourceItem = {};\r\n    if (Array.isArray(dataSource)) {\r\n      // Datasource is an array of elements\r\n      dataSourceItem = dataSource[index];\r\n    } else if (dataSource && 'gallery' in dataSource) {\r\n      // dataSource has gallery property,\r\n      // thus it was created by Lightbox, based on\r\n      // gallery and children options\r\n\r\n      // query DOM elements\r\n      if (!dataSource.items) {\r\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\r\n      }\r\n\r\n      dataSourceItem = dataSource.items[index];\r\n    }\r\n\r\n    let itemData = dataSourceItem;\r\n\r\n    if (itemData instanceof Element) {\r\n      itemData = this._domElementToItemData(itemData);\r\n    }\r\n\r\n    // Dispatching the itemData event,\r\n    // it's a legacy verion before filters were introduced\r\n    const event = this.dispatch('itemData', {\r\n      itemData: itemData || {},\r\n      index\r\n    });\r\n\r\n    return this.applyFilters('itemData', event.itemData, index);\r\n  }\r\n\r\n  /**\r\n   * Get array of gallery DOM elements,\r\n   * based on childSelector and gallery element.\r\n   *\r\n   * @param {HTMLElement} galleryElement\r\n   * @returns {HTMLElement[]}\r\n   */\r\n  _getGalleryDOMElements(galleryElement) {\r\n    if (this.options?.children || this.options?.childSelector) {\r\n      return getElementsFromOption(\r\n        this.options.children,\r\n        this.options.childSelector,\r\n        galleryElement\r\n      ) || [];\r\n    }\r\n\r\n    return [galleryElement];\r\n  }\r\n\r\n  /**\r\n   * Converts DOM element to item data object.\r\n   *\r\n   * @param {HTMLElement} element DOM element\r\n   * @returns {SlideData}\r\n   */\r\n  _domElementToItemData(element) {\r\n    /** @type {SlideData} */\r\n    const itemData = {\r\n      element\r\n    };\r\n\r\n    const linkEl = /** @type {HTMLAnchorElement} */ (\r\n      element.tagName === 'A'\r\n        ? element\r\n        : element.querySelector('a')\r\n    );\r\n\r\n    if (linkEl) {\r\n      // src comes from data-pswp-src attribute,\r\n      // if it's empty link href is used\r\n      itemData.src = linkEl.dataset.pswpSrc || linkEl.href;\r\n\r\n      if (linkEl.dataset.pswpSrcset) {\r\n        itemData.srcset = linkEl.dataset.pswpSrcset;\r\n      }\r\n\r\n      itemData.width = linkEl.dataset.pswpWidth ? parseInt(linkEl.dataset.pswpWidth, 10) : 0;\r\n      itemData.height = linkEl.dataset.pswpHeight ? parseInt(linkEl.dataset.pswpHeight, 10) : 0;\r\n\r\n      // support legacy w & h properties\r\n      itemData.w = itemData.width;\r\n      itemData.h = itemData.height;\r\n\r\n      if (linkEl.dataset.pswpType) {\r\n        itemData.type = linkEl.dataset.pswpType;\r\n      }\r\n\r\n      const thumbnailEl = element.querySelector('img');\r\n\r\n      if (thumbnailEl) {\r\n        // msrc is URL to placeholder image that's displayed before large image is loaded\r\n        // by default it's displayed only for the first slide\r\n        itemData.msrc = thumbnailEl.currentSrc || thumbnailEl.src;\r\n        itemData.alt = thumbnailEl.getAttribute('alt') ?? '';\r\n      }\r\n\r\n      if (linkEl.dataset.pswpCropped || linkEl.dataset.cropped) {\r\n        itemData.thumbCropped = true;\r\n      }\r\n    }\r\n\r\n    return this.applyFilters('domItemData', itemData, element, linkEl);\r\n  }\r\n\r\n  /**\r\n   * Lazy-load by slide data\r\n   *\r\n   * @param {SlideData} itemData Data about the slide\r\n   * @param {number} index\r\n   * @returns {Content} Image that is being decoded or false.\r\n   */\r\n  lazyLoadData(itemData, index) {\r\n    return lazyLoadData(itemData, this, index);\r\n  }\r\n}\r\n\r\nexport default PhotoSwipeBase;\r\n", "import {\r\n  setTransform,\r\n  equalizePoints,\r\n  decodeImage,\r\n  toTransformString\r\n} from './util/util.js';\r\n\r\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('./slide/get-thumb-bounds.js').Bounds} Bounds */\r\n/** @typedef {import('./util/animations.js').AnimationProps} AnimationProps */\r\n\r\n// some browsers do not paint\r\n// elements which opacity is set to 0,\r\n// since we need to pre-render elements for the animation -\r\n// we set it to the minimum amount\r\nconst MIN_OPACITY = 0.003;\r\n\r\n/**\r\n * Manages opening and closing transitions of the PhotoSwipe.\r\n *\r\n * It can perform zoom, fade or no transition.\r\n */\r\nclass Opener {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    this.isClosed = true;\r\n    this.isOpen = false;\r\n    this.isClosing = false;\r\n    this.isOpening = false;\r\n    /**\r\n     * @private\r\n     * @type {number | false | undefined}\r\n     */\r\n    this._duration = undefined;\r\n    /** @private */\r\n    this._useAnimation = false;\r\n    /** @private */\r\n    this._croppedZoom = false;\r\n    /** @private */\r\n    this._animateRootOpacity = false;\r\n    /** @private */\r\n    this._animateBgOpacity = false;\r\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | HTMLImageElement | null | undefined }\r\n     */\r\n    this._placeholder = undefined;\r\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | undefined }\r\n     */\r\n    this._opacityElement = undefined;\r\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | undefined }\r\n     */\r\n    this._cropContainer1 = undefined;\r\n    /**\r\n     * @private\r\n     * @type { HTMLElement | null | undefined }\r\n     */\r\n    this._cropContainer2 = undefined;\r\n\r\n    /**\r\n     * @private\r\n     * @type {Bounds | undefined}\r\n     */\r\n    this._thumbBounds = undefined;\r\n\r\n\r\n    this._prepareOpen = this._prepareOpen.bind(this);\r\n\r\n    // Override initial zoom and pan position\r\n    pswp.on('firstZoomPan', this._prepareOpen);\r\n  }\r\n\r\n  open() {\r\n    this._prepareOpen();\r\n    this._start();\r\n  }\r\n\r\n  close() {\r\n    if (this.isClosed || this.isClosing || this.isOpening) {\r\n      // if we close during opening animation\r\n      // for now do nothing,\r\n      // browsers aren't good at changing the direction of the CSS transition\r\n      return;\r\n    }\r\n\r\n    const slide = this.pswp.currSlide;\r\n\r\n    this.isOpen = false;\r\n    this.isOpening = false;\r\n    this.isClosing = true;\r\n    this._duration = this.pswp.options.hideAnimationDuration;\r\n\r\n    if (slide && slide.currZoomLevel * slide.width >= this.pswp.options.maxWidthToAnimate) {\r\n      this._duration = 0;\r\n    }\r\n\r\n    this._applyStartProps();\r\n    setTimeout(() => {\r\n      this._start();\r\n    }, this._croppedZoom ? 30 : 0);\r\n  }\r\n\r\n  /** @private */\r\n  _prepareOpen() {\r\n    this.pswp.off('firstZoomPan', this._prepareOpen);\r\n    if (!this.isOpening) {\r\n      const slide = this.pswp.currSlide;\r\n      this.isOpening = true;\r\n      this.isClosing = false;\r\n      this._duration = this.pswp.options.showAnimationDuration;\r\n      if (slide && slide.zoomLevels.initial * slide.width >= this.pswp.options.maxWidthToAnimate) {\r\n        this._duration = 0;\r\n      }\r\n      this._applyStartProps();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _applyStartProps() {\r\n    const { pswp } = this;\r\n    const slide = this.pswp.currSlide;\r\n    const { options } = pswp;\r\n\r\n    if (options.showHideAnimationType === 'fade') {\r\n      options.showHideOpacity = true;\r\n      this._thumbBounds = undefined;\r\n    } else if (options.showHideAnimationType === 'none') {\r\n      options.showHideOpacity = false;\r\n      this._duration = 0;\r\n      this._thumbBounds = undefined;\r\n    } else if (this.isOpening && pswp._initialThumbBounds) {\r\n      // Use initial bounds if defined\r\n      this._thumbBounds = pswp._initialThumbBounds;\r\n    } else {\r\n      this._thumbBounds = this.pswp.getThumbBounds();\r\n    }\r\n\r\n    this._placeholder = slide?.getPlaceholderElement();\r\n\r\n    pswp.animations.stopAll();\r\n\r\n    // Discard animations when duration is less than 50ms\r\n    this._useAnimation = Boolean(this._duration && this._duration > 50);\r\n    this._animateZoom = Boolean(this._thumbBounds)\r\n                        && slide?.content.usePlaceholder()\r\n                        && (!this.isClosing || !pswp.mainScroll.isShifted());\r\n    if (!this._animateZoom) {\r\n      this._animateRootOpacity = true;\r\n\r\n      if (this.isOpening && slide) {\r\n        slide.zoomAndPanToInitial();\r\n        slide.applyCurrentZoomPan();\r\n      }\r\n    } else {\r\n      this._animateRootOpacity = options.showHideOpacity ?? false;\r\n    }\r\n    this._animateBgOpacity = !this._animateRootOpacity && this.pswp.options.bgOpacity > MIN_OPACITY;\r\n    this._opacityElement = this._animateRootOpacity ? pswp.element : pswp.bg;\r\n\r\n    if (!this._useAnimation) {\r\n      this._duration = 0;\r\n      this._animateZoom = false;\r\n      this._animateBgOpacity = false;\r\n      this._animateRootOpacity = true;\r\n      if (this.isOpening) {\r\n        if (pswp.element) {\r\n          pswp.element.style.opacity = String(MIN_OPACITY);\r\n        }\r\n        pswp.applyBgOpacity(1);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (this._animateZoom && this._thumbBounds && this._thumbBounds.innerRect) {\r\n      // Properties are used when animation from cropped thumbnail\r\n      this._croppedZoom = true;\r\n      this._cropContainer1 = this.pswp.container;\r\n      this._cropContainer2 = this.pswp.currSlide?.holderElement;\r\n\r\n      if (pswp.container) {\r\n        pswp.container.style.overflow = 'hidden';\r\n        pswp.container.style.width = pswp.viewportSize.x + 'px';\r\n      }\r\n    } else {\r\n      this._croppedZoom = false;\r\n    }\r\n\r\n    if (this.isOpening) {\r\n      // Apply styles before opening transition\r\n      if (this._animateRootOpacity) {\r\n        if (pswp.element) {\r\n          pswp.element.style.opacity = String(MIN_OPACITY);\r\n        }\r\n        pswp.applyBgOpacity(1);\r\n      } else {\r\n        if (this._animateBgOpacity && pswp.bg) {\r\n          pswp.bg.style.opacity = String(MIN_OPACITY);\r\n        }\r\n        if (pswp.element) {\r\n          pswp.element.style.opacity = '1';\r\n        }\r\n      }\r\n\r\n      if (this._animateZoom) {\r\n        this._setClosedStateZoomPan();\r\n        if (this._placeholder) {\r\n          // tell browser that we plan to animate the placeholder\r\n          this._placeholder.style.willChange = 'transform';\r\n\r\n          // hide placeholder to allow hiding of\r\n          // elements that overlap it (such as icons over the thumbnail)\r\n          this._placeholder.style.opacity = String(MIN_OPACITY);\r\n        }\r\n      }\r\n    } else if (this.isClosing) {\r\n      // hide nearby slides to make sure that\r\n      // they are not painted during the transition\r\n      if (pswp.mainScroll.itemHolders[0]) {\r\n        pswp.mainScroll.itemHolders[0].el.style.display = 'none';\r\n      }\r\n      if (pswp.mainScroll.itemHolders[2]) {\r\n        pswp.mainScroll.itemHolders[2].el.style.display = 'none';\r\n      }\r\n\r\n      if (this._croppedZoom) {\r\n        if (pswp.mainScroll.x !== 0) {\r\n          // shift the main scroller to zero position\r\n          pswp.mainScroll.resetPosition();\r\n          pswp.mainScroll.resize();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _start() {\r\n    if (this.isOpening\r\n        && this._useAnimation\r\n        && this._placeholder\r\n        && this._placeholder.tagName === 'IMG') {\r\n      // To ensure smooth animation\r\n      // we wait till the current slide image placeholder is decoded,\r\n      // but no longer than 250ms,\r\n      // and no shorter than 50ms\r\n      // (just using requestanimationframe is not enough in Firefox,\r\n      // for some reason)\r\n      new Promise((resolve) => {\r\n        let decoded = false;\r\n        let isDelaying = true;\r\n        decodeImage(/** @type {HTMLImageElement} */ (this._placeholder)).finally(() => {\r\n          decoded = true;\r\n          if (!isDelaying) {\r\n            resolve(true);\r\n          }\r\n        });\r\n        setTimeout(() => {\r\n          isDelaying = false;\r\n          if (decoded) {\r\n            resolve(true);\r\n          }\r\n        }, 50);\r\n        setTimeout(resolve, 250);\r\n      }).finally(() => this._initiate());\r\n    } else {\r\n      this._initiate();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _initiate() {\r\n    this.pswp.element?.style.setProperty('--pswp-transition-duration', this._duration + 'ms');\r\n\r\n    this.pswp.dispatch(\r\n      this.isOpening ? 'openingAnimationStart' : 'closingAnimationStart'\r\n    );\r\n\r\n    // legacy event\r\n    this.pswp.dispatch(\r\n      /** @type {'initialZoomIn' | 'initialZoomOut'} */\r\n      ('initialZoom' + (this.isOpening ? 'In' : 'Out'))\r\n    );\r\n\r\n    this.pswp.element?.classList.toggle('pswp--ui-visible', this.isOpening);\r\n\r\n    if (this.isOpening) {\r\n      if (this._placeholder) {\r\n        // unhide the placeholder\r\n        this._placeholder.style.opacity = '1';\r\n      }\r\n      this._animateToOpenState();\r\n    } else if (this.isClosing) {\r\n      this._animateToClosedState();\r\n    }\r\n\r\n    if (!this._useAnimation) {\r\n      this._onAnimationComplete();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _onAnimationComplete() {\r\n    const { pswp } = this;\r\n    this.isOpen = this.isOpening;\r\n    this.isClosed = this.isClosing;\r\n    this.isOpening = false;\r\n    this.isClosing = false;\r\n\r\n    pswp.dispatch(\r\n      this.isOpen ? 'openingAnimationEnd' : 'closingAnimationEnd'\r\n    );\r\n\r\n    // legacy event\r\n    pswp.dispatch(\r\n      /** @type {'initialZoomInEnd' | 'initialZoomOutEnd'} */\r\n      ('initialZoom' + (this.isOpen ? 'InEnd' : 'OutEnd'))\r\n    );\r\n\r\n    if (this.isClosed) {\r\n      pswp.destroy();\r\n    } else if (this.isOpen) {\r\n      if (this._animateZoom && pswp.container) {\r\n        pswp.container.style.overflow = 'visible';\r\n        pswp.container.style.width = '100%';\r\n      }\r\n      pswp.currSlide?.applyCurrentZoomPan();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _animateToOpenState() {\r\n    const { pswp } = this;\r\n    if (this._animateZoom) {\r\n      if (this._croppedZoom && this._cropContainer1 && this._cropContainer2) {\r\n        this._animateTo(this._cropContainer1, 'transform', 'translate3d(0,0,0)');\r\n        this._animateTo(this._cropContainer2, 'transform', 'none');\r\n      }\r\n\r\n      if (pswp.currSlide) {\r\n        pswp.currSlide.zoomAndPanToInitial();\r\n        this._animateTo(\r\n          pswp.currSlide.container,\r\n          'transform',\r\n          pswp.currSlide.getCurrentTransform()\r\n        );\r\n      }\r\n    }\r\n\r\n    if (this._animateBgOpacity && pswp.bg) {\r\n      this._animateTo(pswp.bg, 'opacity', String(pswp.options.bgOpacity));\r\n    }\r\n\r\n    if (this._animateRootOpacity && pswp.element) {\r\n      this._animateTo(pswp.element, 'opacity', '1');\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _animateToClosedState() {\r\n    const { pswp } = this;\r\n\r\n    if (this._animateZoom) {\r\n      this._setClosedStateZoomPan(true);\r\n    }\r\n\r\n    // do not animate opacity if it's already at 0\r\n    if (this._animateBgOpacity && pswp.bgOpacity > 0.01 && pswp.bg) {\r\n      this._animateTo(pswp.bg, 'opacity', '0');\r\n    }\r\n\r\n    if (this._animateRootOpacity && pswp.element) {\r\n      this._animateTo(pswp.element, 'opacity', '0');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {boolean} [animate]\r\n   */\r\n  _setClosedStateZoomPan(animate) {\r\n    if (!this._thumbBounds) return;\r\n\r\n    const { pswp } = this;\r\n    const { innerRect } = this._thumbBounds;\r\n    const { currSlide, viewportSize } = pswp;\r\n\r\n    if (this._croppedZoom && innerRect && this._cropContainer1 && this._cropContainer2) {\r\n      const containerOnePanX = -viewportSize.x + (this._thumbBounds.x - innerRect.x) + innerRect.w;\r\n      const containerOnePanY = -viewportSize.y + (this._thumbBounds.y - innerRect.y) + innerRect.h;\r\n      const containerTwoPanX = viewportSize.x - innerRect.w;\r\n      const containerTwoPanY = viewportSize.y - innerRect.h;\r\n\r\n\r\n      if (animate) {\r\n        this._animateTo(\r\n          this._cropContainer1,\r\n          'transform',\r\n          toTransformString(containerOnePanX, containerOnePanY)\r\n        );\r\n\r\n        this._animateTo(\r\n          this._cropContainer2,\r\n          'transform',\r\n          toTransformString(containerTwoPanX, containerTwoPanY)\r\n        );\r\n      } else {\r\n        setTransform(this._cropContainer1, containerOnePanX, containerOnePanY);\r\n        setTransform(this._cropContainer2, containerTwoPanX, containerTwoPanY);\r\n      }\r\n    }\r\n\r\n    if (currSlide) {\r\n      equalizePoints(currSlide.pan, innerRect || this._thumbBounds);\r\n      currSlide.currZoomLevel = this._thumbBounds.w / currSlide.width;\r\n      if (animate) {\r\n        this._animateTo(currSlide.container, 'transform', currSlide.getCurrentTransform());\r\n      } else {\r\n        currSlide.applyCurrentZoomPan();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {HTMLElement} target\r\n   * @param {'transform' | 'opacity'} prop\r\n   * @param {string} propValue\r\n   */\r\n  _animateTo(target, prop, propValue) {\r\n    if (!this._duration) {\r\n      target.style[prop] = propValue;\r\n      return;\r\n    }\r\n\r\n    const { animations } = this.pswp;\r\n    /** @type {AnimationProps} */\r\n    const animProps = {\r\n      duration: this._duration,\r\n      easing: this.pswp.options.easing,\r\n      onComplete: () => {\r\n        if (!animations.activeAnimations.length) {\r\n          this._onAnimationComplete();\r\n        }\r\n      },\r\n      target,\r\n    };\r\n    animProps[prop] = propValue;\r\n    animations.startTransition(animProps);\r\n  }\r\n}\r\n\r\nexport default Opener;\r\n", "import {\r\n  createElement,\r\n  equalizePoints,\r\n  pointsEqual,\r\n  clamp,\r\n} from './util/util.js';\r\n\r\nimport DOMEvents from './util/dom-events.js';\r\nimport Slide from './slide/slide.js';\r\nimport Gestures from './gestures/gestures.js';\r\nimport MainScroll from './main-scroll.js';\r\n\r\nimport Keyboard from './keyboard.js';\r\nimport Animations from './util/animations.js';\r\nimport ScrollWheel from './scroll-wheel.js';\r\nimport UI from './ui/ui.js';\r\nimport { getViewportSize } from './util/viewport-size.js';\r\nimport { getThumbBounds } from './slide/get-thumb-bounds.js';\r\nimport PhotoSwipeBase from './core/base.js';\r\nimport Opener from './opener.js';\r\nimport ContentLoader from './slide/loader.js';\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('./types.js').Type<T>} Type<T>\r\n */\r\n\r\n/** @typedef {import('./slide/slide.js').SlideData} SlideData */\r\n/** @typedef {import('./slide/zoom-level.js').ZoomLevelOption} ZoomLevelOption */\r\n/** @typedef {import('./ui/ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('./main-scroll.js').ItemHolder} ItemHolder */\r\n/** @typedef {import('./core/eventable.js').PhotoSwipeEventsMap} PhotoSwipeEventsMap */\r\n/** @typedef {import('./core/eventable.js').PhotoSwipeFiltersMap} PhotoSwipeFiltersMap */\r\n/** @typedef {import('./slide/get-thumb-bounds').Bounds} Bounds */\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('./core/eventable.js').EventCallback<T>} EventCallback<T>\r\n */\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('./core/eventable.js').AugmentedEvent<T>} AugmentedEvent<T>\r\n */\r\n\r\n/** @typedef {{ x: number; y: number; id?: string | number }} Point */\r\n/** @typedef {{ top: number; bottom: number; left: number; right: number }} Padding */\r\n/** @typedef {SlideData[]} DataSourceArray */\r\n/** @typedef {{ gallery: HTMLElement; items?: HTMLElement[] }} DataSourceObject */\r\n/** @typedef {DataSourceArray | DataSourceObject} DataSource */\r\n/** @typedef {(point: Point, originalEvent: PointerEvent) => void} ActionFn */\r\n/** @typedef {'close' | 'next' | 'zoom' | 'zoom-or-close' | 'toggle-controls'} ActionType */\r\n/** @typedef {Type<PhotoSwipe> | { default: Type<PhotoSwipe> }} PhotoSwipeModule */\r\n/** @typedef {PhotoSwipeModule | Promise<PhotoSwipeModule> | (() => Promise<PhotoSwipeModule>)} PhotoSwipeModuleOption */\r\n\r\n/**\r\n * @typedef {string | NodeListOf<HTMLElement> | HTMLElement[] | HTMLElement} ElementProvider\r\n */\r\n\r\n/** @typedef {Partial<PreparedPhotoSwipeOptions>} PhotoSwipeOptions https://photoswipe.com/options/ */\r\n/**\r\n * @typedef {Object} PreparedPhotoSwipeOptions\r\n *\r\n * @prop {DataSource} [dataSource]\r\n * Pass an array of any items via dataSource option. Its length will determine amount of slides\r\n * (which may be modified further from numItems event).\r\n *\r\n * Each item should contain data that you need to generate slide\r\n * (for image slide it would be src (image URL), width (image width), height, srcset, alt).\r\n *\r\n * If these properties are not present in your initial array, you may \"pre-parse\" each item from itemData filter.\r\n *\r\n * @prop {number} bgOpacity\r\n * Background backdrop opacity, always define it via this option and not via CSS rgba color.\r\n *\r\n * @prop {number} spacing\r\n * Spacing between slides. Defined as ratio relative to the viewport width (0.1 = 10% of viewport).\r\n *\r\n * @prop {boolean} allowPanToNext\r\n * Allow swipe navigation to the next slide when the current slide is zoomed. Does not apply to mouse events.\r\n *\r\n * @prop {boolean} loop\r\n * If set to true you'll be able to swipe from the last to the first image.\r\n * Option is always false when there are less than 3 slides.\r\n *\r\n * @prop {boolean} [wheelToZoom]\r\n * By default PhotoSwipe zooms image with ctrl-wheel, if you enable this option - image will zoom just via wheel.\r\n *\r\n * @prop {boolean} pinchToClose\r\n * Pinch touch gesture to close the gallery.\r\n *\r\n * @prop {boolean} closeOnVerticalDrag\r\n * Vertical drag gesture to close the PhotoSwipe.\r\n *\r\n * @prop {Padding} [padding]\r\n * Slide area padding (in pixels).\r\n *\r\n * @prop {(viewportSize: Point, itemData: SlideData, index: number) => Padding} [paddingFn]\r\n * The option is checked frequently, so make sure it's performant. Overrides padding option if defined. For example:\r\n *\r\n * @prop {number | false} hideAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {number | false} showAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {number | false} zoomAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {string} easing\r\n * String, 'cubic-bezier(.4,0,.22,1)'. CSS easing function for open/close/zoom transitions.\r\n *\r\n * @prop {boolean} escKey\r\n * Esc key to close.\r\n *\r\n * @prop {boolean} arrowKeys\r\n * Left/right arrow keys for navigation.\r\n *\r\n * @prop {boolean} trapFocus\r\n * Trap focus within PhotoSwipe element while it's open.\r\n *\r\n * @prop {boolean} returnFocus\r\n * Restore focus the last active element after PhotoSwipe is closed.\r\n *\r\n * @prop {boolean} clickToCloseNonZoomable\r\n * If image is not zoomable (for example, smaller than viewport) it can be closed by clicking on it.\r\n *\r\n * @prop {ActionType | ActionFn | false} imageClickAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} bgClickAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} tapAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} doubleTapAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {number} preloaderDelay\r\n * Delay before the loading indicator will be displayed,\r\n * if image is loaded during it - the indicator will not be displayed at all. Can be zero.\r\n *\r\n * @prop {string} indexIndicatorSep\r\n * Used for slide count indicator (\"1 of 10 \").\r\n *\r\n * @prop {(options: PhotoSwipeOptions, pswp: PhotoSwipeBase) => Point} [getViewportSizeFn]\r\n * A function that should return slide viewport width and height, in format {x: 100, y: 100}.\r\n *\r\n * @prop {string} errorMsg\r\n * Message to display when the image wasn't able to load. If you need to display HTML - use contentErrorElement filter.\r\n *\r\n * @prop {[number, number]} preload\r\n * Lazy loading of nearby slides based on direction of movement. Should be an array with two integers,\r\n * first one - number of items to preload before the current image, second one - after the current image.\r\n * Two nearby images are always loaded.\r\n *\r\n * @prop {string} [mainClass]\r\n * Class that will be added to the root element of PhotoSwipe, may contain multiple separated by space.\r\n * Example on Styling page.\r\n *\r\n * @prop {HTMLElement} [appendToEl]\r\n * Element to which PhotoSwipe dialog will be appended when it opens.\r\n *\r\n * @prop {number} maxWidthToAnimate\r\n * Maximum width of image to animate, if initial rendered image width\r\n * is larger than this value - the opening/closing transition will be automatically disabled.\r\n *\r\n * @prop {string} [closeTitle]\r\n * Translating\r\n *\r\n * @prop {string} [zoomTitle]\r\n * Translating\r\n *\r\n * @prop {string} [arrowPrevTitle]\r\n * Translating\r\n *\r\n * @prop {string} [arrowNextTitle]\r\n * Translating\r\n *\r\n * @prop {'zoom' | 'fade' | 'none'} [showHideAnimationType]\r\n * To adjust opening or closing transition type use lightbox option `showHideAnimationType` (`String`).\r\n * It supports three values - `zoom` (default), `fade` (default if there is no thumbnail) and `none`.\r\n *\r\n * Animations are automatically disabled if user `(prefers-reduced-motion: reduce)`.\r\n *\r\n * @prop {number} index\r\n * Defines start slide index.\r\n *\r\n * @prop {(e: MouseEvent) => number} [getClickedIndexFn]\r\n *\r\n * @prop {boolean} [arrowPrev]\r\n * @prop {boolean} [arrowNext]\r\n * @prop {boolean} [zoom]\r\n * @prop {boolean} [close]\r\n * @prop {boolean} [counter]\r\n *\r\n * @prop {string} [arrowPrevSVG]\r\n * @prop {string} [arrowNextSVG]\r\n * @prop {string} [zoomSVG]\r\n * @prop {string} [closeSVG]\r\n * @prop {string} [counterSVG]\r\n *\r\n * @prop {string} [arrowPrevTitle]\r\n * @prop {string} [arrowNextTitle]\r\n * @prop {string} [zoomTitle]\r\n * @prop {string} [closeTitle]\r\n * @prop {string} [counterTitle]\r\n *\r\n * @prop {ZoomLevelOption} [initialZoomLevel]\r\n * @prop {ZoomLevelOption} [secondaryZoomLevel]\r\n * @prop {ZoomLevelOption} [maxZoomLevel]\r\n *\r\n * @prop {boolean} [mouseMovePan]\r\n * @prop {Point | null} [initialPointerPos]\r\n * @prop {boolean} [showHideOpacity]\r\n *\r\n * @prop {PhotoSwipeModuleOption} [pswpModule]\r\n * @prop {() => Promise<any>} [openPromise]\r\n * @prop {boolean} [preloadFirstSlide]\r\n * @prop {ElementProvider} [gallery]\r\n * @prop {string} [gallerySelector]\r\n * @prop {ElementProvider} [children]\r\n * @prop {string} [childSelector]\r\n * @prop {string | false} [thumbSelector]\r\n */\r\n\r\n/** @type {PreparedPhotoSwipeOptions} */\r\nconst defaultOptions = {\r\n  allowPanToNext: true,\r\n  spacing: 0.1,\r\n  loop: true,\r\n  pinchToClose: true,\r\n  closeOnVerticalDrag: true,\r\n  hideAnimationDuration: 333,\r\n  showAnimationDuration: 333,\r\n  zoomAnimationDuration: 333,\r\n  escKey: true,\r\n  arrowKeys: true,\r\n  trapFocus: true,\r\n  returnFocus: true,\r\n  maxWidthToAnimate: 4000,\r\n  clickToCloseNonZoomable: true,\r\n  imageClickAction: 'zoom-or-close',\r\n  bgClickAction: 'close',\r\n  tapAction: 'toggle-controls',\r\n  doubleTapAction: 'zoom',\r\n  indexIndicatorSep: ' / ',\r\n  preloaderDelay: 2000,\r\n  bgOpacity: 0.8,\r\n\r\n  index: 0,\r\n  errorMsg: 'The image cannot be loaded',\r\n  preload: [1, 2],\r\n  easing: 'cubic-bezier(.4,0,.22,1)'\r\n};\r\n\r\n/**\r\n * PhotoSwipe Core\r\n */\r\nclass PhotoSwipe extends PhotoSwipeBase {\r\n  /**\r\n   * @param {PhotoSwipeOptions} [options]\r\n   */\r\n  constructor(options) {\r\n    super();\r\n\r\n    this.options = this._prepareOptions(options || {});\r\n\r\n    /**\r\n     * offset of viewport relative to document\r\n     *\r\n     * @type {Point}\r\n     */\r\n    this.offset = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * @type {Point}\r\n     * @private\r\n     */\r\n    this._prevViewportSize = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * Size of scrollable PhotoSwipe viewport\r\n     *\r\n     * @type {Point}\r\n     */\r\n    this.viewportSize = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * background (backdrop) opacity\r\n     */\r\n    this.bgOpacity = 1;\r\n    this.currIndex = 0;\r\n    this.potentialIndex = 0;\r\n    this.isOpen = false;\r\n    this.isDestroying = false;\r\n    this.hasMouse = false;\r\n\r\n    /**\r\n     * @private\r\n     * @type {SlideData}\r\n     */\r\n    this._initialItemData = {};\r\n    /** @type {Bounds | undefined} */\r\n    this._initialThumbBounds = undefined;\r\n\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.topBar = undefined;\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.element = undefined;\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.template = undefined;\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.container = undefined;\r\n    /** @type {HTMLElement | undefined} */\r\n    this.scrollWrap = undefined;\r\n    /** @type {Slide | undefined} */\r\n    this.currSlide = undefined;\r\n\r\n    this.events = new DOMEvents();\r\n    this.animations = new Animations();\r\n    this.mainScroll = new MainScroll(this);\r\n    this.gestures = new Gestures(this);\r\n    this.opener = new Opener(this);\r\n    this.keyboard = new Keyboard(this);\r\n    this.contentLoader = new ContentLoader(this);\r\n  }\r\n\r\n  /** @returns {boolean} */\r\n  init() {\r\n    if (this.isOpen || this.isDestroying) {\r\n      return false;\r\n    }\r\n\r\n    this.isOpen = true;\r\n    this.dispatch('init'); // legacy\r\n    this.dispatch('beforeOpen');\r\n\r\n    this._createMainStructure();\r\n\r\n    // add classes to the root element of PhotoSwipe\r\n    let rootClasses = 'pswp--open';\r\n    if (this.gestures.supportsTouch) {\r\n      rootClasses += ' pswp--touch';\r\n    }\r\n    if (this.options.mainClass) {\r\n      rootClasses += ' ' + this.options.mainClass;\r\n    }\r\n    if (this.element) {\r\n      this.element.className += ' ' + rootClasses;\r\n    }\r\n\r\n    this.currIndex = this.options.index || 0;\r\n    this.potentialIndex = this.currIndex;\r\n    this.dispatch('firstUpdate'); // starting index can be modified here\r\n\r\n    // initialize scroll wheel handler to block the scroll\r\n    this.scrollWheel = new ScrollWheel(this);\r\n\r\n    // sanitize index\r\n    if (Number.isNaN(this.currIndex)\r\n        || this.currIndex < 0\r\n        || this.currIndex >= this.getNumItems()) {\r\n      this.currIndex = 0;\r\n    }\r\n\r\n    if (!this.gestures.supportsTouch) {\r\n      // enable mouse features if no touch support detected\r\n      this.mouseDetected();\r\n    }\r\n\r\n    // causes forced synchronous layout\r\n    this.updateSize();\r\n\r\n    this.offset.y = window.pageYOffset;\r\n\r\n    this._initialItemData = this.getItemData(this.currIndex);\r\n    this.dispatch('gettingData', {\r\n      index: this.currIndex,\r\n      data: this._initialItemData,\r\n      slide: undefined\r\n    });\r\n\r\n    // *Layout* - calculate size and position of elements here\r\n    this._initialThumbBounds = this.getThumbBounds();\r\n    this.dispatch('initialLayout');\r\n\r\n    this.on('openingAnimationEnd', () => {\r\n      const { itemHolders } = this.mainScroll;\r\n\r\n      // Add content to the previous and next slide\r\n      if (itemHolders[0]) {\r\n        itemHolders[0].el.style.display = 'block';\r\n        this.setContent(itemHolders[0], this.currIndex - 1);\r\n      }\r\n      if (itemHolders[2]) {\r\n        itemHolders[2].el.style.display = 'block';\r\n        this.setContent(itemHolders[2], this.currIndex + 1);\r\n      }\r\n\r\n      this.appendHeavy();\r\n\r\n      this.contentLoader.updateLazy();\r\n\r\n      this.events.add(window, 'resize', this._handlePageResize.bind(this));\r\n      this.events.add(window, 'scroll', this._updatePageScrollOffset.bind(this));\r\n      this.dispatch('bindEvents');\r\n    });\r\n\r\n    // set content for center slide (first time)\r\n    if (this.mainScroll.itemHolders[1]) {\r\n      this.setContent(this.mainScroll.itemHolders[1], this.currIndex);\r\n    }\r\n    this.dispatch('change');\r\n\r\n    this.opener.open();\r\n\r\n    this.dispatch('afterInit');\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Get looped slide index\r\n   * (for example, -1 will return the last slide)\r\n   *\r\n   * @param {number} index\r\n   * @returns {number}\r\n   */\r\n  getLoopedIndex(index) {\r\n    const numSlides = this.getNumItems();\r\n\r\n    if (this.options.loop) {\r\n      if (index > numSlides - 1) {\r\n        index -= numSlides;\r\n      }\r\n\r\n      if (index < 0) {\r\n        index += numSlides;\r\n      }\r\n    }\r\n\r\n    return clamp(index, 0, numSlides - 1);\r\n  }\r\n\r\n  appendHeavy() {\r\n    this.mainScroll.itemHolders.forEach((itemHolder) => {\r\n      itemHolder.slide?.appendHeavy();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Change the slide\r\n   * @param {number} index New index\r\n   */\r\n  goTo(index) {\r\n    this.mainScroll.moveIndexBy(\r\n      this.getLoopedIndex(index) - this.potentialIndex\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Go to the next slide.\r\n   */\r\n  next() {\r\n    this.goTo(this.potentialIndex + 1);\r\n  }\r\n\r\n  /**\r\n   * Go to the previous slide.\r\n   */\r\n  prev() {\r\n    this.goTo(this.potentialIndex - 1);\r\n  }\r\n\r\n  /**\r\n   * @see slide/slide.js zoomTo\r\n   *\r\n   * @param {Parameters<Slide['zoomTo']>} args\r\n   */\r\n  zoomTo(...args) {\r\n    this.currSlide?.zoomTo(...args);\r\n  }\r\n\r\n  /**\r\n   * @see slide/slide.js toggleZoom\r\n   */\r\n  toggleZoom() {\r\n    this.currSlide?.toggleZoom();\r\n  }\r\n\r\n  /**\r\n   * Close the gallery.\r\n   * After closing transition ends - destroy it\r\n   */\r\n  close() {\r\n    if (!this.opener.isOpen || this.isDestroying) {\r\n      return;\r\n    }\r\n\r\n    this.isDestroying = true;\r\n\r\n    this.dispatch('close');\r\n\r\n    this.events.removeAll();\r\n    this.opener.close();\r\n  }\r\n\r\n  /**\r\n   * Destroys the gallery:\r\n   * - instantly closes the gallery\r\n   * - unbinds events,\r\n   * - cleans intervals and timeouts\r\n   * - removes elements from DOM\r\n   */\r\n  destroy() {\r\n    if (!this.isDestroying) {\r\n      this.options.showHideAnimationType = 'none';\r\n      this.close();\r\n      return;\r\n    }\r\n\r\n    this.dispatch('destroy');\r\n\r\n    this._listeners = {};\r\n\r\n    if (this.scrollWrap) {\r\n      this.scrollWrap.ontouchmove = null;\r\n      this.scrollWrap.ontouchend = null;\r\n    }\r\n\r\n    this.element?.remove();\r\n\r\n    this.mainScroll.itemHolders.forEach((itemHolder) => {\r\n      itemHolder.slide?.destroy();\r\n    });\r\n\r\n    this.contentLoader.destroy();\r\n    this.events.removeAll();\r\n  }\r\n\r\n  /**\r\n   * Refresh/reload content of a slide by its index\r\n   *\r\n   * @param {number} slideIndex\r\n   */\r\n  refreshSlideContent(slideIndex) {\r\n    this.contentLoader.removeByIndex(slideIndex);\r\n    this.mainScroll.itemHolders.forEach((itemHolder, i) => {\r\n      let potentialHolderIndex = (this.currSlide?.index ?? 0) - 1 + i;\r\n      if (this.canLoop()) {\r\n        potentialHolderIndex = this.getLoopedIndex(potentialHolderIndex);\r\n      }\r\n      if (potentialHolderIndex === slideIndex) {\r\n        // set the new slide content\r\n        this.setContent(itemHolder, slideIndex, true);\r\n\r\n        // activate the new slide if it's current\r\n        if (i === 1) {\r\n          this.currSlide = itemHolder.slide;\r\n          itemHolder.slide?.setIsActive(true);\r\n        }\r\n      }\r\n    });\r\n\r\n    this.dispatch('change');\r\n  }\r\n\r\n\r\n  /**\r\n   * Set slide content\r\n   *\r\n   * @param {ItemHolder} holder mainScroll.itemHolders array item\r\n   * @param {number} index Slide index\r\n   * @param {boolean} [force] If content should be set even if index wasn't changed\r\n   */\r\n  setContent(holder, index, force) {\r\n    if (this.canLoop()) {\r\n      index = this.getLoopedIndex(index);\r\n    }\r\n\r\n    if (holder.slide) {\r\n      if (holder.slide.index === index && !force) {\r\n        // exit if holder already contains this slide\r\n        // this could be common when just three slides are used\r\n        return;\r\n      }\r\n\r\n      // destroy previous slide\r\n      holder.slide.destroy();\r\n      holder.slide = undefined;\r\n    }\r\n\r\n    // exit if no loop and index is out of bounds\r\n    if (!this.canLoop() && (index < 0 || index >= this.getNumItems())) {\r\n      return;\r\n    }\r\n\r\n    const itemData = this.getItemData(index);\r\n    holder.slide = new Slide(itemData, index, this);\r\n\r\n    // set current slide\r\n    if (index === this.currIndex) {\r\n      this.currSlide = holder.slide;\r\n    }\r\n\r\n    holder.slide.append(holder.el);\r\n  }\r\n\r\n  /** @returns {Point} */\r\n  getViewportCenterPoint() {\r\n    return {\r\n      x: this.viewportSize.x / 2,\r\n      y: this.viewportSize.y / 2\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update size of all elements.\r\n   * Executed on init and on page resize.\r\n   *\r\n   * @param {boolean} [force] Update size even if size of viewport was not changed.\r\n   */\r\n  updateSize(force) {\r\n    // let item;\r\n    // let itemIndex;\r\n\r\n    if (this.isDestroying) {\r\n      // exit if PhotoSwipe is closed or closing\r\n      // (to avoid errors, as resize event might be delayed)\r\n      return;\r\n    }\r\n\r\n    //const newWidth = this.scrollWrap.clientWidth;\r\n    //const newHeight = this.scrollWrap.clientHeight;\r\n\r\n    const newViewportSize = getViewportSize(this.options, this);\r\n\r\n    if (!force && pointsEqual(newViewportSize, this._prevViewportSize)) {\r\n      // Exit if dimensions were not changed\r\n      return;\r\n    }\r\n\r\n    //this._prevViewportSize.x = newWidth;\r\n    //this._prevViewportSize.y = newHeight;\r\n    equalizePoints(this._prevViewportSize, newViewportSize);\r\n\r\n    this.dispatch('beforeResize');\r\n\r\n    equalizePoints(this.viewportSize, this._prevViewportSize);\r\n\r\n    this._updatePageScrollOffset();\r\n\r\n    this.dispatch('viewportSize');\r\n\r\n    // Resize slides only after opener animation is finished\r\n    // and don't re-calculate size on inital size update\r\n    this.mainScroll.resize(this.opener.isOpen);\r\n\r\n    if (!this.hasMouse && window.matchMedia('(any-hover: hover)').matches) {\r\n      this.mouseDetected();\r\n    }\r\n\r\n    this.dispatch('resize');\r\n  }\r\n\r\n  /**\r\n   * @param {number} opacity\r\n   */\r\n  applyBgOpacity(opacity) {\r\n    this.bgOpacity = Math.max(opacity, 0);\r\n    if (this.bg) {\r\n      this.bg.style.opacity = String(this.bgOpacity * this.options.bgOpacity);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Whether mouse is detected\r\n   */\r\n  mouseDetected() {\r\n    if (!this.hasMouse) {\r\n      this.hasMouse = true;\r\n      this.element?.classList.add('pswp--has_mouse');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Page resize event handler\r\n   *\r\n   * @private\r\n   */\r\n  _handlePageResize() {\r\n    this.updateSize();\r\n\r\n    // In iOS webview, if element size depends on document size,\r\n    // it'll be measured incorrectly in resize event\r\n    //\r\n    // https://bugs.webkit.org/show_bug.cgi?id=170595\r\n    // https://hackernoon.com/onresize-event-broken-in-mobile-safari-d8469027bf4d\r\n    if (/iPhone|iPad|iPod/i.test(window.navigator.userAgent)) {\r\n      setTimeout(() => {\r\n        this.updateSize();\r\n      }, 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Page scroll offset is used\r\n   * to get correct coordinates\r\n   * relative to PhotoSwipe viewport.\r\n   *\r\n   * @private\r\n   */\r\n  _updatePageScrollOffset() {\r\n    this.setScrollOffset(0, window.pageYOffset);\r\n  }\r\n\r\n  /**\r\n   * @param {number} x\r\n   * @param {number} y\r\n   */\r\n  setScrollOffset(x, y) {\r\n    this.offset.x = x;\r\n    this.offset.y = y;\r\n    this.dispatch('updateScrollOffset');\r\n  }\r\n\r\n  /**\r\n   * Create main HTML structure of PhotoSwipe,\r\n   * and add it to DOM\r\n   *\r\n   * @private\r\n   */\r\n  _createMainStructure() {\r\n    // root DOM element of PhotoSwipe (.pswp)\r\n    this.element = createElement('pswp', 'div');\r\n    this.element.setAttribute('tabindex', '-1');\r\n    this.element.setAttribute('role', 'dialog');\r\n\r\n    // template is legacy prop\r\n    this.template = this.element;\r\n\r\n    // Background is added as a separate element,\r\n    // as animating opacity is faster than animating rgba()\r\n    this.bg = createElement('pswp__bg', 'div', this.element);\r\n    this.scrollWrap = createElement('pswp__scroll-wrap', 'section', this.element);\r\n    this.container = createElement('pswp__container', 'div', this.scrollWrap);\r\n\r\n    // aria pattern: carousel\r\n    this.scrollWrap.setAttribute('aria-roledescription', 'carousel');\r\n    this.container.setAttribute('aria-live', 'off');\r\n    this.container.setAttribute('id', 'pswp__items');\r\n\r\n    this.mainScroll.appendHolders();\r\n\r\n    this.ui = new UI(this);\r\n    this.ui.init();\r\n\r\n    // append to DOM\r\n    (this.options.appendToEl || document.body).appendChild(this.element);\r\n  }\r\n\r\n\r\n  /**\r\n   * Get position and dimensions of small thumbnail\r\n   *   {x:,y:,w:}\r\n   *\r\n   * Height is optional (calculated based on the large image)\r\n   *\r\n   * @returns {Bounds | undefined}\r\n   */\r\n  getThumbBounds() {\r\n    return getThumbBounds(\r\n      this.currIndex,\r\n      this.currSlide ? this.currSlide.data : this._initialItemData,\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * If the PhotoSwipe can have continuous loop\r\n   * @returns Boolean\r\n   */\r\n  canLoop() {\r\n    return (this.options.loop && this.getNumItems() > 2);\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PhotoSwipeOptions} options\r\n   * @returns {PreparedPhotoSwipeOptions}\r\n   */\r\n  _prepareOptions(options) {\r\n    if (window.matchMedia('(prefers-reduced-motion), (update: slow)').matches) {\r\n      options.showHideAnimationType = 'none';\r\n      options.zoomAnimationDuration = 0;\r\n    }\r\n\r\n    /** @type {PreparedPhotoSwipeOptions} */\r\n    return {\r\n      ...defaultOptions,\r\n      ...options\r\n    };\r\n  }\r\n}\r\n\r\nexport default PhotoSwipe;\r\n", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport PhotoSwipe from 'photoswipe';\nimport React, { useRef, useCallback, useEffect, useMemo, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport PropTypes from 'prop-types';\nimport sortNodes from \"./helpers/sort-nodes.js\";\nimport objectToHash from \"./helpers/object-to-hash.js\";\nimport hashToObject from \"./helpers/hash-to-object.js\";\nimport getHashWithoutGidAndPid from \"./helpers/get-hash-without-gid-and-pid.js\";\nimport getHashValue from \"./helpers/get-hash-value.js\";\nimport getBaseUrl from \"./helpers/get-base-url.js\";\nimport hashIncludesNavigationQueryParams from \"./helpers/hash-includes-navigation-query-params.js\";\nimport getInitialActiveSlideIndex from \"./helpers/get-initial-active-slide-index.js\";\nimport ensureRefPassed from \"./helpers/ensure-ref-passed.js\";\nimport entryItemRefIsElement from \"./helpers/entry-item-ref-is-element.js\";\nimport { Context } from \"./context.js\";\nimport PhotoSwipeLightboxStub from \"./lightbox-stub.js\";\nimport { NoRefError } from \"./no-ref-error.js\";\n/**\n * This variable stores the PhotoSwipe instance object\n * It aims to check whether does the PhotoSwipe opened at the moment\n * (analog of window.pswp in 'photoswipe/lightbox')\n */\nlet pswp = null;\n/**\n * Gallery component providing photoswipe context\n */\nexport const Gallery = ({\n  children,\n  options,\n  plugins,\n  uiElements,\n  id: galleryUID,\n  onBeforeOpen,\n  onOpen,\n  withCaption,\n  withDownloadButton\n}) => {\n  const [contentPortal, setContentPortal] = useState(null);\n  const items = useRef(new Map());\n  /**\n   * Store PID from hash if there are no items yet,\n   * but we need to open photoswipe if items appear in the next render\n   */\n  const openWhenReadyPid = useRef(null);\n  const open = useCallback((targetRef, targetId, itemIndex, e) => {\n    // only one photoswipe instance could be opened at once\n    // so if photoswipe is already open, function should do nothing\n    if (pswp) {\n      return;\n    }\n    const entries = Array.from(items.current);\n    if (typeof itemIndex === 'number' && (entries[itemIndex] === undefined || !entryItemRefIsElement(entries[itemIndex]))) {\n      throw new NoRefError(`Failed to open at index ${itemIndex}`);\n    }\n    const {\n      slides,\n      index\n    } = entries.map(ensureRefPassed).sort(([{\n      current: a\n    }], [{\n      current: b\n    }]) => sortNodes(a, b)).reduce((acc, entry, i) => {\n      const [ref, _a] = entry,\n        {\n          width,\n          height,\n          original,\n          originalSrcset,\n          thumbnail,\n          cropped,\n          content,\n          id: pid\n        } = _a,\n        rest = __rest(_a, [\"width\", \"height\", \"original\", \"originalSrcset\", \"thumbnail\", \"cropped\", \"content\", \"id\"]);\n      if (targetRef === ref || pid !== undefined && String(pid) === targetId) {\n        acc.index = i;\n      }\n      acc.slides.push(Object.assign(Object.assign(Object.assign({\n        w: Number(width),\n        h: Number(height),\n        src: original,\n        srcset: originalSrcset,\n        msrc: thumbnail,\n        element: ref.current,\n        thumbCropped: cropped,\n        content\n      }, content !== undefined ? {\n        type: 'html'\n      } : {}), pid !== undefined ? {\n        pid\n      } : {}), rest));\n      return acc;\n    }, {\n      slides: [],\n      index: itemIndex || null\n    });\n    const initialPoint = e && e.clientX !== undefined && e.clientY !== undefined ? {\n      x: e.clientX,\n      y: e.clientY\n    } : null;\n    const instance = new PhotoSwipe(Object.assign({\n      dataSource: slides,\n      index: getInitialActiveSlideIndex(index, targetId),\n      initialPointerPos: initialPoint\n    }, options || {}));\n    pswp = instance;\n    instance.on('contentActivate', ({\n      content: slideContent\n    }) => {\n      if (slideContent.data.content) {\n        setContentPortal(createPortal(slideContent.data.content, slideContent.element));\n      } else {\n        setContentPortal(null);\n      }\n    });\n    instance.on('close', () => {\n      setContentPortal(null);\n    });\n    if (withDownloadButton) {\n      instance.on('uiRegister', () => {\n        var _a;\n        (_a = instance.ui) === null || _a === void 0 ? void 0 : _a.registerElement({\n          name: 'download-button',\n          ariaLabel: 'Download',\n          order: 8,\n          isButton: true,\n          tagName: 'a',\n          appendTo: 'bar',\n          html: {\n            isCustomSVG: true,\n            inner: '<path d=\"M20.5 14.3 17.1 18V10h-2.2v7.9l-3.4-3.6L10 16l6 6.1 6-6.1ZM23 23H9v2h14Z\" id=\"pswp__icn-download\"/>',\n            outlineID: 'pswp__icn-download'\n          },\n          // can't test onInit callback correctly\n          onInit: /* istanbul ignore next */(el, pswpInstance) => {\n            el.setAttribute('download', '');\n            el.setAttribute('target', '_blank');\n            el.setAttribute('rel', 'noopener');\n            instance.on('change', () => {\n              var _a;\n              if (!((_a = pswpInstance.currSlide) === null || _a === void 0 ? void 0 : _a.data.src)) {\n                return;\n              }\n              const downloadButton = el;\n              downloadButton.href = pswpInstance.currSlide.data.src;\n            });\n          }\n        });\n      });\n    }\n    if (withCaption) {\n      instance.on('uiRegister', () => {\n        var _a;\n        (_a = instance.ui) === null || _a === void 0 ? void 0 : _a.registerElement({\n          name: 'default-caption',\n          order: 9,\n          isButton: false,\n          appendTo: 'root',\n          // can't test onInit callback correctly\n          onInit: /* istanbul ignore next */(el, pswpInstance) => {\n            /* eslint-disable no-param-reassign */\n            el.style.position = 'absolute';\n            el.style.bottom = '15px';\n            el.style.left = '0';\n            el.style.right = '0';\n            el.style.padding = '0 20px';\n            el.style.color = 'var(--pswp-icon-color)';\n            el.style.textAlign = 'center';\n            el.style.fontSize = '14px';\n            el.style.lineHeight = '1.5';\n            el.style.textShadow = '1px 1px 3px var(--pswp-icon-color-secondary)';\n            /* eslint-enable no-param-reassign */\n            instance.on('change', () => {\n              if (!pswpInstance.currSlide) {\n                return;\n              }\n              const {\n                caption,\n                alt\n              } = pswpInstance.currSlide.data;\n              // eslint-disable-next-line no-param-reassign\n              el.innerHTML = caption || alt || '';\n            });\n          }\n        });\n      });\n    }\n    if (Array.isArray(uiElements)) {\n      uiElements.forEach(uiElement => {\n        instance.on('uiRegister', () => {\n          var _a;\n          (_a = instance.ui) === null || _a === void 0 ? void 0 : _a.registerElement(uiElement);\n        });\n      });\n    }\n    if (typeof plugins === 'function') {\n      plugins(new PhotoSwipeLightboxStub(instance));\n    }\n    if (typeof onBeforeOpen === 'function') {\n      onBeforeOpen(instance);\n    }\n    const getHistoryState = () => {\n      return {\n        gallery: {\n          galleryUID\n        }\n      };\n    };\n    instance.on('beforeOpen', () => {\n      var _a;\n      if (galleryUID === undefined) {\n        return;\n      }\n      const hashIncludesGidAndPid = hashIncludesNavigationQueryParams(getHashValue());\n      // was openned by react-photoswipe-gallery's open() method call (click on thumbnail, for example)\n      // we need to create new history record to store hash navigation state\n      if (!hashIncludesGidAndPid) {\n        window.history.pushState(getHistoryState(), document.title);\n        return;\n      }\n      const hasGalleryStateInHistory = Boolean((_a = window.history.state) === null || _a === void 0 ? void 0 : _a.gallery);\n      // was openned by history.forward()\n      // we do not need to create new history record for hash navigation\n      // because we already have one\n      if (hasGalleryStateInHistory) {\n        return;\n      }\n      // was openned by link with gid and pid\n      const baseUrl = getBaseUrl();\n      const currentHash = getHashValue();\n      const hashWithoutGidAndPid = getHashWithoutGidAndPid(currentHash);\n      const urlWithoutOpenedSlide = `${baseUrl}${hashWithoutGidAndPid ? `#${hashWithoutGidAndPid}` : ''}`;\n      const urlWithOpenedSlide = `${baseUrl}#${currentHash}`;\n      // firstly, we need to modify current history record - set url without gid and pid\n      // we will return to this state after photoswipe closing\n      window.history.replaceState(window.history.state, document.title, urlWithoutOpenedSlide);\n      // then we need to create new history record to store hash navigation state\n      window.history.pushState(getHistoryState(), document.title, urlWithOpenedSlide);\n    });\n    instance.on('change', () => {\n      var _a;\n      if (galleryUID === undefined) {\n        return;\n      }\n      const pid = ((_a = instance.currSlide) === null || _a === void 0 ? void 0 : _a.data.pid) || instance.currIndex + 1;\n      const baseUrl = getBaseUrl();\n      const baseHash = getHashWithoutGidAndPid(getHashValue());\n      const gidAndPidHash = objectToHash({\n        gid: galleryUID,\n        pid\n      });\n      const urlWithOpenedSlide = `${baseUrl}#${baseHash}&${gidAndPidHash}`;\n      // updates in current history record hash value with actual pid\n      window.history.replaceState(getHistoryState(), document.title, urlWithOpenedSlide);\n    });\n    const closeGalleryOnHistoryPopState = () => {\n      if (galleryUID === undefined) {\n        return;\n      }\n      if (pswp !== null) {\n        pswp.close();\n      }\n    };\n    window.addEventListener('popstate', closeGalleryOnHistoryPopState);\n    instance.on('destroy', () => {\n      if (galleryUID !== undefined) {\n        window.removeEventListener('popstate', closeGalleryOnHistoryPopState);\n        // if hash includes gid and pid => this destroy was called with ordinary instance.close() call\n        // if not => destroy was called by history.back (browser's back button) => history has been already returned to previous state\n        if (hashIncludesNavigationQueryParams(getHashValue())) {\n          window.history.back();\n        }\n      }\n      pswp = null;\n    });\n    instance.init();\n    if (typeof onOpen === 'function') {\n      onOpen(instance);\n    }\n  }, [options, plugins, uiElements, galleryUID, onBeforeOpen, onOpen, withCaption, withDownloadButton]);\n  useEffect(() => {\n    return () => {\n      if (pswp) {\n        pswp.close();\n      }\n    };\n  }, []);\n  const openGalleryBasedOnUrlHash = useCallback(() => {\n    if (galleryUID === undefined) {\n      return;\n    }\n    if (pswp !== null) {\n      return;\n    }\n    const hash = getHashValue();\n    if (hash.length < 5) {\n      return;\n    }\n    const params = hashToObject(hash);\n    const {\n      pid,\n      gid\n    } = params;\n    if (!pid || !gid) {\n      return;\n    }\n    if (items.current.size === 0) {\n      // no items currently, save PID from hash for future use\n      openWhenReadyPid.current = pid;\n      return;\n    }\n    if (pid && gid === String(galleryUID)) {\n      open(null, pid);\n    }\n  }, [open, galleryUID]);\n  useEffect(() => {\n    openGalleryBasedOnUrlHash();\n    // needed for case when gallery was firstly opened, then was closed and user clicked on browser's forward button\n    window.addEventListener('popstate', openGalleryBasedOnUrlHash);\n    return () => {\n      window.removeEventListener('popstate', openGalleryBasedOnUrlHash);\n    };\n  }, [openGalleryBasedOnUrlHash]);\n  const remove = useCallback(ref => {\n    items.current.delete(ref);\n  }, []);\n  const set = useCallback((ref, data) => {\n    items.current.set(ref, data);\n    if (openWhenReadyPid.current === null) {\n      return;\n    }\n    const {\n      id\n    } = data;\n    if (id === openWhenReadyPid.current) {\n      // user provided `id` prop of Item component\n      open(ref);\n      openWhenReadyPid.current = null;\n      return;\n    }\n    if (!id) {\n      // in this case we using index of item as PID\n      const index = parseInt(openWhenReadyPid.current, 10) - 1;\n      const refToOpen = Array.from(items.current.keys())[index];\n      if (refToOpen) {\n        open(refToOpen);\n        openWhenReadyPid.current = null;\n      }\n    }\n  }, [open]);\n  const isRefRegistered = useCallback(ref => {\n    return items.current.has(ref);\n  }, []);\n  const openAt = useCallback(index => {\n    open(null, null, index);\n  }, [open]);\n  const contextValue = useMemo(() => ({\n    remove,\n    set,\n    handleClick: open,\n    open: openAt,\n    isRefRegistered\n  }), [remove, set, open, openAt, isRefRegistered]);\n  return React.createElement(Context.Provider, {\n    value: contextValue\n  }, children, contentPortal);\n};\nGallery.propTypes = {\n  children: PropTypes.any,\n  options: PropTypes.object,\n  plugins: PropTypes.func,\n  uiElements: PropTypes.array,\n  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  onBeforeOpen: PropTypes.func,\n  onOpen: PropTypes.func,\n  withCaption: PropTypes.bool,\n  withDownloadButton: PropTypes.bool\n};", "function sortNodes(a, b) {\n  if (a === b) return 0;\n  // eslint-disable-next-line no-bitwise\n  if (a.compareDocumentPosition(b) & 2) {\n    return 1;\n  }\n  return -1;\n}\nexport default sortNodes;", "function objectToHash(obj) {\n  return Object.entries(obj).map(([key, value]) => value ? `${key}=${value}` : key).join('&');\n}\nexport default objectToHash;", "function hashToObject(hash) {\n  return hash.split('&').reduce((acc, keyValue) => {\n    const [key, value] = keyValue.split('=');\n    if (key) {\n      acc[key] = value;\n    }\n    return acc;\n  }, {});\n}\nexport default hashToObject;", "import hashToObject from \"./hash-to-object.js\";\nimport objectToHash from \"./object-to-hash.js\";\nfunction getHashWithoutGidAndPid(hash) {\n  const obj = hashToObject(hash);\n  delete obj.gid;\n  delete obj.pid;\n  return objectToHash(obj);\n}\nexport default getHashWithoutGidAndPid;", "function getHashValue() {\n  return window.location.hash.substring(1);\n}\nexport default getHashValue;", "function getBaseUrl() {\n  return `${window.location.pathname}${window.location.search}`;\n}\nexport default getBaseUrl;", "import hashToObject from \"./hash-to-object.js\";\nconst hashIncludesNavigationQueryParams = hash => {\n  const hashParts = hashToObject(hash);\n  return Boolean(hashParts.gid) && Boolean(hashParts.pid);\n};\nexport default hashIncludesNavigationQueryParams;", "function getInitialActiveSlideIndex(index, targetId) {\n  if (index !== null) {\n    return index;\n  }\n  return targetId ? parseInt(targetId, 10) - 1 : 0;\n}\nexport default getInitialActiveSlideIndex;", "export class NoRefError extends Error {\n  constructor(msg = '') {\n    super();\n    this.message = `\n    ${msg}\n    No valid \\`ref\\` provided.\n    You should use \\`ref\\` from render prop of Item component.\n    Example:\n    <Item>{({ ref }) => <div ref={ref}></div>}</Item>\\n`;\n  }\n}", "const entryItemRefIsElement = entry => entry[0].current instanceof Element;\nexport default entryItemRefIsElement;", "import { NoRefError } from \"../no-ref-error.js\";\nimport entryItemRefIsElement from \"./entry-item-ref-is-element.js\";\nconst ensureRefPassed = entry => {\n  if (entryItemRefIsElement(entry)) {\n    return entry;\n  }\n  throw new NoRefError();\n};\nexport default ensureRefPassed;", "import { createContext } from 'react';\nexport const Context = createContext({\n  remove: () => {},\n  set: () => {},\n  handleClick: () => {},\n  open: () => {},\n  isRefRegistered: () => false\n});", "/**\n * The purpose of this class is to emulate the behavior of the PhotoSwipeLightbox\n * to provide the ability to use plugins\n */\nexport default class PhotoSwipeLightboxStub {\n  constructor(pswp) {\n    this.pswp = pswp;\n    this.on = pswp.on.bind(pswp);\n    this.off = pswp.off.bind(pswp);\n    this.dispatch = pswp.dispatch.bind(pswp);\n  }\n}", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useRef, useEffect, useMemo, useCallback } from 'react';\nimport PropTypes from 'prop-types';\nimport { useApiContext } from \"./hooks.js\";\nimport { NoRefError } from \"./no-ref-error.js\";\n/**\n * Gallery item\n *\n * Should be a children of Gallery component\n */\nexport const Item = _a => {\n  var {\n      children\n    } = _a,\n    restProps = __rest(_a, [\"children\"]);\n  const ref = useRef(null);\n  const {\n    remove,\n    set,\n    handleClick,\n    isRefRegistered\n  } = useApiContext();\n  const refCallback = useCallback(node => {\n    ref.current = node;\n    set(ref, restProps);\n  }, [set, ...Object.values(restProps)]);\n  const open = useCallback(event => {\n    if (!isRefRegistered(ref)) {\n      throw new NoRefError();\n    }\n    handleClick(ref, null, null, event);\n  }, [handleClick, isRefRegistered]);\n  const childrenFnProps = useMemo(() => ({\n    ref: refCallback,\n    open\n  }), [refCallback, open]);\n  useEffect(() => {\n    return () => {\n      if (ref.current === null) {\n        remove(ref);\n      }\n    };\n  }, [remove]);\n  return children(childrenFnProps);\n};\nItem.propTypes = {\n  children: PropTypes.func.isRequired,\n  original: PropTypes.string,\n  originalSrcset: PropTypes.string,\n  thumbnail: PropTypes.string,\n  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  alt: PropTypes.string,\n  caption: PropTypes.string,\n  content: PropTypes.element,\n  html: PropTypes.string,\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  cropped: PropTypes.bool\n};", "import { useContext } from 'react';\nimport { Context } from \"./context.js\";\n/**\n * A hook that gives you access to provided methods for more advanced usage\n */\nexport const useGallery = () => {\n  const {\n    open\n  } = useContext(Context);\n  return {\n    /**\n     * Function that opens the gallery at the provided index\n     */\n    open\n  };\n};\nexport const useApiContext = () => useContext(Context);"], "mappings": ";;;;;;;;;;;;;;AASO,SAASA,cAAcC,WAAWC,SAASC,YAAY;AAC5D,QAAMC,KAAKC,SAASL,cAAcE,OAAvB;AACX,MAAID,WAAW;AACbG,OAAGH,YAAYA;EAChB;AACD,MAAIE,YAAY;AACdA,eAAWG,YAAYF,EAAvB;EACD;AACD,SAAOA;AACR;AAOM,SAASG,eAAeC,IAAIC,IAAI;AACrCD,KAAGE,IAAID,GAAGC;AACVF,KAAGG,IAAIF,GAAGE;AACV,MAAIF,GAAGG,OAAOC,QAAW;AACvBL,OAAGI,KAAKH,GAAGG;EACZ;AACD,SAAOJ;AACR;AAKM,SAASM,WAAWC,GAAG;AAC5BA,IAAEL,IAAIM,KAAKC,MAAMF,EAAEL,CAAb;AACNK,IAAEJ,IAAIK,KAAKC,MAAMF,EAAEJ,CAAb;AACP;AASM,SAASO,mBAAmBV,IAAIC,IAAI;AACzC,QAAMC,IAAIM,KAAKG,IAAIX,GAAGE,IAAID,GAAGC,CAAnB;AACV,QAAMC,IAAIK,KAAKG,IAAIX,GAAGG,IAAIF,GAAGE,CAAnB;AACV,SAAOK,KAAKI,KAAMV,IAAIA,IAAMC,IAAIA,CAAzB;AACR;AASM,SAASU,YAAYb,IAAIC,IAAI;AAClC,SAAOD,GAAGE,MAAMD,GAAGC,KAAKF,GAAGG,MAAMF,GAAGE;AACrC;AAUM,SAASW,MAAMC,KAAKC,KAAKC,KAAK;AACnC,SAAOT,KAAKQ,IAAIR,KAAKS,IAAIF,KAAKC,GAAd,GAAoBC,GAA7B;AACR;AAUM,SAASC,kBAAkBhB,GAAGC,GAAGgB,OAAO;AAC7C,MAAIC,YAAa,eAAclB,CAAE,MAAKC,KAAK,CAAE;AAE7C,MAAIgB,UAAUd,QAAW;AACvBe,iBAAc,YAAWD,KAAM,IAAGA,KAAM;EACzC;AAED,SAAOC;AACR;AAUM,SAASC,aAAazB,IAAIM,GAAGC,GAAGgB,OAAO;AAC5CvB,KAAG0B,MAAMC,YAAYL,kBAAkBhB,GAAGC,GAAGgB,KAAP;AACvC;AAED,IAAMK,mBAAmB;AAUlB,SAASC,mBAAmB7B,IAAI8B,MAAMC,UAAUC,MAAM;AAI3DhC,KAAG0B,MAAMO,aAAaH,OACjB,GAAEA,IAAK,IAAGC,QAAS,MAAKC,QAAQJ,gBAAiB,KAClD;AACL;AASM,SAASM,eAAelC,IAAImC,GAAGC,GAAG;AACvCpC,KAAG0B,MAAMW,QAAS,OAAOF,MAAM,WAAa,GAAEA,CAAE,OAAMA;AACtDnC,KAAG0B,MAAMY,SAAU,OAAOF,MAAM,WAAa,GAAEA,CAAE,OAAMA;AACxD;AAKM,SAASG,sBAAsBvC,IAAI;AACxC6B,qBAAmB7B,EAAD;AACnB;AAMM,SAASwC,YAAYC,KAAK;AAC/B,MAAI,YAAYA,KAAK;AACnB,WAAOA,IAAIC,OAAJ,EAAaC,MAAM,MAAM;IAAA,CAAzB;EACR;AAED,MAAIF,IAAIG,UAAU;AAChB,WAAOC,QAAQC,QAAQL,GAAhB;EACR;AAED,SAAO,IAAII,QAAQ,CAACC,SAASC,WAAW;AACtCN,QAAIO,SAAS,MAAMF,QAAQL,GAAD;AAC1BA,QAAIQ,UAAUF;EACf,CAHM;AAIR;AAIM,IAAMG,aAAa;EACxBC,MAAM;EACNC,SAAS;EACTC,QAAQ;EACRC,OAAO;AAJiB;AAenB,SAASC,eAAeC,GAAG;AAChC,SAAQ,YAAYA,KAAKA,EAAEC,WAAW,KAAMD,EAAEE,WAAWF,EAAEG,WAAWH,EAAEI,UAAUJ,EAAEK;AACrF;AAUM,SAASC,sBAAsBC,QAAQC,gBAAgBC,SAAShE,UAAU;AAE/E,MAAIiE,WAAW,CAAA;AAEf,MAAIH,kBAAkBI,SAAS;AAC7BD,eAAW,CAACH,MAAD;EACZ,WAAUA,kBAAkBK,YAAYC,MAAMC,QAAQP,MAAd,GAAuB;AAC9DG,eAAWG,MAAME,KAAKR,MAAX;EACZ,OAAM;AACL,UAAMS,WAAW,OAAOT,WAAW,WAAWA,SAASC;AACvD,QAAIQ,UAAU;AACZN,iBAAWG,MAAME,KAAKN,OAAOQ,iBAAiBD,QAAxB,CAAX;IACZ;EACF;AAED,SAAON;AACR;AAmBM,SAASQ,WAAW;AACzB,SAAO,CAAC,EAAEC,UAAUC,UAAUD,UAAUC,OAAOC,MAAM,QAAvB;AAC/B;ACtOD,IAAIC,kBAAkB;AAEtB,IAAI;AAEFC,SAAOC,iBAAiB,QAAQ,MAAMC,OAAOC,eAAe,CAAA,GAAI,WAAW;IACzEC,KAAK,MAAM;AACTL,wBAAkB;IACnB;EAHwE,CAArC,CAAtC;AAKD,SAAQtB,GAAG;AAAA;AAWZ,IAAM4B,YAAN,MAAgB;EACdC,cAAc;AAKZ,SAAKC,QAAQ,CAAA;EACd;;;;;;;;;EAUDC,IAAIC,QAAQC,MAAMC,UAAUC,SAAS;AACnC,SAAKC,gBAAgBJ,QAAQC,MAAMC,UAAUC,OAA7C;EACD;;;;;;;;;EAUDE,OAAOL,QAAQC,MAAMC,UAAUC,SAAS;AACtC,SAAKC,gBAAgBJ,QAAQC,MAAMC,UAAUC,SAAS,IAAtD;EACD;;;;EAKDG,YAAY;AACV,SAAKR,MAAMS,QAASC,cAAa;AAC/B,WAAKJ,gBACHI,SAASR,QACTQ,SAASP,MACTO,SAASN,UACTM,SAASL,SACT,MACA,IANF;KADF;AAUA,SAAKL,QAAQ,CAAA;EACd;;;;;;;;;;;;EAaDM,gBAAgBJ,QAAQC,MAAMC,UAAUC,SAASM,QAAQC,UAAU;AACjE,QAAI,CAACV,QAAQ;AACX;IACD;AAED,UAAMW,aAAaF,SAAS,wBAAwB;AACpD,UAAMG,QAAQX,KAAKY,MAAM,GAAX;AACdD,UAAML,QAASO,WAAU;AACvB,UAAIA,OAAO;AAGT,YAAI,CAACJ,UAAU;AACb,cAAID,QAAQ;AAEV,iBAAKX,QAAQ,KAAKA,MAAMiB,OAAQP,cAAa;AAC3C,qBAAOA,SAASP,SAASa,SACpBN,SAASN,aAAaA,YACtBM,SAASR,WAAWA;YAC1B,CAJY;UAKd,OAAM;AAEL,iBAAKF,MAAMkB,KAAK;cACdhB;cACAC,MAAMa;cACNZ;cACAC;aAJF;UAMD;QACF;AAID,cAAMc,eAAe3B,kBAAkB;UAAEa,SAAUA,WAAW;QAAvB,IAAkC;AAEzEH,eAAOW,UAAD,EACJG,OACAZ,UACAe,YAHF;MAKD;KAhCH;EAkCD;AAtGa;ACXT,SAASC,gBAAgBC,SAASC,OAAM;AAC7C,MAAID,QAAQE,mBAAmB;AAC7B,UAAMC,kBAAkBH,QAAQE,kBAAkBF,SAASC,KAAnC;AACxB,QAAIE,iBAAiB;AACnB,aAAOA;IACR;EACF;AAED,SAAO;IACLxG,GAAGL,SAAS8G,gBAAgBC;;;;;IAM5BzG,GAAGwE,OAAOkC;;AAEb;AAqCM,SAASC,mBAAmBpF,MAAM6E,SAASQ,cAAcC,UAAUC,OAAO;AAC/E,MAAIC,eAAe;AAEnB,MAAIX,QAAQY,WAAW;AACrBD,mBAAeX,QAAQY,UAAUJ,cAAcC,UAAUC,KAA1C,EAAiDvF,IAAjD;EAChB,WAAU6E,QAAQa,SAAS;AAC1BF,mBAAeX,QAAQa,QAAQ1F,IAAhB;EAChB,OAAM;AACL,UAAM2F,iBAAiB,YAAY3F,KAAK,CAAD,EAAI4F,YAAR,IAAwB5F,KAAK6F,MAAM,CAAX;AAE3D,QAAIhB,QAAQc,cAAD,GAAkB;AAE3BH,qBAAeX,QAAQc,cAAD;IACvB;EACF;AAED,SAAOG,OAAON,YAAD,KAAkB;AAChC;AASM,SAASO,eAAelB,SAASQ,cAAcC,UAAUC,OAAO;AACrE,SAAO;IACL/G,GAAG6G,aAAa7G,IACZ4G,mBAAmB,QAAQP,SAASQ,cAAcC,UAAUC,KAA1C,IAClBH,mBAAmB,SAASP,SAASQ,cAAcC,UAAUC,KAA3C;IACtB9G,GAAG4G,aAAa5G,IACZ2G,mBAAmB,OAAOP,SAASQ,cAAcC,UAAUC,KAAzC,IAClBH,mBAAmB,UAAUP,SAASQ,cAAcC,UAAUC,KAA5C;;AAEzB;ACzFD,IAAMS,YAAN,MAAgB;;;;EAIdzC,YAAY0C,OAAO;AACjB,SAAKA,QAAQA;AACb,SAAKC,gBAAgB;AACrB,SAAKC;IAA8B;MAAE3H,GAAG;MAAGC,GAAG;;AAC9C,SAAKc;IAA2B;MAAEf,GAAG;MAAGC,GAAG;;AAC3C,SAAKa;IAA2B;MAAEd,GAAG;MAAGC,GAAG;;EAC5C;;;;;;EAOD2H,OAAOF,eAAe;AACpB,SAAKA,gBAAgBA;AAErB,QAAI,CAAC,KAAKD,MAAM1F,OAAO;AACrB,WAAK8F,MAAL;IACD,OAAM;AACL,WAAKC,YAAY,GAAjB;AACA,WAAKA,YAAY,GAAjB;AACA,WAAKL,MAAMnB,KAAKyB,SAAS,cAAc;QAAEN,OAAO,KAAKA;OAArD;IACD;EACF;;;;;;EAODK,YAAYE,MAAM;AAChB,UAAM;MAAE1B,MAAAA;IAAF,IAAW,KAAKmB;AACtB,UAAMQ,SAAS,KAAKR,MAAMO,SAAS,MAAM,UAAU,QAApC,IAAgD,KAAKN;AACpE,UAAMQ,cAAcF,SAAS,MAAM,SAAS;AAC5C,UAAMd,UAAUN,mBACdsB,aACA5B,MAAKD,SACLC,MAAKO,cACL,KAAKY,MAAMU,MACX,KAAKV,MAAMV,KALqB;AAQlC,UAAMqB,cAAc,KAAKX,MAAMW,YAAYJ,IAAvB;AAIpB,SAAKL,OAAOK,IAAZ,IAAoB1H,KAAKC,OAAO6H,cAAcH,UAAU,CAApC,IAAyCf;AAG7D,SAAKnG,IAAIiH,IAAT,IAAkBC,SAASG,cACvB9H,KAAKC,MAAM6H,cAAcH,MAAzB,IAAmCf,UACnC,KAAKS,OAAOK,IAAZ;AAGJ,SAAKlH,IAAIkH,IAAT,IAAkBC,SAASG,cACvBlB,UACA,KAAKS,OAAOK,IAAZ;EACL;;EAGDH,QAAQ;AACN,SAAKF,OAAO3H,IAAI;AAChB,SAAK2H,OAAO1H,IAAI;AAChB,SAAKc,IAAIf,IAAI;AACb,SAAKe,IAAId,IAAI;AACb,SAAKa,IAAId,IAAI;AACb,SAAKc,IAAIb,IAAI;EACd;;;;;;;;EASDoI,WAAWL,MAAMM,WAAW;AAC1B,WAAO1H,MAAM0H,WAAW,KAAKvH,IAAIiH,IAAT,GAAgB,KAAKlH,IAAIkH,IAAT,CAA5B;EACb;AAlFa;ACVhB,IAAMO,kBAAkB;AAaxB,IAAMC,YAAN,MAAgB;;;;;;;EAOdzD,YAAYsB,SAASS,UAAUC,OAAOT,OAAM;AAC1C,SAAKA,OAAOA;AACZ,SAAKD,UAAUA;AACf,SAAKS,WAAWA;AAChB,SAAKC,QAAQA;AAEb,SAAKqB,cAAc;AAEnB,SAAKK,cAAc;AACnB,SAAKC,MAAM;AACX,SAAKC,OAAO;AACZ,SAAKC,QAAQ;AACb,SAAKC,UAAU;AACf,SAAKC,YAAY;AACjB,SAAK/H,MAAM;AACX,SAAKD,MAAM;EACZ;;;;;;;;;;EAWD8G,OAAOmB,UAAUC,WAAWZ,aAAa;AAEvC,UAAMK,cAAc;MAAEzI,GAAG+I;MAAU9I,GAAG+I;;AACtC,SAAKP,cAAcA;AACnB,SAAKL,cAAcA;AAEnB,UAAMa,SAASb,YAAYpI,IAAIyI,YAAYzI;AAC3C,UAAMkJ,SAASd,YAAYnI,IAAIwI,YAAYxI;AAE3C,SAAKyI,MAAMpI,KAAKQ,IAAI,GAAGmI,SAASC,SAASD,SAASC,MAAvC;AACX,SAAKP,OAAOrI,KAAKQ,IAAI,GAAGmI,SAASC,SAASD,SAASC,MAAvC;AAIZ,SAAKN,QAAQtI,KAAKQ,IAAI,GAAGoI,MAAZ;AAEb,SAAKL,UAAU,KAAKM,YAAL;AACf,SAAKL,YAAY,KAAKM,cAAL;AACjB,SAAKrI,MAAMT,KAAKS,IACd,KAAK8H,SACL,KAAKC,WACL,KAAKO,QAAL,CAHS;AAMX,SAAKvI,MAAMR,KAAKQ,IACd,KAAK4H,KACL,KAAKG,SACL,KAAKC,SAHI;AAMX,QAAI,KAAKxC,MAAM;AACb,WAAKA,KAAKyB,SAAS,oBAAoB;QAAEuB,YAAY;QAAMC,WAAW,KAAKzC;OAA3E;IACD;EACF;;;;;;;;EASD0C,sBAAsBC,cAAc;AAClC,UAAMC;;MACJD,eAAe;;AAEjB,UAAME,cAAc,KAAKtD,QAAQqD,UAAb;AAEpB,QAAI,CAACC,aAAa;AAChB;IACD;AAED,QAAI,OAAOA,gBAAgB,YAAY;AACrC,aAAOA,YAAY,IAAD;IACnB;AAED,QAAIA,gBAAgB,QAAQ;AAC1B,aAAO,KAAKhB;IACb;AAED,QAAIgB,gBAAgB,OAAO;AACzB,aAAO,KAAKjB;IACb;AAED,WAAOpB,OAAOqC,WAAD;EACd;;;;;;;;;;EAWDP,gBAAgB;AACd,QAAI1B,gBAAgB,KAAK8B,sBAAsB,WAA3B;AAEpB,QAAI9B,eAAe;AACjB,aAAOA;IACR;AAGDA,oBAAgBpH,KAAKQ,IAAI,GAAG,KAAK4H,MAAM,CAAvB;AAEhB,QAAI,KAAKD,eAAef,gBAAgB,KAAKe,YAAYzI,IAAIuI,iBAAiB;AAC5Eb,sBAAgBa,kBAAkB,KAAKE,YAAYzI;IACpD;AAED,WAAO0H;EACR;;;;;;;EAQDyB,cAAc;AACZ,WAAO,KAAKK,sBAAsB,SAA3B,KAAyC,KAAKd;EACtD;;;;;;;;;EAUDW,UAAU;AAGR,WAAO,KAAKG,sBAAsB,KAA3B,KAAqClJ,KAAKS,IAAI,GAAG,KAAK2H,MAAM,CAAvB;EAC7C;AArJa;ACuBhB,IAAMkB,QAAN,MAAY;;;;;;EAMV7E,YAAYoD,MAAMpB,OAAOT,OAAM;AAC7B,SAAK6B,OAAOA;AACZ,SAAKpB,QAAQA;AACb,SAAKT,OAAOA;AACZ,SAAKuD,WAAY9C,UAAUT,MAAKwD;AAChC,SAAKC,oBAAoB;AAEzB,SAAK3B,cAAc;MAAEpI,GAAG;MAAGC,GAAG;;AAE9B,SAAK+J,MAAM;MAAEhK,GAAG;MAAGC,GAAG;;AAEtB,SAAKgK,eAAgB,KAAKJ,YAAY,CAACvD,MAAK4D,OAAOC;AAEnD,SAAKb,aAAa,IAAId,UAAUlC,MAAKD,SAAS8B,MAAMpB,OAAOT,KAAzC;AAElB,SAAKA,KAAKyB,SAAS,eAAe;MAChCN,OAAO;MACPU,MAAM,KAAKA;MACXpB;KAHF;AAMA,SAAKqD,UAAU,KAAK9D,KAAK+D,cAAcC,kBAAkB,IAA1C;AACf,SAAKC,YAAYjL,cAAc,mBAAmB,KAApB;AAE9B,SAAKkL,gBAAgB;AAErB,SAAK9C,gBAAgB;AAErB,SAAK3F,QAAQ,KAAKqI,QAAQrI;AAE1B,SAAKC,SAAS,KAAKoI,QAAQpI;AAC3B,SAAKyI,gBAAgB;AACrB,SAAKC,SAAS,IAAIlD,UAAU,IAAd;AAEd,SAAKmD,qBAAqB;AAC1B,SAAKC,sBAAsB;AAE3B,SAAKtE,KAAKyB,SAAS,aAAa;MAAEN,OAAO;KAAzC;EACD;;;;;;EAODoD,YAAYhB,UAAU;AACpB,QAAIA,YAAY,CAAC,KAAKA,UAAU;AAE9B,WAAKiB,SAAL;IACD,WAAU,CAACjB,YAAY,KAAKA,UAAU;AAErC,WAAKkB,WAAL;IACD;EACF;;;;;;EAODC,OAAOR,eAAe;AACpB,SAAKA,gBAAgBA;AAErB,SAAKD,UAAUnJ,MAAM6J,kBAAkB;AAGvC,QAAI,CAAC,KAAK9C,MAAM;AACd;IACD;AAED,SAAK+C,cAAL;AAEA,SAAKC,KAAL;AACA,SAAKC,kBAAL;AACA,SAAKC,YAAL;AAEA,SAAKb,cAAc5K,YAAY,KAAK2K,SAApC;AAEA,SAAKe,oBAAL;AAEA,SAAKhF,KAAKyB,SAAS,gBAAgB;MAAEN,OAAO;KAA5C;AAEA,SAAK8D,oBAAL;AAEA,SAAKjF,KAAKyB,SAAS,mBAAmB;MAAEN,OAAO;KAA/C;AAEA,QAAI,KAAKoC,UAAU;AACjB,WAAKiB,SAAL;IACD;EACF;EAEDK,OAAO;AACL,SAAKf,QAAQe,KAAK,KAAlB;AACA,SAAK7E,KAAKyB,SAAS,aAAa;MAAEN,OAAO;KAAzC;EACD;;;;;;;EAQD4D,cAAc;AACZ,UAAM;MAAE/E,MAAAA;IAAF,IAAW;AACjB,UAAMkF,oBAAoB;AAG1B,QAAI,KAAKf,iBACF,CAACnE,MAAK4D,OAAOC,UACb7D,MAAKmF,WAAWC,UAAhB,KACC,CAAC,KAAK7B,YAAY,CAAC2B,mBAAoB;AAC7C;IACD;AAED,QAAI,KAAKlF,KAAKyB,SAAS,eAAe;MAAEN,OAAO;KAA3C,EAAmDkE,kBAAkB;AACvE;IACD;AAED,SAAKlB,gBAAgB;AAErB,SAAKL,QAAQY,OAAb;AAEA,SAAK1E,KAAKyB,SAAS,sBAAsB;MAAEN,OAAO;KAAlD;EACD;;;;;;;EAQDqD,WAAW;AACT,SAAKjB,WAAW;AAChB,SAAKwB,YAAL;AACA,SAAKjB,QAAQU,SAAb;AACA,SAAKxE,KAAKyB,SAAS,iBAAiB;MAAEN,OAAO;KAA7C;EACD;;;;;;EAODsD,aAAa;AACX,SAAKlB,WAAW;AAChB,SAAKO,QAAQW,WAAb;AAEA,QAAI,KAAKrD,kBAAkB,KAAK4B,WAAWT,SAAS;AAElD,WAAKqC,cAAL;IACD;AAGD,SAAKnB,oBAAoB;AACzB,SAAKuB,oBAAL;AACA,SAAKC,oBAAL;AACA,SAAKH,kBAAL;AAEA,SAAK9E,KAAKyB,SAAS,mBAAmB;MAAEN,OAAO;KAA/C;EACD;;;;;EAMDmE,UAAU;AACR,SAAKxB,QAAQyB,WAAW;AACxB,SAAKzB,QAAQ7E,OAAb;AACA,SAAKgF,UAAUhF,OAAf;AACA,SAAKe,KAAKyB,SAAS,gBAAgB;MAAEN,OAAO;KAA5C;EACD;EAEDqE,SAAS;AACP,QAAI,KAAKpE,kBAAkB,KAAK4B,WAAWT,WAAW,CAAC,KAAKgB,UAAU;AAKpE,WAAKqB,cAAL;AACA,WAAKnB,oBAAoB;AACzB,WAAKuB,oBAAL;AACA,WAAKC,oBAAL;AACA,WAAKH,kBAAL;IACD,OAAM;AAEL,WAAKF,cAAL;AACA,WAAKR,OAAO9C,OAAO,KAAKF,aAAxB;AACA,WAAKqE,MAAM,KAAK/B,IAAIhK,GAAG,KAAKgK,IAAI/J,CAAhC;IACD;EACF;;;;;;;EASDmL,kBAAkBY,OAAO;AAGvB,UAAMC,kBAAkB,KAAKlC,qBAAqB,KAAKT,WAAWT;AAElE,QAAI,CAACoD,iBAAiB;AACpB;IACD;AAED,UAAMlK,QAAQzB,KAAKC,MAAM,KAAKwB,QAAQkK,eAAxB,KAA4C,KAAK3F,KAAKO,aAAa7G;AACjF,UAAMgC,SAAS1B,KAAKC,MAAM,KAAKyB,SAASiK,eAAzB,KAA6C,KAAK3F,KAAKO,aAAa5G;AAEnF,QAAI,CAAC,KAAKiM,YAAYnK,OAAOC,MAAxB,KAAmC,CAACgK,OAAO;AAC9C;IACD;AACD,SAAK5B,QAAQ+B,iBAAiBpK,OAAOC,MAArC;EACD;;;;;EAMDkK,YAAYnK,OAAOC,QAAQ;AACzB,QAAID,UAAU,KAAK4I,sBACZ3I,WAAW,KAAK4I,qBAAqB;AAC1C,WAAKD,qBAAqB5I;AAC1B,WAAK6I,sBAAsB5I;AAC3B,aAAO;IACR;AAED,WAAO;EACR;;EAGDoK,wBAAwB;AAAA,QAAA;AACtB,YAAA,wBAAO,KAAKhC,QAAQiC,iBAApB,QAAA,0BAAA,SAAA,SAAO,sBAA0BC;EAClC;;;;;;;;;;EAWDC,OAAOC,eAAeC,aAAaC,oBAAoBC,cAAc;AACnE,UAAM;MAAErG,MAAAA;IAAF,IAAW;AACjB,QAAI,CAAC,KAAKsG,WAAL,KACEtG,MAAKmF,WAAWC,UAAhB,GAA6B;AAClC;IACD;AAEDpF,IAAAA,MAAKyB,SAAS,gBAAgB;MAC5ByE;MAAeC;MAAaC;IADA,CAA9B;AAKApG,IAAAA,MAAKuG,WAAWC,WAAhB;AAMA,UAAMC,gBAAgB,KAAKrF;AAE3B,QAAI,CAACiF,cAAc;AACjBH,sBAAgB5L,MAAM4L,eAAe,KAAKlD,WAAWxI,KAAK,KAAKwI,WAAWvI,GAArD;IACtB;AAMD,SAAKiM,aAAaR,aAAlB;AACA,SAAKxC,IAAIhK,IAAI,KAAKiN,yBAAyB,KAAKR,aAAaM,aAAhD;AACb,SAAK/C,IAAI/J,IAAI,KAAKgN,yBAAyB,KAAKR,aAAaM,aAAhD;AACb3M,eAAW,KAAK4J,GAAN;AAEV,UAAMkD,mBAAmB,MAAM;AAC7B,WAAKC,eAAeX,aAApB;AACA,WAAKjB,oBAAL;;AAGF,QAAI,CAACmB,oBAAoB;AACvBQ,uBAAgB;IACjB,OAAM;AACL5G,MAAAA,MAAKuG,WAAWO,gBAAgB;QAC9BC,OAAO;QACPC,MAAM;QACNpI,QAAQ,KAAKqF;QACblJ,WAAW,KAAKkM,oBAAL;QACXC,YAAYN;QACZzL,UAAUiL;QACVe,QAAQnH,MAAKD,QAAQoH;OAPvB;IASD;EACF;;;;EAKDC,WAAWjB,aAAa;AACtB,SAAKF,OACH,KAAK7E,kBAAkB,KAAK4B,WAAWT,UACnC,KAAKS,WAAWR,YAAY,KAAKQ,WAAWT,SAChD4D,aACA,KAAKnG,KAAKD,QAAQsH,qBAJpB;EAMD;;;;;;;EAQDX,aAAatF,eAAe;AAC1B,SAAKA,gBAAgBA;AACrB,SAAKgD,OAAO9C,OAAO,KAAKF,aAAxB;EACD;;;;;;;;;;;;;;EAeDuF,yBAAyBjF,MAAM4F,OAAOb,eAAe;AACnD,UAAMc,mBAAmB,KAAKnD,OAAO3J,IAAIiH,IAAhB,IAAwB,KAAK0C,OAAO5J,IAAIkH,IAAhB;AACjD,QAAI6F,qBAAqB,GAAG;AAC1B,aAAO,KAAKnD,OAAO/C,OAAOK,IAAnB;IACR;AAED,QAAI,CAAC4F,OAAO;AACVA,cAAQ,KAAKtH,KAAKwH,uBAAV;IACT;AAED,QAAI,CAACf,eAAe;AAClBA,sBAAgB,KAAKzD,WAAWT;IACjC;AAED,UAAMkF,aAAa,KAAKrG,gBAAgBqF;AACxC,WAAO,KAAKrC,OAAOrC,WACjBL,OACC,KAAKgC,IAAIhC,IAAT,IAAiB4F,MAAM5F,IAAD,KAAU+F,aAAaH,MAAM5F,IAAD,CAF9C;EAIR;;;;;;;EAQD+D,MAAMiC,MAAMC,MAAM;AAChB,SAAKjE,IAAIhK,IAAI,KAAK0K,OAAOrC,WAAW,KAAK2F,IAA5B;AACb,SAAKhE,IAAI/J,IAAI,KAAKyK,OAAOrC,WAAW,KAAK4F,IAA5B;AACb,SAAK1C,oBAAL;EACD;;;;;EAMD2C,aAAa;AACX,WAAOC,QAAQ,KAAKpM,KAAN,KAAiB,KAAK2F,gBAAgB,KAAK4B,WAAWZ;EACrE;;;;;EAMDkE,aAAa;AACX,WAAOuB,QAAQ,KAAKpM,KAAN,KAAgB,KAAKqI,QAAQwC,WAAb;EAC/B;;;;;EAMDrB,sBAAsB;AACpB,SAAK6C,oBAAoB,KAAKpE,IAAIhK,GAAG,KAAKgK,IAAI/J,GAAG,KAAKyH,aAAtD;AACA,QAAI,SAAS,KAAKpB,KAAK+H,WAAW;AAChC,WAAK/H,KAAKyB,SAAS,iBAAiB;QAAEN,OAAO;OAA7C;IACD;EACF;EAED6D,sBAAsB;AACpB,SAAK5D,gBAAgB,KAAK4B,WAAWT;AAGrC,SAAK6B,OAAO9C,OAAO,KAAKF,aAAxB;AACA7H,mBAAe,KAAKmK,KAAK,KAAKU,OAAO/C,MAAvB;AACd,SAAKrB,KAAKyB,SAAS,kBAAkB;MAAEN,OAAO;KAA9C;EACD;;;;;;;;;EAUD2G,oBAAoBpO,GAAGC,GAAGqO,MAAM;AAC9BA,YAAQ,KAAKvE,qBAAqB,KAAKT,WAAWT;AAClD1H,iBAAa,KAAKoJ,WAAWvK,GAAGC,GAAGqO,IAAvB;EACb;EAEDpD,gBAAgB;AACd,UAAM;MAAE5E,MAAAA;IAAF,IAAW;AAEjBzG,mBACE,KAAKuI,aACLb,eAAejB,MAAKD,SAASC,MAAKO,cAAc,KAAKsB,MAAM,KAAKpB,KAAlD,CAFF;AAKd,SAAKuC,WAAW1B,OAAO,KAAK7F,OAAO,KAAKC,QAAQ,KAAKoG,WAArD;AAEA9B,IAAAA,MAAKyB,SAAS,iBAAiB;MAC7BN,OAAO;KADT;EAGD;;EAGD8F,sBAAsB;AACpB,UAAMtM,QAAQ,KAAKyG,iBAAiB,KAAKqC,qBAAqB,KAAKT,WAAWT;AAC9E,WAAO7H,kBAAkB,KAAKgJ,IAAIhK,GAAG,KAAKgK,IAAI/J,GAAGgB,KAAzB;EACzB;;;;;;;;;;;;;;;;EAiBDkM,eAAeoB,eAAe;AAC5B,QAAIA,kBAAkB,KAAKxE,mBAAmB;AAC5C;IACD;AAED,SAAKA,oBAAoBwE;AACzB,SAAKnD,kBAAL;AAEA,SAAK9E,KAAKyB,SAAS,mBAAnB;EACD;AAndS;AC7BZ,IAAMyG,mBAAmB;AACzB,IAAMC,yBAAyB;AAG/B,IAAMC,qBAAqB;AAI3B,IAAMC,uBAAuB;AAO7B,SAASC,QAAQC,iBAAiBC,kBAAkB;AAClD,SAAOD,kBAAkBC,oBAAoB,IAAIA;AAClD;AAKD,IAAMC,cAAN,MAAkB;;;;EAIhBhK,YAAYiK,UAAU;AACpB,SAAKA,WAAWA;AAChB,SAAK1I,OAAO0I,SAAS1I;AAErB,SAAK2I,WAAW;MAAEjP,GAAG;MAAGC,GAAG;;EAC5B;EAEDiP,QAAQ;AACN,QAAI,KAAK5I,KAAK+H,WAAW;AACvBxO,qBAAe,KAAKoP,UAAU,KAAK3I,KAAK+H,UAAUrE,GAApC;IACf;AACD,SAAK1D,KAAKuG,WAAWsC,QAArB;EACD;EAEDC,SAAS;AACP,UAAM;MAAEtP;MAAIuP;MAAQC;IAAd,IAA2B,KAAKN;AACtC,UAAM;MAAEX;IAAF,IAAgB,KAAK/H;AAE3B,QAAIgJ,aAAa,OACV,KAAKhJ,KAAKD,QAAQkJ,uBACjBlB,aAAaA,UAAU3G,iBAAiB2G,UAAU/E,WAAWZ,OAC9D,CAAC,KAAKsG,SAASQ,cAAc;AAElC,YAAMvB,OAAOI,UAAUrE,IAAI/J,KAAKH,GAAGG,IAAIoP,OAAOpP;AAC9C,UAAI,CAAC,KAAKqG,KAAKyB,SAAS,gBAAgB;QAAEkG;OAArC,EAA6CtC,kBAAkB;AAClE,aAAK8D,oBAAoB,KAAKxB,MAAMQ,sBAApC;AACA,cAAMiB,YAAY,IAAIpP,KAAKG,IAAI,KAAKkP,sBAAsBtB,UAAUrE,IAAI/J,CAAzC,CAAT;AACtB,aAAKqG,KAAKsJ,eAAeF,SAAzB;AACArB,kBAAU9C,oBAAV;MACD;IACF,OAAM;AACL,YAAMsE,oBAAoB,KAAKC,qBAAqB,GAA1B;AAC1B,UAAI,CAACD,mBAAmB;AACtB,aAAKC,qBAAqB,GAA1B;AAEA,YAAIzB,WAAW;AACbjO,qBAAWiO,UAAUrE,GAAX;AACVqE,oBAAU9C,oBAAV;QACD;MACF;IACF;EACF;EAEDwE,MAAM;AACJ,UAAM;MAAEC;IAAF,IAAe,KAAKhB;AAC1B,UAAM;MAAEvD;MAAY4C;IAAd,IAA4B,KAAK/H;AACvC,QAAI2J,YAAY;AAEhB,SAAK3J,KAAKuG,WAAWsC,QAArB;AAGA,QAAI1D,WAAWC,UAAX,GAAwB;AAE1B,YAAMwE,sBAAsBzE,WAAWzL,IAAIyL,WAAW0E,cAAX;AAM3C,YAAMC,8BAA+BF,sBAAsB,KAAK5J,KAAKO,aAAa7G;AAUlF,UAAKgQ,SAAShQ,IAAI,CAAC2O,wBAAwByB,8BAA8B,KACjEJ,SAAShQ,IAAI,OAAOoQ,8BAA8B,MAAO;AAE/DH,oBAAY;AACZD,iBAAShQ,IAAIM,KAAKQ,IAAIkP,SAAShQ,GAAG,CAArB;iBACHgQ,SAAShQ,IAAI2O,wBAAwByB,8BAA8B,KACvEJ,SAAShQ,IAAI,QAAQoQ,8BAA8B,KAAM;AAE/DH,oBAAY;AACZD,iBAAShQ,IAAIM,KAAKS,IAAIiP,SAAShQ,GAAG,CAArB;MACd;AAEDyL,iBAAW4E,YAAYJ,WAAW,MAAMD,SAAShQ,CAAjD;IACD;AAGD,QAAKqO,aAAaA,UAAU3G,gBAAgB2G,UAAU/E,WAAWvI,OAC1D,KAAKiO,SAASQ,cAAc;AACjC,WAAKR,SAAS1F,WAAWgH,eAAe,IAAxC;IACD,OAAM;AAKL,WAAKC,yBAAyB,GAA9B;AACA,WAAKA,yBAAyB,GAA9B;IACD;EACF;;;;;EAMDA,yBAAyBvI,MAAM;AAC7B,UAAM;MAAEgI;IAAF,IAAe,KAAKhB;AAC1B,UAAM;MAAEX;IAAF,IAAgB,KAAK/H;AAE3B,QAAI,CAAC+H,WAAW;AACd;IACD;AAED,UAAM;MAAErE;MAAKU;IAAP,IAAkB2D;AACxB,UAAMmC,SAASxG,IAAIhC,IAAD;AAClB,UAAMyI,mBAAoB,KAAKnK,KAAKoJ,YAAY,KAAK1H,SAAS;AAI9D,UAAM8G,mBAAmB;AAGzB,UAAM4B,oBAAoBF,SAAS5B,QAAQoB,SAAShI,IAAD,GAAQ8G,gBAAjB;AAE1C,QAAI2B,kBAAkB;AACpB,YAAME,aAAa,KAAKhB,sBAAsBa,MAA3B;AACnB,YAAMI,sBAAsB,KAAKjB,sBAAsBe,iBAA3B;AAI5B,UAAKC,aAAa,KAAKC,sBAAsB,CAAClC,sBACtCiC,aAAa,KAAKC,sBAAsBlC,oBAAqB;AACnE,aAAKpI,KAAKuK,MAAV;AACA;MACD;IACF;AAGD,UAAMC,uBAAuBpG,OAAOrC,WAAWL,MAAM0I,iBAAxB;AAI7B,QAAIF,WAAWM,sBAAsB;AACnC;IACD;AAGD,UAAMC,eAAgBD,yBAAyBJ,oBAAqB,IAAI;AAExE,UAAMM,mBAAmB,KAAK1K,KAAKoJ;AACnC,UAAMuB,eAAeH,uBAAuBN;AAE5C,SAAKlK,KAAKuG,WAAWqE,YAAY;MAC/B5D,MAAM,eAAetF;MACrBqF,OAAO;MACP6B,OAAOsB;MACPT,KAAKe;MACLd,UAAUA,SAAShI,IAAD;MAClB+I;MACAI,UAAWC,SAAQ;AAEjB,YAAIX,oBAAoB,KAAKnK,KAAKoJ,YAAY,GAAG;AAE/C,gBAAM2B,yBAAyB,KAAKP,uBAAuBM,OAAOH;AAKlE,eAAK3K,KAAKsJ,eAAehP,MACvBoQ,oBAAoB,IAAIA,oBAAoBK,wBAC5C,GACA,CAH4B,CAA9B;QAKD;AAEDrH,YAAIhC,IAAD,IAAS1H,KAAKgR,MAAMF,GAAX;AACZ/C,kBAAU9C,oBAAV;MACD;KAzBH;EA2BD;;;;;;;;;;;EAYDuE,qBAAqB9H,MAAM;AACzB,UAAM;MAAElI;MAAIwP;MAAUD;MAAQG;IAAxB,IAAyC,KAAKR;AACpD,UAAM;MAAEX;MAAW5C;IAAb,IAA4B,KAAKnF;AACvC,UAAMiL,QAASzR,GAAGkI,IAAD,IAASqH,OAAOrH,IAAD;AAChC,UAAMwJ,iBAAiB/F,WAAWzL,IAAIuR;AAEtC,QAAI,CAACA,SAAS,CAAClD,WAAW;AACxB,aAAO;IACR;AAGD,QAAIrG,SAAS,OAAO,CAACqG,UAAUH,WAAV,KAA0B,CAACsB,cAAc;AAC5D/D,iBAAWgG,OAAOD,gBAAgB,IAAlC;AACA,aAAO;IACR;AAED,UAAM;MAAE9G;IAAF,IAAa2D;AACnB,UAAMqD,SAASrD,UAAUrE,IAAIhC,IAAd,IAAsBuJ;AAErC,QAAI,KAAKjL,KAAKD,QAAQsL,kBACfrC,aAAa,OACbtH,SAAS,OACT,CAACwH,cAAc;AACpB,YAAMoC,uBAAuBnG,WAAW0E,cAAX;AAG7B,YAAMD,sBAAsBzE,WAAWzL,IAAI4R;AAE3C,YAAMC,gBAAgBN,QAAQ;AAC9B,YAAMO,gBAAgB,CAACD;AAEvB,UAAIH,SAAShH,OAAO5J,IAAIkH,IAAX,KAAoB6J,eAAe;AAM9C,cAAME,sBAAuBrH,OAAO5J,IAAIkH,IAAX,KAAoB,KAAKiH,SAASjH,IAAd;AAEjD,YAAI+J,qBAAqB;AACvBtG,qBAAWgG,OAAOD,gBAAgB,IAAlC;AACA,iBAAO;QACR,OAAM;AACL,eAAK/B,oBAAoBzH,MAAM0J,MAA/B;QAED;MACF,WAAUA,SAAShH,OAAO3J,IAAIiH,IAAX,KAAoB8J,eAAe;AAIrD,cAAME,sBAAuB,KAAK/C,SAASjH,IAAd,KAAuB0C,OAAO3J,IAAIiH,IAAX;AAEpD,YAAIgK,qBAAqB;AACvBvG,qBAAWgG,OAAOD,gBAAgB,IAAlC;AACA,iBAAO;QACR,OAAM;AACL,eAAK/B,oBAAoBzH,MAAM0J,MAA/B;QAED;MACF,OAAM;AAEL,YAAIxB,wBAAwB,GAAG;AAE7B,cAAIA,sBAAsB,GAAwB;AAChDzE,uBAAWgG,OAAOnR,KAAKS,IAAIyQ,gBAAgBI,oBAAzB,GAAgD,IAAlE;AACA,mBAAO;qBACE1B,sBAAsB,GAAwB;AAEvDzE,uBAAWgG,OAAOnR,KAAKQ,IAAI0Q,gBAAgBI,oBAAzB,GAAgD,IAAlE;AACA,mBAAO;UACR;QACF,OAAM;AAEL,eAAKnC,oBAAoBzH,MAAM0J,MAA/B;QACD;MACF;IACF,OAAM;AACL,UAAI1J,SAAS,KAAK;AAEhB,YAAI,CAACyD,WAAWC,UAAX,KAA0BhB,OAAO5J,IAAIb,MAAMyK,OAAO3J,IAAId,GAAG;AAC5D,eAAKwP,oBAAoBzH,MAAM0J,MAA/B;QACD;MACF,OAAM;AACL,aAAKjC,oBAAoBzH,MAAM0J,MAA/B;MACD;IACF;AAED,WAAO;EACR;;;;;;;;;;;;;;EAgBD/B,sBAAsB1B,MAAM;AAAA,QAAA,uBAAA;AAC1B,YAAQA,SAAI,yBAAA,uBAAI,KAAK3H,KAAK+H,eAAV,QAAA,yBAAA,SAAA,SAAA,qBAAqB3D,OAAO/C,OAAO1H,OAAK,QAAA,0BAAA,SAAA,wBAAA,OAAO,KAAKqG,KAAKO,aAAa5G,IAAI;EAC3F;;;;;;;;;;;EAYDwP,oBAAoBzH,MAAMiK,cAAcC,gBAAgB;AACtD,UAAM;MAAE7D;IAAF,IAAgB,KAAK/H;AAE3B,QAAI,CAAC+H,WAAW;AACd;IACD;AAED,UAAM;MAAErE;MAAKU;IAAP,IAAkB2D;AACxB,UAAM8D,eAAezH,OAAOrC,WAAWL,MAAMiK,YAAxB;AAErB,QAAIE,iBAAiBF,gBAAgBC,gBAAgB;AACnD,YAAMX,QAAQjR,KAAKC,MAAM0R,eAAejI,IAAIhC,IAAD,CAA7B;AACdgC,UAAIhC,IAAD,KAAUuJ,SAASW,kBAAkB1D;IACzC,OAAM;AACLxE,UAAIhC,IAAD,IAASiK;IACb;EACF;AAtUe;ACtBlB,IAAMG,sBAAsB;AAC5B,IAAMC,sBAAsB;AAW5B,SAASC,oBAAoBjS,GAAGP,IAAIC,IAAI;AACtCM,IAAEL,KAAKF,GAAGE,IAAID,GAAGC,KAAK;AACtBK,IAAEJ,KAAKH,GAAGG,IAAIF,GAAGE,KAAK;AACtB,SAAOI;AACR;AAED,IAAMkS,cAAN,MAAkB;;;;EAIhBxN,YAAYiK,UAAU;AACpB,SAAKA,WAAWA;AAKhB,SAAKwD,YAAY;MAAExS,GAAG;MAAGC,GAAG;;AAK5B,SAAKwS,kBAAkB;MAAEzS,GAAG;MAAGC,GAAG;;AAKlC,SAAKyS,aAAa;MAAE1S,GAAG;MAAGC,GAAG;;AAE7B,SAAK0S,uBAAuB;AAE5B,SAAKC,kBAAkB;EACxB;EAED1D,QAAQ;AACN,UAAM;MAAEb;QAAc,KAAKW,SAAS1I;AACpC,QAAI+H,WAAW;AACb,WAAKuE,kBAAkBvE,UAAU3G;AACjC7H,qBAAe,KAAK2S,WAAWnE,UAAUrE,GAA3B;IACf;AAED,SAAKgF,SAAS1I,KAAKuG,WAAWC,WAA9B;AACA,SAAK6F,uBAAuB;EAC7B;EAEDvD,SAAS;AACP,UAAM;MAAEtP;MAAI+S;MAAS9S;MAAI+S;MAASxM,MAAAA;IAA5B,IAAqC,KAAK0I;AAChD,UAAM;MAAEX;IAAF,IAAgB/H;AAEtB,QAAI,CAAC+H,WAAW;AACd;IACD;AAED,UAAM0E,eAAe1E,UAAU/E,WAAWxI;AAC1C,UAAMkS,eAAe3E,UAAU/E,WAAWvI;AAE1C,QAAI,CAACsN,UAAUzB,WAAV,KAA0BtG,MAAKmF,WAAWC,UAAhB,GAA6B;AAC1D;IACD;AAED4G,wBAAoB,KAAKG,iBAAiBI,SAASC,OAAhC;AACnBR,wBAAoB,KAAKI,YAAY5S,IAAIC,EAAtB;AAEnB,QAAI2H,gBAAiB,IAAIlH,mBAAmBqS,SAASC,OAAV,IACvBtS,mBAAmBV,IAAIC,EAAL,IAClB,KAAK6S;AAGzB,QAAIlL,gBAAgB2G,UAAU/E,WAAWT,UAAWwF,UAAU/E,WAAWT,UAAU,IAAK;AACtF,WAAK8J,uBAAuB;IAC7B;AAED,QAAIjL,gBAAgBqL,cAAc;AAChC,UAAIzM,MAAKD,QAAQ4M,gBACV,CAAC,KAAKN,wBACN,KAAKC,mBAAmBvE,UAAU/E,WAAWT,SAAS;AAE3D,cAAM6G,YAAY,KAAMqD,eAAerL,kBAAkBqL,eAAe;AACxE,YAAI,CAACzM,MAAKyB,SAAS,cAAc;UAAE2H;SAA9B,EAA2C/D,kBAAkB;AAChErF,UAAAA,MAAKsJ,eAAeF,SAApB;QACD;MACF,OAAM;AAELhI,wBAAgBqL,gBAAgBA,eAAerL,iBAAiB2K;MACjE;IACF,WAAU3K,gBAAgBsL,cAAc;AAEvCtL,sBAAgBsL,gBAAgBtL,gBAAgBsL,gBAAgBZ;IACjE;AAED/D,cAAUrE,IAAIhK,IAAI,KAAKkT,0BAA0B,KAAKxL,aAApC;AAClB2G,cAAUrE,IAAI/J,IAAI,KAAKiT,0BAA0B,KAAKxL,aAApC;AAElB2G,cAAUrB,aAAatF,aAAvB;AACA2G,cAAU9C,oBAAV;EACD;EAEDwE,MAAM;AACJ,UAAM;MAAEzJ,MAAAA;IAAF,IAAW,KAAK0I;AACtB,UAAM;MAAEX;IAAF,IAAgB/H;AACtB,SAAK,CAAC+H,aAAaA,UAAU3G,gBAAgB2G,UAAU/E,WAAWT,YAC3D,CAAC,KAAK8J,wBACNrM,MAAKD,QAAQ4M,cAAc;AAChC3M,MAAAA,MAAKuK,MAAL;IACD,OAAM;AACL,WAAKP,eAAL;IACD;EACF;;;;;;;EAQD4C,0BAA0BlL,MAAMN,eAAe;AAC7C,UAAMqG,aAAarG,gBAAgB,KAAKkL;AACxC,WAAO,KAAKF,WAAW1K,IAAhB,KACK,KAAKyK,gBAAgBzK,IAArB,IAA6B,KAAKwK,UAAUxK,IAAf,KAAwB+F;EAClE;;;;;;;;;EAUDuC,eAAe6C,eAAe;AAC5B,UAAM;MAAE7M,MAAAA;IAAF,IAAW,KAAK0I;AACtB,UAAM;MAAEX;IAAF,IAAgB/H;AAEtB,QAAI,EAAC+H,cAAD,QAACA,cAAD,UAACA,UAAWzB,WAAX,IAAyB;AAC5B;IACD;AAED,QAAI,KAAK8F,WAAW1S,MAAM,GAAG;AAC3BmT,sBAAgB;IACjB;AAED,UAAMpG,gBAAgBsB,UAAU3G;AAGhC,QAAI0L;AACJ,QAAIC,2BAA2B;AAE/B,QAAItG,gBAAgBsB,UAAU/E,WAAWT,SAAS;AAChDuK,6BAAuB/E,UAAU/E,WAAWT;eAEnCkE,gBAAgBsB,UAAU/E,WAAWvI,KAAK;AACnDqS,6BAAuB/E,UAAU/E,WAAWvI;IAE7C,OAAM;AACLsS,iCAA2B;AAC3BD,6BAAuBrG;IACxB;AAED,UAAMiE,mBAAmB1K,MAAKoJ;AAC9B,UAAMe,mBAAmBnK,MAAKoJ,YAAY;AAE1C,UAAM4D,aAAazT,eAAe;MAAEG,GAAG;MAAGC,GAAG;IAAX,GAAgBoO,UAAUrE,GAA3B;AACjC,QAAIuJ,iBAAiB1T,eAAe;MAAEG,GAAG;MAAGC,GAAG;OAAKqT,UAAjB;AAEnC,QAAIH,eAAe;AACjB,WAAKT,WAAW1S,IAAI;AACpB,WAAK0S,WAAWzS,IAAI;AACpB,WAAKwS,gBAAgBzS,IAAI;AACzB,WAAKyS,gBAAgBxS,IAAI;AACzB,WAAK2S,kBAAkB7F;AACvBlN,qBAAe,KAAK2S,WAAWc,UAAjB;IACf;AAED,QAAID,0BAA0B;AAC5BE,uBAAiB;QACfvT,GAAG,KAAKkT,0BAA0B,KAAKE,oBAApC;QACHnT,GAAG,KAAKiT,0BAA0B,KAAKE,oBAApC;;IAEN;AAGD/E,cAAUrB,aAAaoG,oBAAvB;AAEAG,qBAAiB;MACfvT,GAAGqO,UAAU3D,OAAOrC,WAAW,KAAKkL,eAAevT,CAAhD;MACHC,GAAGoO,UAAU3D,OAAOrC,WAAW,KAAKkL,eAAetT,CAAhD;IAFY;AAMjBoO,cAAUrB,aAAaD,aAAvB;AAEA,UAAMyG,iBAAiB,CAAC7S,YAAY4S,gBAAgBD,UAAjB;AAEnC,QAAI,CAACE,kBAAkB,CAACH,4BAA4B,CAAC5C,kBAAkB;AAErEpC,gBAAUlB,eAAeiG,oBAAzB;AACA/E,gBAAU9C,oBAAV;AAGA;IACD;AAEDjF,IAAAA,MAAKuG,WAAWC,WAAhB;AAEAxG,IAAAA,MAAKuG,WAAWqE,YAAY;MAC1B7D,OAAO;MACP6B,OAAO;MACPa,KAAK;MACLC,UAAU;MACVe,cAAc;MACd0C,kBAAkB;MAClBtC,UAAWuC,SAAQ;AACjBA,eAAO;AAEP,YAAIF,kBAAkBH,0BAA0B;AAC9C,cAAIG,gBAAgB;AAClBnF,sBAAUrE,IAAIhK,IAAIsT,WAAWtT,KAAKuT,eAAevT,IAAIsT,WAAWtT,KAAK0T;AACrErF,sBAAUrE,IAAI/J,IAAIqT,WAAWrT,KAAKsT,eAAetT,IAAIqT,WAAWrT,KAAKyT;UACtE;AAED,cAAIL,0BAA0B;AAC5B,kBAAMM,eAAe5G,iBACNqG,uBAAuBrG,iBAAiB2G;AACvDrF,sBAAUrB,aAAa2G,YAAvB;UACD;AAEDtF,oBAAU9C,oBAAV;QACD;AAGD,YAAIkF,oBAAoBnK,MAAKoJ,YAAY,GAAG;AAI1CpJ,UAAAA,MAAKsJ,eAAehP,MAClBoQ,oBAAoB,IAAIA,oBAAoB0C,KAAK,GAAG,CAD7B,CAAzB;QAGD;;MAEHlG,YAAY,MAAM;AAEhBa,kBAAUlB,eAAeiG,oBAAzB;AACA/E,kBAAU9C,oBAAV;MACD;KAvCH;EAyCD;AA9Oe;ACPlB,SAASqI,oBAAoBC,OAAO;AAClC,SAAO,CAAC;EAA8BA,MAAM3O,OAAQ4O,QAAQ,kBAAvB;AACtC;AAKD,IAAMC,aAAN,MAAiB;;;;EAIfhP,YAAYiK,UAAU;AACpB,SAAKA,WAAWA;EACjB;;;;;EAMDgF,MAAMpG,OAAOqG,eAAe;AAC1B,UAAMC;;MAA8CD,cAAc/O,OAAQiP;;AAC1E,UAAMC,eAAeF,gBAAgBG,SAAS,WAAzB;AACrB,UAAMC,oBAAoBJ,gBAAgBG,SAAS,YAAzB,KACGH,gBAAgBG,SAAS,iBAAzB;AAE7B,QAAID,cAAc;AAChB,WAAKG,oBAAoB,cAAc3G,OAAOqG,aAA9C;eACSK,mBAAmB;AAC5B,WAAKC,oBAAoB,WAAW3G,OAAOqG,aAA3C;IACD;EACF;;;;;EAMDO,IAAI5G,OAAOqG,eAAe;AACxB,QAAIL,oBAAoBK,aAAD,GAAiB;AACtC,WAAKM,oBAAoB,OAAO3G,OAAOqG,aAAvC;IACD;EACF;;;;;EAMDQ,UAAU7G,OAAOqG,eAAe;AAC9B,QAAIL,oBAAoBK,aAAD,GAAiB;AACtC,WAAKM,oBAAoB,aAAa3G,OAAOqG,aAA7C;IACD;EACF;;;;;;;EAQDM,oBAAoBG,YAAY9G,OAAOqG,eAAe;AAAA,QAAA;AACpD,UAAM;MAAE3N,MAAAA;IAAF,IAAW,KAAK0I;AACtB,UAAM;MAAEX;IAAF,IAAgB/H;AACtB,UAAMqO;;MAA+DD,aAAa;;AAClF,UAAM/K,cAAcrD,MAAKD,QAAQsO,cAAb;AAEpB,QAAIrO,MAAKyB,SAAS4M,gBAAgB;MAAE/G;MAAOqG;KAAvC,EAAwDtI,kBAAkB;AAC5E;IACD;AAED,QAAI,OAAOhC,gBAAgB,YAAY;AACrCA,kBAAYiL,KAAKtO,OAAMsH,OAAOqG,aAA9B;AACA;IACD;AAED,YAAQtK,aAAR;MACE,KAAK;MACL,KAAK;AACHrD,QAAAA,MAAKqD,WAAD,EAAJ;AACA;MACF,KAAK;AACH0E,sBAAS,QAATA,cAAS,UAATA,UAAWX,WAAWE,KAAtB;AACA;MACF,KAAK;AAGH,YAAIS,cAAS,QAATA,cAAS,UAATA,UAAWzB,WAAX,KACGyB,UAAU/E,WAAWR,cAAcuF,UAAU/E,WAAWT,SAAS;AACtEwF,oBAAUX,WAAWE,KAArB;QACD,WAAUtH,MAAKD,QAAQwO,yBAAyB;AAC/CvO,UAAAA,MAAKuK,MAAL;QACD;AACD;MACF,KAAK;AACH,SAAA,wBAAA,KAAK7B,SAAS1I,KAAKgG,aAAnB,QAAA,0BAAA,UAAA,sBAA4B6H,UAAUW,OAAO,kBAA7C;AAMA;IAzBJ;EA2BD;AA7Fc;ACZjB,IAAMC,wBAAwB;AAG9B,IAAMC,mBAAmB;AACzB,IAAMC,mBAAmB;AASzB,IAAMC,WAAN,MAAe;;;;EAIbnQ,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AAGZ,SAAKgJ,WAAW;AAKhB,SAAKxP,KAAK;MAAEE,GAAG;MAAGC,GAAG;IAAX;AAEV,SAAKF,KAAK;MAAEC,GAAG;MAAGC,GAAG;IAAX;AAEV,SAAKoP,SAAS;MAAErP,GAAG;MAAGC,GAAG;;AAEzB,SAAKkV,SAAS;MAAEnV,GAAG;MAAGC,GAAG;;AAEzB,SAAK4S,UAAU;MAAE7S,GAAG;MAAGC,GAAG;;AAE1B,SAAK6S,UAAU;MAAE9S,GAAG;MAAGC,GAAG;;AAE1B,SAAK+P,WAAW;MAAEhQ,GAAG;MAAGC,GAAG;;AAK3B,SAAKmV,eAAe;MAAEpV,GAAG;MAAGC,GAAG;;AAI/B,SAAKoV,cAAc;MAAErV,GAAG;MAAGC,GAAG;;AAE9B,SAAKqV,mBAAmB;AAIxB,SAAKC,mBAAmB,CAAA;AAExB,SAAKC,qBAAqB,kBAAkB/Q;AAE5C,SAAKgR,uBAAuB,CAAC,CAAEhR,OAAOiR;AACtC,SAAKC,gBAAgB,KAAKH,sBACA,KAAKC,wBAAwBpR,UAAUuR,iBAAiB;AAElF,SAAKN,mBAAmB;AAExB,SAAKO,gBAAgB;AAErB,SAAKC,sBAAsB;AAC3B,SAAKtG,eAAe;AACpB,SAAKuG,aAAa;AAClB,SAAKC,YAAY;AAEjB,SAAKC,MAAM;AAIX,SAAKC,YAAY;AAEjB,QAAI,CAAC,KAAKP,eAAe;AAEvBrP,MAAAA,MAAKD,QAAQsL,iBAAiB;IAC/B;AAED,SAAKwE,OAAO,IAAIpH,YAAY,IAAhB;AACZ,SAAKzF,aAAa,IAAIiJ,YAAY,IAAhB;AAClB,SAAK6D,aAAa,IAAIrC,WAAW,IAAf;AAElBzN,IAAAA,MAAK+P,GAAG,cAAc,MAAM;AAC1B/P,MAAAA,MAAKgQ,OAAOrR;QACVqB,MAAKiQ;QACL;;QAC2B,KAAKC,SAASC,KAAK,IAAnB;MAH7B;AAMA,UAAI,KAAKhB,sBAAsB;AAC7B,aAAKiB,YAAY,WAAW,QAAQ,MAAM,QAA1C;MACD,WAAU,KAAKlB,oBAAoB;AAClC,aAAKkB,YAAY,SAAS,SAAS,OAAO,QAA1C;AAUA,YAAIpQ,MAAKiQ,YAAY;AACnBjQ,UAAAA,MAAKiQ,WAAWI,cAAc,MAAM;UAAA;AACpCrQ,UAAAA,MAAKiQ,WAAWK,aAAa,MAAM;UAAA;QACpC;MACF,OAAM;AACL,aAAKF,YAAY,SAAS,QAAQ,IAAlC;MACD;KA1BH;EA4BD;;;;;;;;EASDA,YAAYG,MAAMC,MAAMC,IAAIC,QAAQ;AAClC,UAAM;MAAE1Q,MAAAA;IAAF,IAAW;AACjB,UAAM;MAAEgQ;IAAF,IAAahQ;AAEnB,UAAM2Q,cAAcD,SAASH,OAAOG,SAAS;AAE7CV,WAAOrR;MACLqB,MAAKiQ;MACLM,OAAOC;;MACoB,KAAKI,cAAcT,KAAK,IAAxB;IAH7B;AAKAH,WAAOrR;MAAIR;MAAQoS,OAAO;;MAAmC,KAAKM,cAAcV,KAAK,IAAxB;IAA7D;AACAH,WAAOrR;MAAIR;MAAQoS,OAAOE;;MAA+B,KAAKK,YAAYX,KAAK,IAAtB;IAAzD;AACA,QAAIQ,aAAa;AACfX,aAAOrR;QACLqB,MAAKiQ;QACLU;;QAC2B,KAAKG,YAAYX,KAAK,IAAtB;MAH7B;IAKD;EACF;;;;EAKDS,cAAchU,GAAG;AAOf,UAAMmU,iBAAiBnU,EAAEiC,SAAS,eAAejC,EAAEoU,gBAAgB;AAKnE,QAAID,kBAAkBnU,EAAEC,SAAS,GAAG;AAClC;IACD;AAED,UAAM;MAAEmD,MAAAA;QAAS;AAGjB,QAAI,CAACA,MAAK4D,OAAOC,QAAQ;AACvBjH,QAAEqU,eAAF;AACA;IACD;AAED,QAAIjR,MAAKyB,SAAS,eAAe;MAAEkM,eAAe/Q;KAA9C,EAAmDyI,kBAAkB;AACvE;IACD;AAED,QAAI0L,gBAAgB;AAClB/Q,MAAAA,MAAKkR,cAAL;AAIA,WAAKC,8BAA8BvU,GAAG,MAAtC;IACD;AAEDoD,IAAAA,MAAKuG,WAAWsC,QAAhB;AAEA,SAAKuI,cAAcxU,GAAG,MAAtB;AAEA,QAAI,KAAKoS,qBAAqB,GAAG;AAC/B,WAAKhG,WAAW;AAGhBzP,qBAAe,KAAKgT,SAAS,KAAK/S,EAApB;IACf;AAED,QAAI,KAAKwV,mBAAmB,GAAG;AAE7B,WAAKqC,eAAL;AACA,WAAKnI,eAAe;IACrB,OAAM;AACL,WAAKA,eAAe;IACrB;EACF;;;;EAKD2H,cAAcjU,GAAG;AACf,SAAKuU,8BAA8BvU,GAAG,MAAtC;AAEA,QAAI,CAAC,KAAKoS,kBAAkB;AAC1B;IACD;AAED,SAAKoC,cAAcxU,GAAG,MAAtB;AAEA,QAAI,KAAKoD,KAAKyB,SAAS,eAAe;MAAEkM,eAAe/Q;KAAnD,EAAwDyI,kBAAkB;AAC5E;IACD;AAED,QAAI,KAAK2J,qBAAqB,KAAK,CAAC,KAAKS,YAAY;AACnD,UAAI,CAAC,KAAKzG,UAAU;AAClB,aAAKsI,wBAAL;MACD;AAGD,UAAI,KAAKtI,YAAY,CAAC,KAAKyG,YAAY;AACrC,YAAI,KAAKC,WAAW;AAClB,eAAKA,YAAY;AACjB,eAAK1M,WAAWyG,IAAhB;QACD;AAED,aAAKgG,aAAa;AAClB,aAAK4B,eAAL;AAGA,aAAKE,mBAAL;AACA,aAAKhC,gBAAgBiC,KAAKpE,IAAL;AAErB,aAAKoC,sBAAsB;AAC3BjW,uBAAe,KAAKwV,aAAa,KAAKvV,EAAxB;AACd,aAAKkQ,SAAShQ,IAAI;AAClB,aAAKgQ,SAAS/P,IAAI;AAClB,aAAKkW,KAAKjH,MAAV;AAEA,aAAK6I,aAAL;AACA,aAAKC,eAAL;MACD;eACQ,KAAK1C,mBAAmB,KAAK,CAAC,KAAKU,WAAW;AACvD,WAAKiC,YAAL;AAEA,WAAKjC,YAAY;AAGjB,WAAK6B,mBAAL;AAEA,WAAKvO,WAAW4F,MAAhB;AAEA,WAAK6I,aAAL;AACA,WAAKC,eAAL;IACD;EACF;;;;EAKDC,cAAc;AACZ,QAAI,KAAKlC,YAAY;AACnB,WAAKA,aAAa;AAIlB,UAAI,CAAC,KAAKD,qBAAqB;AAC7B,aAAKoC,gBAAgB,IAArB;MACD;AAED,WAAK/B,KAAKpG,IAAV;AACA,WAAKT,WAAW;IACjB;EACF;;;;EAKD8H,YAAYlU,GAAG;AACb,QAAI,CAAC,KAAKoS,kBAAkB;AAC1B;IACD;AAED,SAAKoC,cAAcxU,GAAG,IAAtB;AAEA,QAAI,KAAKoD,KAAKyB,SAAS,aAAa;MAAEkM,eAAe/Q;KAAjD,EAAsDyI,kBAAkB;AAC1E;IACD;AAED,QAAI,KAAK2J,qBAAqB,GAAG;AAC/B,WAAKyC,aAAL;AAEA,UAAI,KAAKhC,YAAY;AACnB,aAAKkC,YAAL;iBACS,CAAC,KAAKjC,aAAa,CAAC,KAAKxG,cAAc;AAEhD,aAAK2I,WAAWjV,CAAhB;MACD;IACF;AAED,QAAI,KAAKoS,mBAAmB,KAAK,KAAKU,WAAW;AAC/C,WAAKA,YAAY;AACjB,WAAK1M,WAAWyG,IAAhB;AAEA,UAAI,KAAKuF,qBAAqB,GAAG;AAE/B,aAAKhG,WAAW;AAChB,aAAKuI,mBAAL;MACD;IACF;EACF;;;;EAKDG,iBAAiB;AACf,QAAI,KAAKjC,cAAc,KAAKC,WAAW;AACrC,WAAKkC,gBAAL;AAEA,UAAI,KAAKnC,YAAY;AAEnB,YAAI,CAACpV,YAAY,KAAKb,IAAI,KAAKuP,MAAf,GAAwB;AACtC,eAAK8G,KAAK/G,OAAV;QACD;aAC8B;AAC/B,YAAI,CAACzO,YAAY,KAAKb,IAAI,KAAKuP,MAAf,KACT,CAAC1O,YAAY,KAAKZ,IAAI,KAAKoV,MAAf,GAAwB;AACzC,eAAK7L,WAAW8F,OAAhB;QACD;MACF;AAED,WAAKgJ,kBAAL;AACA,WAAKnC,MAAMoC,sBAAsB,KAAKL,eAAevB,KAAK,IAAzB,CAAD;IACjC;EACF;;;;;;;EAQDyB,gBAAgBlM,OAAO;AACrB,UAAMsM,OAAOR,KAAKpE,IAAL;AACb,UAAMjS,WAAW6W,OAAO,KAAKzC;AAE7B,QAAIpU,WAAW,MAAM,CAACuK,OAAO;AAC3B;IACD;AAGD,SAAKgE,SAAShQ,IAAI,KAAKuY,aAAa,KAAK9W,QAAvB;AAClB,SAAKuO,SAAS/P,IAAI,KAAKsY,aAAa,KAAK9W,QAAvB;AAElB,SAAKoU,gBAAgByC;AACrBzY,mBAAe,KAAKwV,aAAa,KAAKvV,EAAxB;AACd,SAAKgW,sBAAsB;EAC5B;;;;;EAMDqC,WAAWjV,GAAG;AACZ,UAAM;MAAEuI;QAAe,KAAKnF;AAG5B,QAAImF,WAAWC,UAAX,GAAwB;AAG1BD,iBAAW4E,YAAY,GAAG,IAA1B;AACA;IACD;AAGD,QAAInN,EAAEiC,KAAKqT,QAAQ,QAAf,IAA2B,GAAG;AAChC;IACD;AAGD,QAAItV,EAAEiC,SAAS,aAAajC,EAAEoU,gBAAgB,SAAS;AACrD,WAAKlB,WAAWpC,MAAM,KAAKnB,SAAS3P,CAApC;AACA;IACD;AAGD,UAAMuV,WAAW,KAAKnS,KAAKD,QAAQqS,kBAAkB1D,mBAAmB;AAKxE,QAAI,KAAKkB,WAAW;AAClB,WAAKyB,eAAL;AAEA,UAAInX,mBAAmB,KAAK4U,cAAc,KAAKvC,OAAzB,IAAoCoC,kBAAkB;AAC1E,aAAKmB,WAAW3B,UAAU,KAAK5B,SAAS3P,CAAxC;MACD;IACF,OAAM;AACLrD,qBAAe,KAAKuV,cAAc,KAAKvC,OAAzB;AACd,WAAKqD,YAAYyC,WAAW,MAAM;AAChC,aAAKvC,WAAW5B,IAAI,KAAK3B,SAAS3P,CAAlC;AACA,aAAKyU,eAAL;SACCc,QAHwB;IAI5B;EACF;;;;EAKDd,iBAAiB;AACf,QAAI,KAAKzB,WAAW;AAClB0C,mBAAa,KAAK1C,SAAN;AACZ,WAAKA,YAAY;IAClB;EACF;;;;;;;;;EAUDqC,aAAavQ,MAAMvG,UAAU;AAE3B,UAAMoX,eAAe,KAAK/Y,GAAGkI,IAAR,IAAgB,KAAKqN,YAAYrN,IAAjB;AAErC,QAAI1H,KAAKG,IAAIoY,YAAT,IAAyB,KAAKpX,WAAW,GAAG;AAC9C,aAAOoX,eAAepX;IACvB;AAED,WAAO;EACR;;;;EAKDsW,eAAe;AACb,QAAI,KAAK9B,KAAK;AACZ6C,2BAAqB,KAAK7C,GAAN;AACpB,WAAKA,MAAM;IACZ;EACF;;;;;;EAODwB,8BAA8BvU,GAAGoU,aAAa;AAC5C,UAAMyB,sBAAsB,KAAKzS,KAAK0S,aAAa,uBAAuB,MAAM9V,GAAGoU,WAAvD;AAC5B,QAAIyB,qBAAqB;AACvB7V,QAAEqU,eAAF;IACD;EACF;;;;;;;;;EAUDG,cAAcxU,GAAGoU,aAAa;AAC5B,QAAI,KAAK7B,sBAAsB;AAC7B,YAAMwD;;QAA4C/V;;AAElD,YAAMgW,eAAe,KAAK3D,iBAAiB4D,UAAWC,oBAAmB;AACvE,eAAOA,eAAelZ,OAAO+Y,aAAaI;MAC3C,CAFoB;AAIrB,UAAI/B,gBAAgB,QAAQ4B,eAAe,IAAI;AAE7C,aAAK3D,iBAAiB+D,OAAOJ,cAAc,CAA3C;iBACS5B,gBAAgB,UAAU4B,iBAAiB,IAAI;AAExD,aAAK3D,iBAAiBrP,KAAK,KAAKqT,wBAAwBN,cAAc;UAAEjZ,GAAG;UAAGC,GAAG;QAAX,CAA3C,CAA3B;MACD,WAAUiZ,eAAe,IAAI;AAE5B,aAAKK,wBAAwBN,cAAc,KAAK1D,iBAAiB2D,YAAtB,CAA3C;MACD;AAED,WAAK5D,mBAAmB,KAAKC,iBAAiBiE;AAI9C,UAAI,KAAKlE,mBAAmB,GAAG;AAC7BzV,uBAAe,KAAKC,IAAI,KAAKyV,iBAAiB,CAAtB,CAAV;MACf;AAED,UAAI,KAAKD,mBAAmB,GAAG;AAC7BzV,uBAAe,KAAKE,IAAI,KAAKwV,iBAAiB,CAAtB,CAAV;MACf;IACF,OAAM;AACL,YAAMkE;;QAAwCvW;;AAE9C,WAAKoS,mBAAmB;AACxB,UAAImE,WAAWtU,KAAKqT,QAAQ,OAAxB,IAAmC,IAAI;AAGzC,YAAIiB,WAAWC,WAAWD,WAAWC,QAAQF,SAAS,GAAG;AACvD,eAAKD,wBAAwBE,WAAWC,QAAQ,CAAnB,GAAuB,KAAK5Z,EAAzD;AACA,eAAKwV;AACL,cAAImE,WAAWC,QAAQF,SAAS,GAAG;AACjC,iBAAKD,wBAAwBE,WAAWC,QAAQ,CAAnB,GAAuB,KAAK3Z,EAAzD;AACA,iBAAKuV;UACN;QACF;MACF,OAAM;AAEL,aAAKiE;;UAAqDrW;UAAI,KAAKpD;QAAnE;AACA,YAAIwX,gBAAgB,MAAM;AAExB,eAAKhC,mBAAmB;QACzB,OAAM;AACL,eAAKA;QACN;MACF;IACF;EACF;;;;EAKD8C,oBAAoB;AAClBvY,mBAAe,KAAKwP,QAAQ,KAAKvP,EAAnB;AACdD,mBAAe,KAAKsV,QAAQ,KAAKpV,EAAnB;EACf;;;;EAKD8X,qBAAqB;AACnBhY,mBAAe,KAAKgT,SAAS,KAAK/S,EAApB;AACdD,mBAAe,KAAKiT,SAAS,KAAK/S,EAApB;AACd,SAAKqY,kBAAL;EACD;;EAGDR,0BAA0B;AACxB,QAAI,KAAKtR,KAAKmF,WAAWC,UAArB,GAAkC;AAEpC,WAAK4D,WAAW;IACjB,OAAM;AAEL,YAAMqK,OAAOrZ,KAAKG,IAAI,KAAKX,GAAGE,IAAI,KAAK6S,QAAQ7S,CAAlC,IAAuCM,KAAKG,IAAI,KAAKX,GAAGG,IAAI,KAAK4S,QAAQ5S,CAAlC;AAEpD,UAAI0Z,SAAS,GAAG;AAEd,cAAMC,cAAcD,OAAO,IAAI,MAAM;AAErC,YAAIrZ,KAAKG,IAAI,KAAKX,GAAG8Z,WAAR,IAAuB,KAAK/G,QAAQ+G,WAAb,CAAhC,KAA8D7E,uBAAuB;AACvF,eAAKzF,WAAWsK;QACjB;MACF;IACF;EACF;;;;;;;;;;EAWDL,wBAAwBrW,GAAG7C,GAAG;AAC5BA,MAAEL,IAAIkD,EAAE2W,QAAQ,KAAKvT,KAAKwT,OAAO9Z;AACjCK,MAAEJ,IAAIiD,EAAE6W,QAAQ,KAAKzT,KAAKwT,OAAO7Z;AAEjC,QAAI,eAAeiD,GAAG;AACpB7C,QAAEH,KAAKgD,EAAEmW;IACV,WAAUnW,EAAE8W,eAAe7Z,QAAW;AACrCE,QAAEH,KAAKgD,EAAE8W;IACV;AAED,WAAO3Z;EACR;;;;;EAMDmW,SAAStT,GAAG;AAEV,QAAI,KAAKoD,KAAKmF,WAAWC,UAArB,GAAkC;AACpCxI,QAAEqU,eAAF;AACArU,QAAE+W,gBAAF;IACD;EACF;AAxkBY;AChBf,IAAMC,2BAA2B;AAajC,IAAMC,aAAN,MAAiB;;;;EAIfpV,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AACZ,SAAKtG,IAAI;AACT,SAAKoa,aAAa;AAElB,SAAKC,qBAAqB;AAE1B,SAAKC,qBAAqB;AAE1B,SAAKC,uBAAuB;AAG5B,SAAKC,cAAc,CAAA;EACpB;;;;;;;EAQD1O,OAAO2O,cAAc;AACnB,UAAM;MAAEnU,MAAAA;IAAF,IAAW;AACjB,UAAMoU,gBAAgBpa,KAAKC,MACzB+F,MAAKO,aAAa7G,IAAIsG,MAAKO,aAAa7G,IAAIsG,MAAKD,QAAQsU,OADrC;AAMtB,UAAMC,oBAAqBF,kBAAkB,KAAKN;AAElD,QAAIQ,mBAAmB;AACrB,WAAKR,aAAaM;AAClB,WAAKjJ,OAAO,KAAKtB,cAAL,CAAZ;IACD;AAED,SAAKqK,YAAY/U,QAAQ,CAACoV,YAAY9T,UAAU;AAC9C,UAAI6T,mBAAmB;AACrBzZ,qBAAa0Z,WAAWnb,KAAKqH,QAAQ,KAAKwT,wBACZ,KAAKH,UADvB;MAEb;AAED,UAAIK,gBAAgBI,WAAWpT,OAAO;AACpCoT,mBAAWpT,MAAMqE,OAAjB;MACD;KARH;EAUD;;;;EAKDgP,gBAAgB;AAGd,SAAKT,qBAAqB;AAC1B,SAAKC,qBAAqB;AAG1B,SAAKF,aAAa;AAGlB,SAAKG,uBAAuB;EAC7B;;;;;EAMDQ,gBAAgB;AACd,SAAKP,cAAc,CAAA;AAInB,aAASQ,IAAI,GAAGA,IAAI,GAAGA,KAAK;AAC1B,YAAMtb,KAAKJ,cAAc,cAAc,OAAO,KAAKgH,KAAKiE,SAAhC;AACxB7K,SAAGub,aAAa,QAAQ,OAAxB;AACAvb,SAAGub,aAAa,wBAAwB,OAAxC;AACAvb,SAAGub,aAAa,eAAe,MAA/B;AAGAvb,SAAG0B,MAAM8Z,UAAWF,MAAM,IAAK,UAAU;AAEzC,WAAKR,YAAYtU,KAAK;QACpBxG;;OADF;IAID;EACF;;;;;EAMDyb,cAAc;AACZ,WAAO,KAAK7U,KAAK8U,YAAV,IAA0B;EAClC;;;;;;;;;;;;;;;;EAiBD/K,YAAYsJ,MAAM0B,SAASC,WAAW;AACpC,UAAM;MAAEhV,MAAAA;IAAF,IAAW;AACjB,QAAIiV,WAAWjV,MAAKkV,iBAAiB7B;AACrC,UAAM8B,YAAYnV,MAAK8U,YAAL;AAElB,QAAI9U,MAAKoV,QAAL,GAAgB;AAClBH,iBAAWjV,MAAKqV,eAAeJ,QAApB;AACX,YAAMK,YAAYjC,OAAO8B,aAAaA;AACtC,UAAIG,YAAYH,YAAY,GAAG;AAE7B9B,eAAOiC;MACR,OAAM;AAELjC,eAAOiC,WAAWH;MACnB;IACF,OAAM;AACL,UAAIF,WAAW,GAAG;AAChBA,mBAAW;MACZ,WAAUA,YAAYE,WAAW;AAChCF,mBAAWE,YAAY;MACxB;AACD9B,aAAO4B,WAAWjV,MAAKkV;IACxB;AAEDlV,IAAAA,MAAKkV,iBAAiBD;AACtB,SAAKlB,sBAAsBV;AAE3BrT,IAAAA,MAAKuG,WAAWgP,eAAhB;AAEA,UAAMC,eAAe,KAAK3L,cAAL;AACrB,QAAI,CAACkL,SAAS;AACZ,WAAK5J,OAAOqK,YAAZ;AACA,WAAKC,eAAL;IACD,OAAM;AACLzV,MAAAA,MAAKuG,WAAWqE,YAAY;QAC1B8K,cAAc;QACd9M,OAAO,KAAKlP;QACZ+P,KAAK+L;QACL9L,UAAUsL,aAAa;QACvB7H,kBAAkB;QAClB1C,cAAc;;QACdI,UAAWnR,OAAM;AACf,eAAKyR,OAAOzR,CAAZ;;QAEFwN,YAAY,MAAM;AAChB,eAAKuO,eAAL;AACAzV,UAAAA,MAAK+E,YAAL;QACD;OAbH;AAgBA,UAAI4Q,WAAW3V,MAAKkV,iBAAiBlV,MAAKwD;AAC1C,UAAIxD,MAAKoV,QAAL,GAAgB;AAClB,cAAMQ,gBAAgBD,WAAWR,aAAaA;AAC9C,YAAIS,gBAAgBT,YAAY,GAAG;AAEjCQ,qBAAWC;QACZ,OAAM;AAELD,qBAAWC,eAAeT;QAC3B;MACF;AAID,UAAInb,KAAKG,IAAIwb,QAAT,IAAqB,GAAG;AAC1B,aAAKF,eAAL;MACD;IACF;AAED,WAAO5N,QAAQwL,IAAD;EACf;;;;;;EAODxJ,gBAAgB;AACd,WAAO,KAAKiK,aAAa,KAAKC;EAC/B;;;;;;EAOD3O,YAAY;AACV,WAAO,KAAK1L,MAAM,KAAKmQ,cAAL;EACnB;;;;EAKD4L,iBAAiB;AAAA,QAAA;AACf,UAAM;MAAEzV,MAAAA;IAAF,IAAW;AACjB,UAAM6V,qBAAqB,KAAK7B,qBAAqB,KAAKD;AAE1D,QAAI,CAAC8B,oBAAoB;AACvB;IACD;AAED,SAAK7B,qBAAqB,KAAKD;AAE/B/T,IAAAA,MAAKwD,YAAYxD,MAAKkV;AAEtB,QAAIY,UAAU9b,KAAKG,IAAI0b,kBAAT;AAEd,QAAIE;AAEJ,QAAID,WAAW,GAAG;AAChB,WAAK7B,wBAAwB4B,sBAAsBA,qBAAqB,IAAI,KAAK;AACjFC,gBAAU;AAGV,WAAK5B,YAAY/U,QAASoV,gBAAe;AAAA,YAAA;AACvC,SAAA,oBAAAA,WAAWpT,WAAX,QAAA,sBAAA,UAAA,kBAAkBmE,QAAlB;AACAiP,mBAAWpT,QAAQtH;OAFrB;IAID;AAED,aAAS6a,IAAI,GAAGA,IAAIoB,SAASpB,KAAK;AAChC,UAAImB,qBAAqB,GAAG;AAC1BE,qBAAa,KAAK7B,YAAY8B,MAAjB;AACb,YAAID,YAAY;AACd,eAAK7B,YAAY,CAAjB,IAAsB6B;AAEtB,eAAK9B;AAELpZ,uBAAakb,WAAW3c,KAAK,KAAK6a,uBAAuB,KAAK,KAAKH,UAAvD;AAEZ9T,UAAAA,MAAKiW,WAAWF,YAAa/V,MAAKwD,YAAYsS,UAAWpB,IAAI,CAA7D;QACD;MACF,OAAM;AACLqB,qBAAa,KAAK7B,YAAYgC,IAAjB;AACb,YAAIH,YAAY;AACd,eAAK7B,YAAYiC,QAAQJ,UAAzB;AAEA,eAAK9B;AAELpZ,uBAAakb,WAAW3c,IAAI,KAAK6a,uBAAuB,KAAKH,UAAjD;AAEZ9T,UAAAA,MAAKiW,WAAWF,YAAa/V,MAAKwD,YAAYsS,UAAWpB,IAAI,CAA7D;QACD;MACF;IACF;AAQD,QAAI1a,KAAKG,IAAI,KAAK8Z,oBAAd,IAAsC,MAAM,CAAC,KAAK7O,UAAL,GAAkB;AACjE,WAAKoP,cAAL;AACA,WAAKhP,OAAL;IACD;AAGDxF,IAAAA,MAAKuG,WAAWC,WAAhB;AAEA,SAAK0N,YAAY/U,QAAQ,CAACoV,YAAYG,MAAM;AAC1C,UAAIH,WAAWpT,OAAO;AAEpBoT,mBAAWpT,MAAMoD,YAAYmQ,MAAM,CAAnC;MACD;KAJH;AAOA1U,IAAAA,MAAK+H,aAAL,qBAAiB,KAAKmM,YAAY,CAAjB,OAAA,QAAA,uBAAA,SAAA,SAAA,mBAAqB/S;AACtCnB,IAAAA,MAAK+D,cAAcqS,WAAWP,kBAA9B;AAEA,QAAI7V,MAAK+H,WAAW;AAClB/H,MAAAA,MAAK+H,UAAU9C,oBAAf;IACD;AAEDjF,IAAAA,MAAKyB,SAAS,QAAd;EACD;;;;;;;EAQD0J,OAAOzR,GAAG2c,UAAU;AAClB,QAAI,CAAC,KAAKrW,KAAKoV,QAAV,KAAuBiB,UAAU;AAEpC,UAAIC,uBAAwB,KAAKxC,aAAa,KAAKC,qBAAsBra,KAAK,KAAKoa;AACnFwC,6BAAuB,KAAKtW,KAAKwD;AACjC,YAAMyH,QAAQjR,KAAKC,MAAMP,IAAI,KAAKA,CAApB;AAEd,UAAK4c,sBAAsB,KAAKrL,QAAQ,KAChCqL,uBAAuB,KAAKtW,KAAK8U,YAAV,IAA0B,KAAK7J,QAAQ,GAAI;AACxEvR,YAAI,KAAKA,IAAKuR,QAAQ2I;MACvB;IACF;AAED,SAAKla,IAAIA;AAET,QAAI,KAAKsG,KAAKiE,WAAW;AACvBpJ,mBAAa,KAAKmF,KAAKiE,WAAWvK,CAAtB;IACb;AAED,SAAKsG,KAAKyB,SAAS,kBAAkB;MAAE/H;MAAG2c,UAAUA,aAAF,QAAEA,aAAF,SAAEA,WAAY;KAAhE;EACD;AA/Tc;ACdjB,IAAME,sBAAsB;EAC1BC,QAAQ;EACRC,GAAG;EACHC,WAAW;EACXC,SAAS;EACTC,YAAY;EACZC,WAAW;EACXC,KAAK;AAPqB;AAgB5B,IAAMC,sBAAsB,CAACC,KAAKC,mBAAmB;AACnD,SAAOA,iBAAiBD,MAAMT,oBAAoBS,GAAD;AAClD;AAMD,IAAME,WAAN,MAAe;;;;EAIbzY,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AAEZ,SAAKmX,cAAc;AAEnBnX,IAAAA,MAAK+P,GAAG,cAAc,MAAM;AAC1B,UAAI/P,MAAKD,QAAQqX,WAAW;AAE1B,YAAI,CAACpX,MAAKD,QAAQsX,mBAAmB;AAInC,eAAKC,WAAL;QACD;AAEDtX,QAAAA,MAAKgQ,OAAOrR;UACVtF;UACA;;UAC2B,KAAKke,WAAWpH,KAAK,IAArB;QAH7B;MAKD;AAEDnQ,MAAAA,MAAKgQ,OAAOrR;QAAItF;QAAU;;QAAsC,KAAKme,WAAWrH,KAAK,IAArB;MAAhE;KAjBF;AAoBA,UAAMsH;;MAAgDpe,SAASqe;;AAC/D1X,IAAAA,MAAK+P,GAAG,WAAW,MAAM;AACvB,UAAI/P,MAAKD,QAAQ4X,eACVF,qBACA,KAAKN,aAAa;AACvBM,0BAAkBG,MAAlB;MACD;KALH;EAOD;;EAGDN,aAAa;AACX,QAAI,CAAC,KAAKH,eAAe,KAAKnX,KAAKgG,SAAS;AAC1C,WAAKhG,KAAKgG,QAAQ4R,MAAlB;AACA,WAAKT,cAAc;IACpB;EACF;;;;;EAMDK,WAAW5a,GAAG;AACZ,UAAM;MAAEoD,MAAAA;IAAF,IAAW;AAEjB,QAAIA,MAAKyB,SAAS,WAAW;MAAEkM,eAAe/Q;KAA1C,EAA+CyI,kBAAkB;AACnE;IACD;AAED,QAAI1I,eAAeC,CAAD,GAAK;AAIrB;IACD;AAGD,QAAIib;AAEJ,QAAInW;AACJ,QAAIoW,YAAY;AAChB,UAAMb,iBAAiB,SAASra;AAEhC,YAAQqa,iBAAiBra,EAAEoa,MAAMpa,EAAEmb,SAAnC;MACE,KAAKhB,oBAAoB,UAAUE,cAAX;AACtB,YAAIjX,MAAKD,QAAQiY,QAAQ;AACvBH,0BAAgB;QACjB;AACD;MACF,KAAKd,oBAAoB,KAAKE,cAAN;AACtBY,wBAAgB;AAChB;MACF,KAAKd,oBAAoB,aAAaE,cAAd;AACtBvV,eAAO;AACP;MACF,KAAKqV,oBAAoB,WAAWE,cAAZ;AACtBvV,eAAO;AACP;MACF,KAAKqV,oBAAoB,cAAcE,cAAf;AACtBvV,eAAO;AACPoW,oBAAY;AACZ;MACF,KAAKf,oBAAoB,aAAaE,cAAd;AACtBa,oBAAY;AACZpW,eAAO;AACP;MACF,KAAKqV,oBAAoB,OAAOE,cAAR;AACtB,aAAKK,WAAL;AACA;IAzBJ;AA8BA,QAAI5V,MAAM;AAER9E,QAAEqU,eAAF;AAEA,YAAM;QAAElJ;MAAF,IAAgB/H;AAEtB,UAAIA,MAAKD,QAAQkY,aACVvW,SAAS,OACT1B,MAAK8U,YAAL,IAAqB,GAAG;AAC7B+C,wBAAgBC,YAAY,SAAS;MACtC,WAAU/P,aAAaA,UAAU3G,gBAAgB2G,UAAU/E,WAAWZ,KAAK;AAK1E2F,kBAAUrE,IAAIhC,IAAd,KAAuBoW,YAAY,MAAM;AACzC/P,kBAAUtC,MAAMsC,UAAUrE,IAAIhK,GAAGqO,UAAUrE,IAAI/J,CAA/C;MACD;IACF;AAED,QAAIke,eAAe;AACjBjb,QAAEqU,eAAF;AAEAjR,MAAAA,MAAK6X,aAAD,EAAJ;IACD;EACF;;;;;;;EAQDN,WAAW3a,GAAG;AACZ,UAAM;MAAEsb;IAAF,IAAe,KAAKlY;AAC1B,QAAIkY,YACG7e,aAAauD,EAAEgC,UACfsZ,aAAatb,EAAEgC,UACf,CAACsZ,SAASnK;;MAA8BnR,EAAEgC;IAAzC,GAAmD;AAEzDsZ,eAASN,MAAT;IACD;EACF;AAhJY;AC/Bf,IAAMO,iBAAiB;AAkBvB,IAAMC,eAAN,MAAmB;;;;;;EAMjB3Z,YAAY4Z,OAAO;AAAA,QAAA;AACjB,SAAKA,QAAQA;AACb,UAAM;MACJzZ;MACAsI;MACAnM;MACAud,WAAW,MAAM;MAAA;MACjBnd,WAAW;MACXgM,SAASgR;IANL,IAOFE;AAEJ,SAAKC,WAAWA;AAGhB,UAAMpd,OAAOH,YAAY,cAAc;AACvC,UAAMH,aAAYyd,cAAAA,MAAMnd,IAAD,OAAR,QAAA,gBAAA,SAAA,cAAkB;AAGjC,SAAKqd,UAAU3Z;AAEf,SAAK4Z,cAActR;AAEnB,SAAKuR,YAAY;AAGjB,SAAKC,mBAAmB,KAAKA,iBAAiBvI,KAAK,IAA3B;AASxB,SAAKwI,iBAAiBtG,WAAW,MAAM;AACrCpX,yBAAmB2D,QAAQ1D,MAAMC,UAAUgM,MAAzB;AAClB,WAAKwR,iBAAiBtG,WAAW,MAAM;AACrCzT,eAAOR,iBAAiB,iBAAiB,KAAKsa,kBAAkB,KAAhE;AACA9Z,eAAOR,iBAAiB,oBAAoB,KAAKsa,kBAAkB,KAAnE;AAMA,aAAKC,iBAAiBtG,WAAW,MAAM;AACrC,eAAKuG,mBAAL;QACD,GAAEzd,WAAW,GAFkB;AAGhCyD,eAAO9D,MAAMI,IAAb,IAAqBN;MACtB,GAAE,EAZ6B;OAa/B,CAf6B;EAgBjC;;;;;EAMD8d,iBAAiB9b,GAAG;AAClB,QAAIA,EAAEgC,WAAW,KAAK2Z,SAAS;AAC7B,WAAKK,mBAAL;IACD;EACF;;;;EAKDA,qBAAqB;AACnB,QAAI,CAAC,KAAKH,WAAW;AACnB,WAAKA,YAAY;AACjB,WAAKH,SAAL;AACA,UAAI,KAAKE,aAAa;AACpB,aAAKA,YAAL;MACD;IACF;EACF;;EAGDlT,UAAU;AACR,QAAI,KAAKqT,gBAAgB;AACvBrG,mBAAa,KAAKqG,cAAN;IACb;AACDhd,0BAAsB,KAAK4c,OAAN;AACrB,SAAKA,QAAQM,oBAAoB,iBAAiB,KAAKH,kBAAkB,KAAzE;AACA,SAAKH,QAAQM,oBAAoB,oBAAoB,KAAKH,kBAAkB,KAA5E;AACA,QAAI,CAAC,KAAKD,WAAW;AACnB,WAAKG,mBAAL;IACD;EACF;AA5FgB;ACpBnB,IAAME,4BAA4B;AAClC,IAAMC,wBAAwB;AAK9B,IAAMC,cAAN,MAAkB;;;;;;;;;;;;;;;;EAgBhBva,YAAY8J,iBAAiBkC,cAAc0C,kBAAkB;AAC3D,SAAKzD,WAAWnB,kBAAkB;AAGlC,SAAK0Q,gBAAgBxO,gBAAgBsO;AAGrC,SAAKG,oBAAoB/L,oBAAoB2L;AAE7C,SAAKK,mBAAmB,KAAKD;AAE7B,QAAI,KAAKD,gBAAgB,GAAG;AAC1B,WAAKE,oBAAoBnf,KAAKI,KAAK,IAAI,KAAK6e,gBAAgB,KAAKA,aAAxC;IAC1B;EACF;;;;;;;EAQDG,UAAUC,eAAeC,WAAW;AAMlC,QAAI/G,eAAe;AACnB,QAAIgH;AAEJD,iBAAa;AAEb,UAAME,oBAAoBxf,KAAKyf,MAAM,CAAC,KAAKR,gBAAgB,KAAKC,oBAAoBI;AAEpF,QAAI,KAAKL,kBAAkB,GAAG;AAC5BM,cAAQ,KAAK7P,WAAW,KAAKwP,oBAAoBG;AAEjD9G,sBAAgB8G,gBAAgBE,QAAQD,aAAaE;AAErD,WAAK9P,WAAW6I,eACK,CAAC,KAAK2G,oBAAqBK,QAC5BC;IACrB,WAAU,KAAKP,gBAAgB,GAAG;AACjCM,cAAS,IAAI,KAAKJ,oBACL,KAAKF,gBAAgB,KAAKC,oBAAoBG,gBAAgB,KAAK3P;AAEhF,YAAMgQ,aAAa1f,KAAK2f,IAAI,KAAKR,mBAAmBG,SAAjC;AACnB,YAAMM,aAAa5f,KAAK6f,IAAI,KAAKV,mBAAmBG,SAAjC;AAEnB/G,qBAAeiH,qBACKH,gBAAgBK,aAAaH,QAAQK;AAEzD,WAAKlQ,WAAW6I,eACK,CAAC,KAAK2G,oBACP,KAAKD,gBACLO,qBACC,CAAC,KAAKL,mBAAmBE,gBAAgBO,aAC1C,KAAKT,mBAAmBI,QAAQG;IACrD;AAID,WAAOnH;EACR;AAhFe;ACWlB,IAAMuH,kBAAN,MAAsB;;;;EAIpBrb,YAAY4Z,OAAO;AACjB,SAAKA,QAAQA;AACb,SAAK0B,OAAO;AAEZ,UAAM;MACJnR;MACAa;MACAC;MACAmB;MACA3D;MACAoR,WAAW,MAAM;MAAA;MACjB7N;MACA0C;IARI,IASFkL;AAEJ,SAAKC,WAAWA;AAEhB,UAAM0B,QAAQ,IAAIhB,YAAYtP,UAAUe,cAAc0C,gBAAxC;AACd,QAAI8M,WAAWzI,KAAKpE,IAAL;AACf,QAAIiM,gBAAgBzQ,QAAQa;AAE5B,UAAMyQ,gBAAgB,MAAM;AAC1B,UAAI,KAAKH,MAAM;AACbV,wBAAgBW,MAAMZ,UAAUC,eAAe7H,KAAKpE,IAAL,IAAa6M,QAA5C;AAGhB,YAAIjgB,KAAKG,IAAIkf,aAAT,IAA0B,KAAKrf,KAAKG,IAAI6f,MAAMtQ,QAAf,IAA2B,IAAI;AAEhEmB,mBAASpB,GAAD;AACR,cAAIvC,YAAY;AACdA,uBAAU;UACX;AACD,eAAKoR,SAAL;QACD,OAAM;AACL2B,qBAAWzI,KAAKpE,IAAL;AACXvC,mBAASwO,gBAAgB5P,GAAjB;AACR,eAAKsQ,OAAOhI,sBAAsBmI,aAAD;QAClC;MACF;;AAGH,SAAKH,OAAOhI,sBAAsBmI,aAAD;EAClC;;EAGD5U,UAAU;AACR,QAAI,KAAKyU,QAAQ,GAAG;AAClBvH,2BAAqB,KAAKuH,IAAN;IACrB;AACD,SAAKA,OAAO;EACb;AAtDmB;ACGtB,IAAMI,aAAN,MAAiB;EACf1b,cAAc;AAEZ,SAAK2b,mBAAmB,CAAA;EACzB;;;;EAKDxP,YAAYyN,OAAO;AACjB,SAAKgC,OAAOhC,OAAO,IAAnB;EACD;;;;EAKDvR,gBAAgBuR,OAAO;AACrB,SAAKgC,OAAOhC,KAAZ;EACD;;;;;;;EAQDgC,OAAOhC,OAAOiC,UAAU;AACtB,UAAMC,YAAYD,WACd,IAAIR;;MAAmDzB;IAAvD,IACA,IAAID;;MAA6CC;IAAjD;AAEJ,SAAK+B,iBAAiBxa,KAAK2a,SAA3B;AACAA,cAAUjC,WAAW,MAAM,KAAKkC,KAAKD,SAAV;AAE3B,WAAOA;EACR;;;;EAKDC,KAAKD,WAAW;AACdA,cAAUjV,QAAV;AACA,UAAM7E,QAAQ,KAAK2Z,iBAAiBlI,QAAQqI,SAA9B;AACd,QAAI9Z,QAAQ,IAAI;AACd,WAAK2Z,iBAAiBpH,OAAOvS,OAAO,CAApC;IACD;EACF;EAEDoI,UAAU;AACR,SAAKuR,iBAAiBjb,QAASob,eAAc;AAC3CA,gBAAUjV,QAAV;KADF;AAGA,SAAK8U,mBAAmB,CAAA;EACzB;;;;EAKD5T,aAAa;AACX,SAAK4T,mBAAmB,KAAKA,iBAAiBza,OAAQ4a,eAAc;AAClE,UAAIA,UAAUlC,MAAMtR,OAAO;AACzBwT,kBAAUjV,QAAV;AACA,eAAO;MACR;AAED,aAAO;IACR,CAPuB;EAQzB;EAEDiQ,iBAAiB;AACf,SAAK6E,mBAAmB,KAAKA,iBAAiBza,OAAQ4a,eAAc;AAClE,UAAIA,UAAUlC,MAAM3C,cAAc;AAChC6E,kBAAUjV,QAAV;AACA,eAAO;MACR;AAED,aAAO;IACR,CAPuB;EAQzB;;;;;;;;;;;;EAcDmV,eAAe;AACb,WAAO,KAAKL,iBAAiBM,KAAMH,eAAc;AAC/C,aAAOA,UAAUlC,MAAMtR;IACxB,CAFM;EAGR;AAhGc;ACdjB,IAAM4T,cAAN,MAAkB;;;;EAIhBlc,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AACZA,IAAAA,MAAKgQ,OAAOrR;MAAIqB,MAAKgG;MAAS;;MAAoC,KAAK4U,SAASzK,KAAK,IAAnB;IAAlE;EACD;;;;;EAMDyK,SAAShe,GAAG;AACVA,MAAEqU,eAAF;AACA,UAAM;MAAElJ;IAAF,IAAgB,KAAK/H;AAC3B,QAAI;MAAE6a;MAAQC;IAAV,IAAqBle;AAEzB,QAAI,CAACmL,WAAW;AACd;IACD;AAED,QAAI,KAAK/H,KAAKyB,SAAS,SAAS;MAAEkM,eAAe/Q;KAA7C,EAAkDyI,kBAAkB;AACtE;IACD;AAED,QAAIzI,EAAEE,WAAW,KAAKkD,KAAKD,QAAQgb,aAAa;AAE9C,UAAIhT,UAAUzB,WAAV,GAAwB;AAC1B,YAAImB,aAAa,CAACqT;AAClB,YAAIle,EAAEoe,cAAc,GAAwB;AAC1CvT,wBAAc;QACf,OAAM;AACLA,wBAAc7K,EAAEoe,YAAY,IAAI;QACjC;AACDvT,qBAAa,KAAKA;AAElB,cAAMvB,gBAAgB6B,UAAU3G,gBAAgBqG;AAChDM,kBAAU9B,OAAOC,eAAe;UAC9BxM,GAAGkD,EAAEqe;UACLthB,GAAGiD,EAAEse;SAFP;MAID;IACF,OAAM;AAEL,UAAInT,UAAUH,WAAV,GAAwB;AAC1B,YAAIhL,EAAEoe,cAAc,GAAwB;AAE1CH,oBAAU;AACVC,oBAAU;QACX;AAED/S,kBAAUtC,MACRsC,UAAUrE,IAAIhK,IAAImhB,QAClB9S,UAAUrE,IAAI/J,IAAImhB,MAFpB;MAID;IACF;EACF;AA1De;ACkClB,SAASK,eAAeC,UAAU;AAChC,MAAI,OAAOA,aAAa,UAAU;AAQhC,WAAOA;EACR;AAED,MAAI,CAACA,YAAY,CAACA,SAASC,aAAa;AACtC,WAAO;EACR;AAED,QAAMC,UAAUF;AAChB,MAAIG,MAAM;AAEVA,QAAMA,IAAI9b,MAAM,IAAV,EAAgB+b;;IAA4BF,QAAQG,QAAQ;EAA5D;AAON,MAAIH,QAAQI,WAAW;AACrBH,WAAO,gDAAgDD,QAAQI,YAAY;EAC5E;AAEDH,SAAOD,QAAQK;AAEfJ,SAAO;AAEP,SAAOA;AACR;AAED,IAAMK,YAAN,MAAgB;;;;;EAKdnd,YAAYuB,OAAM6B,MAAM;AAAA,QAAA;AACtB,UAAMmF,OAAOnF,KAAKmF,QAAQnF,KAAK5I;AAC/B,QAAI4iB,cAAcha,KAAKia;AAGvB,QAAI9b,MAAKD,QAAQiH,IAAb,MAAuB,OAAO;AAEhC;IACD;AAID,QAAI,OAAOhH,MAAKD,QAAQiH,OAAO,KAApB,MAA+B,UAAU;AAMlD6U,oBAAc7b,MAAKD,QAAQiH,OAAO,KAApB;IACf;AAEDhH,IAAAA,MAAKyB,SAAS,mBAAmB;MAAEI;KAAnC;AAEA,QAAI5I,YAAY;AAChB,QAAI4I,KAAKka,UAAU;AACjB9iB,mBAAa;AACbA,mBAAc4I,KAAK5I,aAAc,iBAAgB4I,KAAKmF,IAAK;IAC5D,OAAM;AACL/N,mBAAc4I,KAAK5I,aAAc,SAAQ4I,KAAKmF,IAAK;IACpD;AAED,QAAI9N,UAAU2I,KAAKka,WAAYla,KAAK3I,WAAW,WAAa2I,KAAK3I,WAAW;AAC5EA;IAAsDA,QAAQ8iB,YAAR;AAEtD,UAAMhW,UAAUhN,cAAcC,WAAWC,OAAZ;AAE7B,QAAI2I,KAAKka,UAAU;AACjB,UAAI7iB,YAAY,UAAU;AACU8M,gBAASnH,OAAO;MACnD;AAED,UAAI;QAAEod;MAAF,IAAYpa;AAChB,YAAM;QAAEqa;UAAcra;AAGtB,UAAI,OAAO7B,MAAKD,QAAQiH,OAAO,OAApB,MAAiC,UAAU;AAEpDiV,gBAAQjc,MAAKD,QAAQiH,OAAO,OAApB;MACT;AAED,UAAIiV,OAAO;AACTjW,gBAAQiW,QAAQA;MACjB;AAED,YAAME,WAAWD,aAAaD;AAC9B,UAAIE,UAAU;AACZnW,gBAAQ2O,aAAa,cAAcwH,QAAnC;MACD;IACF;AAEDnW,YAAQoW,YAAYjB,eAAeU,WAAD;AAElC,QAAIha,KAAKwa,QAAQ;AACfxa,WAAKwa,OAAOrW,SAAShG,KAArB;IACD;AAED,QAAI6B,KAAKya,SAAS;AAChBtW,cAAQuW,UAAW3f,OAAM;AACvB,YAAI,OAAOiF,KAAKya,YAAY,UAAU;AAEpCtc,UAAAA,MAAK6B,KAAKya,OAAN,EAAJ;mBACS,OAAOza,KAAKya,YAAY,YAAY;AAC7Cza,eAAKya,QAAQ1f,GAAGoJ,SAAShG,KAAzB;QACD;;IAEJ;AAGD,UAAMwc,WAAW3a,KAAK2a,YAAY;AAElC,QAAIvY,YAAYjE,MAAKgG;AACrB,QAAIwW,aAAa,OAAO;AACtB,UAAI,CAACxc,MAAKyc,QAAQ;AAChBzc,QAAAA,MAAKyc,SAASzjB,cAAc,qCAAqC,OAAOgH,MAAKiQ,UAAlD;MAC5B;AACDhM,kBAAYjE,MAAKyc;IAClB,OAAM;AAGLzW,cAAQ6H,UAAUlP,IAAI,qBAAtB;AAEA,UAAI6d,aAAa,WAAW;AAC1BvY,oBAAYjE,MAAKiQ;MAClB;IACF;AAED,KAAA,aAAAhM,eAAS,QAAE3K,eAAAA,UAAAA,WAAAA,YAAY0G,MAAK0S,aAAa,aAAa1M,SAASnE,IAAxC,CAAvB;EACD;AAtGa;AChEhB,SAAS6a,gBAAgB1W,SAAShG,OAAM2c,cAAc;AACpD3W,UAAQ6H,UAAUlP,IAAI,qBAAtB;AAEAqH,UAAQ2O,aAAa,iBAAiB,aAAtC;AACA3U,EAAAA,MAAK+P,GAAG,UAAU,MAAM;AACtB,QAAI,CAAC/P,MAAKD,QAAQ6c,MAAM;AACtB,UAAID,cAAc;AAEf3W,gBAAS6W,WAAW,EAAE7c,MAAKwD,YAAYxD,MAAK8U,YAAL,IAAqB;MAC9D,OAAM;AAEJ9O,gBAAS6W,WAAW,EAAE7c,MAAKwD,YAAY;MACzC;IACF;GATH;AAWD;AAGM,IAAMsZ,YAAY;EACvB9V,MAAM;EACN/N,WAAW;EACXgjB,OAAO;EACPc,OAAO;EACPhB,UAAU;EACVS,UAAU;EACVV,MAAM;IACJT,aAAa;IACbI,MAAM;IACNE,OAAO;IACPD,WAAW;;EAEbY,SAAS;EACTD,QAAQK;AAde;AAkBlB,IAAMM,YAAY;EACvBhW,MAAM;EACN/N,WAAW;EACXgjB,OAAO;EACPc,OAAO;EACPhB,UAAU;EACVS,UAAU;EACVV,MAAM;IACJT,aAAa;IACbI,MAAM;IACNE,OAAO;IACPD,WAAW;;EAEbY,SAAS;EACTD,QAAQ,CAACjjB,IAAI4G,UAAS;AACpB0c,oBAAgBtjB,IAAI4G,OAAM,IAAX;EAChB;AAhBsB;AChDzB,IAAMid,cAAc;EAClBjW,MAAM;EACNiV,OAAO;EACPc,OAAO;EACPhB,UAAU;EACVD,MAAM;IACJT,aAAa;IACbM,OAAO;IACPD,WAAW;;EAEbY,SAAS;AAVS;ACApB,IAAMY,aAAa;EACjBlW,MAAM;EACNiV,OAAO;EACPc,OAAO;EACPhB,UAAU;EACVD,MAAM;IACJT,aAAa;;IAEbM,OAAO;IAGPD,WAAW;;EAEbY,SAAS;AAbQ;ACAZ,IAAMa,mBAAmB;EAC9BnW,MAAM;EACNwV,UAAU;EACVO,OAAO;EACPjB,MAAM;IACJT,aAAa;;IAEbM,OAAO;IACPD,WAAW;;EAEbW,QAAQ,CAACe,kBAAkBpd,UAAS;AAElC,QAAIqd;AAEJ,QAAIC,eAAe;AAMnB,UAAMC,uBAAuB,CAACtkB,WAAW0F,QAAQ;AAC/Cye,uBAAiBvP,UAAUW,OAAO,sBAAsBvV,WAAW0F,GAAnE;;AAMF,UAAM6e,yBAA0BC,aAAY;AAC1C,UAAIJ,cAAcI,SAAS;AACzBJ,oBAAYI;AACZF,6BAAqB,UAAUE,OAAX;MACrB;;AAGH,UAAMC,4BAA4B,MAAM;AAAA,UAAA;AACtC,UAAI,GAAA,kBAAC1d,MAAK+H,eAAN,QAAA,oBAAA,UAAC,gBAAgBjE,QAAQ6Z,UAAxB,IAAqC;AACxCH,+BAAuB,KAAD;AACtB,YAAIF,cAAc;AAChBhL,uBAAagL,YAAD;AACZA,yBAAe;QAChB;AACD;MACD;AAED,UAAI,CAACA,cAAc;AAEjBA,uBAAejL,WAAW,MAAM;AAAA,cAAA;AAC9BmL,iCAAuB3V,SAAQ7H,mBAAAA,MAAK+H,eAAL,QAAA,qBAAA,SAAA,SAAA,iBAAgBjE,QAAQ6Z,UAAxB,CAAD,CAAR;AACtBL,yBAAe;QAChB,GAAEtd,MAAKD,QAAQ6d,cAHS;MAI1B;;AAGH5d,IAAAA,MAAK+P,GAAG,UAAU2N,yBAAlB;AAEA1d,IAAAA,MAAK+P,GAAG,gBAAiBnT,OAAM;AAC7B,UAAIoD,MAAK+H,cAAcnL,EAAEuE,OAAO;AAC9Buc,kCAAyB;MAC1B;IACF,CAJD;AAOA,QAAI1d,MAAK6d,IAAI;AACX7d,MAAAA,MAAK6d,GAAGH,4BAA4BA;IACrC;EACF;AAjE6B;ACAzB,IAAMI,mBAAmB;EAC9B9W,MAAM;EACN+V,OAAO;EACPV,QAAQ,CAAC0B,gBAAgB/d,UAAS;AAChCA,IAAAA,MAAK+P,GAAG,UAAU,MAAM;AACtBgO,qBAAeC,YAAahe,MAAKwD,YAAY,IACfxD,MAAKD,QAAQke,oBACbje,MAAK8U,YAAL;KAHhC;EAKD;AAT6B;ACkBhC,SAASoJ,YAAY9kB,IAAI+kB,YAAY;AACnC/kB,KAAGyU,UAAUW,OAAO,mBAAmB2P,UAAvC;AACD;AAED,IAAMC,KAAN,MAAS;;;;EAIP3f,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AACZ,SAAKqe,eAAe;AAEpB,SAAKC,iBAAiB,CAAA;AAEtB,SAAKC,QAAQ,CAAA;AAEb,SAAKb,4BAA4B,MAAM;IAAA;AAMvC,SAAKc,wBAAwB3kB;EAC9B;EAED4kB,OAAO;AACL,UAAM;MAAEze,MAAAA;IAAF,IAAW;AACjB,SAAKqe,eAAe;AACpB,SAAKC,iBAAiB,CACpBrB,aACAH,WACAE,WACAE,YACAC,kBACAW,gBANoB;AAStB9d,IAAAA,MAAKyB,SAAS,YAAd;AAGA,SAAK6c,eAAeI,KAAK,CAACC,GAAGC,MAAM;AAEjC,cAAQD,EAAE5B,SAAS,MAAM6B,EAAE7B,SAAS;KAFtC;AAKA,SAAKwB,QAAQ,CAAA;AAEb,SAAKF,eAAe;AACpB,SAAKC,eAAenf,QAAS0f,mBAAkB;AAC7C,WAAKC,gBAAgBD,aAArB;KADF;AAIA7e,IAAAA,MAAK+P,GAAG,UAAU,MAAM;AAAA,UAAA;AACtB,OAAA,gBAAA/P,MAAKgG,aAAL,QAAA,kBAAA,UAAA,cAAc6H,UAAUW,OAAO,mBAAmBxO,MAAK8U,YAAL,MAAuB,CAAzE;KADF;AAIA9U,IAAAA,MAAK+P,GAAG,iBAAiB,MAAM,KAAKgP,iBAAL,CAA/B;EACD;;;;EAKDD,gBAAgBE,aAAa;AAC3B,QAAI,KAAKX,cAAc;AACrB,WAAKE,MAAM3e,KACT,IAAIgc,UAAU,KAAK5b,MAAMgf,WAAzB,CADF;IAGD,OAAM;AACL,WAAKV,eAAe1e,KAAKof,WAAzB;IACD;EACF;;;;;;;EAQDD,mBAAmB;AACjB,UAAM;MAAE7G;MAAUnQ;MAAWhI;IAAvB,IAAmC,KAAKC;AAE9C,QAAI,KAAKA,KAAK4D,OAAOqb,aAAa,CAAC/G,YAAY,CAACnQ,WAAW;AACzD;IACD;AAED,QAAI;MAAE3G;QAAkB2G;AAGxB,QAAI,CAAC,KAAK/H,KAAK4D,OAAOC,QAAQ;AAC5BzC,sBAAgB2G,UAAU/E,WAAWT;IACtC;AAED,QAAInB,kBAAkB,KAAKod,uBAAuB;AAChD;IACD;AACD,SAAKA,wBAAwBpd;AAE7B,UAAM8d,oBAAoBnX,UAAU/E,WAAWT,UAAUwF,UAAU/E,WAAWR;AAG9E,QAAIxI,KAAKG,IAAI+kB,iBAAT,IAA8B,QAAQ,CAACnX,UAAUzB,WAAV,GAAwB;AAEjE4X,kBAAYhG,UAAU,KAAX;AACXA,eAASrK,UAAU5O,OAAO,oBAA1B;AACA;IACD;AAEDiZ,aAASrK,UAAUlP,IAAI,oBAAvB;AAEA,UAAMwgB,qBAAqB/d,kBAAkB2G,UAAU/E,WAAWT,UAC9DwF,UAAU/E,WAAWR,YAAYuF,UAAU/E,WAAWT;AAE1D2b,gBAAYhG,UAAUiH,sBAAsB/d,aAAjC;AAEX,QAAIrB,QAAQqf,qBAAqB,UAC1Brf,QAAQqf,qBAAqB,iBAAiB;AACnDlH,eAASrK,UAAUlP,IAAI,qBAAvB;IACD;EACF;AAlHM;ACdT,SAAS0gB,mBAAmBjmB,IAAI;AAC9B,QAAMkmB,gBAAgBlmB,GAAGmmB,sBAAH;AACtB,SAAO;IACL7lB,GAAG4lB,cAAcE;IACjB7lB,GAAG2lB,cAAcG;IACjBlkB,GAAG+jB,cAAc7jB;;AAEpB;AAQD,SAASikB,0BAA0BtmB,IAAIumB,YAAYC,aAAa;AAC9D,QAAMN,gBAAgBlmB,GAAGmmB,sBAAH;AAItB,QAAM5c,SAAS2c,cAAc7jB,QAAQkkB;AACrC,QAAM/c,SAAS0c,cAAc5jB,SAASkkB;AACtC,QAAMC,gBAAgBld,SAASC,SAASD,SAASC;AAEjD,QAAMkd,WAAWR,cAAc7jB,QAAQkkB,aAAaE,iBAAiB;AACrE,QAAME,WAAWT,cAAc5jB,SAASkkB,cAAcC,iBAAiB;AASvE,QAAMzb,SAAS;IACb1K,GAAG4lB,cAAcE,OAAOM;IACxBnmB,GAAG2lB,cAAcG,MAAMM;IACvBxkB,GAAGokB,aAAaE;EAHH;AAQfzb,SAAO4b,YAAY;IACjBzkB,GAAG+jB,cAAc7jB;IACjBD,GAAG8jB,cAAc5jB;IACjBhC,GAAGomB;IACHnmB,GAAGomB;;AAGL,SAAO3b;AACR;AAWM,SAAS6b,eAAexf,OAAOD,UAAU0f,UAAU;AAExD,QAAM3S,QAAQ2S,SAASze,SAAS,eAAe;IAC7ChB;IACAD;IACA0f;GAHY;AAMd,MAAI3S,MAAM4S,aAAa;AAErB,WAAO5S,MAAM4S;EACd;AAED,QAAM;IAAEna;EAAF,IAAcxF;AAEpB,MAAI2f;AAEJ,MAAIC;AAEJ,MAAIpa,WAAWka,SAASngB,QAAQsgB,kBAAkB,OAAO;AACvD,UAAMA,gBAAgBH,SAASngB,QAAQsgB,iBAAiB;AACxDD,gBAAYpa,QAAQsa,QAAQD,aAAhB,IACRra;;MAA6CA,QAAQua,cAAcF,aAAtB;;EAClD;AAEDD,cAAYF,SAASxN,aAAa,WAAW0N,WAAW5f,UAAUC,KAAtD;AAEZ,MAAI2f,WAAW;AACb,QAAI,CAAC5f,SAASggB,cAAc;AAC1BL,oBAAcd,mBAAmBe,SAAD;IACjC,OAAM;AACLD,oBAAcT,0BACZU,WACA5f,SAAS/E,SAAS+E,SAASjF,KAAK,GAChCiF,SAAS9E,UAAU8E,SAAShF,KAAK,CAHI;IAKxC;EACF;AAED,SAAO0kB,SAASxN,aAAa,eAAeyN,aAAa3f,UAAUC,KAA5D;AACR;ACkGD,IAAMggB,kBAAN,MAAsB;;;;;EAKpBhiB,YAAYI,MAAM6hB,SAAS;AACzB,SAAK7hB,OAAOA;AACZ,SAAKwG,mBAAmB;AACxB,QAAIqb,SAAS;AACXriB,aAAOsiB,OAAO,MAAMD,OAApB;IACD;EACF;EAEDzP,iBAAiB;AACf,SAAK5L,mBAAmB;EACzB;AAfmB;AAsBtB,IAAMub,YAAN,MAAgB;EACdniB,cAAc;AAIZ,SAAKoiB,aAAa,CAAA;AAKlB,SAAKC,WAAW,CAAA;AAGhB,SAAK9gB,OAAOnG;AAGZ,SAAKkG,UAAUlG;EAChB;;;;;;;EAQDknB,UAAU/Z,MAAMga,IAAIC,WAAW,KAAK;AAAA,QAAA,qBAAA,sBAAA;AAClC,QAAI,CAAC,KAAKH,SAAS9Z,IAAd,GAAqB;AACxB,WAAK8Z,SAAS9Z,IAAd,IAAsB,CAAA;IACvB;AAED,KAAA,sBAAA,KAAK8Z,SAAS9Z,IAAd,OAAA,QAAA,wBAAA,UAAA,oBAAqBpH,KAAK;MAAEohB;MAAIC;KAAhC;AACA,KAAA,uBAAA,KAAKH,SAAS9Z,IAAd,OAAqB0X,QAAAA,yBAAAA,UAAAA,qBAAAA,KAAK,CAACwC,IAAIC,OAAOD,GAAGD,WAAWE,GAAGF,QAAvD;AAEA,KAAKjhB,aAAAA,KAAAA,UAAL,QAAA,eAAA,UAAA,WAAW+gB,UAAU/Z,MAAMga,IAAIC,QAA/B;EACD;;;;;;EAODG,aAAapa,MAAMga,IAAI;AACrB,QAAI,KAAKF,SAAS9Z,IAAd,GAAqB;AAEvB,WAAK8Z,SAAS9Z,IAAd,IAAsB,KAAK8Z,SAAS9Z,IAAd,EAAoBrH,OAAOA,YAAWA,OAAOqhB,OAAOA,EAApD;IACvB;AAED,QAAI,KAAKhhB,MAAM;AACb,WAAKA,KAAKohB,aAAapa,MAAMga,EAA7B;IACD;EACF;;;;;;;EAQDtO,aAAa1L,SAASqa,MAAM;AAAA,QAAA;AAC1B,KAAA,uBAAA,KAAKP,SAAS9Z,IAAd,OAAA,QAAA,yBAAA,UAAA,qBAAqB7H,QAASQ,YAAW;AAEvC0hB,WAAK,CAAD,IAAM1hB,OAAOqhB,GAAGM,MAAM,MAAMD,IAAtB;KAFZ;AAIA,WAAOA,KAAK,CAAD;EACZ;;;;;;EAODtR,GAAG/I,MAAMga,IAAI;AAAA,QAAA,uBAAA;AACX,QAAI,CAAC,KAAKH,WAAW7Z,IAAhB,GAAuB;AAC1B,WAAK6Z,WAAW7Z,IAAhB,IAAwB,CAAA;IACzB;AACD,KAAK6Z,wBAAAA,KAAAA,WAAW7Z,IAAhB,OAAA,QAAA,0BAAA,UAAA,sBAAuBpH,KAAKohB,EAA5B;AAKA,KAAA,cAAA,KAAKhhB,UAAM+P,QAAAA,gBAAAA,UAAAA,YAAAA,GAAG/I,MAAMga,EAApB;EACD;;;;;;EAODO,IAAIva,MAAMga,IAAI;AAAA,QAAA;AACZ,QAAI,KAAKH,WAAW7Z,IAAhB,GAAuB;AAEzB,WAAK6Z,WAAW7Z,IAAhB,IAAwB,KAAK6Z,WAAW7Z,IAAhB,EAAsBrH,OAAOb,cAAakiB,OAAOliB,QAAjD;IACzB;AAED,KAAA,cAAA,KAAKkB,UAAMuhB,QAAAA,gBAAAA,UAAAA,YAAAA,IAAIva,MAAMga,EAArB;EACD;;;;;;;EAQDvf,SAASuF,MAAM0Z,SAAS;AAAA,QAAA;AACtB,QAAI,KAAK1gB,MAAM;AACb,aAAO,KAAKA,KAAKyB,SAASuF,MAAM0Z,OAAzB;IACR;AAED,UAAMnT;;MAA0C,IAAIkT,gBAAgBzZ,MAAM0Z,OAA1B;;AAEhD,KAAA,yBAAA,KAAKG,WAAW7Z,IAAhB,OAAA,QAAA,2BAAA,UAAA,uBAAuB7H,QAASL,cAAa;AAC3CA,eAASwP,KAAK,MAAMf,KAApB;KADF;AAIA,WAAOA;EACR;AAnHa;ACpOhB,IAAMiU,cAAN,MAAkB;;;;;EAKhB/iB,YAAYgjB,UAAUxd,WAAW;AAI/B,SAAK+B,UAAUhN,cACb,oCACAyoB,WAAW,QAAQ,OACnBxd,SAH0B;AAM5B,QAAIwd,UAAU;AACZ,YAAMC;;QAAyC,KAAK1b;;AACpD0b,YAAMC,WAAW;AACjBD,YAAME,MAAM;AACZF,YAAMG,MAAMJ;AACZC,YAAM/M,aAAa,QAAQ,cAA3B;IACD;AAED,SAAK3O,QAAQ2O,aAAa,eAAe,MAAzC;EACD;;;;;EAMD9O,iBAAiBpK,OAAOC,QAAQ;AAC9B,QAAI,CAAC,KAAKsK,SAAS;AACjB;IACD;AAED,QAAI,KAAKA,QAAQ9M,YAAY,OAAO;AAIlCoC,qBAAe,KAAK0K,SAAS,KAAK,MAApB;AACd,WAAKA,QAAQlL,MAAM6J,kBAAkB;AACrC,WAAKqB,QAAQlL,MAAMC,YAAYL,kBAAkB,GAAG,GAAGe,QAAQ,GAAf;IACjD,OAAM;AACLH,qBAAe,KAAK0K,SAASvK,OAAOC,MAAtB;IACf;EACF;EAED4J,UAAU;AAAA,QAAA;AACR,SAAA,gBAAI,KAAKU,aAAL,QAAA,kBAAA,UAAA,cAAc8b,YAAY;AAC5B,WAAK9b,QAAQ/G,OAAb;IACD;AACD,SAAK+G,UAAU;EAChB;AApDe;ACMlB,IAAM+b,UAAN,MAAc;;;;;;EAMZtjB,YAAY+B,UAAU0f,UAAUzf,OAAO;AACrC,SAAKyf,WAAWA;AAChB,SAAKre,OAAOrB;AACZ,SAAKC,QAAQA;AAGb,SAAKuF,UAAUnM;AAEf,SAAKkM,cAAclM;AAEnB,SAAKsH,QAAQtH;AAEb,SAAKmoB,sBAAsB;AAC3B,SAAKC,uBAAuB;AAE5B,SAAKxmB,QAAQuF,OAAO,KAAKa,KAAKtG,CAAX,KAAiByF,OAAO,KAAKa,KAAKpG,KAAX,KAAqB;AAC/D,SAAKC,SAASsF,OAAO,KAAKa,KAAKrG,CAAX,KAAiBwF,OAAO,KAAKa,KAAKnG,MAAX,KAAsB;AAEjE,SAAKwmB,aAAa;AAClB,SAAK3c,WAAW;AAChB,SAAK4c,aAAa;AAElB,SAAKC,QAAQ9lB,WAAWC;AAExB,QAAI,KAAKsF,KAAKhD,MAAM;AAClB,WAAKA,OAAO,KAAKgD,KAAKhD;IACvB,WAAU,KAAKgD,KAAKggB,KAAK;AACxB,WAAKhjB,OAAO;IACb,OAAM;AACL,WAAKA,OAAO;IACb;AAED,SAAKqhB,SAASze,SAAS,eAAe;MAAEqC,SAAS;KAAjD;EACD;EAEDue,oBAAoB;AAClB,QAAI,KAAKtc,eAAe,CAAC,KAAKuc,gBAAL,GAAwB;AAE/CjQ,iBAAW,MAAM;AACf,YAAI,KAAKtM,aAAa;AACpB,eAAKA,YAAYT,QAAjB;AACA,eAAKS,cAAclM;QACpB;SACA,GALO;IAMX;EACF;;;;;;;EAQDgL,KAAK0d,QAAQC,QAAQ;AACnB,QAAI,KAAKrhB,SAAS,KAAKshB,eAAL,GAAuB;AACvC,UAAI,CAAC,KAAK1c,aAAa;AACrB,cAAM2c,iBAAiB,KAAKxC,SAASxN;UACnC;;;UAGC,KAAK7Q,KAAK8gB,QAAQ,KAAKxhB,MAAMwC,eAAgB,KAAK9B,KAAK8gB,OAAO;UAC/D;QALqB;AAOvB,aAAK5c,cAAc,IAAIyb,YACrBkB,gBACA,KAAKvhB,MAAM8C,SAFM;MAIpB,OAAM;AACL,cAAM2e,gBAAgB,KAAK7c,YAAYC;AAEvC,YAAI4c,iBAAiB,CAACA,cAAcC,eAAe;AACjD,eAAK1hB,MAAM8C,UAAU6e,QAAQF,aAA7B;QACD;MACF;IACF;AAED,QAAI,KAAK5c,WAAW,CAACwc,QAAQ;AAC3B;IACD;AAED,QAAI,KAAKtC,SAASze,SAAS,eAAe;MAAEqC,SAAS;MAAMye;KAAvD,EAAiEld,kBAAkB;AACrF;IACD;AAED,QAAI,KAAK0d,eAAL,GAAuB;AACzB,WAAK/c,UAAUhN,cAAc,aAAa,KAAd;AAG5B,UAAI,KAAKgpB,qBAAqB;AAC5B,aAAKgB,UAAUT,MAAf;MACD;IACF,OAAM;AACL,WAAKvc,UAAUhN,cAAc,iBAAiB,KAAlB;AAC5B,WAAKgN,QAAQoW,YAAY,KAAKva,KAAKia,QAAQ;IAC5C;AAED,QAAI0G,UAAU,KAAKrhB,OAAO;AACxB,WAAKA,MAAM2D,kBAAkB,IAA7B;IACD;EACF;;;;;;EAODke,UAAUT,QAAQ;AAAA,QAAA,gBAAA;AAChB,QAAI,CAAC,KAAKQ,eAAL,KACA,CAAC,KAAK/c,WACN,KAAKka,SAASze,SAAS,oBAAoB;MAAEqC,SAAS;MAAMye;KAA5D,EAAsEld,kBAAkB;AAC3F;IACD;AAED,UAAM4d;;MAA8C,KAAKjd;;AAEzD,SAAKkd,kBAAL;AAEA,QAAI,KAAKrhB,KAAKshB,QAAQ;AACpBF,mBAAaE,SAAS,KAAKthB,KAAKshB;IACjC;AAEDF,iBAAapB,OAAM,iBAAA,KAAKhgB,KAAKggB,SAA7B,QAAA,mBAAA,SAAA,iBAAoC;AACpCoB,iBAAarB,OAAM,iBAAA,KAAK/f,KAAK+f,SAA7B,QAAA,mBAAA,SAAA,iBAAoC;AAEpC,SAAKQ,QAAQ9lB,WAAWE;AAExB,QAAIymB,aAAajnB,UAAU;AACzB,WAAKonB,SAAL;IACD,OAAM;AACLH,mBAAa7mB,SAAS,MAAM;AAC1B,aAAKgnB,SAAL;;AAGFH,mBAAa5mB,UAAU,MAAM;AAC3B,aAAKgnB,QAAL;;IAEH;EACF;;;;;;EAODC,SAASniB,OAAO;AACd,SAAKA,QAAQA;AACb,SAAKoE,WAAW;AAChB,SAAK2a,WAAW/e,MAAMnB;EAGvB;;;;EAKDojB,WAAW;AACT,SAAKhB,QAAQ9lB,WAAWG;AAExB,QAAI,KAAK0E,SAAS,KAAK6E,SAAS;AAC9B,WAAKka,SAASze,SAAS,gBAAgB;QAAEN,OAAO,KAAKA;QAAO2C,SAAS;MAA9B,CAAvC;AAGA,UAAI,KAAK3C,MAAMoC,YACR,KAAKpC,MAAMgD,iBACX,CAAC,KAAK6B,QAAQ8b,YAAY;AAC/B,aAAKpd,OAAL;AACA,aAAKvD,MAAM2D,kBAAkB,IAA7B;MACD;AAED,UAAI,KAAKsd,UAAU9lB,WAAWG,UAAU,KAAK2lB,UAAU9lB,WAAWI,OAAO;AACvE,aAAK2lB,kBAAL;MACD;IACF;EACF;;;;EAKDgB,UAAU;AACR,SAAKjB,QAAQ9lB,WAAWI;AAExB,QAAI,KAAKyE,OAAO;AACd,WAAKoiB,aAAL;AACA,WAAKrD,SAASze,SAAS,gBAAgB;QAAEN,OAAO,KAAKA;QAAOqiB,SAAS;QAAM1f,SAAS;OAApF;AACA,WAAKoc,SAASze,SAAS,aAAa;QAAEN,OAAO,KAAKA;QAAO2C,SAAS;OAAlE;IACD;EACF;;;;EAKD6Z,YAAY;AACV,WAAO,KAAKuC,SAASxN,aACnB,oBACA,KAAK0P,UAAU9lB,WAAWE,SAC1B,IAHK;EAKR;;;;EAKDgnB,UAAU;AACR,WAAO,KAAKpB,UAAU9lB,WAAWI;EAClC;;;;EAKDqmB,iBAAiB;AACf,WAAO,KAAKlkB,SAAS;EACtB;;;;;;;EAQDgH,iBAAiBpK,OAAOC,QAAQ;AAC9B,QAAI,CAAC,KAAKsK,SAAS;AACjB;IACD;AAED,QAAI,KAAKD,aAAa;AACpB,WAAKA,YAAYF,iBAAiBpK,OAAOC,MAAzC;IACD;AAED,QAAI,KAAKwkB,SAASze,SAChB,iBACA;MAAEqC,SAAS;MAAMrI;MAAOC;KAFtB,EAEgC2J,kBAClC;AACA;IACD;AAED/J,mBAAe,KAAK0K,SAASvK,OAAOC,MAAtB;AAEd,QAAI,KAAKqnB,eAAL,KAAyB,CAAC,KAAKS,QAAL,GAAgB;AAC5C,YAAMC,sBAAuB,CAAC,KAAKzB,uBAAuBvmB;AAE1D,WAAKumB,sBAAsBvmB;AAC3B,WAAKwmB,uBAAuBvmB;AAE5B,UAAI+nB,qBAAqB;AACvB,aAAKT,UAAU,KAAf;MACD,OAAM;AACL,aAAKE,kBAAL;MACD;AAED,UAAI,KAAK/hB,OAAO;AACd,aAAK+e,SAASze,SACZ,mBACA;UAAEN,OAAO,KAAKA;UAAO1F;UAAOC;UAAQoI,SAAS;SAF/C;MAID;IACF;EACF;;;;EAKDwC,aAAa;AACX,WAAO,KAAK4Z,SAASxN,aACnB,qBACA,KAAKqQ,eAAL,KAA0B,KAAKX,UAAU9lB,WAAWI,OACpD,IAHK;EAKR;;;;EAKDwmB,oBAAoB;AAMlB,QAAI,CAAC,KAAKH,eAAL,KAAyB,CAAC,KAAK/c,WAAW,CAAC,KAAKnE,KAAKshB,QAAQ;AAChE;IACD;AAED,UAAMO;;MAAuC,KAAK1d;;AAClD,UAAM2d,aAAa,KAAKzD,SAASxN,aAC/B,oBACA,KAAKsP,qBACL,IAHiB;AAMnB,QACE,CAAC0B,MAAME,QAAQC,mBACZF,aAAaG,SAASJ,MAAME,QAAQC,iBAAiB,EAAhC,GACxB;AACAH,YAAMK,QAAQJ,aAAa;AAC3BD,YAAME,QAAQC,kBAAkBG,OAAOL,UAAD;IACvC;EACF;;;;EAKDlB,iBAAiB;AACf,WAAO,KAAKvC,SAASxN,aACnB,yBACA,KAAKqQ,eAAL,GACA,IAHK;EAKR;;;;EAKDkB,WAAW;AACT,QAAI,KAAK/D,SAASze,SAAS,mBAAmB;MAAEqC,SAAS;KAArD,EAA6DuB,kBAAkB;AACjF;IACD;AAED,SAAKR,KAAK,IAAV;EACD;;;;EAKDyd,kBAAkB;AAChB,WAAO,KAAKpC,SAASxN,aACnB,wBACA,KAAKiL,UAAL,GACA,IAHK;EAKR;;;;EAKDrY,UAAU;AACR,SAAKC,WAAW;AAChB,SAAKpE,QAAQtH;AAEb,QAAI,KAAKqmB,SAASze,SAAS,kBAAkB;MAAEqC,SAAS;KAApD,EAA4DuB,kBAAkB;AAChF;IACD;AAED,SAAKpG,OAAL;AAEA,QAAI,KAAK8G,aAAa;AACpB,WAAKA,YAAYT,QAAjB;AACA,WAAKS,cAAclM;IACpB;AAED,QAAI,KAAKkpB,eAAL,KAAyB,KAAK/c,SAAS;AACzC,WAAKA,QAAQ5J,SAAS;AACtB,WAAK4J,QAAQ3J,UAAU;AACvB,WAAK2J,UAAUnM;IAChB;EACF;;;;EAKD0pB,eAAe;AACb,QAAI,KAAKpiB,OAAO;AAAA,UAAA,uBAAA;AACd,UAAI+iB,aAAalrB,cAAc,mBAAmB,KAApB;AAC9BkrB,iBAAWlG,aAAX,yBAAA,yBAAuB,KAAKkC,SAASngB,aAAd,QAAA,2BAAA,SAAA,SAAA,uBAAuBokB,cAA9C,QAAA,0BAAA,SAAA,wBAA0D;AAC1DD;MAA4C,KAAKhE,SAASxN,aACxD,uBACAwR,YACA,IAH0C;AAK5C,WAAKle,UAAUhN,cAAc,2CAA2C,KAA5C;AAC5B,WAAKgN,QAAQ1M,YAAY4qB,UAAzB;AACA,WAAK/iB,MAAM8C,UAAU+Z,YAAY;AACjC,WAAK7c,MAAM8C,UAAU3K,YAAY,KAAK0M,OAAtC;AACA,WAAK7E,MAAM2D,kBAAkB,IAA7B;AACA,WAAKud,kBAAL;IACD;EACF;;;;EAKD3d,SAAS;AACP,QAAI,KAAKwd,cAAc,CAAC,KAAKlc,SAAS;AACpC;IACD;AAED,SAAKkc,aAAa;AAElB,QAAI,KAAKE,UAAU9lB,WAAWI,OAAO;AACnC,WAAK6mB,aAAL;AACA;IACD;AAED,QAAI,KAAKrD,SAASze,SAAS,iBAAiB;MAAEqC,SAAS;KAAnD,EAA2DuB,kBAAkB;AAC/E;IACD;AAED,UAAM+e,iBAAkB,YAAY,KAAKpe;AAEzC,QAAI,KAAK+c,eAAL,GAAuB;AAazB,UAAIqB,kBAAkB,KAAKjjB,UAAU,CAAC,KAAKA,MAAMoC,YAAYzF,SAAQ,IAAK;AACxE,aAAKqkB,aAAa;AAIjB,aAAKnc,QAASlK,OAAf,EAAwBC,MAAM,MAAM;QAAA,CAApC,EAAwCsoB,QAAQ,MAAM;AACpD,eAAKlC,aAAa;AAClB,eAAKmC,YAAL;SAFF;MAID,OAAM;AACL,aAAKA,YAAL;MACD;eACQ,KAAKnjB,SAAS,CAAC,KAAK6E,QAAQ8b,YAAY;AACjD,WAAK3gB,MAAM8C,UAAU3K,YAAY,KAAK0M,OAAtC;IACD;EACF;;;;;;EAODxB,WAAW;AACT,QAAI,KAAK0b,SAASze,SAAS,mBAAmB;MAAEqC,SAAS;IAAX,CAA1C,EAA6DuB,oBAC5D,CAAC,KAAKlE,OAAO;AAChB;IACD;AAED,QAAI,KAAK4hB,eAAL,KAAyB,KAAKZ,cAAc,CAACrkB,SAAQ,GAAI;AAG3D,WAAKwmB,YAAL;IACD,WAAU,KAAKd,QAAL,GAAgB;AACzB,WAAK3e,KAAK,OAAO,IAAjB;IACD;AAED,QAAI,KAAK1D,MAAM+C,eAAe;AAC5B,WAAK/C,MAAM+C,cAAcyQ,aAAa,eAAe,OAArD;IACD;EACF;;;;EAKDlQ,aAAa;AACX,SAAKyb,SAASze,SAAS,qBAAqB;MAAEqC,SAAS;KAAvD;AACA,QAAI,KAAK3C,SAAS,KAAKA,MAAM+C,eAAe;AAC1C,WAAK/C,MAAM+C,cAAcyQ,aAAa,eAAe,MAArD;IACD;EACF;;;;EAMD1V,SAAS;AACP,SAAKijB,aAAa;AAElB,QAAI,KAAKhC,SAASze,SAAS,iBAAiB;MAAEqC,SAAS;KAAnD,EAA2DuB,kBAAkB;AAC/E;IACD;AAED,QAAI,KAAKW,WAAW,KAAKA,QAAQ8b,YAAY;AAC3C,WAAK9b,QAAQ/G,OAAb;IACD;AAED,QAAI,KAAK8G,eAAe,KAAKA,YAAYC,SAAS;AAChD,WAAKD,YAAYC,QAAQ/G,OAAzB;IACD;EACF;;;;EAKDqlB,cAAc;AACZ,QAAI,CAAC,KAAKpC,YAAY;AACpB;IACD;AAED,QAAI,KAAKhC,SAASze,SAAS,sBAAsB;MAAEqC,SAAS;KAAxD,EAAgEuB,kBAAkB;AACpF;IACD;AAGD,QAAI,KAAKlE,SAAS,KAAK6E,WAAW,CAAC,KAAKA,QAAQ8b,YAAY;AAC1D,WAAK3gB,MAAM8C,UAAU3K,YAAY,KAAK0M,OAAtC;IACD;AAED,QAAI,KAAKoc,UAAU9lB,WAAWG,UAAU,KAAK2lB,UAAU9lB,WAAWI,OAAO;AACvE,WAAK2lB,kBAAL;IACD;EACF;AA5fW;ACCd,IAAMkC,sBAAsB;AAYrB,SAASC,aAAahkB,UAAU0f,UAAUzf,OAAO;AACtD,QAAMqD,UAAUoc,SAASuE,sBAAsBjkB,UAAUC,KAAzC;AAEhB,MAAIikB;AAEJ,QAAM;IAAE3kB;MAAYmgB;AAIpB,MAAIngB,SAAS;AACX2kB,gBAAY,IAAIxiB,UAAUnC,SAASS,UAAU,EAAjC;AAEZ,QAAID;AACJ,QAAI2f,SAASlgB,MAAM;AACjBO,qBAAe2f,SAASlgB,KAAKO;IAC9B,OAAM;AACLA,qBAAeT,gBAAgBC,SAASmgB,QAAV;IAC/B;AAED,UAAMpe,cAAcb,eAAelB,SAASQ,cAAcC,UAAUC,KAAlC;AAClCikB,cAAUpjB,OAAOwC,QAAQrI,OAAOqI,QAAQpI,QAAQoG,WAAhD;EACD;AAEDgC,UAAQmgB,SAAR;AAEA,MAAIS,WAAW;AACb5gB,YAAQ+B,iBACN7L,KAAK2qB,KAAK7gB,QAAQrI,QAAQipB,UAAUniB,OAApC,GACAvI,KAAK2qB,KAAK7gB,QAAQpI,SAASgpB,UAAUniB,OAArC,CAFF;EAID;AAED,SAAOuB;AACR;AAcM,SAAS8gB,cAAcnkB,OAAOyf,UAAU;AAC7C,QAAM1f,WAAW0f,SAAS2E,YAAYpkB,KAArB;AAEjB,MAAIyf,SAASze,SAAS,iBAAiB;IAAEhB;IAAOD;GAA5C,EAAwD6E,kBAAkB;AAC5E;EACD;AAED,SAAOmf,aAAahkB,UAAU0f,UAAUzf,KAArB;AACpB;AAED,IAAMqkB,gBAAN,MAAoB;;;;EAIlBrmB,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AAEZ,SAAK+kB,QAAQ/qB,KAAKS,IAChBuF,MAAKD,QAAQilB,QAAQ,CAArB,IAA0BhlB,MAAKD,QAAQilB,QAAQ,CAArB,IAA0B,GACpDT,mBAFW;AAKb,SAAKU,eAAe,CAAA;EACrB;;;;;;EAOD7O,WAAW/C,MAAM;AACf,UAAM;MAAErT,MAAAA;IAAF,IAAW;AAEjB,QAAIA,MAAKyB,SAAS,UAAd,EAA0B4D,kBAAkB;AAC9C;IACD;AAED,UAAM;MAAE2f;QAAYhlB,MAAKD;AACzB,UAAM+X,YAAYzE,SAASxZ,SAAY,OAAQwZ,QAAQ;AACvD,QAAIqB;AAGJ,SAAKA,IAAI,GAAGA,KAAKsQ,QAAQ,CAAD,GAAKtQ,KAAK;AAChC,WAAKwQ,iBAAiBllB,MAAKwD,aAAasU,YAAYpD,IAAK,CAACA,EAA1D;IACD;AAGD,SAAKA,IAAI,GAAGA,KAAKsQ,QAAQ,CAAD,GAAKtQ,KAAK;AAChC,WAAKwQ,iBAAiBllB,MAAKwD,aAAasU,YAAa,CAACpD,IAAKA,EAA3D;IACD;EACF;;;;EAKDwQ,iBAAiBC,cAAc;AAC7B,UAAM1kB,QAAQ,KAAKT,KAAKqV,eAAe8P,YAAzB;AAEd,QAAIrhB,UAAU,KAAKshB,kBAAkB3kB,KAAvB;AACd,QAAI,CAACqD,SAAS;AAEZA,gBAAU8gB,cAAcnkB,OAAO,KAAKT,IAAb;AAEvB,UAAI8D,SAAS;AACX,aAAKuhB,WAAWvhB,OAAhB;MACD;IACF;EACF;;;;;EAMDE,kBAAkB7C,OAAO;AACvB,QAAI2C,UAAU,KAAKshB,kBAAkBjkB,MAAMV,KAA7B;AACd,QAAI,CAACqD,SAAS;AAEZA,gBAAU,KAAK9D,KAAKykB,sBAAsBtjB,MAAMU,MAAMV,MAAMV,KAAlD;AACV,WAAK4kB,WAAWvhB,OAAhB;IACD;AAGDA,YAAQwf,SAASniB,KAAjB;AAEA,WAAO2C;EACR;;;;EAKDuhB,WAAWvhB,SAAS;AAElB,SAAKwhB,cAAcxhB,QAAQrD,KAA3B;AACA,SAAKwkB,aAAarlB,KAAKkE,OAAvB;AAEA,QAAI,KAAKmhB,aAAa/R,SAAS,KAAK6R,OAAO;AAEzC,YAAMQ,gBAAgB,KAAKN,aAAapS,UAAW2S,UAAS;AAC1D,eAAO,CAACA,KAAKtD,cAAc,CAACsD,KAAKjgB;MAClC,CAFqB;AAGtB,UAAIggB,kBAAkB,IAAI;AACxB,cAAME,cAAc,KAAKR,aAAajS,OAAOuS,eAAe,CAAxC,EAA2C,CAA3C;AACpBE,oBAAYngB,QAAZ;MACD;IACF;EACF;;;;;;EAODggB,cAAc7kB,OAAO;AACnB,UAAM8kB,gBAAgB,KAAKN,aAAapS,UAAU2S,UAAQA,KAAK/kB,UAAUA,KAAnD;AACtB,QAAI8kB,kBAAkB,IAAI;AACxB,WAAKN,aAAajS,OAAOuS,eAAe,CAAxC;IACD;EACF;;;;;EAMDH,kBAAkB3kB,OAAO;AACvB,WAAO,KAAKwkB,aAAaS,KAAK5hB,aAAWA,QAAQrD,UAAUA,KAApD;EACR;EAED6E,UAAU;AACR,SAAK2f,aAAa9lB,QAAQ2E,aAAWA,QAAQwB,QAAR,CAArC;AACA,SAAK2f,eAAe,CAAA;EACrB;AAxHiB;AClEpB,IAAMU,iBAAN,cAA6B/E,UAAU;;;;;;EAMrC9L,cAAc;AAAA,QAAA;AACZ,QAAI8Q,WAAW;AACf,UAAMC,cAAa,gBAAA,KAAK9lB,aAAR,QAAA,kBAAA,SAAA,SAAG,cAAc8lB;AAEjC,QAAIA,cAAc,YAAYA,YAAY;AAExCD,iBAAWC,WAAW3S;IACvB,WAAU2S,cAAc,aAAaA,YAAY;AAEhD,UAAI,CAACA,WAAWtH,OAAO;AACrBsH,mBAAWtH,QAAQ,KAAKuH,uBAAuBD,WAAWE,OAAvC;MACpB;AAED,UAAIF,WAAWtH,OAAO;AACpBqH,mBAAWC,WAAWtH,MAAMrL;MAC7B;IACF;AAGD,UAAM3F,QAAQ,KAAK9L,SAAS,YAAY;MACtCokB;MACAD;IAFsC,CAA1B;AAId,WAAO,KAAKlT,aAAa,YAAYnF,MAAMqY,UAAUC,UAA9C;EACR;;;;;;EAODpB,sBAAsBxhB,WAAWxC,OAAO;AACtC,WAAO,IAAIshB,QAAQ9e,WAAW,MAAMxC,KAA7B;EACR;;;;;;;;;;;EAYDokB,YAAYpkB,OAAO;AAAA,QAAA;AACjB,UAAMolB,cAAa,iBAAA,KAAK9lB,aAAR,QAAA,mBAAA,SAAA,SAAG,eAAc8lB;AAEjC,QAAIG,iBAAiB,CAAA;AACrB,QAAIvoB,MAAMC,QAAQmoB,UAAd,GAA2B;AAE7BG,uBAAiBH,WAAWplB,KAAD;IAC5B,WAAUolB,cAAc,aAAaA,YAAY;AAMhD,UAAI,CAACA,WAAWtH,OAAO;AACrBsH,mBAAWtH,QAAQ,KAAKuH,uBAAuBD,WAAWE,OAAvC;MACpB;AAEDC,uBAAiBH,WAAWtH,MAAM9d,KAAjB;IAClB;AAED,QAAID,WAAWwlB;AAEf,QAAIxlB,oBAAoBjD,SAAS;AAC/BiD,iBAAW,KAAKylB,sBAAsBzlB,QAA3B;IACZ;AAID,UAAM+M,QAAQ,KAAK9L,SAAS,YAAY;MACtCjB,UAAUA,YAAY,CAAA;MACtBC;IAFsC,CAA1B;AAKd,WAAO,KAAKiS,aAAa,YAAYnF,MAAM/M,UAAUC,KAA9C;EACR;;;;;;;;EASDqlB,uBAAuBI,gBAAgB;AAAA,QAAA,gBAAA;AACrC,SAAI,iBAAA,KAAKnmB,aAAL,QAAA,mBAAA,UAAA,eAAcomB,aAAd,iBAA0B,KAAKpmB,aAA/B,QAAA,mBAAA,UAA0B,eAAcqmB,eAAe;AACzD,aAAOlpB,sBACL,KAAK6C,QAAQomB,UACb,KAAKpmB,QAAQqmB,eACbF,cAH0B,KAIvB,CAAA;IACN;AAED,WAAO,CAACA,cAAD;EACR;;;;;;;EAQDD,sBAAsBjgB,SAAS;AAE7B,UAAMxF,WAAW;MACfwF;;AAGF,UAAMqgB;;MACJrgB,QAAQ9M,YAAY,MAChB8M,UACAA,QAAQua,cAAc,GAAtB;;AAGN,QAAI8F,QAAQ;AAGV7lB,eAASqhB,MAAMwE,OAAOzC,QAAQ0C,WAAWD,OAAOE;AAEhD,UAAIF,OAAOzC,QAAQ4C,YAAY;AAC7BhmB,iBAAS2iB,SAASkD,OAAOzC,QAAQ4C;MAClC;AAEDhmB,eAAS/E,QAAQ4qB,OAAOzC,QAAQ6C,YAAY3C,SAASuC,OAAOzC,QAAQ6C,WAAW,EAA3B,IAAiC;AACrFjmB,eAAS9E,SAAS2qB,OAAOzC,QAAQ8C,aAAa5C,SAASuC,OAAOzC,QAAQ8C,YAAY,EAA5B,IAAkC;AAGxFlmB,eAASjF,IAAIiF,SAAS/E;AACtB+E,eAAShF,IAAIgF,SAAS9E;AAEtB,UAAI2qB,OAAOzC,QAAQ+C,UAAU;AAC3BnmB,iBAAS3B,OAAOwnB,OAAOzC,QAAQ+C;MAChC;AAED,YAAMC,cAAc5gB,QAAQua,cAAc,KAAtB;AAEpB,UAAIqG,aAAa;AAAA,YAAA;AAGfpmB,iBAASmiB,OAAOiE,YAAYC,cAAcD,YAAY/E;AACtDrhB,iBAASohB,OAAT,wBAAegF,YAAYE,aAAa,KAAzB,OAAf,QAAA,0BAAA,SAAA,wBAAkD;MACnD;AAED,UAAIT,OAAOzC,QAAQmD,eAAeV,OAAOzC,QAAQoD,SAAS;AACxDxmB,iBAASggB,eAAe;MACzB;IACF;AAED,WAAO,KAAK9N,aAAa,eAAelS,UAAUwF,SAASqgB,MAApD;EACR;;;;;;;;EASD7B,aAAahkB,UAAUC,OAAO;AAC5B,WAAO+jB,aAAahkB,UAAU,MAAMC,KAAjB;EACpB;AA1KoC;ACGvC,IAAMwmB,cAAc;AAOpB,IAAMC,SAAN,MAAa;;;;EAIXzoB,YAAYuB,OAAM;AAChB,SAAKA,OAAOA;AACZ,SAAKmnB,WAAW;AAChB,SAAKtjB,SAAS;AACd,SAAKob,YAAY;AACjB,SAAKmI,YAAY;AAKjB,SAAKC,YAAYxtB;AAEjB,SAAKytB,gBAAgB;AAErB,SAAKC,eAAe;AAEpB,SAAKC,sBAAsB;AAE3B,SAAKC,oBAAoB;AAKzB,SAAKC,eAAe7tB;AAKpB,SAAK8tB,kBAAkB9tB;AAKvB,SAAK+tB,kBAAkB/tB;AAKvB,SAAKguB,kBAAkBhuB;AAMvB,SAAKiuB,eAAejuB;AAGpB,SAAKkuB,eAAe,KAAKA,aAAa5X,KAAK,IAAvB;AAGpBnQ,IAAAA,MAAK+P,GAAG,gBAAgB,KAAKgY,YAA7B;EACD;EAEDC,OAAO;AACL,SAAKD,aAAL;AACA,SAAK1N,OAAL;EACD;EAED9P,QAAQ;AACN,QAAI,KAAK4c,YAAY,KAAKlI,aAAa,KAAKmI,WAAW;AAIrD;IACD;AAED,UAAMjmB,QAAQ,KAAKnB,KAAK+H;AAExB,SAAKlE,SAAS;AACd,SAAKujB,YAAY;AACjB,SAAKnI,YAAY;AACjB,SAAKoI,YAAY,KAAKrnB,KAAKD,QAAQkoB;AAEnC,QAAI9mB,SAASA,MAAMC,gBAAgBD,MAAM1F,SAAS,KAAKuE,KAAKD,QAAQmoB,mBAAmB;AACrF,WAAKb,YAAY;IAClB;AAED,SAAKc,iBAAL;AACA9V,eAAW,MAAM;AACf,WAAKgI,OAAL;IACD,GAAE,KAAKkN,eAAe,KAAK,CAFlB;EAGX;;EAGDQ,eAAe;AACb,SAAK/nB,KAAKuhB,IAAI,gBAAgB,KAAKwG,YAAnC;AACA,QAAI,CAAC,KAAKX,WAAW;AACnB,YAAMjmB,QAAQ,KAAKnB,KAAK+H;AACxB,WAAKqf,YAAY;AACjB,WAAKnI,YAAY;AACjB,WAAKoI,YAAY,KAAKrnB,KAAKD,QAAQqoB;AACnC,UAAIjnB,SAASA,MAAM6B,WAAWT,UAAUpB,MAAM1F,SAAS,KAAKuE,KAAKD,QAAQmoB,mBAAmB;AAC1F,aAAKb,YAAY;MAClB;AACD,WAAKc,iBAAL;IACD;EACF;;EAGDA,mBAAmB;AACjB,UAAM;MAAEnoB,MAAAA;IAAF,IAAW;AACjB,UAAMmB,QAAQ,KAAKnB,KAAK+H;AACxB,UAAM;MAAEhI;IAAF,IAAcC;AAEpB,QAAID,QAAQsoB,0BAA0B,QAAQ;AAC5CtoB,cAAQuoB,kBAAkB;AAC1B,WAAKR,eAAejuB;IACrB,WAAUkG,QAAQsoB,0BAA0B,QAAQ;AACnDtoB,cAAQuoB,kBAAkB;AAC1B,WAAKjB,YAAY;AACjB,WAAKS,eAAejuB;eACX,KAAKutB,aAAapnB,MAAKuoB,qBAAqB;AAErD,WAAKT,eAAe9nB,MAAKuoB;IAC1B,OAAM;AACL,WAAKT,eAAe,KAAK9nB,KAAKigB,eAAV;IACrB;AAED,SAAKyH,eAAevmB,UAApB,QAAoBA,UAAAA,SAAAA,SAAAA,MAAO2E,sBAAP;AAEpB9F,IAAAA,MAAKuG,WAAWsC,QAAhB;AAGA,SAAKye,gBAAgBzf,QAAQ,KAAKwf,aAAa,KAAKA,YAAY,EAApC;AAC5B,SAAKmB,eAAe3gB,QAAQ,KAAKigB,YAAN,MACJ3mB,UADH,QACGA,UADH,SAAA,SACGA,MAAO2C,QAAQ2e,eAAf,OACC,CAAC,KAAKxD,aAAa,CAACjf,MAAKmF,WAAWC,UAAhB;AAC5C,QAAI,CAAC,KAAKojB,cAAc;AACtB,WAAKhB,sBAAsB;AAE3B,UAAI,KAAKJ,aAAajmB,OAAO;AAC3BA,cAAM6D,oBAAN;AACA7D,cAAM8D,oBAAN;MACD;IACF,OAAM;AAAA,UAAA;AACL,WAAKuiB,uBAAsBznB,wBAAAA,QAAQuoB,qBAAnC,QAAA,0BAAA,SAAA,wBAAsD;IACvD;AACD,SAAKb,oBAAoB,CAAC,KAAKD,uBAAuB,KAAKxnB,KAAKD,QAAQqJ,YAAY6d;AACpF,SAAKU,kBAAkB,KAAKH,sBAAsBxnB,MAAKgG,UAAUhG,MAAKyoB;AAEtE,QAAI,CAAC,KAAKnB,eAAe;AACvB,WAAKD,YAAY;AACjB,WAAKmB,eAAe;AACpB,WAAKf,oBAAoB;AACzB,WAAKD,sBAAsB;AAC3B,UAAI,KAAKJ,WAAW;AAClB,YAAIpnB,MAAKgG,SAAS;AAChBhG,UAAAA,MAAKgG,QAAQlL,MAAM4tB,UAAU1E,OAAOiD,WAAD;QACpC;AACDjnB,QAAAA,MAAKsJ,eAAe,CAApB;MACD;AACD;IACD;AAED,QAAI,KAAKkf,gBAAgB,KAAKV,gBAAgB,KAAKA,aAAa9H,WAAW;AAAA,UAAA;AAEzE,WAAKuH,eAAe;AACpB,WAAKK,kBAAkB,KAAK5nB,KAAKiE;AACjC,WAAK4jB,mBAAL,uBAAuB,KAAK7nB,KAAK+H,eAAjC,QAAA,yBAAA,SAAA,SAAuB,qBAAqB7D;AAE5C,UAAIlE,MAAKiE,WAAW;AAClBjE,QAAAA,MAAKiE,UAAUnJ,MAAM6tB,WAAW;AAChC3oB,QAAAA,MAAKiE,UAAUnJ,MAAMW,QAAQuE,MAAKO,aAAa7G,IAAI;MACpD;IACF,OAAM;AACL,WAAK6tB,eAAe;IACrB;AAED,QAAI,KAAKH,WAAW;AAElB,UAAI,KAAKI,qBAAqB;AAC5B,YAAIxnB,MAAKgG,SAAS;AAChBhG,UAAAA,MAAKgG,QAAQlL,MAAM4tB,UAAU1E,OAAOiD,WAAD;QACpC;AACDjnB,QAAAA,MAAKsJ,eAAe,CAApB;MACD,OAAM;AACL,YAAI,KAAKme,qBAAqBznB,MAAKyoB,IAAI;AACrCzoB,UAAAA,MAAKyoB,GAAG3tB,MAAM4tB,UAAU1E,OAAOiD,WAAD;QAC/B;AACD,YAAIjnB,MAAKgG,SAAS;AAChBhG,UAAAA,MAAKgG,QAAQlL,MAAM4tB,UAAU;QAC9B;MACF;AAED,UAAI,KAAKF,cAAc;AACrB,aAAKI,uBAAL;AACA,YAAI,KAAKlB,cAAc;AAErB,eAAKA,aAAa5sB,MAAM+tB,aAAa;AAIrC,eAAKnB,aAAa5sB,MAAM4tB,UAAU1E,OAAOiD,WAAD;QACzC;MACF;IACF,WAAU,KAAKhI,WAAW;AAGzB,UAAIjf,MAAKmF,WAAW+O,YAAY,CAA5B,GAAgC;AAClClU,QAAAA,MAAKmF,WAAW+O,YAAY,CAA5B,EAA+B9a,GAAG0B,MAAM8Z,UAAU;MACnD;AACD,UAAI5U,MAAKmF,WAAW+O,YAAY,CAA5B,GAAgC;AAClClU,QAAAA,MAAKmF,WAAW+O,YAAY,CAA5B,EAA+B9a,GAAG0B,MAAM8Z,UAAU;MACnD;AAED,UAAI,KAAK2S,cAAc;AACrB,YAAIvnB,MAAKmF,WAAWzL,MAAM,GAAG;AAE3BsG,UAAAA,MAAKmF,WAAWqP,cAAhB;AACAxU,UAAAA,MAAKmF,WAAWK,OAAhB;QACD;MACF;IACF;EACF;;EAGD6U,SAAS;AACP,QAAI,KAAK+M,aACF,KAAKE,iBACL,KAAKI,gBACL,KAAKA,aAAaxuB,YAAY,OAAO;AAO1C,UAAI+C,QAASC,aAAY;AACvB,YAAI4sB,UAAU;AACd,YAAIC,aAAa;AACjBntB;;UAA6C,KAAK8rB;QAAvC,EAAsDrD,QAAQ,MAAM;AAC7EyE,oBAAU;AACV,cAAI,CAACC,YAAY;AACf7sB,oBAAQ,IAAD;UACR;SAJH;AAMAmW,mBAAW,MAAM;AACf0W,uBAAa;AACb,cAAID,SAAS;AACX5sB,oBAAQ,IAAD;UACR;WACA,EALO;AAMVmW,mBAAWnW,SAAS,GAAV;MACX,CAhBD,EAgBGmoB,QAAQ,MAAM,KAAK2E,UAAL,CAhBjB;IAiBD,OAAM;AACL,WAAKA,UAAL;IACD;EACF;;EAGDA,YAAY;AAAA,QAAA,oBAAA;AACV,KAAA,qBAAA,KAAKhpB,KAAKgG,aAAV,QAAA,uBAAA,UAAA,mBAAmBlL,MAAMmuB,YAAY,8BAA8B,KAAK5B,YAAY,IAApF;AAEA,SAAKrnB,KAAKyB,SACR,KAAK2lB,YAAY,0BAA0B,uBAD7C;AAKA,SAAKpnB,KAAKyB;;MAEP,iBAAiB,KAAK2lB,YAAY,OAAO;IAF5C;AAKA,KAAKpnB,sBAAAA,KAAAA,KAAKgG,aAAS6H,QAAAA,wBAAAA,UAAAA,oBAAAA,UAAUW,OAAO,oBAAoB,KAAK4Y,SAA7D;AAEA,QAAI,KAAKA,WAAW;AAClB,UAAI,KAAKM,cAAc;AAErB,aAAKA,aAAa5sB,MAAM4tB,UAAU;MACnC;AACD,WAAKQ,oBAAL;IACD,WAAU,KAAKjK,WAAW;AACzB,WAAKkK,sBAAL;IACD;AAED,QAAI,CAAC,KAAK7B,eAAe;AACvB,WAAK8B,qBAAL;IACD;EACF;;EAGDA,uBAAuB;AACrB,UAAM;MAAEppB,MAAAA;IAAF,IAAW;AACjB,SAAK6D,SAAS,KAAKujB;AACnB,SAAKD,WAAW,KAAKlI;AACrB,SAAKmI,YAAY;AACjB,SAAKnI,YAAY;AAEjBjf,IAAAA,MAAKyB,SACH,KAAKoC,SAAS,wBAAwB,qBADxC;AAKA7D,IAAAA,MAAKyB;;MAEF,iBAAiB,KAAKoC,SAAS,UAAU;IAF5C;AAKA,QAAI,KAAKsjB,UAAU;AACjBnnB,MAAAA,MAAKsF,QAAL;IACD,WAAU,KAAKzB,QAAQ;AAAA,UAAA;AACtB,UAAI,KAAK2kB,gBAAgBxoB,MAAKiE,WAAW;AACvCjE,QAAAA,MAAKiE,UAAUnJ,MAAM6tB,WAAW;AAChC3oB,QAAAA,MAAKiE,UAAUnJ,MAAMW,QAAQ;MAC9B;AACD,OAAA,kBAAAuE,MAAK+H,eAAL,QAAA,oBAAA,UAAA,gBAAgB9C,oBAAhB;IACD;EACF;;EAGDikB,sBAAsB;AACpB,UAAM;MAAElpB,MAAAA;IAAF,IAAW;AACjB,QAAI,KAAKwoB,cAAc;AACrB,UAAI,KAAKjB,gBAAgB,KAAKK,mBAAmB,KAAKC,iBAAiB;AACrE,aAAKwB,WAAW,KAAKzB,iBAAiB,aAAa,oBAAnD;AACA,aAAKyB,WAAW,KAAKxB,iBAAiB,aAAa,MAAnD;MACD;AAED,UAAI7nB,MAAK+H,WAAW;AAClB/H,QAAAA,MAAK+H,UAAU/C,oBAAf;AACA,aAAKqkB,WACHrpB,MAAK+H,UAAU9D,WACf,aACAjE,MAAK+H,UAAUd,oBAAf,CAHF;MAKD;IACF;AAED,QAAI,KAAKwgB,qBAAqBznB,MAAKyoB,IAAI;AACrC,WAAKY,WAAWrpB,MAAKyoB,IAAI,WAAWzE,OAAOhkB,MAAKD,QAAQqJ,SAAd,CAA1C;IACD;AAED,QAAI,KAAKoe,uBAAuBxnB,MAAKgG,SAAS;AAC5C,WAAKqjB,WAAWrpB,MAAKgG,SAAS,WAAW,GAAzC;IACD;EACF;;EAGDmjB,wBAAwB;AACtB,UAAM;MAAEnpB,MAAAA;IAAF,IAAW;AAEjB,QAAI,KAAKwoB,cAAc;AACrB,WAAKI,uBAAuB,IAA5B;IACD;AAGD,QAAI,KAAKnB,qBAAqBznB,MAAKoJ,YAAY,QAAQpJ,MAAKyoB,IAAI;AAC9D,WAAKY,WAAWrpB,MAAKyoB,IAAI,WAAW,GAApC;IACD;AAED,QAAI,KAAKjB,uBAAuBxnB,MAAKgG,SAAS;AAC5C,WAAKqjB,WAAWrpB,MAAKgG,SAAS,WAAW,GAAzC;IACD;EACF;;;;;EAMD4iB,uBAAuB7T,SAAS;AAC9B,QAAI,CAAC,KAAK+S,aAAc;AAExB,UAAM;MAAE9nB,MAAAA;IAAF,IAAW;AACjB,UAAM;MAAEggB;IAAF,IAAgB,KAAK8H;AAC3B,UAAM;MAAE/f;MAAWxH;IAAb,IAA8BP;AAEpC,QAAI,KAAKunB,gBAAgBvH,aAAa,KAAK4H,mBAAmB,KAAKC,iBAAiB;AAClF,YAAMyB,mBAAmB,CAAC/oB,aAAa7G,KAAK,KAAKouB,aAAapuB,IAAIsmB,UAAUtmB,KAAKsmB,UAAUzkB;AAC3F,YAAMguB,mBAAmB,CAAChpB,aAAa5G,KAAK,KAAKmuB,aAAanuB,IAAIqmB,UAAUrmB,KAAKqmB,UAAUxkB;AAC3F,YAAMguB,mBAAmBjpB,aAAa7G,IAAIsmB,UAAUzkB;AACpD,YAAMkuB,mBAAmBlpB,aAAa5G,IAAIqmB,UAAUxkB;AAGpD,UAAIuZ,SAAS;AACX,aAAKsU,WACH,KAAKzB,iBACL,aACAltB,kBAAkB4uB,kBAAkBC,gBAAnB,CAHnB;AAMA,aAAKF,WACH,KAAKxB,iBACL,aACAntB,kBAAkB8uB,kBAAkBC,gBAAnB,CAHnB;MAKD,OAAM;AACL5uB,qBAAa,KAAK+sB,iBAAiB0B,kBAAkBC,gBAAzC;AACZ1uB,qBAAa,KAAKgtB,iBAAiB2B,kBAAkBC,gBAAzC;MACb;IACF;AAED,QAAI1hB,WAAW;AACbxO,qBAAewO,UAAUrE,KAAKsc,aAAa,KAAK8H,YAAlC;AACd/f,gBAAU3G,gBAAgB,KAAK0mB,aAAavsB,IAAIwM,UAAUtM;AAC1D,UAAIsZ,SAAS;AACX,aAAKsU,WAAWthB,UAAU9D,WAAW,aAAa8D,UAAUd,oBAAV,CAAlD;MACD,OAAM;AACLc,kBAAU9C,oBAAV;MACD;IACF;EACF;;;;;;;EAQDokB,WAAWzqB,QAAQ1D,MAAMN,WAAW;AAClC,QAAI,CAAC,KAAKysB,WAAW;AACnBzoB,aAAO9D,MAAMI,IAAb,IAAqBN;AACrB;IACD;AAED,UAAM;MAAE2L;IAAF,IAAiB,KAAKvG;AAE5B,UAAM0pB,YAAY;MAChBvuB,UAAU,KAAKksB;MACflgB,QAAQ,KAAKnH,KAAKD,QAAQoH;MAC1BD,YAAY,MAAM;AAChB,YAAI,CAACX,WAAW6T,iBAAiBlH,QAAQ;AACvC,eAAKkW,qBAAL;QACD;;MAEHxqB;;AAEF8qB,cAAUxuB,IAAD,IAASN;AAClB2L,eAAWO,gBAAgB4iB,SAA3B;EACD;AAhbU;AC4Mb,IAAMC,iBAAiB;EACrBte,gBAAgB;EAChBgJ,SAAS;EACTuI,MAAM;EACNjQ,cAAc;EACd1D,qBAAqB;EACrBgf,uBAAuB;EACvBG,uBAAuB;EACvB/gB,uBAAuB;EACvB2Q,QAAQ;EACRC,WAAW;EACXb,WAAW;EACXO,aAAa;EACbuQ,mBAAmB;EACnB3Z,yBAAyB;EACzB6Q,kBAAkB;EAClBwK,eAAe;EACfC,WAAW;EACXzX,iBAAiB;EACjB6L,mBAAmB;EACnBL,gBAAgB;EAChBxU,WAAW;EAEX3I,OAAO;EACP0jB,UAAU;EACVa,SAAS,CAAC,GAAG,CAAJ;EACT7d,QAAQ;AA1Ba;AAgCvB,IAAM2iB,aAAN,cAAyBnE,eAAe;;;;EAItClnB,YAAYsB,SAAS;AACnB,UAAA;AAEA,SAAKA,UAAU,KAAKgqB,gBAAgBhqB,WAAW,CAAA,CAAhC;AAOf,SAAKyT,SAAS;MAAE9Z,GAAG;MAAGC,GAAG;;AAMzB,SAAKqwB,oBAAoB;MAAEtwB,GAAG;MAAGC,GAAG;;AAOpC,SAAK4G,eAAe;MAAE7G,GAAG;MAAGC,GAAG;;AAK/B,SAAKyP,YAAY;AACjB,SAAK5F,YAAY;AACjB,SAAK0R,iBAAiB;AACtB,SAAKrR,SAAS;AACd,SAAKomB,eAAe;AACpB,SAAKC,WAAW;AAMhB,SAAKC,mBAAmB,CAAA;AAExB,SAAK5B,sBAAsB1uB;AAG3B,SAAK4iB,SAAS5iB;AAEd,SAAKmM,UAAUnM;AAEf,SAAKqe,WAAWre;AAEhB,SAAKoK,YAAYpK;AAEjB,SAAKoW,aAAapW;AAElB,SAAKkO,YAAYlO;AAEjB,SAAKmW,SAAS,IAAIxR,UAAJ;AACd,SAAK+H,aAAa,IAAI4T,WAAJ;AAClB,SAAKhV,aAAa,IAAI0O,WAAW,IAAf;AAClB,SAAKnL,WAAW,IAAIkG,SAAS,IAAb;AAChB,SAAKhL,SAAS,IAAIsjB,OAAO,IAAX;AACd,SAAKkD,WAAW,IAAIlT,SAAS,IAAb;AAChB,SAAKnT,gBAAgB,IAAI+gB,cAAc,IAAlB;EACtB;;EAGDrG,OAAO;AACL,QAAI,KAAK5a,UAAU,KAAKomB,cAAc;AACpC,aAAO;IACR;AAED,SAAKpmB,SAAS;AACd,SAAKpC,SAAS,MAAd;AACA,SAAKA,SAAS,YAAd;AAEA,SAAK4oB,qBAAL;AAGA,QAAIC,cAAc;AAClB,QAAI,KAAK5hB,SAAS2G,eAAe;AAC/Bib,qBAAe;IAChB;AACD,QAAI,KAAKvqB,QAAQwqB,WAAW;AAC1BD,qBAAe,MAAM,KAAKvqB,QAAQwqB;IACnC;AACD,QAAI,KAAKvkB,SAAS;AAChB,WAAKA,QAAQ/M,aAAa,MAAMqxB;IACjC;AAED,SAAK9mB,YAAY,KAAKzD,QAAQU,SAAS;AACvC,SAAKyU,iBAAiB,KAAK1R;AAC3B,SAAK/B,SAAS,aAAd;AAGA,SAAK+oB,cAAc,IAAI7P,YAAY,IAAhB;AAGnB,QAAI3Z,OAAOypB,MAAM,KAAKjnB,SAAlB,KACG,KAAKA,YAAY,KACjB,KAAKA,aAAa,KAAKsR,YAAL,GAAoB;AAC3C,WAAKtR,YAAY;IAClB;AAED,QAAI,CAAC,KAAKkF,SAAS2G,eAAe;AAEhC,WAAK6B,cAAL;IACD;AAGD,SAAKwZ,WAAL;AAEA,SAAKlX,OAAO7Z,IAAIwE,OAAOwsB;AAEvB,SAAKR,mBAAmB,KAAKtF,YAAY,KAAKrhB,SAAtB;AACxB,SAAK/B,SAAS,eAAe;MAC3BhB,OAAO,KAAK+C;MACZ3B,MAAM,KAAKsoB;MACXhpB,OAAOtH;IAHoB,CAA7B;AAOA,SAAK0uB,sBAAsB,KAAKtI,eAAL;AAC3B,SAAKxe,SAAS,eAAd;AAEA,SAAKsO,GAAG,uBAAuB,MAAM;AACnC,YAAM;QAAEmE;UAAgB,KAAK/O;AAG7B,UAAI+O,YAAY,CAAD,GAAK;AAClBA,oBAAY,CAAD,EAAI9a,GAAG0B,MAAM8Z,UAAU;AAClC,aAAKqB,WAAW/B,YAAY,CAAD,GAAK,KAAK1Q,YAAY,CAAjD;MACD;AACD,UAAI0Q,YAAY,CAAD,GAAK;AAClBA,oBAAY,CAAD,EAAI9a,GAAG0B,MAAM8Z,UAAU;AAClC,aAAKqB,WAAW/B,YAAY,CAAD,GAAK,KAAK1Q,YAAY,CAAjD;MACD;AAED,WAAKuB,YAAL;AAEA,WAAKhB,cAAcqS,WAAnB;AAEA,WAAKpG,OAAOrR,IAAIR,QAAQ,UAAU,KAAKysB,kBAAkBza,KAAK,IAA5B,CAAlC;AACA,WAAKH,OAAOrR,IAAIR,QAAQ,UAAU,KAAK0sB,wBAAwB1a,KAAK,IAAlC,CAAlC;AACA,WAAK1O,SAAS,YAAd;IACD,CApBD;AAuBA,QAAI,KAAK0D,WAAW+O,YAAY,CAA5B,GAAgC;AAClC,WAAK+B,WAAW,KAAK9Q,WAAW+O,YAAY,CAA5B,GAAgC,KAAK1Q,SAArD;IACD;AACD,SAAK/B,SAAS,QAAd;AAEA,SAAKmC,OAAOokB,KAAZ;AAEA,SAAKvmB,SAAS,WAAd;AAEA,WAAO;EACR;;;;;;;;EASD4T,eAAe5U,OAAO;AACpB,UAAM0U,YAAY,KAAKL,YAAL;AAElB,QAAI,KAAK/U,QAAQ6c,MAAM;AACrB,UAAInc,QAAQ0U,YAAY,GAAG;AACzB1U,iBAAS0U;MACV;AAED,UAAI1U,QAAQ,GAAG;AACbA,iBAAS0U;MACV;IACF;AAED,WAAO7a,MAAMmG,OAAO,GAAG0U,YAAY,CAAvB;EACb;EAEDpQ,cAAc;AACZ,SAAKI,WAAW+O,YAAY/U,QAASoV,gBAAe;AAAA,UAAA;AAClD,OAAA,oBAAAA,WAAWpT,WAAX,QAAA,sBAAA,UAAA,kBAAkB4D,YAAlB;KADF;EAGD;;;;;EAMD+lB,KAAKrqB,OAAO;AACV,SAAK0E,WAAW4E,YACd,KAAKsL,eAAe5U,KAApB,IAA6B,KAAKyU,cADpC;EAGD;;;;EAKD6V,OAAO;AACL,SAAKD,KAAK,KAAK5V,iBAAiB,CAAhC;EACD;;;;EAKD8V,OAAO;AACL,SAAKF,KAAK,KAAK5V,iBAAiB,CAAhC;EACD;;;;;;EAODjP,UAAUob,MAAM;AAAA,QAAA;AACd,KAAA,kBAAA,KAAKtZ,eAAL,QAAA,oBAAA,UAAA,gBAAgB9B,OAAO,GAAGob,IAA1B;EACD;;;;EAKDja,aAAa;AAAA,QAAA;AACX,KAAKW,mBAAAA,KAAAA,eAAL,QAAA,qBAAA,UAAA,iBAAgBX,WAAhB;EACD;;;;;EAMDmD,QAAQ;AACN,QAAI,CAAC,KAAK3G,OAAOC,UAAU,KAAKomB,cAAc;AAC5C;IACD;AAED,SAAKA,eAAe;AAEpB,SAAKxoB,SAAS,OAAd;AAEA,SAAKuO,OAAO9Q,UAAZ;AACA,SAAK0E,OAAO2G,MAAZ;EACD;;;;;;;;EASDjF,UAAU;AAAA,QAAA;AACR,QAAI,CAAC,KAAK2kB,cAAc;AACtB,WAAKlqB,QAAQsoB,wBAAwB;AACrC,WAAK9d,MAAL;AACA;IACD;AAED,SAAK9I,SAAS,SAAd;AAEA,SAAKof,aAAa,CAAA;AAElB,QAAI,KAAK5Q,YAAY;AACnB,WAAKA,WAAWI,cAAc;AAC9B,WAAKJ,WAAWK,aAAa;IAC9B;AAED,KAAKtK,gBAAAA,KAAAA,aAAL,QAAA,kBAAA,UAAA,cAAc/G,OAAd;AAEA,SAAKkG,WAAW+O,YAAY/U,QAASoV,gBAAe;AAAA,UAAA;AAClD,OAAA,qBAAAA,WAAWpT,WAAX,QAAA,uBAAA,UAAA,mBAAkBmE,QAAlB;KADF;AAIA,SAAKvB,cAAcuB,QAAnB;AACA,SAAK0K,OAAO9Q,UAAZ;EACD;;;;;;EAOD+rB,oBAAoBC,YAAY;AAC9B,SAAKnnB,cAAcuhB,cAAc4F,UAAjC;AACA,SAAK/lB,WAAW+O,YAAY/U,QAAQ,CAACoV,YAAYG,MAAM;AAAA,UAAA,uBAAA;AACrD,UAAIyW,yBAAwB,yBAAA,mBAAA,KAAKpjB,eAAN,QAAA,qBAAA,SAAA,SAAC,iBAAgBtH,WAAS,QAAA,0BAAA,SAAA,wBAAA,KAAK,IAAIiU;AAC9D,UAAI,KAAKU,QAAL,GAAgB;AAClB+V,+BAAuB,KAAK9V,eAAe8V,oBAApB;MACxB;AACD,UAAIA,yBAAyBD,YAAY;AAEvC,aAAKjV,WAAW1B,YAAY2W,YAAY,IAAxC;AAGA,YAAIxW,MAAM,GAAG;AAAA,cAAA;AACX,eAAK3M,YAAYwM,WAAWpT;AAC5B,WAAA,qBAAAoT,WAAWpT,WAAOoD,QAAAA,uBAAAA,UAAAA,mBAAAA,YAAY,IAA9B;QACD;MACF;KAdH;AAiBA,SAAK9C,SAAS,QAAd;EACD;;;;;;;;EAUDwU,WAAWmV,QAAQ3qB,OAAOiF,OAAO;AAC/B,QAAI,KAAK0P,QAAL,GAAgB;AAClB3U,cAAQ,KAAK4U,eAAe5U,KAApB;IACT;AAED,QAAI2qB,OAAOjqB,OAAO;AAChB,UAAIiqB,OAAOjqB,MAAMV,UAAUA,SAAS,CAACiF,OAAO;AAG1C;MACD;AAGD0lB,aAAOjqB,MAAMmE,QAAb;AACA8lB,aAAOjqB,QAAQtH;IAChB;AAGD,QAAI,CAAC,KAAKub,QAAL,MAAmB3U,QAAQ,KAAKA,SAAS,KAAKqU,YAAL,IAAqB;AACjE;IACD;AAED,UAAMtU,WAAW,KAAKqkB,YAAYpkB,KAAjB;AACjB2qB,WAAOjqB,QAAQ,IAAImC,MAAM9C,UAAUC,OAAO,IAA3B;AAGf,QAAIA,UAAU,KAAK+C,WAAW;AAC5B,WAAKuE,YAAYqjB,OAAOjqB;IACzB;AAEDiqB,WAAOjqB,MAAMuD,OAAO0mB,OAAOhyB,EAA3B;EACD;;EAGDoO,yBAAyB;AACvB,WAAO;MACL9N,GAAG,KAAK6G,aAAa7G,IAAI;MACzBC,GAAG,KAAK4G,aAAa5G,IAAI;;EAE5B;;;;;;;EAQD+wB,WAAWhlB,OAAO;AAIhB,QAAI,KAAKukB,cAAc;AAGrB;IACD;AAKD,UAAM/pB,kBAAkBJ,gBAAgB,KAAKC,SAAS,IAAf;AAEvC,QAAI,CAAC2F,SAASrL,YAAY6F,iBAAiB,KAAK8pB,iBAAvB,GAA2C;AAElE;IACD;AAIDzwB,mBAAe,KAAKywB,mBAAmB9pB,eAAzB;AAEd,SAAKuB,SAAS,cAAd;AAEAlI,mBAAe,KAAKgH,cAAc,KAAKypB,iBAAzB;AAEd,SAAKa,wBAAL;AAEA,SAAKppB,SAAS,cAAd;AAIA,SAAK0D,WAAWK,OAAO,KAAK5B,OAAOC,MAAnC;AAEA,QAAI,CAAC,KAAKqmB,YAAY/rB,OAAOktB,WAAW,oBAAlB,EAAwC/K,SAAS;AACrE,WAAKpP,cAAL;IACD;AAED,SAAKzP,SAAS,QAAd;EACD;;;;EAKD6H,eAAeof,SAAS;AACtB,SAAKtf,YAAYpP,KAAKS,IAAIiuB,SAAS,CAAlB;AACjB,QAAI,KAAKD,IAAI;AACX,WAAKA,GAAG3tB,MAAM4tB,UAAU1E,OAAO,KAAK5a,YAAY,KAAKrJ,QAAQqJ,SAA/B;IAC/B;EACF;;;;EAKD8H,gBAAgB;AACd,QAAI,CAAC,KAAKgZ,UAAU;AAAA,UAAA;AAClB,WAAKA,WAAW;AAChB,OAAA,iBAAA,KAAKlkB,aAAS6H,QAAAA,mBAAAA,UAAAA,eAAAA,UAAUlP,IAAI,iBAA5B;IACD;EACF;;;;;;EAODisB,oBAAoB;AAClB,SAAKF,WAAL;AAOA,QAAI,oBAAoBY,KAAKntB,OAAOJ,UAAUwtB,SAA1C,GAAsD;AACxDlZ,iBAAW,MAAM;AACf,aAAKqY,WAAL;SACC,GAFO;IAGX;EACF;;;;;;;;EASDG,0BAA0B;AACxB,SAAKW,gBAAgB,GAAGrtB,OAAOwsB,WAA/B;EACD;;;;;EAMDa,gBAAgB9xB,GAAGC,GAAG;AACpB,SAAK6Z,OAAO9Z,IAAIA;AAChB,SAAK8Z,OAAO7Z,IAAIA;AAChB,SAAK8H,SAAS,oBAAd;EACD;;;;;;;EAQD4oB,uBAAuB;AAErB,SAAKrkB,UAAUhN,cAAc,QAAQ,KAAT;AAC5B,SAAKgN,QAAQ2O,aAAa,YAAY,IAAtC;AACA,SAAK3O,QAAQ2O,aAAa,QAAQ,QAAlC;AAGA,SAAKuD,WAAW,KAAKlS;AAIrB,SAAKyiB,KAAKzvB,cAAc,YAAY,OAAO,KAAKgN,OAAzB;AACvB,SAAKiK,aAAajX,cAAc,qBAAqB,WAAW,KAAKgN,OAAtC;AAC/B,SAAK/B,YAAYjL,cAAc,mBAAmB,OAAO,KAAKiX,UAAhC;AAG9B,SAAKA,WAAW0E,aAAa,wBAAwB,UAArD;AACA,SAAK1Q,UAAU0Q,aAAa,aAAa,KAAzC;AACA,SAAK1Q,UAAU0Q,aAAa,MAAM,aAAlC;AAEA,SAAKxP,WAAWsP,cAAhB;AAEA,SAAKoJ,KAAK,IAAIO,GAAG,IAAP;AACV,SAAKP,GAAGY,KAAR;AAGA,KAAC,KAAK1e,QAAQ5G,cAAcE,SAASoyB,MAAMnyB,YAAY,KAAK0M,OAA5D;EACD;;;;;;;;;EAWDia,iBAAiB;AACf,WAAOA,eACL,KAAKzc,WACL,KAAKuE,YAAY,KAAKA,UAAUlG,OAAO,KAAKsoB,kBAC5C,IAHmB;EAKtB;;;;;EAMD/U,UAAU;AACR,WAAQ,KAAKrV,QAAQ6c,QAAQ,KAAK9H,YAAL,IAAqB;EACnD;;;;;;EAODiV,gBAAgBhqB,SAAS;AACvB,QAAI5B,OAAOktB,WAAW,0CAAlB,EAA8D/K,SAAS;AACzEvgB,cAAQsoB,wBAAwB;AAChCtoB,cAAQsH,wBAAwB;IACjC;AAGD,WAAO;MACL,GAAGsiB;MACH,GAAG5pB;;EAEN;AAhiBqC;;;ACzPxC,IAAA2rB,gBAAyE;AACzE,uBAA6B;AAC7B,wBAAsB;;;ACXtB,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,EAAE,wBAAwB,CAAC,IAAI,GAAG;AACpC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAO,qBAAQ;;;ACRf,SAAS,aAAa,KAAK;AACzB,SAAO,OAAO,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,KAAK,GAAG;AAC5F;AACA,IAAO,yBAAQ;;;ACHf,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,aAAa;AAC/C,UAAM,CAAC,KAAK,KAAK,IAAI,SAAS,MAAM,GAAG;AACvC,QAAI,KAAK;AACP,UAAI,GAAG,IAAI;AAAA,IACb;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAO,yBAAQ;;;ACPf,SAAS,wBAAwB,MAAM;AACrC,QAAM,MAAM,uBAAa,IAAI;AAC7B,SAAO,IAAI;AACX,SAAO,IAAI;AACX,SAAO,uBAAa,GAAG;AACzB;AACA,IAAO,uCAAQ;;;ACRf,SAAS,eAAe;AACtB,SAAO,OAAO,SAAS,KAAK,UAAU,CAAC;AACzC;AACA,IAAO,yBAAQ;;;ACHf,SAAS,aAAa;AACpB,SAAO,GAAG,OAAO,SAAS,QAAQ,GAAG,OAAO,SAAS,MAAM;AAC7D;AACA,IAAO,uBAAQ;;;ACFf,IAAM,oCAAoC,UAAQ;AAChD,QAAM,YAAY,uBAAa,IAAI;AACnC,SAAO,QAAQ,UAAU,GAAG,KAAK,QAAQ,UAAU,GAAG;AACxD;AACA,IAAO,gDAAQ;;;ACLf,SAAS,2BAA2B,OAAO,UAAU;AACnD,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,SAAO,WAAW,SAAS,UAAU,EAAE,IAAI,IAAI;AACjD;AACA,IAAO,yCAAQ;;;ACNR,IAAM,aAAN,cAAyB,MAAM;AAAA,EACpC,YAAY,MAAM,IAAI;AACpB,UAAM;AACN,SAAK,UAAU;AAAA,MACb,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP;AACF;;;ACVA,IAAM,wBAAwB,WAAS,MAAM,CAAC,EAAE,mBAAmB;AACnE,IAAO,oCAAQ;;;ACCf,IAAM,kBAAkB,WAAS;AAC/B,MAAI,kCAAsB,KAAK,GAAG;AAChC,WAAO;AAAA,EACT;AACA,QAAM,IAAI,WAAW;AACvB;AACA,IAAO,4BAAQ;;;ACRf,mBAA8B;AACvB,IAAM,cAAU,4BAAc;AAAA,EACnC,QAAQ,MAAM;AAAA,EAAC;AAAA,EACf,KAAK,MAAM;AAAA,EAAC;AAAA,EACZ,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,MAAM,MAAM;AAAA,EAAC;AAAA,EACb,iBAAiB,MAAM;AACzB,CAAC;;;ACHD,IAAqB,yBAArB,MAA4C;AAAA,EAC1C,YAAYC,OAAM;AAChB,SAAK,OAAOA;AACZ,SAAK,KAAKA,MAAK,GAAG,KAAKA,KAAI;AAC3B,SAAK,MAAMA,MAAK,IAAI,KAAKA,KAAI;AAC7B,SAAK,WAAWA,MAAK,SAAS,KAAKA,KAAI;AAAA,EACzC;AACF;;;AbXA,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAuBA,IAAI,OAAO;AAIJ,IAAM,UAAU,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAS,IAAI;AACvD,QAAM,YAAQ,sBAAO,oBAAI,IAAI,CAAC;AAK9B,QAAM,uBAAmB,sBAAO,IAAI;AACpC,QAAM,WAAO,2BAAY,CAAC,WAAW,UAAU,WAAW,MAAM;AAG9D,QAAI,MAAM;AACR;AAAA,IACF;AACA,UAAM,UAAU,MAAM,KAAK,MAAM,OAAO;AACxC,QAAI,OAAO,cAAc,aAAa,QAAQ,SAAS,MAAM,UAAa,CAAC,kCAAsB,QAAQ,SAAS,CAAC,IAAI;AACrH,YAAM,IAAI,WAAW,2BAA2B,SAAS,EAAE;AAAA,IAC7D;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,QAAQ,IAAI,yBAAe,EAAE,KAAK,CAAC,CAAC;AAAA,MACtC,SAAS;AAAA,IACX,CAAC,GAAG,CAAC;AAAA,MACH,SAAS;AAAA,IACX,CAAC,MAAM,mBAAU,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,OAAO,MAAM;AAChD,YAAM,CAAC,KAAK,EAAE,IAAI,OAChB;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,MACN,IAAI,IACJ,OAAO,OAAO,IAAI,CAAC,SAAS,UAAU,YAAY,kBAAkB,aAAa,WAAW,WAAW,IAAI,CAAC;AAC9G,UAAI,cAAc,OAAO,QAAQ,UAAa,OAAO,GAAG,MAAM,UAAU;AACtE,YAAI,QAAQ;AAAA,MACd;AACA,UAAI,OAAO,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,QACxD,GAAG,OAAO,KAAK;AAAA,QACf,GAAG,OAAO,MAAM;AAAA,QAChB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,IAAI;AAAA,QACb,cAAc;AAAA,QACd;AAAA,MACF,GAAG,YAAY,SAAY;AAAA,QACzB,MAAM;AAAA,MACR,IAAI,CAAC,CAAC,GAAG,QAAQ,SAAY;AAAA,QAC3B;AAAA,MACF,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AACd,aAAO;AAAA,IACT,GAAG;AAAA,MACD,QAAQ,CAAC;AAAA,MACT,OAAO,aAAa;AAAA,IACtB,CAAC;AACD,UAAM,eAAe,KAAK,EAAE,YAAY,UAAa,EAAE,YAAY,SAAY;AAAA,MAC7E,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACP,IAAI;AACJ,UAAM,WAAW,IAAI,WAAW,OAAO,OAAO;AAAA,MAC5C,YAAY;AAAA,MACZ,OAAO,uCAA2B,OAAO,QAAQ;AAAA,MACjD,mBAAmB;AAAA,IACrB,GAAG,WAAW,CAAC,CAAC,CAAC;AACjB,WAAO;AACP,aAAS,GAAG,mBAAmB,CAAC;AAAA,MAC9B,SAAS;AAAA,IACX,MAAM;AACJ,UAAI,aAAa,KAAK,SAAS;AAC7B,6BAAiB,+BAAa,aAAa,KAAK,SAAS,aAAa,OAAO,CAAC;AAAA,MAChF,OAAO;AACL,yBAAiB,IAAI;AAAA,MACvB;AAAA,IACF,CAAC;AACD,aAAS,GAAG,SAAS,MAAM;AACzB,uBAAiB,IAAI;AAAA,IACvB,CAAC;AACD,QAAI,oBAAoB;AACtB,eAAS,GAAG,cAAc,MAAM;AAC9B,YAAI;AACJ,SAAC,KAAK,SAAS,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,UACzE,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,MAAM;AAAA,YACJ,aAAa;AAAA,YACb,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA;AAAA,YAAkC,CAAC,IAAI,iBAAiB;AACtD,iBAAG,aAAa,YAAY,EAAE;AAC9B,iBAAG,aAAa,UAAU,QAAQ;AAClC,iBAAG,aAAa,OAAO,UAAU;AACjC,uBAAS,GAAG,UAAU,MAAM;AAC1B,oBAAIC;AACJ,oBAAI,GAAGA,MAAK,aAAa,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,MAAM;AACrF;AAAA,gBACF;AACA,sBAAM,iBAAiB;AACvB,+BAAe,OAAO,aAAa,UAAU,KAAK;AAAA,cACpD,CAAC;AAAA,YACH;AAAA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,aAAa;AACf,eAAS,GAAG,cAAc,MAAM;AAC9B,YAAI;AACJ,SAAC,KAAK,SAAS,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,UACzE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA;AAAA,UAEV;AAAA;AAAA,YAAkC,CAAC,IAAI,iBAAiB;AAEtD,iBAAG,MAAM,WAAW;AACpB,iBAAG,MAAM,SAAS;AAClB,iBAAG,MAAM,OAAO;AAChB,iBAAG,MAAM,QAAQ;AACjB,iBAAG,MAAM,UAAU;AACnB,iBAAG,MAAM,QAAQ;AACjB,iBAAG,MAAM,YAAY;AACrB,iBAAG,MAAM,WAAW;AACpB,iBAAG,MAAM,aAAa;AACtB,iBAAG,MAAM,aAAa;AAEtB,uBAAS,GAAG,UAAU,MAAM;AAC1B,oBAAI,CAAC,aAAa,WAAW;AAC3B;AAAA,gBACF;AACA,sBAAM;AAAA,kBACJ;AAAA,kBACA;AAAA,gBACF,IAAI,aAAa,UAAU;AAE3B,mBAAG,YAAY,WAAW,OAAO;AAAA,cACnC,CAAC;AAAA,YACH;AAAA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,iBAAW,QAAQ,eAAa;AAC9B,iBAAS,GAAG,cAAc,MAAM;AAC9B,cAAI;AACJ,WAAC,KAAK,SAAS,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,SAAS;AAAA,QACtF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,OAAO,YAAY,YAAY;AACjC,cAAQ,IAAI,uBAAuB,QAAQ,CAAC;AAAA,IAC9C;AACA,QAAI,OAAO,iBAAiB,YAAY;AACtC,mBAAa,QAAQ;AAAA,IACvB;AACA,UAAM,kBAAkB,MAAM;AAC5B,aAAO;AAAA,QACL,SAAS;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,GAAG,cAAc,MAAM;AAC9B,UAAI;AACJ,UAAI,eAAe,QAAW;AAC5B;AAAA,MACF;AACA,YAAM,wBAAwB,8CAAkC,uBAAa,CAAC;AAG9E,UAAI,CAAC,uBAAuB;AAC1B,eAAO,QAAQ,UAAU,gBAAgB,GAAG,SAAS,KAAK;AAC1D;AAAA,MACF;AACA,YAAM,2BAA2B,SAAS,KAAK,OAAO,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAIpH,UAAI,0BAA0B;AAC5B;AAAA,MACF;AAEA,YAAM,UAAU,qBAAW;AAC3B,YAAM,cAAc,uBAAa;AACjC,YAAM,uBAAuB,qCAAwB,WAAW;AAChE,YAAM,wBAAwB,GAAG,OAAO,GAAG,uBAAuB,IAAI,oBAAoB,KAAK,EAAE;AACjG,YAAM,qBAAqB,GAAG,OAAO,IAAI,WAAW;AAGpD,aAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,SAAS,OAAO,qBAAqB;AAEvF,aAAO,QAAQ,UAAU,gBAAgB,GAAG,SAAS,OAAO,kBAAkB;AAAA,IAChF,CAAC;AACD,aAAS,GAAG,UAAU,MAAM;AAC1B,UAAI;AACJ,UAAI,eAAe,QAAW;AAC5B;AAAA,MACF;AACA,YAAM,QAAQ,KAAK,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,SAAS,YAAY;AACjH,YAAM,UAAU,qBAAW;AAC3B,YAAM,WAAW,qCAAwB,uBAAa,CAAC;AACvD,YAAM,gBAAgB,uBAAa;AAAA,QACjC,KAAK;AAAA,QACL;AAAA,MACF,CAAC;AACD,YAAM,qBAAqB,GAAG,OAAO,IAAI,QAAQ,IAAI,aAAa;AAElE,aAAO,QAAQ,aAAa,gBAAgB,GAAG,SAAS,OAAO,kBAAkB;AAAA,IACnF,CAAC;AACD,UAAM,gCAAgC,MAAM;AAC1C,UAAI,eAAe,QAAW;AAC5B;AAAA,MACF;AACA,UAAI,SAAS,MAAM;AACjB,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,WAAO,iBAAiB,YAAY,6BAA6B;AACjE,aAAS,GAAG,WAAW,MAAM;AAC3B,UAAI,eAAe,QAAW;AAC5B,eAAO,oBAAoB,YAAY,6BAA6B;AAGpE,YAAI,8CAAkC,uBAAa,CAAC,GAAG;AACrD,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACD,aAAS,KAAK;AACd,QAAI,OAAO,WAAW,YAAY;AAChC,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,SAAS,SAAS,YAAY,YAAY,cAAc,QAAQ,aAAa,kBAAkB,CAAC;AACpG,+BAAU,MAAM;AACd,WAAO,MAAM;AACX,UAAI,MAAM;AACR,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,gCAA4B,2BAAY,MAAM;AAClD,QAAI,eAAe,QAAW;AAC5B;AAAA,IACF;AACA,QAAI,SAAS,MAAM;AACjB;AAAA,IACF;AACA,UAAM,OAAO,uBAAa;AAC1B,QAAI,KAAK,SAAS,GAAG;AACnB;AAAA,IACF;AACA,UAAM,SAAS,uBAAa,IAAI;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,OAAO,CAAC,KAAK;AAChB;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,SAAS,GAAG;AAE5B,uBAAiB,UAAU;AAC3B;AAAA,IACF;AACA,QAAI,OAAO,QAAQ,OAAO,UAAU,GAAG;AACrC,WAAK,MAAM,GAAG;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,MAAM,UAAU,CAAC;AACrB,+BAAU,MAAM;AACd,8BAA0B;AAE1B,WAAO,iBAAiB,YAAY,yBAAyB;AAC7D,WAAO,MAAM;AACX,aAAO,oBAAoB,YAAY,yBAAyB;AAAA,IAClE;AAAA,EACF,GAAG,CAAC,yBAAyB,CAAC;AAC9B,QAAM,aAAS,2BAAY,SAAO;AAChC,UAAM,QAAQ,OAAO,GAAG;AAAA,EAC1B,GAAG,CAAC,CAAC;AACL,QAAM,UAAM,2BAAY,CAAC,KAAK,SAAS;AACrC,UAAM,QAAQ,IAAI,KAAK,IAAI;AAC3B,QAAI,iBAAiB,YAAY,MAAM;AACrC;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,iBAAiB,SAAS;AAEnC,WAAK,GAAG;AACR,uBAAiB,UAAU;AAC3B;AAAA,IACF;AACA,QAAI,CAAC,IAAI;AAEP,YAAM,QAAQ,SAAS,iBAAiB,SAAS,EAAE,IAAI;AACvD,YAAM,YAAY,MAAM,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,KAAK;AACxD,UAAI,WAAW;AACb,aAAK,SAAS;AACd,yBAAiB,UAAU;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,sBAAkB,2BAAY,SAAO;AACzC,WAAO,MAAM,QAAQ,IAAI,GAAG;AAAA,EAC9B,GAAG,CAAC,CAAC;AACL,QAAM,aAAS,2BAAY,WAAS;AAClC,SAAK,MAAM,MAAM,KAAK;AAAA,EACxB,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,mBAAe,uBAAQ,OAAO;AAAA,IAClC;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,MAAM;AAAA,IACN;AAAA,EACF,IAAI,CAAC,QAAQ,KAAK,MAAM,QAAQ,eAAe,CAAC;AAChD,SAAO,cAAAC,QAAM,cAAc,QAAQ,UAAU;AAAA,IAC3C,OAAO;AAAA,EACT,GAAG,UAAU,aAAa;AAC5B;AACA,QAAQ,YAAY;AAAA,EAClB,UAAU,kBAAAC,QAAU;AAAA,EACpB,SAAS,kBAAAA,QAAU;AAAA,EACnB,SAAS,kBAAAA,QAAU;AAAA,EACnB,YAAY,kBAAAA,QAAU;AAAA,EACtB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC5D,cAAc,kBAAAA,QAAU;AAAA,EACxB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,aAAa,kBAAAA,QAAU;AAAA,EACvB,oBAAoB,kBAAAA,QAAU;AAChC;;;AczXA,IAAAC,gBAAwD;AACxD,IAAAC,qBAAsB;;;ACTtB,IAAAC,gBAA2B;AAKpB,IAAM,aAAa,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,EACF,QAAI,0BAAW,OAAO;AACtB,SAAO;AAAA;AAAA;AAAA;AAAA,IAIL;AAAA,EACF;AACF;AACO,IAAM,gBAAgB,UAAM,0BAAW,OAAO;;;ADhBrD,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAUO,IAAM,OAAO,QAAM;AACxB,MAAI;AAAA,IACA;AAAA,EACF,IAAI,IACJ,YAAYA,QAAO,IAAI,CAAC,UAAU,CAAC;AACrC,QAAM,UAAM,sBAAO,IAAI;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAClB,QAAM,kBAAc,2BAAY,UAAQ;AACtC,QAAI,UAAU;AACd,QAAI,KAAK,SAAS;AAAA,EACpB,GAAG,CAAC,KAAK,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC;AACrC,QAAM,WAAO,2BAAY,WAAS;AAChC,QAAI,CAAC,gBAAgB,GAAG,GAAG;AACzB,YAAM,IAAI,WAAW;AAAA,IACvB;AACA,gBAAY,KAAK,MAAM,MAAM,KAAK;AAAA,EACpC,GAAG,CAAC,aAAa,eAAe,CAAC;AACjC,QAAM,sBAAkB,uBAAQ,OAAO;AAAA,IACrC,KAAK;AAAA,IACL;AAAA,EACF,IAAI,CAAC,aAAa,IAAI,CAAC;AACvB,+BAAU,MAAM;AACd,WAAO,MAAM;AACX,UAAI,IAAI,YAAY,MAAM;AACxB,eAAO,GAAG;AAAA,MACZ;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AACX,SAAO,SAAS,eAAe;AACjC;AACA,KAAK,YAAY;AAAA,EACf,UAAU,mBAAAC,QAAU,KAAK;AAAA,EACzB,UAAU,mBAAAA,QAAU;AAAA,EACpB,gBAAgB,mBAAAA,QAAU;AAAA,EAC1B,WAAW,mBAAAA,QAAU;AAAA,EACrB,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/D,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAChE,KAAK,mBAAAA,QAAU;AAAA,EACf,SAAS,mBAAAA,QAAU;AAAA,EACnB,SAAS,mBAAAA,QAAU;AAAA,EACnB,MAAM,mBAAAA,QAAU;AAAA,EAChB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC5D,SAAS,mBAAAA,QAAU;AACrB;", "names": ["createElement", "className", "tagName", "appendToEl", "el", "document", "append<PERSON><PERSON><PERSON>", "equalizePoints", "p1", "p2", "x", "y", "id", "undefined", "roundPoint", "p", "Math", "round", "getDistanceBetween", "abs", "sqrt", "pointsEqual", "clamp", "val", "min", "max", "toTransformString", "scale", "propValue", "setTransform", "style", "transform", "defaultCSSEasing", "setTransitionStyle", "prop", "duration", "ease", "transition", "setWidthHeight", "w", "h", "width", "height", "removeTransitionStyle", "decodeImage", "img", "decode", "catch", "complete", "Promise", "resolve", "reject", "onload", "onerror", "LOAD_STATE", "IDLE", "LOADING", "LOADED", "ERROR", "specialKeyUsed", "e", "button", "ctrl<PERSON>ey", "metaKey", "altKey", "shift<PERSON>ey", "getElementsFromOption", "option", "legacySelector", "parent", "elements", "Element", "NodeList", "Array", "isArray", "from", "selector", "querySelectorAll", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "vendor", "match", "supportsPassive", "window", "addEventListener", "Object", "defineProperty", "get", "DOMEvents", "constructor", "_pool", "add", "target", "type", "listener", "passive", "_toggleListener", "remove", "removeAll", "for<PERSON>ach", "poolItem", "unbind", "skip<PERSON><PERSON>", "methodName", "types", "split", "eType", "filter", "push", "eventOptions", "getViewportSize", "options", "pswp", "getViewportSizeFn", "newViewportSize", "documentElement", "clientWidth", "innerHeight", "parsePaddingOption", "viewportSize", "itemData", "index", "paddingValue", "paddingFn", "padding", "legacyPropName", "toUpperCase", "slice", "Number", "getPanAreaSize", "PanBounds", "slide", "currZoomLevel", "center", "update", "reset", "_updateAxis", "dispatch", "axis", "elSize", "paddingProp", "data", "panAreaSize", "correctPan", "panOffset", "MAX_IMAGE_WIDTH", "ZoomLevel", "elementSize", "fit", "fill", "vFill", "initial", "secondary", "max<PERSON><PERSON><PERSON>", "maxHeight", "hRatio", "vRatio", "_getInitial", "_getSecondary", "_getMax", "zoomLevels", "slideData", "_parseZoomLevelOption", "optionPrefix", "optionName", "optionValue", "Slide", "isActive", "currIndex", "currentResolution", "pan", "isFirstSlide", "opener", "isOpen", "content", "contentLoader", "getContentBySlide", "container", "holderElement", "heavyAppended", "bounds", "prevDis<PERSON><PERSON><PERSON><PERSON>", "prevDisplayedHeight", "setIsActive", "activate", "deactivate", "append", "transform<PERSON><PERSON>in", "calculateSize", "load", "updateContentSize", "appendHeavy", "zoomAndPanToInitial", "applyCurrentZoomPan", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mainScroll", "isShifted", "defaultPrevented", "destroy", "hasSlide", "resize", "panTo", "force", "scaleMultiplier", "sizeChanged", "setDisplayedSize", "getPlaceholderElement", "placeholder", "element", "zoomTo", "destZoomLevel", "centerPoint", "transitionDuration", "ignoreBounds", "isZoomable", "animations", "stopAllPan", "prevZoomLevel", "setZoomLevel", "calculateZoomToPanOffset", "finishTransition", "_setResolution", "startTransition", "isPan", "name", "getCurrentTransform", "onComplete", "easing", "toggleZoom", "zoomAnimationDuration", "point", "totalPanDistance", "getViewportCenterPoint", "zoomFactor", "panX", "panY", "isPannable", "Boolean", "_applyZoomTransform", "currSlide", "zoom", "newResolution", "PAN_END_FRICTION", "VERTICAL_DRAG_FRICTION", "MIN_RATIO_TO_CLOSE", "MIN_NEXT_SLIDE_SPEED", "project", "initialVelocity", "decelerationRate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gestures", "startPan", "start", "stopAll", "change", "prevP1", "dragAxis", "closeOnVerticalDrag", "isMultitouch", "_setPanWithFriction", "bgOpacity", "_getVerticalDragRatio", "applyBgOpacity", "mainScrollChanged", "_panOrMoveMainScroll", "end", "velocity", "indexDiff", "mainScrollShiftDiff", "getCurrSlideX", "currentSlideVisibilityRatio", "moveIndexBy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_finishPanGestureForAxis", "panPos", "restoreBgOpacity", "projectedPosition", "vDragRatio", "projectedVDragRatio", "close", "correctedPanPosition", "dampingRatio", "initialBgOpacity", "totalPanDist", "startSpring", "onUpdate", "pos", "animationProgressRatio", "floor", "delta", "newMainScrollX", "moveTo", "newPan", "allowPanToNext", "currSlideMainScrollX", "isLeftToRight", "isRightToLeft", "wasAtMinPanPosition", "wasAtMaxPanPosition", "potentialPan", "customFriction", "corrected<PERSON>an", "UPPER_ZOOM_FRICTION", "LOWER_ZOOM_FRICTION", "getZoomPointsCenter", "Zoom<PERSON><PERSON><PERSON>", "_startPan", "_startZoomPoint", "_zoomPoint", "_wasOverFitZoomLevel", "_startZoomLevel", "startP1", "startP2", "minZoomLevel", "maxZoomLevel", "pinchToClose", "_calculatePanForZoomLevel", "ignoreGesture", "destinationZoomLevel", "currZoomLevelNeedsChange", "initialPan", "destinationPan", "panNeedsChange", "naturalFrequency", "now", "newZoomLevel", "didTapOnMainContent", "event", "closest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "click", "originalEvent", "targetClassList", "classList", "isImageClick", "contains", "isBackgroundClick", "_doClickOrTapAction", "tap", "doubleTap", "actionName", "actionFullName", "call", "clickToCloseNonZoomable", "toggle", "AXIS_SWIPE_HYSTERISIS", "DOUBLE_TAP_DELAY", "MIN_TAP_DISTANCE", "Gestures", "prevP2", "_lastStartP1", "_intervalP1", "_numActivePoints", "_ongoingPointers", "_touchEventEnabled", "_pointerEventEnabled", "PointerEvent", "supportsTouch", "maxTouchPoints", "_intervalTime", "_velocityCalculated", "isDragging", "isZooming", "raf", "_tapTimer", "drag", "<PERSON><PERSON><PERSON><PERSON>", "on", "events", "scrollWrap", "_onClick", "bind", "_bindEvents", "ontouchmove", "ontouchend", "pref", "down", "up", "cancel", "cancelEvent", "onPointerDown", "onPointerMove", "onPointerUp", "isMousePointer", "pointerType", "preventDefault", "mouseDetected", "_preventPointerEventBehaviour", "_updatePoints", "_clearTapTimer", "_calculateDragDirection", "_updateStartPoints", "Date", "_rafStopLoop", "_rafRender<PERSON>oop", "_finishDrag", "_updateVelocity", "_finishTap", "_updatePrevPoints", "requestAnimationFrame", "time", "_getVelocity", "indexOf", "tap<PERSON>elay", "doubleTapAction", "setTimeout", "clearTimeout", "displacement", "cancelAnimationFrame", "preventPointerEvent", "applyFilters", "pointerEvent", "pointerIndex", "findIndex", "ongoingPointer", "pointerId", "splice", "_convertEventPosToPoint", "length", "touchEvent", "touches", "diff", "axisToCheck", "pageX", "offset", "pageY", "identifier", "stopPropagation", "MAIN_SCROLL_END_FRICTION", "MainScroll", "slideWidth", "_currPositionIndex", "_prevPositionIndex", "_containerShiftIndex", "itemHolders", "resizeSlides", "newSlideWidth", "spacing", "slideWidthChanged", "itemHolder", "resetPosition", "appendHolders", "i", "setAttribute", "display", "canBeSwiped", "getNumItems", "animate", "velocityX", "newIndex", "potentialIndex", "numSlides", "canLoop", "getLoopedIndex", "distance", "stopMainScroll", "destinationX", "updateCurrItem", "isMainScroll", "currDiff", "currDistance", "positionDifference", "diffAbs", "tempHolder", "shift", "<PERSON><PERSON><PERSON><PERSON>", "pop", "unshift", "updateLazy", "dragging", "newSlideIndexOffset", "KeyboardKeyCodesMap", "Escape", "z", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "Tab", "getKeyboardEventKey", "key", "isKeySupported", "Keyboard", "_wasFocused", "trapFocus", "initialPointerPos", "_focusRoot", "_onFocusIn", "_onKeyDown", "lastActiveElement", "activeElement", "returnFocus", "focus", "keydownAction", "isForward", "keyCode", "escKey", "arrowKeys", "template", "DEFAULT_EASING", "CSSAnimation", "props", "onFinish", "_target", "_onComplete", "_finished", "_onTransitionEnd", "_helperTimeout", "_finalizeAnimation", "removeEventListener", "DEFAULT_NATURAL_FREQUENCY", "DEFAULT_DAMPING_RATIO", "SpringEaser", "_dampingRatio", "_naturalFrequency", "_dampedFrequency", "easeFrame", "deltaPosition", "deltaTime", "coeff", "naturalDumpingPow", "E", "dumpedFCos", "cos", "dumpedFSin", "sin", "SpringAnimation", "_raf", "easer", "prevTime", "animationLoop", "Animations", "activeAnimations", "_start", "isSpring", "animation", "stop", "isPanR<PERSON>ning", "some", "ScrollWheel", "_onWheel", "deltaX", "deltaY", "wheelToZoom", "deltaMode", "clientX", "clientY", "addElementHTML", "htmlData", "isCustomSVG", "svgData", "out", "join", "size", "outlineID", "inner", "UIElement", "elementHTML", "html", "isButton", "toLowerCase", "title", "aria<PERSON><PERSON><PERSON>", "ariaText", "innerHTML", "onInit", "onClick", "onclick", "appendTo", "topBar", "initArrowButton", "isNextButton", "loop", "disabled", "arrowPrev", "order", "arrowNext", "closeButton", "zoomButton", "loadingIndicator", "indicatorElement", "isVisible", "delayTimeout", "toggleIndicatorClass", "setIndicatorVisibility", "visible", "updatePreloaderVisibility", "isLoading", "preloader<PERSON>elay", "ui", "counterIndicator", "counterElement", "innerText", "indexIndicatorSep", "setZoomedIn", "isZoomedIn", "UI", "isRegistered", "uiElementsData", "items", "_lastUpdatedZoomLevel", "init", "sort", "a", "b", "uiElementData", "registerElement", "_onZoomPanUpdate", "elementData", "isClosing", "currZoomLevelDiff", "potentialZoomLevel", "imageClickAction", "getBoundsByElement", "thumbAreaRect", "getBoundingClientRect", "left", "top", "getCroppedBoundsByElement", "imageWidth", "imageHeight", "fillZoomLevel", "offsetX", "offsetY", "innerRect", "getThumbBounds", "instance", "thumbBounds", "thumbnail", "thumbSelector", "matches", "querySelector", "thumbCropped", "PhotoSwipeEvent", "details", "assign", "Eventable", "_listeners", "_filters", "addFilter", "fn", "priority", "f1", "f2", "removeFilter", "args", "apply", "off", "Placeholder", "imageSrc", "imgEl", "decoding", "alt", "src", "parentNode", "Content", "displayedImageWidth", "displayedImageHeight", "isAttached", "isDecoding", "state", "removePlaceholder", "keepPlaceholder", "isLazy", "reload", "usePlaceholder", "placeholderSrc", "msrc", "placeholder<PERSON><PERSON>", "parentElement", "prepend", "isImageContent", "loadImage", "imageElement", "updateSrcsetSizes", "srcset", "onLoaded", "onError", "setSlide", "displayError", "isError", "isInitialSizeUpdate", "image", "sizesWidth", "dataset", "largestUsedSize", "parseInt", "sizes", "String", "lazyLoad", "errorMsgEl", "errorMsg", "supportsDecode", "finally", "appendImage", "MIN_SLIDES_TO_CACHE", "lazyLoadData", "createContentFromData", "zoomLevel", "ceil", "lazyLoadSlide", "getItemData", "ContentLoader", "limit", "preload", "_cachedItems", "loadSlideByIndex", "initialIndex", "getContentByIndex", "addToCache", "removeByIndex", "indexToRemove", "item", "removedItem", "find", "PhotoSwipeBase", "numItems", "dataSource", "_getGalleryDOMElements", "gallery", "dataSourceItem", "_domElementToItemData", "galleryElement", "children", "childSelector", "linkEl", "pswpSrc", "href", "pswpSrcset", "pswpWidth", "pswpHeight", "pswpType", "thumbnailEl", "currentSrc", "getAttribute", "pswpCropped", "cropped", "MIN_OPACITY", "Opener", "isClosed", "isOpening", "_duration", "_useAnimation", "_croppedZoom", "_animateRootOpacity", "_animateBgOpacity", "_placeholder", "_opacityElement", "_cropContainer1", "_cropContainer2", "_thumbBounds", "_prepareOpen", "open", "hideAnimationDuration", "maxWidthToAnimate", "_applyStartProps", "showAnimationDuration", "showHideAnimationType", "showHideOpacity", "_initialThumbBounds", "_animateZoom", "bg", "opacity", "overflow", "_setClosedStateZoomPan", "<PERSON><PERSON><PERSON><PERSON>", "decoded", "is<PERSON><PERSON>ying", "_initiate", "setProperty", "_animateToOpenState", "_animateToClosedState", "_onAnimationComplete", "_animateTo", "containerOnePanX", "containerOnePanY", "containerTwoPanX", "containerTwoPanY", "animProps", "defaultOptions", "bgClickAction", "tapAction", "PhotoSwipe", "_prepareOptions", "_prevViewportSize", "isDestroying", "hasMouse", "_initialItemData", "keyboard", "_createMainStructure", "rootClasses", "mainClass", "scrollWheel", "isNaN", "updateSize", "pageYOffset", "_handlePageResize", "_updatePageScrollOffset", "goTo", "next", "prev", "refreshSlideContent", "slideIndex", "potentialHolderIndex", "holder", "matchMedia", "test", "userAgent", "setScrollOffset", "body", "import_react", "pswp", "_a", "React", "PropTypes", "import_react", "import_prop_types", "import_react", "__rest", "PropTypes"]}