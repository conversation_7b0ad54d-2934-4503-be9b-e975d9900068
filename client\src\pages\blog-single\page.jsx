import Footer from "@/components/footers/Footer";

import Header from "@/components/headers/Header";

import React, { useState, useEffect } from "react";
import { menuItems } from "@/data/menu";
import { useParams } from "react-router-dom";
import Comments from "@/components/blog/Comments";
import Form from "@/components/blog/commentForm/Form";
import Widget1 from "@/components/blog/widgets/Widget1";
import { blogAPI } from "@/utils/api";
import { useTranslation } from "react-i18next";
import MultilingualSEO from "@/components/common/MultilingualSEO";

export default function ElegantBlogSinglePageDark() {
  let params = useParams();
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setLoading(true);
        const result = await blogAPI.getPost(params.id);

        if (result.response.ok && result.data) {
          console.log("Blog single API response:", result.data);
          setBlog(result.data.data || result.data);
        } else {
          console.error("Failed to fetch blog post:", result.response.status);
          setError("Blog post not found");
        }
      } catch (error) {
        console.error("Error fetching blog post:", error);
        setError("Failed to load blog post");
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchBlogPost();
    }
  }, [params.id]);

  // Helper function to get translation for current language
  const getTranslation = (post, field) => {
    if (!post || !post.translations) return "";
    const translation = post.translations.find(
      (t) => t.language === currentLanguage
    );
    return (
      translation?.[field] ||
      post.translations.find((t) => t.language === "en")?.[field] ||
      ""
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-1 light-content">
                <div className="container">
                  <div className="row">
                    <div className="col-12 text-center">
                      <h1>Loading...</h1>
                      <p>Please wait while we load the blog post.</p>
                    </div>
                  </div>
                </div>
              </section>
            </main>
          </div>
        </div>
      </div>
    );
  }

  // If no blog post found or error, show 404 or redirect
  if (!blog || error) {
    return (
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-1 light-content">
                <div className="container">
                  <div className="row">
                    <div className="col-12 text-center">
                      <h1>Blog Post Not Found</h1>
                      <p>
                        The blog post you&apos;re looking for doesn&apos;t
                        exist.
                      </p>
                      <a
                        href="/blog"
                        className="btn btn-mod btn-border btn-large btn-round"
                      >
                        Back to Blog
                      </a>
                    </div>
                  </div>
                </div>
              </section>
            </main>
          </div>
        </div>
      </div>
    );
  }

  const metadata = {
    title: `${getTranslation(blog, "title")} || DevSkills`,
    description: getTranslation(blog, "excerpt"),
  };
  return (
    <>
      <MultilingualSEO
        title={metadata.title}
        description={metadata.description}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-elegant/section-bg-1.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <div className="row">
                    <div className="col-lg-10 offset-lg-1">
                      <h1
                        className="hs-title-3a mb-0 wow fadeInUpShort"
                        data-wow-duration="0.6s"
                      >
                        {getTranslation(blog, "title")}
                      </h1>
                    </div>
                  </div>
                  {/* Author, Categories, Comments */}
                  <div
                    className="blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn"
                    data-wow-delay="0.2s"
                  >
                    <div className="d-inline-block me-3">
                      <a href="#">
                        <i className="mi-clock size-16" />
                        <span className="visually-hidden">Date:</span>{" "}
                        {new Date(
                          blog.publishedAt || blog.createdAt
                        ).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </a>
                    </div>
                    <div className="d-inline-block me-3">
                      <a href="#">
                        <i className="mi-user size-16" />
                        <span className="visually-hidden">Author:</span>{" "}
                        {blog.author?.name || "DevSkills Team"}
                      </a>
                    </div>
                    {blog.categories && blog.categories.length > 0 && (
                      <div className="d-inline-block me-3">
                        <i className="mi-folder size-16" />
                        <span className="visually-hidden">Category:</span>
                        <a href="#">{blog.categories[0].name}</a>
                      </div>
                    )}
                    <div className="d-inline-block me-3">
                      <i className="mi-time size-16" />
                      <span className="visually-hidden">Read time:</span>{" "}
                      {blog.readTime || 5} min
                    </div>
                  </div>
                  {/* End Author, Categories, Comments */}
                </div>
              </section>
              <section className="page-section bg-dark-1 light-content">
                <div className="container relative">
                  <div className="row">
                    {/* Content */}
                    <div className="col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last">
                      {/* Post */}
                      <div className="blog-item mb-80 mb-xs-40">
                        <div className="blog-item-body">
                          {blog.featuredImage && (
                            <div className="mb-40 mb-xs-30">
                              <img
                                src={blog.featuredImage}
                                alt={getTranslation(blog, "title")}
                                width={1350}
                                height={796}
                              />
                            </div>
                          )}

                          {/* Blog excerpt */}
                          <div className="lead mb-40">
                            {getTranslation(blog, "excerpt")}
                          </div>

                          {/* Blog content */}
                          <div
                            className="blog-content"
                            style={{
                              lineHeight: "1.8",
                              fontSize: "16px",
                            }}
                            dangerouslySetInnerHTML={{
                              __html: getTranslation(blog, "content"),
                            }}
                          />
                        </div>
                      </div>
                      {/* End Post */}
                      {/* Comments */}
                      <div className="mb-80 mb-xs-40">
                        <h4 className="blog-page-title">
                          Comments <small className="number">(3)</small>
                        </h4>
                        <ul className="media-list comment-list clearlist">
                          <Comments />
                        </ul>
                      </div>
                      {/* End Comments */}
                      {/* Add Comment */}
                      <div className="mb-80 mb-xs-40">
                        <h4 className="blog-page-title">Leave a comment</h4>
                        {/* Form */}
                        <Form />
                        {/* End Form */}
                      </div>
                      {/* End Add Comment */}
                      {/* Prev/Next Post */}
                      <div className="clearfix mt-40">
                        <a href="#" className="blog-item-more left">
                          <i className="mi-chevron-left" />
                          &nbsp;Prev post
                        </a>
                        <a href="#" className="blog-item-more right">
                          Next post&nbsp;
                          <i className="mi-chevron-right" />
                        </a>
                      </div>
                      {/* End Prev/Next Post */}
                    </div>
                    {/* End Content */}
                    {/* Sidebar */}
                    <div className="col-lg-4 col-xl-3">
                      <Widget1 searchInputClass="form-control input-lg search-field round" />
                      {/* End Widget */}
                    </div>
                    {/* End Sidebar */}
                  </div>
                </div>
              </section>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
