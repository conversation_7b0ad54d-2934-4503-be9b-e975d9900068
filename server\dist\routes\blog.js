"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const blogController_1 = require("../controllers/blogController");
const auth_1 = require("../middleware/auth");
const upload_1 = require("../middleware/upload");
const router = express_1.default.Router();
router.get("/", blogController_1.getBlogPosts);
router.get("/:slug", blogController_1.getBlogPost);
router.post("/", auth_1.authenticate, (0, auth_1.authorize)("ADMIN"), upload_1.uploadBlogImage.single("featuredImage"), blogController_1.createBlogPost);
router.put("/:id", auth_1.authenticate, (0, auth_1.authorize)("ADMIN"), upload_1.uploadBlogImage.single("featuredImage"), blogController_1.updateBlogPost);
router.delete("/:id", auth_1.authenticate, (0, auth_1.authorize)("ADMIN"), blogController_1.deleteBlogPost);
router.patch("/:id/toggle-visibility", auth_1.authenticate, (0, auth_1.authorize)("ADMIN"), blogController_1.toggleBlogPostVisibility);
exports.default = router;
