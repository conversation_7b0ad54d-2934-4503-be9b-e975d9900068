#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/nodemon@3.1.10/node_modules/nodemon/bin/node_modules:/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/nodemon@3.1.10/node_modules/nodemon/node_modules:/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/nodemon@3.1.10/node_modules:/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/nodemon@3.1.10/node_modules/nodemon/bin/node_modules:/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/nodemon@3.1.10/node_modules/nodemon/node_modules:/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/nodemon@3.1.10/node_modules:/mnt/c/Users/<USER>/dev-skills/server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nodemon/bin/nodemon.js" "$@"
else
  exec node  "$basedir/../nodemon/bin/nodemon.js" "$@"
fi
