import { Request, Response } from "express";
import { AuthRequest } from "../middleware/auth";
export declare const login: (req: Request, res: Response) => Promise<Response | void>;
export declare const register: (req: Request, res: Response) => Promise<Response | void>;
export declare const getMe: (req: AuthRequest, res: Response) => Promise<Response | void>;
export declare const logout: (_req: Request, res: Response) => Promise<Response>;
//# sourceMappingURL=authController.d.ts.map