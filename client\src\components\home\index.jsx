// client\src\components\home\index.jsx

import React from "react";
import PropTypes from "prop-types";
import About from "./About";
import { useTranslation } from "react-i18next";
import { usePageAnalytics } from "@/hooks/usePageAnalytics";

// Keeping imports for potential future use
// import Team from "./Team";
import Service from "./Service";
// import Portfolio from "./Portfolio";
import DevskillsBMS from "./DevskillsBMS";

import Blog from "./Blog";
// import NewsLetter from "./NewsLetter";
import Contact from "./Contact";
import { Link } from "react-router-dom";
import MarqueeDark from "./MarqueeDark";

export default function Home({ onePage = false, dark = false }) {
  const { t } = useTranslation();

  // Add page analytics tracking
  usePageAnalytics("DevSkills - Business Management Solutions");
  return (
    <>
      <section
        className={`page-section  scrollSpysection pb-40 ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="about"
      >
        <div className="container position-relative">
          <div className="row">
            <div className="col-lg-5 d-flex align-items-center mb-md-50">
              <div>
                <div className="wow linesAnimIn" data-splitting="lines">
                  <h2 className="section-title mb-30 mb-sm-20">
                    <span className="text-gray">{t("menu.about")}</span>{" "}
                    DEVSKILLS STUDIO
                    <span className="text-gray">.</span>
                  </h2>
                  <div className="text-gray mb-30 mb-sm-20">
                    <p className="mb-0">{t("home.about.description")}</p>
                  </div>
                </div>
                <div className="local-scroll wow fadeInUpShort wch-unset">
                  {onePage ? (
                    <>
                      {" "}
                      <a
                        href="#team"
                        className="link-hover-anim link-circle-1 align-middle"
                        data-link-animate="y"
                      >
                        <span className="link-strong link-strong-unhovered">
                          {t("home.about.learnMore")}{" "}
                          <span className="visually-hidden">
                            {t("home.about.visually-hidden")}
                          </span>{" "}
                          <i
                            className="mi-arrow-right size-18 align-middle"
                            aria-hidden="true"
                          ></i>
                        </span>
                        <span
                          className="link-strong link-strong-hovered"
                          aria-hidden="true"
                        >
                          {t("home.about.learnMore")}{" "}
                          <span className="visually-hidden">
                            {t("home.about.visually-hidden")}
                          </span>{" "}
                          <i
                            className="mi-arrow-right size-18 align-middle"
                            aria-hidden="true"
                          ></i>
                        </span>
                      </a>
                    </>
                  ) : (
                    <>
                      {" "}
                      <Link
                        to={`/elegant-about${dark ? "-dark" : ""}`}
                        className="link-hover-anim link-circle-1 align-middle"
                        data-link-animate="y"
                      >
                        <span className="link-strong link-strong-unhovered">
                          {t("home.about.learnMore")}{" "}
                          <span className="visually-hidden">
                            {t("home.about.visually-hidden")}
                          </span>{" "}
                          <i
                            className="mi-arrow-right size-18 align-middle"
                            aria-hidden="true"
                          ></i>
                        </span>
                        <span
                          className="link-strong link-strong-hovered"
                          aria-hidden="true"
                        >
                          {t("home.about.learnMore")}{" "}
                          <span className="visually-hidden">
                            {t("home.about.visually-hidden")}
                          </span>{" "}
                          <i
                            className="mi-arrow-right size-18 align-middle"
                            aria-hidden="true"
                          ></i>
                        </span>
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>
            <About />
          </div>
        </div>
      </section>

      <MarqueeDark />

      <section
        className={`page-section pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content scrollSpysection ${
          dark ? "bg-dark-1 light-content" : ""
        }`}
        style={{
          backgroundImage: "url(/assets/images/demo-elegant/5.jpg)",
        }}
        id="bms"
      >
        <DevskillsBMS />
      </section>

      <div className="page-section overflow-hidden">
        <MarqueeDark />
      </div>

      <section
        className="page-section pt-0 pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content"
        style={{
          backgroundImage: "url(/assets/images/demo-elegant/5.jpg)",
        }}
      >
        <div className="container position-relative">
          <div className="row">
            <div className="col-md-6 col-xl-5">
              <div className="call-action-1-images pb-60 pb-md-0 mt-n30 mt-md-70 mb-n30 mb-md-70 mb-sm-0">
                <div className="call-action-1-image-1 round grayscale">
                  <img
                    src="/assets/images/demo-elegant/code2.jpg"
                    width={678}
                    height={840}
                    alt="Image Description"
                  />
                </div>
                <div className="call-action-1-image-2">
                  <div
                    className="call-action-1-image-2-inner"
                    data-rellax-y=""
                    data-rellax-speed="0.7"
                    data-rellax-percentage="0.427"
                  >
                    <img
                      src="/assets/images/demo-elegant/code3.jpg"
                      alt="Image Description"
                      width={300}
                      height={409}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6 offset-xl-1 d-flex align-items-center">
              <div className="row small-section">
                <div className="col-xl-11">
                  <h2 className="section-title mb-30 mb-sm-20">
                    {t("home.services.title")}
                  </h2>
                  <div className="text-gray mb-30 mb-sm-20">
                    <p className="mb-0">{t("home.services.description")}</p>
                  </div>
                  <div className="local-scroll">
                    {onePage ? (
                      <>
                        {" "}
                        <a
                          href="#services"
                          className="link-hover-anim link-circle-1 align-middle"
                          data-link-animate="y"
                        >
                          <span className="link-strong link-strong-unhovered">
                            {t("home.services.viewServices")}{" "}
                            <i
                              className="mi-arrow-right size-18 align-middle"
                              aria-hidden="true"
                            ></i>
                          </span>
                          <span
                            className="link-strong link-strong-hovered"
                            aria-hidden="true"
                          >
                            {t("home.services.viewServices")}{" "}
                            <i
                              className="mi-arrow-right size-18 align-middle"
                              aria-hidden="true"
                            ></i>
                          </span>
                        </a>
                      </>
                    ) : (
                      <>
                        {" "}
                        <Link
                          to={`/alegant-services${dark ? "-dark" : ""}`}
                          className="link-hover-anim link-circle-1 align-middle"
                          data-link-animate="y"
                        >
                          <span className="link-strong link-strong-unhovered">
                            {t("home.services.viewServices")}{" "}
                            <i
                              className="mi-arrow-right size-18 align-middle"
                              aria-hidden="true"
                            ></i>
                          </span>
                          <span
                            className="link-strong link-strong-hovered"
                            aria-hidden="true"
                          >
                            {t("home.services.viewServices")}{" "}
                            <i
                              className="mi-arrow-right size-18 align-middle"
                              aria-hidden="true"
                            ></i>
                          </span>
                        </Link>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Commenting out Team section */}

      {/* <section
        className={`page-section pb-0  scrollSpysection  ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="team"
      >
        <Team />
      </section> */}

      <div className="page-section overflow-hidden">
        <MarqueeDark />
      </div>
      <section
        className={`page-section pt-0  scrollSpysection  ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="services"
      >
        <Service />
      </section>
      <hr className="mt-0 mb-0" />

      {/* Commenting out Portfolio section */}
      {/*
      <section
        className={`page-section pb-0  scrollSpysection  ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="portfolio"
      >
        <div className="container">
          <div className="row mb-70 mb-sm-50">
            <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
              <h2 className="section-title mb-30 mb-sm-20">
                <span className="text-gray">Our</span> Portfolio
                <span className="text-gray">.</span>
              </h2>
              <div className="text-gray">
                The action centric perspective is a label given to a collection
                of concepts, which are antithetical to the rational model.
              </div>
            </div>
          </div>
        </div>
        <Portfolio />
      </section>
      */}
      <section
        className={`small-section ${
          dark ? "bg-dark-2 light-content" : "bg-dark-1 light-content"
        } bg-dark-alpha-80 parallax-6`}
        style={{
          backgroundImage: "url(/assets/images/demo-elegant/5.jpg)",
        }}
      >
        <div className="container">
          <div className="row mb-n10">
            <div className="col-md-6 offset-md-1 col-lg-5 offset-lg-2 mb-sm-30 text-center text-md-start">
              <h2 className="section-title-small mb-0 opacity-08">
                {t("home.cta.title")}
              </h2>
            </div>
            <div className="col-md-4 col-lg-3 text-center text-md-end">
              <div className="mt-n20">
                {onePage ? (
                  <>
                    {" "}
                    <a
                      href="#contact"
                      className="link-hover-anim link-circle-1 align-middle"
                      data-link-animate="y"
                    >
                      <span className="link-strong link-strong-unhovered">
                        {t("home.cta.button")}
                      </span>
                      <span
                        className="link-strong link-strong-hovered"
                        aria-hidden="true"
                      >
                        {t("home.cta.button")}
                      </span>
                    </a>
                  </>
                ) : (
                  <>
                    {" "}
                    <Link
                      to={`/elegant-contact${dark ? "-dark" : ""}`}
                      className="link-hover-anim link-circle-1 align-middle"
                      data-link-animate="y"
                    >
                      <span className="link-strong link-strong-unhovered">
                        {t("home.cta.button")}
                      </span>
                      <span
                        className="link-strong link-strong-hovered"
                        aria-hidden="true"
                      >
                        {t("home.cta.button")}
                      </span>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Commenting out Newsletter section */}
      {/*
      <section
        className={`small-section pb-0 ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
      >
        <NewsLetter />
      </section>
      */}

      {/* Blog Section */}
      <section
        className={`page-section scrollSpysection ${
          dark ? "bg-dark-1 light-content" : ""
        }`}
        id="blog"
      >
        <Blog />
      </section>

      <section
        className={`page-section  scrollSpysection  ${
          dark ? "bg-dark-1 light-content" : ""
        } `}
        id="contact"
      >
        <div className="container">
          <div className="row mb-70 mb-sm-50">
            <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
              <h2 className="section-title mb-30 mb-sm-20">
                <span className="text-gray">{t("home.contact.title")}</span>
                <span className="text-gray">.</span>
              </h2>
            </div>
          </div>
        </div>
        <Contact />
      </section>
    </>
  );
}

Home.propTypes = {
  onePage: PropTypes.bool,
  dark: PropTypes.bool,
};
